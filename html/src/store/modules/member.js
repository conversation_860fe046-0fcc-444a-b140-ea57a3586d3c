// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------
const state = {
  tabsItemType: 0,
};

const mutations = {
  SET_TABS_ITEM_TYPE: (state, tabsItemType) => {
    state.tabsItemType = tabsItemType;
  },
};

const actions = {
  // 设置会员返现&奖励金展示
  setTabsItemType({ commit }, tabsItemType = 0) {
    return new Promise((resolve) => {
      commit("SET_TABS_ITEM_TYPE", tabsItemType);
      resolve();
    });
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
};
