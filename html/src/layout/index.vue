<template>
  <!-- <div :class="classObj" class="app-wrapper">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <sidebar class="sidebar-container" />
    <div :class="{ hasTagsView: needTagsView }" class="main-container">
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar />
        <tags-view v-if="needTagsView" />
      </div>
      <app-main />
      <right-panel v-if="showSettings">
        <settings />
      </right-panel>
    </div>
  </div> -->
  <el-container class="app-container">
    <!-- <el-header class="app-header"><Header /></el-header> -->
    <!-- <el-container class="app-main-box"> -->
    <el-aside class="app-aside"><sidebar /></el-aside>
    <el-main class="app-main">
      <div class="main-right">
        <div class="navbar-container">
          <navbar />
          <!-- <tags-view /> -->
        </div>
        <div class="main-content">
          <app-main />
          <!-- <div class="main-part"><app-main /></div>
          <div class="footer-info">
            <span>联系电话：028-62122223</span
            ><span style="margin-left: 20px">版本：V1.0</span>
          </div> -->
        </div>
      </div>
    </el-main>
    <!-- </el-container> -->
  </el-container>
</template>

<script>
import RightPanel from '@/components/RightPanel'
import {
  AppMain,
  Header,
  Navbar,
  Settings,
  Sidebar,
  TagsView
} from './components'
import ResizeMixin from './mixin/ResizeHandler'
import { mapState } from 'vuex'

export default {
  name: 'Layout',
  data() {
    return {
      openImage: true
    }
  },
  components: {
    AppMain,
    Navbar,
    Header,
    RightPanel,
    Settings,
    Sidebar,
    TagsView
  },
  mixins: [ResizeMixin],
  computed: {
    ...mapState({
      sidebar: (state) => state.app.sidebar,
      device: (state) => state.app.device,
      showSettings: (state) => state.settings.showSettings,
      needTagsView: (state) => state.settings.tagsView,
      fixedHeader: (state) => state.settings.fixedHeader
    }),
    classObj() {
      return {
        hideSidebar: !this.sidebar.opened,
        openSidebar: this.sidebar.opened,
        withoutAnimation: this.sidebar.withoutAnimation,
        mobile: this.device === 'mobile'
      }
    }
  },
  methods: {
    clear() {
      this.openImage = false
    },
    handleClickOutside() {
      this.$store.dispatch('app/closeSideBar', { withoutAnimation: false })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';
@import '~@/styles/variables.scss';
.app-container {
  width: 100vw;
  height: 100vh;
  padding: 0;
  .app-header {
    height: 60px;
    background-color: #fff;
    border-bottom: 2px solid #eee;
  }
  .app-aside {
    padding: 0;
    margin: 0;
    border-radius: 0;
    min-width: 80px;
    width: auto !important;
  }
  .app-main {
    padding: 0;
    .main-right {
      height: 100%;
      overflow: auto;
      display: flex;
      flex-direction: column;
      .navbar-container {
        border-bottom: 2px solid #eee;
      }
      .main-content {
        flex: 1;
        overflow-y: auto;
        /* padding: 16px 16px 0; */
        /* display: flex;
        flex-direction: column; */
        /* .main-part {
          flex: 1;
          overflow-y: auto;
          background-color: #fff;
          margin: 20px 20px 0;
          border-radius: 6px;
        }
        .footer-info {
          flex: 0 0 40px;
          padding: 0 20px;
          line-height: 40px;
          text-align: right;
          color: #888;
        } */
      }
    }
  }
}

.open-image {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  background-color: rgba(0, 0, 0, 0.6);
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  z-index: 999999;
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - 280px);
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 280px);
}

.mobile .fixed-header {
  width: 100%;
}
</style>
