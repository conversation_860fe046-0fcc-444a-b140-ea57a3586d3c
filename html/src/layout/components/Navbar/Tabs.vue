<template>
  <div class="tabs-container">
    <el-tabs class="el-tabs" v-model="type" @tab-click="handlerTabsClick">
      <el-tab-pane
        :label="item.name"
        :name="item.type.toString()"
        v-for="(item, index) in tabsItems"
        :key="index"
      />
    </el-tabs>
  </div>
</template>

<script>
import { mapGetters } from "vuex";

export default {
  data() {
    return {
      type: null,
    };
  },
  computed: {
    ...mapGetters(["tabsItemType"]),
  },
  mounted() {
    this.type = this.tabsItemType.toString();
    this.handlerTabsClick();
  },
  props: {
    tabsItems: {
      type: Array,
      default: () => [],
    },
  },
  methods: {
    handlerTabsClick() {
      this.$store.dispatch("member/setTabsItemType", this.type);
    },
  },
};
</script>

<style lang="scss" scoped>
.tabs-container {
  height: 100%;
  padding-top: 5px;
  .el-tabs {
    ::v-deep .el-tabs__item {
      &.is-active {
        font-size: 15px;
      }
      font-size: 14px;
      height: 44px;
    }
    ::v-deep .el-tabs__nav-wrap::after {
      height: 0 !important;
    }
  }
}
</style>
