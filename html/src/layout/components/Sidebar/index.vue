<template>
  <div :class="{ 'has-logo': showLogo, menu: true }">
    <!-- <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="variables.menuBackground"
        :text-color=" variables.menuColor"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="true"
        mode="vertical"
      >
        <sidebar-item v-for="route in sidebarRouters" :key="route.url" :item="route" :base-path="route.url" /> 
      </el-menu> -->
    <div class="first-menu">
      <div class="logo-part">
        <logo v-if="showLogo" />
      </div>

      <div class="menu-list">
        <div
          v-for="route in sidebarRouters"
          :key="route.url"
          @click="skipPage(route.url, route.name)"
          :class="`menu-item first-menu-item ${
            activeFirstMenu === route.url ? 'first-menu-active' : ''
          }`"
        >
          <span
            v-if="route.extra && route.extra.includes('custom_svg')"
            class="icon"
            ><svg-icon :icon-class="route.extra"
          /></span>
          <span
            v-else
            class="icon"
            ><i :class="`el-icon-${route.extra}`"></i
          ></span>
          <span class="first-menu-name">{{ route.name }}</span>
          <svg-icon
            style="color: red"
            class="menu-embellish"
            icon-class="menu_active_embellish"
          />
          <svg-icon
            style="color: red"
            class="menu-embellish right-top"
            icon-class="menu_active_embellish"
          />
        </div>
      </div>
      <!-- <div
          v-show="!showSidebar"
        class="fold"
      >
        <span
          class="fold-icon"
          @click="toggleSidebar"
          ><i class="el-icon-s-fold"></i
        ></span>
      </div> -->
    </div>
    <transition name="sidebar">
      <div
        v-show="showSidebar"
        class="child-menu"
      >
        <div class="header-title">
          <span class="title">{{ curFirstMenuTitle }}</span>
          <!-- <span
            class="fold-icon"
            @click="toggleSidebar"
            ><i class="el-icon-s-fold"></i
          ></span> -->
        </div>
        <div class="menu-list">
          <el-tree
            ref="chilMenuTree"
            :data="childMenuList"
            node-key="id"
            highlight-current
            default-expand-all
            :props="defaultProps"
            @node-click="handleNodeClick"
          ></el-tree>
        </div>
      </div>
    </transition>
  </div>
</template>

<script>
import { mapGetters, mapState } from 'vuex'
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/styles/variables.scss'
export default {
  components: { SidebarItem, Logo },
  data() {
    return {
      defaultProps: {
        children: 'child',
        label: 'name'
      },
      curFirstMenuTitle: '',
      showSidebar: true
    }
  },

  computed: {
    ...mapState(['settings']),
    ...mapGetters(['permission_routes', 'sidebarRouters', 'sidebar']),

    // 当前选中的一级菜单
    activeFirstMenu() {
      const { meta, path, matched } = this.$route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return matched[0].path
    },

    // 子集菜单
    childMenuList() {
      const childItem = this.sidebarRouters.find((route) => {
        return this.activeFirstMenu.includes(route.url)
      })
      if (childItem && childItem.child) return childItem.child
      return []
    },

    showLogo() {
      return this.$store.state.settings.sidebarLogo
    },
    variables() {
      return variables
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  },
  watch: {
    $route: {
      handler({ fullPath: curPath, matched, meta }) {
        const activeChildMenuId = this.findChildMenuId(
          this.childMenuList,
          meta.parentUrl || curPath
        )
        if (this.$route.path === '/shop/overview') {
          this.curFirstMenuTitle = '养乐铺子'
        } else {
          this.curFirstMenuTitle = matched[0].meta.title
        }
        this.$nextTick(() => {
          this.$refs.chilMenuTree.setCurrentKey(activeChildMenuId)
        })
      },
      immediate: true
    }
  },
  methods: {
    // 页面跳转
    skipPage(url, name) {
      this.$router.push(url)
    },
    // 递归寻找当前需要选中的子菜单ID
    findChildMenuId(childMenuList, curPath) {
      for (let item of childMenuList) {
        // 如果有子菜单，则递归调用查找
        if (item.child && item.child.length > 0) {
          const result = this.findChildMenuId(item.child, curPath)
          if (result) return result // 如果递归找到了匹配的项，则返回结果
        } else {
          // 如果当前项的 URL 匹配
          if (curPath === item.url) return item.id
        }
      }
    },
    toggleSidebar() {
      this.$nextTick(() => {
        this.showSidebar = !this.showSidebar
      })
    },
    // 跳转
    handleNodeClick(data, node) {
      if (node.isLeaf) {
        this.$router.push(data.url)
      }
    }
  }
}
</script>
<style scoped lang="scss">
.el-submenu__icon-arrow {
  color: #fff !important;
}
.menu {
  display: flex;
  color: white;
  background-color: #fff;
  width: 100%;
  height: 100%;
  text-align: center;
  overflow-y: auto;
  .first-menu {
    display: flex;
    flex-direction: column;
    z-index: 1;
    overflow-y: auto;
    flex: 0 0 80px;
    background-color: #323232;
    color: #c4c4c4;
    font-weight: 700;
    font-size: 14px;
    height: 100%;
    .logo-part {
      flex: 0 0 80px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .menu-list {
      flex: 1;
      overflow: auto;
      padding-top: 2px;
      &::-webkit-scrollbar-track-piece {
        background: #323232;
      }

      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba($color: #ffffff, $alpha: 0.3);
        border-radius: 20px;
      }
    }
  }
  .child-menu {
    overflow-y: auto;
    flex: 1;
    height: 100%;
    transition: transform 0.1s ease;
    width: 228px;
    border-right: 2px solid #eee;
    .header-title {
      display: flex;
      align-items: center;
      color: #333;
      padding: 9px 20px;
      padding-left: 36px;
      border-bottom: 2px solid #eee;
      font-size: 14px;
      .title {
        font-weight: 700;
        font-size: 16px;
      }
      .fold-icon {
        cursor: pointer;
        font-size: 16px;
        color: #666;
        &:hover {
          color: #e03d35;
        }
      }
    }
    .menu-list {
      padding-top: 20px;
    }
    ::v-deep .el-tree-node__content {
      height: 32px;
      color: #333333;
      margin: 6px 12px;
      &:hover {
        background-color: #fce4e4;
        border-radius: 4px;
      }
    }
    ::v-deep
      .el-tree--highlight-current
      .el-tree-node.is-current
      > .el-tree-node__content {
      border-radius: 4px;
      color: #e03d35;
      background-color: #fce4e4;
      font-weight: bold;
    }
  }
  .sidebar-enter,
  .sidebar-leave-to {
    transform: translateX(-100%);
  }

  .sidebar-enter-to,
  .sidebar-leave {
    transform: translateX(0);
  }
  .menu-item {
    cursor: pointer;
    line-height: 1;

    &.first-menu-item {
      position: relative;
      padding: 16px 0;
      display: flex;
      flex-direction: column;
      gap: 6px;
      .icon {
        font-size: 19px;
      }
      &:hover {
        color: #e03d35;
        background-color: white;
      }

      &.first-menu-active {
        color: #e03d35;
        background-color: white;
        .menu-embellish {
          display: block;
          z-index: 20;
        }
      }
      .menu-embellish {
        position: absolute;
        display: none;
        bottom: -2px;
        right: 0;
        font-size: 2px;
        fill: white;
        &.right-top {
          transform: rotate(90deg);
          top: -2px;
        }
      }
    }
  }
}
</style>
