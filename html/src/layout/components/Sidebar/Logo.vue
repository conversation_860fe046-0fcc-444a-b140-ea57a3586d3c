<template>
  <div class="sidebar-logo-container">
    
      <router-link key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logoSmall" :src="logoSmall" class="sidebar-logo-small" />
      </router-link>
   
  </div>
</template>

<script>
import * as systemConfigApi from "@/api/systemConfig.js";
export default {
  name: "SidebarLogo",
  props: {
    collapse: {
      type: Boolean,
      required: false,
    },
  },
  data() {
    return {
      logoSmall: "",
    };
  },
  mounted() {
    this.getSquareLogo();
  },
  methods: {
    getSquareLogo() {
      systemConfigApi
        .configGetUniq({ key: "site_logo_square" })
        .then((data) => {
          this.logoSmall = data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebarLogoFade-enter-active {
  transition: opacity 1.5s;
}

.sidebarLogoFade-enter,
.sidebarLogoFade-leave-to {
  opacity: 0;
}

.sidebar-logo-container {
  width: 44px;
  line-height: 44px;
  background-color: #fff;
  border-radius: 50%;

  /* line-height: 65px; */
  text-align: center;
  overflow: hidden;

  & .sidebar-logo-link {
    height: 100%;
    width: 100%;

    & .sidebar-logo-small {
      width: 36px;
      height: 36px;
      vertical-align: middle;
    }
  }
}
</style>
