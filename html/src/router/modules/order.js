// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from "@/layout";

const orderRouter = {
  path: "/order",
  component: Layout,
  redirect: "/order/index",
  name: "Order",
  alwaysShow: true,
  meta: {
    title: "订单",
    icon: "clipboard",
  },
  children: [
    {
      path: "index",
      component: () => import("@/views/order/index"),
      name: "OrderIndex",
      meta: { title: "订单管理" },
    },
    {
      path: "orderSetting",
      component: () => import("@/views/order/orderSetting"),
      name: "OrderSetting",
      meta: { title: "订单设置" },
    },
    {
      path: "orderList/orderDetails/:id?",
      component: () => import("@/views/order/orderDetails"),
      name: "OrderDetails",
      meta: { title: "订单详情", parentUrl: "/order/index" },
      hidden: true,
    },
    {
      path: "printingShipment",
      component: () => import("@/views/order/printingShipment"),
      name: "PrintingShipment",
      meta: { title: "打单发货" },
    },
    {
      path: "afterSales",
      component: () => import("@/views/order/afterSales"),
      name: "afterSales",
      meta: { title: "售后服务" },
    },
    {
      path: "afterSalesList/afterSalesDetails/:id?",
      component: () => import("@/views/order/afterSalesDetails"),
      name: "afterSalesDetails",
      meta: { title: "售后详情", parentUrl: "/order/afterSales" },
    },
    {
      path: "addressAudit",
      component: () => import("@/views/order/addressAudit/index"),
      name: "AddressAudit",
      meta: { title: "地址审核" },
    },
  ],
};

export default orderRouter;
