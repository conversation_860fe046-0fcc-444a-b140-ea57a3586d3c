// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/** When your routing table is too long, you can split it into small modules **/

import Layout from "@/layout";

const marketingRouter = {
  path: "/shop",
  component: Layout,
  redirect: "/shop/overview",
  name: "Shop",
  meta: {
    title: "店铺",
    icon: "dashboard",
  },
  children: [
    {
      path: "overview",
      component: () => import("@/views/shop/overview/index"),
      name: "Overview",
      meta: { title: "概况", icon: "" },
    },
    {
      path: "shopInfo",
      component: () => import("@/views/shop/shopInfo/index"),
      name: "ShopInfo",
      meta: { title: "店铺信息", icon: "" },
    },
    {
      path: "homePageMgt",
      component: () => import("@/views/shop/homePageMgt/index"),
      name: "HomePageMgt",
      meta: { title: "首页管理", icon: "" },
    },
    {
      path: "microPage",
      component: () => import("@/views/shop/microPage/index"),
      name: "microPage",
      meta: { title: "微页面", icon: "" },
    },
    {
      path: "microPage/creatMicroPage/:id?",
      component: () => import("@/views/shop/creatMicroPage/index"),
      name: "CreatMicroPage",
      meta: {
        title: "新增微页面",
        parentUrl: "/shop/microPage",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
  ],
};

export default marketingRouter;
