// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

import Layout from "@/layout";

const storeRouter = {
  path: "/store",
  component: Layout,
  redirect: "/store/index",
  name: "Store",
  meta: {
    title: "产品",
    icon: "clipboard",
  },
  children: [
    {
      path: "index",
      component: () => import("@/views/store/index"),
      name: "StoreIndex",
      meta: { title: "商品管理", icon: "" },
    },
    {
      path: "inventory",
      component: () => import("@/views/store/storeInventory/index"),
      name: "Inventory",
      meta: { title: "库存管理", icon: "" },
    },
    {
      path: "sort",
      component: () => import("@/views/store/sort/index"),
      name: "Sort",
      meta: { title: "产品分类", icon: "" },
    },
    {
      path: "match",
      component: () => import("@/views/store/storeMatch/index"),
      name: "Match",
      meta: { title: "产品组合", icon: "" },
    },
    {
      path: "freightSet",
      component: () => import("@/views/store/freightSet/index"),
      name: "freightSet",
      meta: { title: "运费模板", icon: "" },
    },
    {
      path: "templatesList/creatFreightTemplates/:id?/:isDisabled?",
      component: () => import("@/views/store/creatFreightTemplates/index"),
      name: "FreightTemplatesCreat",
      meta: {
        title: "新增运费模板",
        parentUrl: "/store/freightSet",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "matchList/creatMatch/:id?/:isDisabled?",
      component: () => import("@/views/store/creatStoreMatch/index"),
      name: "MatchCreat",
      meta: {
        title: "新增产品组合",
        parentUrl: "/store/match",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "attr",
      component: () => import("@/views/store/storeAttr/index"),
      name: "SortAttr",
      meta: { title: "产品规格", icon: "" },
    },
    {
      path: "comment",
      component: () => import("@/views/store/storeComment/index"),
      name: "StoreComment",
      meta: { title: "产品评论", icon: "" },
    },
    {
      path: "list/creatProduct/:id?/:isDisabled?",
      component: () => import("@/views/store/creatStore/index"),
      name: "SortCreat",
      meta: {
        title: "产品添加",
        noCache: true,
        parentUrl: "/store/index",
      },
      hidden: true,
    },
  ],
};

export default storeRouter;
