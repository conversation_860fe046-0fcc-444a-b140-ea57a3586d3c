// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/** When your routing table is too long, you can split it into small modules **/

import Layout from "@/layout";

const marketingRouter = {
  path: "/member",
  component: Layout,
  redirect: "/member/memberManagement",
  name: "Marketing",
  meta: {
    title: "会员",
    icon: "clipboard",
  },
  children: [
    {
      path: "memberManagement",
      component: () => import("@/views/member/memberManagement/index"),
      name: "MemberManagement",
      meta: { title: "会员管理", icon: "" },
    },
    {
      path: "discountCouponList/creatDiscountCoupon/:id?/:type?",
      component: () => import("@/views/marketing/creatDiscountCoupon/index"),
      name: "DiscountCouponCreat",
      meta: { title: "新增优惠券", noCache: true, icon: "" },
      hidden: true,
    },
    {
      path: "memberTagManagement",
      component: () => import("@/views/member/memberTagManagement/index"),
      name: "MemberTagManagement",
      meta: { title: "标签管理", icon: "" },
    },
    {
      path: "memberTagList/creatMemberTag/:id?",
      component: () => import("@/views/member/creatMemberTag/index"),
      name: "MemberTagCreat",
      meta: {
        title: "新增标签",
        parentUrl: "/member/memberTagManagement",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "memberSystem/memberDetails/:id?",
      component: () => import("@/views/member/memberDetails/index"),
      name: "MemberDetails",
      meta: {
        title: "会员详情",
        parentUrl: "/member/memberManagement",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "memberSystem",
      component: () => import("@/views/member/memberSystem/index"),
      name: "MemberSystem",
      meta: { title: "会员体系", icon: "" },
    },
    {
      path: "memberSystem/creatMemberSystem/:id?",
      component: () => import("@/views/member/creatMemberSystem/index"),
      name: "MemberSystemCreat",
      meta: {
        title: "新增会员等级",
        parentUrl: "/member/memberSystem",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "memberSystem/creatMemberSystem/:id?",
      component: () => import("@/views/member/creatMemberSystem/index"),
      name: "MemberSystemCreat",
      meta: {
        title: "新增会员等级",
        parentUrl: "/member/memberSystem",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },

    {
      path: "memberBalance",
      component: () => import("@/views/member/memberBalance/index"),
      name: "MemberBalance",
      meta: { title: "会员余额", icon: "" },
    },
    {
      path: "parameterSetting",
      component: () => import("@/views/member/parameterSetting/index"),
      name: "ParameterSetting",
      meta: { title: "参数设置", icon: "" },
    },
    {
      path: "memberBalance/balanceDetails",
      component: () => import("@/views/member/balanceDetails/index"),
      name: "BalanceDetails",
      meta: {
        title: "余额明细",
        parentUrl: "/member/memberBalance",
        noCache: true,
        icon: "",
      },
      hidden: true,
    },
    {
      path: "memberBalanceDetails",
      component: () => import("@/views/member/memberBalanceDetails/index"),
      name: "MemberBalanceDetails",
      meta: { title: "余额明细", icon: "" },
    },
    {
      path: "memberCashbackAndBonus",
      name: "MemberCashbackAndBonus",
      meta: { title: "返现&奖励金", icon: "" },
      component: () => import("@/views/member/memberCashbackAndBonus/index"),
      redirect: "/member/memberCashbackAndBonus/cashback",
      children: [
        {
          path: "cashback",
          component: () =>
            import("@/views/member/memberCashbackAndBonus/cashback/index"),
          name: "Cashback",
          meta: { title: "佣金返现", icon: "" },
        },
        {
          path: "bonus",
          component: () =>
            import("@/views/member/memberCashbackAndBonus/bonus/index"),
          name: "Bonus",
          meta: { title: "奖励金", icon: "" },
        },
      ],
    },
    {
      path: "memberBalanceWithdrawal",
      name: "MemberBalanceWithdrawal",
      meta: { title: "余额提现", icon: "" },
      component: () => import("@/views/member/memberBalanceWithdrawal/index"),
      redirect: "/member/memberBalanceWithdrawal/wechatWithdrawal",
      children: [
        {
          path: "wechatWithdrawal",
          component: () =>
            import(
              "@/views/member/memberBalanceWithdrawal/wechatWithdrawal/index"
            ),
          name: "WechatWithdrawal",
          meta: { title: "微信提现", icon: "" },
        },
      ],
    },
    {
      path: "relationshipQuery",
      component: () => import("@/views/member/relationshipQuery/index"),
      name: "RelationshipQuery",
      meta: { title: "关系查询", icon: "" },
    },
    {
      path: "salesReport",
      name: "SalesReport",
      meta: { title: "销售报表", icon: "" },
      component: () => import("@/views/member/salesReport/index"),
      redirect: "/member/salesReport/salesData",
      children: [
        {
          path: "salesData",
          component: () => import("@/views/member/salesReport/salesData/index"),
          name: "SalesData",
          meta: { title: "销售数据", icon: "" },
        },
        {
          path: "rankingList",
          component: () =>
            import("@/views/member/salesReport/rankingList/index"),
          name: "RankingList",
          meta: { title: "排行榜单", icon: "" },
        },
      ],
    },
    {
      path: "SVIPMemberManagement",
      component: () => import("@/views/member/SVIPMemberManagement/index"),
      name: "SVIPMemberManagement",
      meta: { title: "SVIP会员管理", icon: "" },
    },
    {
      path: "SVIPMemberManagement/creatSvip",
      component: () => import("@/views/member/creatSvip/index"),
      name: "SvipCreat",
      meta: {
        title: "新增Svip",
        noCache: true,
        parentUrl: "/member/SVIPMemberManagement",
        icon: "",
      },
      hidden: true,
    },
    // {
    //   path: 'memberSystemList/creatMemberSystem/:id?',
    //   component: () => import('@/views/marketing/creatMemberSystem/index'),
    //   name: 'MemberSystemCreat',
    //   meta: { title: '新增标签', noCache: true,  icon: '' },
    //   hidden: true
    // },
    // {
    //   path: 'memberDiscount',
    //   component: () => import('@/views/marketing/memberDiscount/index'),
    //   name: 'MemberDiscount',
    //   meta: { title: '会员折扣', icon: '' },
    // },
    // {
    //   path: 'memberDiscountList/creatMemberDiscount/:id?',
    //   component: () => import('@/views/marketing/creatMemberDiscount/index'),
    //   name: 'MemberDiscountCreat',
    //   meta: { title: '新建折扣', noCache: true,  icon: '' },
    //   hidden: true
    // },
    // {
    //   path: 'activityManagement',
    //   component: () => import('@/views/marketing/activityManagement/index'),
    //   name: 'ActivityManagement',
    //   meta: { title: '活动管理', icon: '' },
    // },
    // {
    //   path: 'activityList/creatActivity/:id?',
    //   component: () => import('@/views/marketing/creatActivity/index'),
    //   name: 'ActivityCreat',
    //   meta: { title: '新建活动', noCache: true,  icon: '' },
    //   hidden: true
    // },
    // {
    //   path: 'coupon',
    //   component: () => import('@/views/marketing/coupon/index'),
    //   name: 'Coupon',
    //   meta: { title: '优惠券', icon: '' },
    //   children: [
    //     {
    //       path: 'template',
    //       component: () =>
    //         import('@/views/marketing/coupon/couponTemplate/index'),
    //       name: 'couponTemplate',
    //       hidden: true,
    //       meta: { title: '优惠券模板', icon: '' }
    //     },
    //     {
    //       path: 'list/save/:id?',
    //       name: 'couponAdd',
    //       meta: {
    //         title: '优惠劵添加',
    //         noCache: true,
    //         activeMenu: `/marketing/coupon/list`
    //       },
    //       hidden: true,
    //       component: () => import('@/views/marketing/coupon/list/creatCoupon')
    //     },
    //     {
    //       path: 'list',
    //       component: () => import('@/views/marketing/coupon/list/index'),
    //       name: 'List',
    //       meta: { title: '优惠券列表', icon: '' }
    //     },
    //     {
    //       path: 'record',
    //       component: () => import('@/views/marketing/coupon/record/index'),
    //       name: 'Record',
    //       meta: { title: '领取记录', icon: '' }
    //     }
    //   ]
    // },
    // {
    //   path: 'discount',
    //   component: () => import('@/views/marketing/discount/index'),
    //   name: 'Discount',
    //   meta: { title: '折扣', icon: '' },
    //   children: [
    //     {
    //       path: 'template',
    //       component: () =>
    //         import('@/views/marketing/discount/discountTemplate/index'),
    //       name: 'discountTemplate',
    //       hidden: true,
    //       meta: { title: '折扣模板', icon: '' }
    //     },
    //     {
    //       path: 'list/save/:id?',
    //       name: 'discountAdd',
    //       meta: {
    //         title: '折扣添加',
    //         noCache: true,
    //         activeMenu: `/marketing/discount/list`
    //       },
    //       hidden: true,
    //       component: () => import('@/views/marketing/discount/list/creatDiscount')
    //     },
    //     {
    //       path: 'list',
    //       component: () => import('@/views/marketing/discount/list/index'),
    //       name: 'List',
    //       meta: { title: '折扣列表', icon: '' }
    //     },
    //     {
    //       path: 'record',
    //       component: () => import('@/views/marketing/discount/record/index'),
    //       name: 'Record',
    //       meta: { title: '领取记录', icon: '' }
    //     }
    //   ]
    // }
  ],
};

export default marketingRouter;
