<template>
  <div class="main-card">
    <div
      v-if="cardType == 1"
      class="card card1"
    >
      <div class="scroll">
        <div class="main"><slot /></div>
        <div class="footer"><FooterInfo /></div>
      </div>
    </div>
    <div
      v-if="cardType == 2"
      class="card card2"
    >
      <slot name="header" />

      <div class="scroll">
        <div class="main">
          <slot />
        </div>
        <!-- <div class="sys-info"><FooterInfo /></div> -->
        <div class="footer">
          <slot name="footer" />
          <div class="img-box">
            <img
              :src="img"
              alt=""
            />
          </div>
        </div>
      </div>
    </div>
    <div
      v-if="cardType == 3"
      class="card card3"
    >
      <slot name="header" />
      <div class="scroll">
        <div class="main">
          <slot />
        </div>
        <div class="footer"><FooterInfo /></div>
      </div>
    </div>
  </div>
</template>

<script>
import FooterInfo from '@/layout/components/FooterInfo.vue'
export default {
  components: { FooterInfo },
  data() {
    return {
      img: require('@/assets/imgs/ylpz.png')
    }
  },
  props: {
    cardType: {
      type: Number,
      default: 1
    }
  }
}
</script>

<style lang="scss" scoped>
.main-card {
  width: 100%;
  height: 100%;
  padding: 0 0 0 16px;
  .card {
    height: 100%;
    display: flex;
    flex-direction: column;
    .scroll {
      flex: 1;
      overflow-y: auto;
      overflow-x: hidden;
      padding: 16px 16px 0 0;
      .main {
        min-height: calc(100% - 60px);
      }
      .footer {
        position: sticky;
        bottom: 0;
        z-index: 3;
      }
    }
  }
  .card1 {
    .main {
      background-color: #fff;
      border-radius: 6px;
    }
    .footer {
      height: 60px;
      background-color: #f5f5f5;
    }
  }
  .card2 {
    .header {
      flex: 0 0 auto;
    }
    .main {
      flex: 1;
      border-radius: 6px 6px 0 0;
      padding-bottom: 32px;
    }
    .sys-info {
      height: 60px;
      margin: -20px;
    }
    .footer {
      position: relative;
      height: 60px;
      background-color: #fff;
      border-radius: 6px 6px 0 0;
      box-shadow: 0 -1px 6px rgba(0, 0, 0, 0.05);
      line-height: 60px;
      .img-box {
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        top: -50px;
        img {
          height: 20px;
          opacity: 0.7;
        }
      }
    }
  }
  .card3 {
    .header {
      flex: 0 0 auto;
    }
    .main {
      border-radius: 6px 6px 0 0;
      /* background-color: #fff; */
    }
    .sys-info {
      height: 60px;
      margin: -20px;
    }
    .footer {
      height: 60px;
      background-color: #f5f5f5;
    }
  }
}
</style>
