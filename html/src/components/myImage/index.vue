<template>
  <div
    class="image-container"
    :style="{
      width: (width || size) + 'px',
      height: width ? 'auto' : size + 'px',
    }"
  >
    <div
      class="image-type1"
      v-if="type == 1"
      style="width: 100%; height: 100%"
      @click="openPreview"
    >
      <img :src="imagePath" alt="Image" class="image" />
      <div
        v-if="isPreviewVisible"
        :style="`align-items: ${isImageOverflow ? 'flex-start' : 'center'} `"
        class="preview-modal"
        @click.stop
      >
        <div class="modal-content">
          <div class="close-button" @click.stop="closePreview">
            <i class="el-icon-close"></i>
          </div>
          <img
            @click.stop
            @load="checkImageOverflow"
            :src="previewPath"
            alt="Preview Image"
            class="preview-image"
            ref="previewImage"
          />
        </div>
      </div>
    </div>
    <div v-if="type == 2" class="image-type2" style="width: 100%; height: 100%">
      <img
        style="height: 100%; object-fit: contain"
        :src="imagePath"
        alt="Image"
        class="image"
      />
      <i @click.stop="openPreview" class="el-icon-zoom-in"></i>
      <div
        v-if="isPreviewVisible"
        @click.stop
        :style="`align-items: ${isImageOverflow ? 'flex-start' : 'center'} `"
        class="preview-modal"
      >
        <div class="modal-content">
          <div class="close-button" @click.stop="closePreview">
            <i class="el-icon-close"></i>
          </div>
          <img
            @load="checkImageOverflow"
            :src="previewPath"
            alt="Preview Image"
            class="preview-image"
            ref="previewImage"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    imagePath: {
      type: String,
      required: true,
    },
    previewPath: {
      type: String,
    },
    size: {
      type: Number,
      default: 48,
    },
    width: {
      type: Number,
    },
    type: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {
      isPreviewVisible: false,
      isImageOverflow: false,
    };
  },
  methods: {
    openPreview() {
      this.isPreviewVisible = true;
      this.checkImageOverflow();
    },
    closePreview() {
      this.isPreviewVisible = false;
    },
    checkImageOverflow() {
      const image = this.$refs.previewImage;
      if (image) {
        const viewportHeight = window.innerHeight;
        if (image.offsetHeight > viewportHeight) {
          this.$nextTick(() => {
            this.isImageOverflow = true;
          });
        } else {
          this.$nextTick(() => {
            this.isImageOverflow = false;
          });
        }
      }
    },
  },
  watch: {
    isPreviewVisible(newVal) {
      if (newVal) {
        window.addEventListener("resize", this.checkImageOverflow);
      } else {
        window.removeEventListener("resize", this.checkImageOverflow);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.image-container {
  cursor: pointer;
  display: inline-block;
  text-align: center;
}

.image-type1:hover .image {
  transform: scale(1.05);
}
.image {
  max-width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 2px;
  transition: transform 0.3s;
}

.preview-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  overflow-y: auto;
  z-index: 1000;
}

.modal-content {
  .close-button {
    background: rgba(255, 255, 255, 0.6);
    border-radius: 40px;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    cursor: pointer;
    height: 40px;
    position: fixed;
    right: 20px;
    top: 20px;
    width: 40px;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      background: #fff;
    }
  }
}
.image-type2 {
  position: relative;
  .el-icon-zoom-in {
    display: none;
  }
  &:hover {
    .el-icon-zoom-in {
      position: absolute;
      display: block;
      background-color: rgba(0, 0, 0, 0.3);
      color: #fff;
      padding: 4px;
      top: 0;
      left: 0;
      cursor: pointer;
      &:hover {
        background-color: rgba(0, 0, 0, 0.5);
      }
    }
  }
}
</style>
