<template>
  <div>
    <!-- <div>
      <button @click="printEditorHtml">print html</button>
      <button @click="getEditorText">print text</button>
    </div> -->
    <div style="border: 1px solid #ccc; margin-top: 10px">
      <!-- 工具栏 -->
      <Toolbar
        style="border-bottom: 1px solid #ccc"
        :editor="editor"
        :defaultConfig="toolbarConfig"
      />
      <!-- 编辑器 -->
      <Editor
        style="height: 400px; overflow-y: hidden"
        :defaultConfig="editorConfig"
        v-model="html"
        @onChange="onChange"
        @onCreated="onCreated"
      />
    </div>
    <!-- <div style="margin-top: 10px">
      <textarea
        v-model="html"
        readonly
        style="width: 100%; height: 200px; outline: none"
      ></textarea>
    </div> -->
  </div>
</template>

<script>
import { Editor, Toolbar } from "@wangeditor/editor-for-vue";
import { fileImageApi } from "@/api/systemSetting";
export default {
  name: "MyEditor",
  components: { Editor, Toolbar },
  props: {
    // 图片上传接口地址
    htmlContent: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      editor: null,
      html: "",
      toolbarConfig: {
        // toolbarKeys: [ /* 显示哪些菜单，如何排序、分组 */ ],
        // excludeKeys: [ /* 隐藏哪些菜单 */ ],
      },
      editorConfig: {
        placeholder: "请输入内容...",
        // autoFocus: false,

        // 所有的菜单配置，都要在 MENU_CONF 属性下
        MENU_CONF: {
          // 配置上传图片
          uploadImage: {
            // 自定义上传
            customUpload: this.customUploadImg,
            // 上传超时时间，默认为 10 秒
            timeout: 30 * 1000, // 30 秒
            // 限制文件大小，默认为 2M
            maxFileSize: 5 * 1024 * 1024, // 5M
            // 限制文件类型
            allowedFileTypes: ["image/*"],
          },
        },
      },
    };
  },
  watch: {
    html(val) {
      this.$emit("update:htmlContent", val);
    },
    htmlContent(val) {
      this.html = val;
    },
  },
  methods: {
    onCreated(editor) {
      this.editor = Object.seal(editor); // 【注意】一定要用 Object.seal() 否则会报错
    },
    onChange(editor) {
      console.log("onChange", editor.getHtml()); // onChange 时获取编辑器最新内容
    },
    getEditorText() {
      const editor = this.editor;
      if (editor == null) return;

      console.log(editor.getText()); // 执行 editor API
    },
    printEditorHtml() {
      const editor = this.editor;
      if (editor == null) return;

      console.log(editor.getHtml()); // 执行 editor API
    },

    // 自定义上传图片方法
    async customUploadImg(file, insertFn) {
      console.log("开始上传图片", file);
      let loading = this.$loading({
        lock: true,
        text: "上传中，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      try {
        // 创建FormData
        const formData = new FormData();
        formData.append("multipart", file);
        const data = {
          model: "content",
          pid: 0,
        };
        // 调用上传接口 - 这里使用axios或者你项目中的http请求方法
        const response = await fileImageApi(formData, data);
        loading.close();
        // 处理响应数据 - 根据你的后端接口返回格式调整
        if (response) {
          const imgUrl = response.url || response.thumbnailUrl;
          // 插入图片到编辑器
          insertFn(imgUrl, file.name, imgUrl);
          console.log("图片上传成功:", imgUrl);
        } else {
          throw new Error(response.message || "上传失败");
        }
      } catch (error) {
        console.error("图片上传失败:", error);
        loading.close();
        // 如果你使用了element-ui的message组件
        this.$message && this.$message.error(`图片上传失败: ${error.message}`);
      }
    },
  },
  beforeDestroy() {
    const editor = this.editor;
    if (editor == null) return;
    editor.destroy(); // 组件销毁时，及时销毁 editor ，重要！！！
  },
};
</script>

<style src="@wangeditor/editor/dist/css/style.css"></style>
<style>
.w-e-full-screen-container {
  z-index: 100;
}
</style>
