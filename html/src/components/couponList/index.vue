<template>
  <div>
    <div class="header clearfix">
      <div class="container">
        <MyHeaderSearch>
          <template v-slot:left>
            <!-- #/marketing/discountCoupon -->
            <el-button
              @click="openCouponList"
              type="primary"
              >优惠券管理</el-button
            >
            <el-button
              @click="getList(1)"
              type="text"
              >刷新</el-button
            >
          </template>
          <template v-slot:right>
            <el-form
              inline
              size="small"
            >
              <el-form-item label="优惠卷名称：">
                <el-input
                  v-model="tableFrom.name"
                  placeholder="请输入优惠券名称"
                  class="selWidth"
                  size="small"
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    size="small"
                    @click="getList(1)"
                  />
                </el-input>
              </el-form-item>
            </el-form>
          </template>
        </MyHeaderSearch>
      </div>
    </div>
    <el-table
      ref="table"
      v-loading="listLoading"
      :data="tableData.data"
      :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
      style="width: 100%"
      size="mini"
      max-height="400"
      tooltip-effect="dark"
      highlight-current-row
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        v-if="handle === 'wu'"
        type="selection"
        width="55"
      />
      <el-table-column
        prop="id"
        label="ID"
        min-width="50"
      />
      <el-table-column
        prop="name"
        label="优惠券名称"
      />
      <el-table-column label="优惠内容">
        <template slot-scope="scope">
          <span v-if="scope.row.couponType === 1">
            订单满{{ scope.row.minPrice }}元,减{{ scope.row.money }}元
          </span>
          <span v-if="scope.row.couponType === 2">
            无门槛,减{{ scope.row.money }}元
          </span>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="money"
        label="优惠券面值"
        min-width="90"
      /> -->
      <!-- <el-table-column
        prop="minPrice"
        label="最低消费额"
        min-width="90"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.minPrice===0?'不限制':scope.row.minPrice }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="有效期限"
        min-width="190"
      >
        <template slot-scope="scope">
          <span>{{ scope.row.isFixedTime===1?scope.row.useStartTime+' 一 '+scope.row.useEndTime:'不限制' }}</span>
        </template>
      </el-table-column> -->
      <!-- <el-table-column
        label="剩余数量"
        min-width="90"
      >
        <template slot-scope="scope">
          <span>{{ !scope.row.isLimited ? '不限量' : scope.row.lastTotal }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="handle === 'send'"
        label="操作"
        min-width="120"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            size="small"
            class="mr10"
            @click="sendGrant(scope.row.id)"
            v-hasPermi="['admin:coupon:user:receive']"
            >发送</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <div style="margin-top: 16px">
      <el-pagination
        :page-sizes="[10, 20, 30, 40]"
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
    <div
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="close">取 消</el-button>
      <el-button
        type="primary"
        @click="ok"
        >确 定</el-button
      >
    </div>
  </div>
</template>

<script>
import { marketingListApi, couponUserApi } from '@/api/marketing'
export default {
  name: 'CouponList',
  props: {
    handle: {
      type: String,
      default: ''
    },
    couponData: {
      type: Array,
      default: () => []
    },
    keyNum: {
      type: Number,
      default: 0
    },
    userIds: {
      type: String,
      default: ''
    },
    userType: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      isFirst: true,
      tableFrom: {
        page: 1,
        limit: 20,
        name: '',
        status: true,
        useType: ''
      },
      multipleSelection: [],
      multipleSelectionAll: [],
      idKey: 'id',
      nextPageFlag: false,
      attr: []
    }
  },
  watch: {
    keyNum: {
      deep: true,
      handler(val) {
        this.getList()
      }
    }
  },
  mounted() {
    this.tableFrom.page = 1
    this.init()
  },
  methods: {
    async init() {
      await this.getList()
      if (!this.couponData) return
      this.couponData.forEach((row) => {
        const row1 = this.tableData.data.find((item) => {
          return item.id == row.id
        })
        this.$refs.table.toggleRowSelection(row1)
      })
    },
    openCouponList() {
      window.open('/admin/#/marketing/discountCoupon')
    },
    close() {
      this.$parent.handleClose()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
      setTimeout(() => {
        this.changePageCoreRecordData()
      }, 50)
    },
    // 设置选中的方法
    setSelectRow() {
      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {
        return
      }
      // 标识当前行的唯一键的名称
      const idKey = this.idKey
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })
      this.$refs.table.clearSelection()
      for (var i = 0; i < this.tableData.data.length; i++) {
        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {
          // 设置选中，记住table组件需要使用ref="table"
          this.$refs.table.toggleRowSelection(this.tableData.data[i], true)
        }
      }
    },
    // 记忆选择核心方法
    changePageCoreRecordData() {
      // 标识当前行的唯一键的名称
      const idKey = this.idKey
      const that = this
      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算
      if (this.multipleSelectionAll.length <= 0) {
        this.multipleSelectionAll = this.multipleSelection
        return
      }
      // 总选择里面的key集合
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })
      const selectIds = []
      // 获取当前页选中的id
      this.multipleSelection.forEach((row) => {
        selectIds.push(row[idKey])
        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里
        if (selectAllIds.indexOf(row[idKey]) < 0) {
          that.multipleSelectionAll.push(row)
        }
      })
      const noSelectIds = []
      // 得到当前页没有选中的id
      this.tableData.data.forEach((row) => {
        if (selectIds.indexOf(row[idKey]) < 0) {
          noSelectIds.push(row[idKey])
        }
      })
      noSelectIds.forEach((id) => {
        if (selectAllIds.indexOf(id) >= 0) {
          for (let i = 0; i < that.multipleSelectionAll.length; i++) {
            if (that.multipleSelectionAll[i][idKey] == id) {
              // 如果总选择中有未被选中的，那么就删除这条
              that.multipleSelectionAll.splice(i, 1)
              break
            }
          }
        }
      })
    },
    ok() {
      // if (this.multipleSelection.length > 0) {
      this.$emit('getCouponId', this.multipleSelectionAll)
      this.multipleSelection = []
      // } else {
      // this.$message.warning("请先选择优惠劵");
      // }
    },
    // 列表
    async getList(num) {
      this.listLoading = true
      this.tableFrom.page = num ? num : this.tableFrom.page
      // this.userType ? (this.tableFrom.type = 1) : (this.tableFrom.type = 3);
      await marketingListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list
          this.tableData.total = res.total
          this.listLoading = false

          if (!this.isFirst) {
            this.$nextTick(() => {
              this.setSelectRow() // 调用跨页选中方法
            })
          }
        })
        .catch((res) => {
          this.listLoading = false
        })
    },
    pageChange(page) {
      this.changePageCoreRecordData()
      this.tableFrom.page = page
      this.isFirst = false
      this.getList()
    },
    handleSizeChange(val) {
      this.changePageCoreRecordData()
      this.tableFrom.limit = val
      this.isFirst = false
      this.getList()
    },
    // 发送
    sendGrant(id) {
      this.$modalSure('发送优惠劵吗').then(() => {
        couponUserApi({ couponId: id, uid: this.userIds }).then(() => {
          this.$message.success('发送成功')
          this.getList()
        })
      })
    }
  }
}
</script>

<style scoped lang="scss">
.selWidth {
  width: 219px !important;
}
.seachTiele {
  line-height: 35px;
}
.fr {
  float: right;
}
.dialog-footer {
  text-align: right;
}
</style>
