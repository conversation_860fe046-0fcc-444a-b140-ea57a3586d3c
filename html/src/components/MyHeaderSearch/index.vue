<template>
  <div class="header-search-container">
    <div class="newAdd">
      <slot name="left" />
    </div>
    <div class="search-form">
      <slot name="right" />
    </div>
  </div>
</template>

<script>
export default {
  name: "MyHeaderSearch",
};
</script>

<style lang="scss" scoped>
.header-search-container {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

::v-deep .el-form-item {
  margin-bottom: 0 !important;
}
</style>
