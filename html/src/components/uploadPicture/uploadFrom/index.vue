<template>
  <el-dialog
    title="素材中心"
    :visible.sync="visible"
    width="1020px"
    :modal="booleanVal"
    append-to-body
    :before-close="handleClose"
  >
    <div class="upload-form">
      <upload-index
        v-if="visible"
        :isMore="isMore"
        :modelName="modelName"
        @getImage="getImage"
      />
    </div>
  </el-dialog>
</template>

<script>
// import UploadIndex from '@/components/uploadPicture/index.vue'
export default {
  name: 'UploadFroms',
  // components: { UploadIndex },
  data() {
    return {
      visible: false,
      callback: function () {},
      isMore: '',
      modelName: '',
      ISmodal: false,
      booleanVal: false
    }
  },
  watch: {
    // show() {
    //   this.visible = this.show
    // }
  },
  methods: {
    handleClose() {
      this.visible = false
    },
    getImage(img) {
      this.callback(img)
      this.visible = false
    }
  }
}
</script>

<style scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.upload-form {
  height: 800px;
  overflow-y: auto;
}
</style>
