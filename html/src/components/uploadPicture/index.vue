<template>
  <div class="upload-picture">
    <!-- 左侧导航区域 -->
    <div class="left-panel">
      <div class="upload-section">
        <el-upload
          class="upload-demo"
          action
          :http-request="handleUploadForm"
          :on-change="imgSaveToUrl"
          :headers="myHeaders"
          :show-file-list="false"
          multiple
        >
          <el-button plain>
            <span>
              <i class="el-icon-upload2"></i>
              <span>上传图片</span>
            </span>
          </el-button>
        </el-upload>
      </div>

      <div class="search-section">
        <el-input
          v-model="filterText"
          placeholder="搜索分类名称"
          prefix-icon="el-icon-search"
          clearable
        />
      </div>

      <div class="tree-section">
        <div class="tree-container">
          <el-tree
            ref="tree"
            :data="treeData2"
            :filter-node-method="filterNode"
            :props="defaultProps"
            :highlight-current="true"
          >
            <div
              slot-scope="{ node, data }"
              @click="handleNodeClick(data)"
              class="custom-tree-node"
            >
              <div>
                <span class="custom-tree-node-label" :title="node.label">{{
                  node.label
                }}</span>
                <span
                  v-if="data.space_property_name"
                  style="font-size: 11px; color: #3889b1"
                  >（{{ data.name }}）</span
                >
              </div>
              <span @click.stop class="el-ic">
                <el-dropdown>
                  <span class="el-dropdown-link">
                    <i class="el-icon-more"></i>
                  </span>
                  <el-dropdown-menu slot="dropdown">
                    <el-dropdown-item @click.native="onAdd(data.id)"
                      >添加分类</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="node.label !== '未分类'"
                      @click.native="onEdit(data.id)"
                      >编辑分类</el-dropdown-item
                    >
                    <el-dropdown-item
                      v-if="node.label !== '未分类'"
                      @click.native="handleDelete(data.id)"
                      >删除分类</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </el-dropdown>
              </span>
            </div>
          </el-tree>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="right-panel">
      <div class="toolbar">
        <div class="toolbar-left">
          <!-- <el-tooltip
            class="item"
            effect="dark"
            content="上传图片"
            placement="top-start"
          >
            <el-upload
              class="upload-demo"
              action
              :http-request="handleUploadForm"
              :on-change="imgSaveToUrl"
              :headers="myHeaders"
              :show-file-list="false"
              multiple
            >
              <el-button
                icon="el-icon-upload2"
                size="small"
                v-if="!pictureType"
              ></el-button>
            </el-upload>
          </el-tooltip> -->

          <el-tooltip
            class="item"
            effect="dark"
            content="删除图片"
            placement="top-start"
          >
            <el-button
              icon="el-icon-delete"
              size="small"
              type="primary"
              plain
              @click.stop="editPicList('图片')"
              v-if="!pictureType"
            ></el-button>
          </el-tooltip>

          <!-- <el-upload
            class="upload-demo"
            action
            :http-request="handleUploadForm"
            :on-change="imgSaveToUrl"
            :headers="myHeaders"
            :show-file-list="false"
            multiple
          >
            <el-button type="primary" v-if="pictureType"
              >上传图片</el-button
            >
          </el-upload> -->

          <el-button
            icon="el-icon-delete"
            type="primary"
            plain
            @click.stop="editPicList('图片')"
            v-if="pictureType"
          ></el-button>

          <el-select
            class="move-img"
            v-model="sleOptions.attachment_category_name"
            placeholder="图片移动至"
            :size="pictureType ? '' : 'small'"
          >
            <el-option
              class="demo"
              :label="sleOptions.attachment_category_name"
              :value="sleOptions.attachment_category_id"
              style="min-height: 200px; background-color: #fff"
            >
              <el-tree
                ref="tree2"
                :data="treeData2"
                :filter-node-method="filterNode"
                :props="defaultProps"
                highlight-current
                @node-click="handleSelClick"
              />
            </el-option>
          </el-select>
        </div>

        <!-- <div class="toolbar-right">
          <el-radio-group v-model="typeDate" @change="radioChange" size="small">
            <el-radio-button label="pic">图片</el-radio-button>
            <el-radio-button label="video">视频</el-radio-button>
          </el-radio-group>
        </div> -->
      </div>

      <div class="content-area" v-loading="loadingPic">
        <div v-show="isShowPic" class="empty-state">
          <i
            class="el-icon-picture"
            style="font-size: 60px; color: rgb(219, 219, 219)"
          />
          <span class="empty-text">图片库为空</span>
        </div>

        <div class="image-grid">
          <div
            v-for="(item, index) in pictrueList.list"
            :key="index"
            class="grid-item"
            :style="`border:${item.isSelect ? '1px solid #409eff' : 'none'}`"
          >
            <span class="num-badge" v-if="item.num > 0">{{ item.num }}</span>
            <MyImage
              :type="2"
              :class="item.isSelect ? 'selected' : ''"
              @click.native="changImage(item, index, pictrueList.list)"
              v-if="item.attType !== 'video/mp4'"
              :imagePath="item.sattDir ? item.sattDir : localImg"
              :previewPath="item.attDir"
              :size="110"
            />
            <video
              :src="item.sattDir"
              :class="item.isSelect ? 'selected' : ''"
              @click="changImage(item, index, pictrueList.list)"
              v-if="item.attType == 'video/mp4'"
            ></video>
          </div>
        </div>
      </div>

      <div class="pagination-container">
        <el-pagination
          :page-sizes="!pictureType ? [10, 20, 30, 40] : [30, 60, 90, 120]"
          :page-size="tableData.limit"
          :current-page="tableData.page"
          :pager-count="5"
          size="mini"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pictrueList.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
        <el-button
          v-if="!pictureType"
          size="small"
          type="primary"
          @click="checkPics"
          >确认</el-button
        >
      </div>
    </div>

    <el-dialog
      :title="bizTitle"
      :visible.sync="visible"
      destroy-on-close
      @close="closeModel"
    >
      <el-form
        ref="editPram"
        :model="editPram"
        label-width="100px"
        v-loading="loading"
      >
        <el-form-item
          label="上级分类"
          prop="pid"
          :rules="[
            {
              type: 'number',
              required: true,
              message: '请选择上级分类',
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <el-cascader
            v-model="editPram.pid"
            :options="treeData2"
            :props="categoryProps"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item
          label="分类名称"
          prop="name"
          :rules="[
            {
              required: true,
              message: '请输入分类名称',
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <el-input v-model="editPram.name" placeholder="分类名称" />
        </el-form-item>
        <el-form-item label="排序">
          <el-input-number v-model="editPram.sort" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handlerSubmit('editPram')"
            >确定</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  addCategroy,
  listCategroy,
  treeCategroy,
  infoCategroy,
  updateCategroy,
  deleteCategroy,
} from "@/api/categoryApi";
import {
  fileImageApi,
  fileListApi,
  fileDeleteApi,
  attachmentMoveApi,
} from "@/api/systemSetting";
import { getToken } from "@/utils/auth";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
export default {
  name: "Upload",
  props: {
    pictureType: {
      type: String,
      default: "",
    },
    isMore: {
      type: String,
      default: "1",
    },
    modelName: {
      type: String,
      default: "",
    },
    checkedMore: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loadingPic: false,
      loading: false,
      modals: false,
      allTreeList: [],
      categoryProps: {
        value: "id",
        label: "name",
        children: "child",
        expandTrigger: "hover",
        checkStrictly: true,
        emitPath: false,
      },
      editPram: {
        pid: 1000,
        name: "",
        type: 2,
        sort: 1,
        status: 0,
        url: "url",
        id: 0,
      },
      visible: false,
      bizTitle: "",
      sleOptions: {
        attrId: "",
        pid: "",
      },
      list: [],
      filterText: "",
      treeData: [],
      treeData2: [],
      defaultProps: {
        children: "child",
        label: "name",
      },
      tableData: {
        page: 1,
        limit: 10,
        pid: 0,
        attType: "jpg,jpeg,gif,png,bmp,PNG,JPG,svg+xml",
      },
      classifyId: 0,
      myHeaders: { "X-Token": getToken() },
      treeFrom: {
        status: -1,
        type: 2,
      },
      pictrueList: {
        list: [],
        total: 0,
      },
      isShowPic: false,
      checkPicList: [],
      ids: [],
      listPram: {
        pid: 0,
        type: 2,
        status: 0,
        name: "",
        page: 1,
        limit: 9999,
      },
      localImg: "",
      videoStatus: false,
      typeDate: "pic",
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    this.pictureType
      ? (this.tableData.limit = 30)
      : (this.tableData.limit = 10);
    if (this.$route && this.$route.query.field === "dialog")
      import("./internal.js");
    this.getList();
    this.getFileList();
  },
  methods: {
    checkPermi,
    imgSaveToUrl(event) {
      this.localFile = event.raw;

      let reader = new FileReader();
      reader.readAsDataURL(this.localFile);

      reader.onload = () => {};

      let URL = window.URL || window.webkitURL;
      this.localImg = URL.createObjectURL(event.raw);
    },
    closeModel() {
      this.$refs["editPram"].resetFields();
    },
    handlerSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.editPram.pid == 10000) this.editPram.pid = 0;
          this.bizTitle === "添加分类"
            ? addCategroy(this.editPram).then((data) => {
                this.$message.success("创建成功");
                this.visible = false;
                this.getList();
              })
            : updateCategroy(this.editPram).then((data) => {
                this.$message.success("编辑成功");
                this.visible = false;
                this.getList();
              });
        } else {
          return false;
        }
      });
    },
    handlerGetList() {
      let datas = {
        name: "未分类",
        id: "",
      };
      treeCategroy(this.treeFrom).then((data) => {
        this.allTreeList = data;
        this.allTreeLis.unshift(datas);
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    getList() {
      const data = {
        name: "未分类",
        id: 10000,
      };
      treeCategroy(this.treeFrom).then((res) => {
        this.treeData = res;
        this.treeData.unshift(data);
        this.treeData2 = [...this.treeData];
      });
    },
    onAdd(id) {
      this.tableData.pid = id;
      if (this.tableData.pid === 10000) this.tableData.pid = 0;
      this.bizTitle = "添加分类";
      this.visible = true;
      if (id)
        this.editPram = {
          pid: id,
          name: "",
          type: 2,
          sort: 1,
          status: 0,
          url: "url",
          id: 0,
        };
    },
    onEdit(id) {
      if (id === 10000) id = 0;
      this.bizTitle = "编辑分类";
      this.loading = true;
      infoCategroy({ id: id }).then((res) => {
        this.editPram = res;
        this.loading = false;
      });
      this.visible = true;
    },
    handleDelete(id) {
      if (id === 10000) id = 0;
      this.$modalSure().then(() => {
        deleteCategroy({ id: id }).then(() => {
          this.$message.success("删除成功");
          this.getList();
        });
      });
    },
    handleNodeClick(data) {
      this.checkPicList = [];
      this.tableData.pid = data.id;
      this.getFileList();
    },
    handleUploadForm(param) {
      const formData = new FormData();
      const data = {
        model: this.modelName ? this.modelName : this.$route.path.split("/")[1],
        pid: this.tableData.pid,
      };
      formData.append("multipart", param.file);
      let loading = this.$loading({
        lock: true,
        text: "上传中，请稍候...",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });
      fileImageApi(formData, data)
        .then((res) => {
          loading.close();
          this.$message.success("上传成功");
          this.tableData.page = 1;
          this.getFileList();
        })
        .catch((res) => {
          loading.close();
        });
    },
    getFileList() {
      if (this.tableData.pid === 10000) this.tableData.pid = 0;
      this.loadingPic = true;
      fileListApi(this.tableData)
        .then(async (res) => {
          this.pictrueList.list = res.list;
          if (this.tableData.page === 1 && this.pictrueList.list.length > 0)
            this.pictrueList.list[0].localImg = this.localImg;
          if (this.pictrueList.list.length) {
            this.isShowPic = false;
          } else {
            this.isShowPic = true;
          }
          this.pictrueList.total = res.total;
          this.loadingPic = false;
        })
        .catch(() => {
          this.loadingPic = false;
        });
    },
    pageChange(page) {
      this.tableData.page = page;
      this.checkPicList = [];
      this.getFileList();
    },
    handleSizeChange(val) {
      this.tableData.limit = val;
      this.getFileList();
    },
    changImage(item, index, row) {
      let activeIndex = 0;
      if (!item.isSelect) {
        this.$set(item, "isSelect", true);
        this.checkPicList.push(item);
      } else {
        this.$set(item, "isSelect", !item.isSelect);
        this.checkPicList.map((el, index) => {
          if (el.attId == item.attId) {
            activeIndex = index;
          }
        });
        this.checkPicList.splice(activeIndex, 1);
      }
      this.ids = [];
      this.checkPicList.map((item, i) => {
        this.ids.push(item.attId);
      });

      this.pictrueList.list.map((el, i) => {
        if (el.isSelect) {
          this.checkPicList.filter((el2, j) => {
            if (el.attId == el2.attId) {
              el.num = j + 1;
              this.$nextTick(() => {
                this.pictrueList.list;
              });
            }
          });
        } else {
          el.num = 0;
        }
      });
    },
    checkPics() {
      if (!this.checkPicList.length)
        return this.$message.warning("请先选择图片");
      if (this.$route && this.$route.query.field === "dialog") {
        let str = "";
        for (let i = 0; i < this.checkPicList.length; i++) {
          str += '<img src="' + this.checkPicList[i].sattDir + '">';
        }
        nowEditor.dialog.close(true);
        nowEditor.editor.setContent(str, true);
      } else {
        if (this.isMore === "1" && this.checkPicList.length > 1) {
          return this.$message.warning("最多只能选一张图片");
        }

        this.$emit("getImage", [...this.checkedMore, ...this.checkPicList]);
      }
    },
    editPicList(tit) {
      if (!this.checkPicList.length)
        return this.$message.warning("请先选择图片");
      this.$modalSure().then(() => {
        fileDeleteApi(this.ids.join(",")).then(() => {
          this.$message.success("刪除成功");
          this.getFileList();
          this.checkPicList = [];
        });
      });
    },
    handleSelClick(node) {
      if (this.ids.length) {
        this.sleOptions = {
          attrId: this.ids.join(","),
          pid: node.id,
        };
        this.getMove();
      } else {
        this.$message.warning("请先选择图片");
      }
    },
    getMove() {
      attachmentMoveApi(this.sleOptions)
        .then(async (res) => {
          this.$message.success("操作成功");
          this.clearBoth();
          this.getFileList();
        })
        .catch((res) => {
          this.clearBoth();
        });
    },
    clearBoth() {
      this.sleOptions = {
        attrId: "",
        pid: "",
      };
      this.checkPicList = [];
      this.ids = [];
    },
    videoChange(val) {
      if (val == false) {
        this.$set(
          this.tableData,
          "attType",
          "jpg,jpeg,gif,png,bmp,PNG,JPG,svg"
        );
        this.getFileList();
      } else {
        this.$set(this.tableData, "attType", "video/mp4");
        this.getFileList();
      }
    },
    radioChange(val) {
      if (val === "video") {
        this.videoChange(true);
      } else {
        this.videoChange(false);
      }
    },
  },
};
</script>

<style scoped lang="scss">
.upload-picture {
  height: 100%;
  width: 100%;
  display: flex;
  overflow: hidden;
}

// 左侧面板
.left-panel {
  width: 300px;
  flex-shrink: 0;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  height: 100%;

  .upload-section {
    padding: 16px;
    /* border-bottom: 1px solid #e8e8e8; */
  }

  .search-section {
    padding: 0 16px;
    /* border-bottom: 1px solid #e8e8e8; */
  }

  .tree-section {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .tree-container {
      flex: 1;
      overflow-y: auto;
      padding: 12px 16px;
      @extend %scrollbar-style;
    }
  }
}

// 右侧面板
.right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;

  .toolbar {
    padding: 16px;
    /* border-bottom: 1px solid #e8e8e8; */
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 12px;
    flex-shrink: 0;

    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-wrap: wrap;
    }

    .toolbar-right {
      display: flex;
      align-items: center;
    }
  }

  .content-area {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    position: relative;

    .empty-state {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;

      .empty-text {
        font-size: 13px;
        color: #dbdbdb;
        margin-top: 12px;
      }
    }

    .image-grid {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(110px, 1fr));
      gap: 15px;
      align-content: start;
      @extend %scrollbar-style;
    }
  }

  .pagination-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-top: 1px solid #e8e8e8;
    flex-shrink: 0;
  }
}

// 网格项目
.grid-item {
  width: 110px;
  height: 110px;
  position: relative;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;

  img,
  video {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    border-radius: 4px;

    &.selected {
      border: 2px solid #409eff;
    }
  }

  .num-badge {
    position: absolute;
    bottom: 4px;
    right: 4px;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    background: #409eff;
    color: white;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
  }
}

// 保留必要的样式
.upload-demo {
  display: inline-block;
}

%scrollbar-style {
  &::-webkit-scrollbar {
    width: 6px;
  }
  &::-webkit-scrollbar-thumb {
    background-color: #ddd;
    border-radius: 3px;
  }
  &::-webkit-scrollbar-track {
    background-color: #f5f5f5;
  }
}

// 树形节点样式
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 8px;

  &-label {
    flex: 1;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin-right: 8px;
  }
}

.el-ic {
  display: none;

  i,
  span {
    font-size: 14px;
    font-weight: 600;
  }

  .svg-icon {
    color: #4386c6;
  }
}

::v-deep .el-tree-node__content {
  height: 36px;
  border-radius: 4px;
  margin-bottom: 2px;

  &:hover {
    background-color: #f0f0f0;

    .el-ic {
      color: #409eff !important;
      display: inline-block;
    }
  }
}

::v-deep
  .el-tree--highlight-current
  .el-tree-node.is-current
  > .el-tree-node__content {
  background-color: #f0f0f0;
  color: #666;
}

::v-deep .el-tree-node__expand-icon {
  color: #409eff;

  &.is-leaf {
    display: none;
  }
}

// 下拉选择器样式
.demo {
  overflow-x: hidden;
  overflow-y: auto;
 padding: 0 10px;
  ::v-deep .el-tree-node__content {
    font-weight: 500;
    padding: 0 10px !important;
  }
}

// 对话框样式保持不变
.el-dialog__body {
  .upload-container .image-preview {
    .image-preview-wrapper {
      width: 120px;
      img {
        height: 100px;
      }
    }

    .image-preview-action {
      line-height: 100px;
      height: 100px;
    }
  }

  .el-collapse-item__wrap,
  .spatial_img .el-collapse-item__wrap {
    margin-bottom: 0;
    padding-top: 0px;
  }
}
</style>
