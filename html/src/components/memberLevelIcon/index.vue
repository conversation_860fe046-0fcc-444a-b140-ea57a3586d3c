<template>
  <img
  class="img"
    :src="icon"
    :title="title"
    alt=""
  />
</template>

<script>
import { mapGetters } from "vuex";
export default {
  computed: {
    ...mapGetters(["memberLevelList"]),
    icon() {
      const curLevel = this.memberLevelList.find(
        (item) => item.grade == this.level
      );
      if (curLevel) return curLevel.icon;
    },
    title() {
      const curLevel = this.memberLevelList.find(
        (item) => item.grade == this.level
      );
      if (curLevel) return curLevel.name;
    },
  },
  props: {
    level: {
      type: Number,
      default: -1,
    },
    iconWidth: {
      type: Number,
      default: 32,
    },
  },
};
</script>

<style lang="scss" scoped>
.img {
  display: block;
}
</style>
