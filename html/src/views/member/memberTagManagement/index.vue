<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link
                :to="{
                  path: '/member/memberTagList/creatMemberTag',
                }"
              >
                <el-button type="primary">新增标签</el-button></router-link
              >
            </template>
          </MyHeaderSearch>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column label="标签名" prop="name"> </el-table-column>
        <el-table-column label="会员数" prop="memberCount"> </el-table-column>
        <el-table-column label="标签类型" prop="tagType">
          <template slot-scope="scope">
            <span>{{ scope.row.tagType == 1 && "手动打标" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="scope">
            <router-link
              :to="{
                path: '/member/memberTagList/creatMemberTag/' + scope.row.id,
              }"
            >
              <el-button type="text" size="small" class="mr10">编辑</el-button>
            </router-link>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              v-hasPermi="['admin:product:rule:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>
<script>
import { memberTagListApi, memberTagDeleteApi } from "@/api/memberTag";

export default {
  name: "StoreAttr",
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20,
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      listLoading: true,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    seachList() {
      this.getList();
    },

    // 列表
    getList() {
      this.listLoading = true;
      memberTagListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除id为" + id + "的标签吗？")
        .then(() => {
          memberTagDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.fr {
  float: right;
}

.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
</style>
