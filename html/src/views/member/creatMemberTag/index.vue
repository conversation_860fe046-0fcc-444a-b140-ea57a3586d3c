<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="store-info base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item style="width: 40%" label="标签名：" prop="name">
                <el-input v-model="formValidate.name" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="标签类型：" prop="tagType">
                <el-select
                  v-model="formValidate.tagType"
                  placeholder="标签类型"
                >
                  <el-option label="手动打标" :value="1"> </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  memberTagCreateApi,
  memberTagUpdateApi,
  memberTagInfoApi,
} from "@/api/memberTag";
import { Debounce } from "@/utils/validate";

const defaultObj = {
  name: "",
  tagType: 1,
};
export default {
  name: "ProductProductAdd",
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        name: [{ required: true, message: "请输入标签名称", trigger: "blur" }],
      },
      fullscreenLoading: false,
    };
  },

  mounted() {
    if (this.$route.params.id) {
      this.getInfo();
    }
  },
  methods: {
    // 详情
    getInfo() {
      this.fullscreenLoading = true;
      memberTagInfoApi(this.$route.params.id)
        .then(async (res) => {
          let info = res;
          this.formValidate = {
            name: info.name,
            tagType: info.tagType,
          };
          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.params.id
            ? memberTagUpdateApi({
                ...this.formValidate,
                id:this.$route.params.id,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/member/memberTagManagement" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : memberTagCreateApi({
                ...this.formValidate,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/member/memberTagManagement" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          this.$message.warning("请填写完整信息！");
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }

  .form-info {
    padding-left: 80px;
  }

  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
