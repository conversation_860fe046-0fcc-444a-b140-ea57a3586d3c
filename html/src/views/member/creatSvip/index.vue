<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item style="width: 60%" label="姓名：" prop="extField1">
                <el-input
                  v-model="formValidate.extField1"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item style="width: 60%" prop="phone" label="手机号：">
                <el-input v-model="formValidate.phone" placeholder="请输入" />
              </el-form-item>
              <el-form-item
                style="width: 60%"
                prop="extField2"
                label="身份证号："
              >
                <el-input
                  v-model="formValidate.extField2"
                  placeholder="请输入"
                />
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import { Debounce } from "@/utils/validate";
import {
  svipAddApi,
} from "@/api/menberSVIPManagement";
const defaultObj = {
  extField1: null,
  phone: null,
  extField2: null,
};
export default {
  name: "ProductProductAdd",
  data() {
    const validatePhone = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("手机号不能为空"));
      }
      const phoneRegex = /^1[3456789]\d{9}$/;
      if (!phoneRegex.test(value)) {
        return callback(new Error("手机号格式不正确"));
      }
      callback();
    };

    const validateIdCard = (rule, value, callback) => {
      if (!value) {
        return callback(new Error("身份证号不能为空"));
      }
      const idCardRegex =
        /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/;
      if (!idCardRegex.test(value)) {
        return callback(new Error("身份证号格式不正确"));
      }
      callback();
    };
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        extField1: [
          { required: true, message: "姓名不能为空", trigger: "blur" },
        ],
        phone: [
          { required: true, validator: validatePhone, trigger: "blur" },
        ],
        extField2: [
          { required: true, validator: validateIdCard, trigger: "blur" },
        ],
      },
    };
  },

  methods: {
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          svipAddApi({
            ...this.formValidate,
          })
            .then(async (res) => {
              this.$message.success("新增成功");
              setTimeout(() => {
                this.$router.push({ path: "/member/SVIPMemberManagement" });
              }, 500);
            })
            .catch((res) => {});
        } else {
          // this.$message.warning("请填写完整信息！");
          // 这里可以根据需要，更精确地提示哪个字段不合法
          if (!this.formValidate.extField1) {
            this.$message.warning("姓名不能为空！");
          } else if (!this.formValidate.phone) {
            this.$message.warning("手机号不能为空！");
          } else if (!/^1[3456789]\d{9}$/.test(this.formValidate.phone)) {
            this.$message.warning("手机号格式不正确！");
          } else if (!this.formValidate.extField2) {
            this.$message.warning("身份证号不能为空！");
          } else if (
            !/^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/.test(
              this.formValidate.extField2
            )
          ) {
            this.$message.warning("身份证号格式不正确！");
          }
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }
  .form-info {
    padding-left: 80px;
    .upLoadPicBox {
      display: inline-flex;
    }
  }

  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>