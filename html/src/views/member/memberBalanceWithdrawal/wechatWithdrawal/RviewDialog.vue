<template>
  <el-dialog
    :title="oprationType == 'singleRview' ? '提现审核' : '批量提现审核'"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="40%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <!-- <div class="left-section">
        <div class="base-card">
          <div class="info-title">申请人：</div>
          <div class="details-info">
            <el-row :gutter="20">
              <el-col :span="12"
                ><div><span>用户昵称：</span><span>套路</span></div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>手机号：</span><span>18998110213 拷贝</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>申请时间：</span><span>2024-02-22 09:21</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div><span>成长值：</span><span>50421</span></div></el-col
              >
            </el-row>
          </div>
        </div>
        <div class="base-card">
          <div class="info-title">申请资料信息：</div>
          <div class="details-info">
            <el-row :gutter="20">
              <el-col :span="12"
                ><div><span>真实姓名：</span><span>套路</span></div></el-col
              >

              <el-col :span="12"
                ><div><span>身份证号：</span><span>xxx</span></div></el-col
              >
            </el-row>
          </div>
        </div>
      </div> -->

      <div class="right-section">
        <!-- <div class="title">是否通过审核？</div> -->
        <div class="form">
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="100px"
            @submit.native.prevent
          >
            <el-form-item label="审核意见：" prop="status">
              <el-radio-group class="radio-group" v-model="formValidate.status">
                <el-radio-button :label="1">审核通过</el-radio-button>
                <el-radio-button :label="-1">不通过</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formValidate.status == -1"
              label="拒绝原因："
              prop="backMessage"
            >
              <el-input
                :autosize="{ minRows: 10, maxRows: 14 }"
                type="textarea"
                v-model="formValidate.backMessage"
              ></el-input>
            </el-form-item>
            <!-- <el-form-item>
              <div class="btn">
                <el-button @click="handleSubmit('notPassed', 'formValidate')"
                  >不通过</el-button
                >
                <el-button
                  type="primary"
                  class="submission"
                  @click="handleSubmit('passed')"
                  >审核通过</el-button
                >
              </div>
            </el-form-item> -->
          </el-form>
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSubmit('formValidate')"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  memberFinanceApplyApi,
  memberBatchFinanceApplyApi,
} from "@/api/memberBalanceWithdrawal";
import { Debounce } from "@/utils/validate";
const defaultObj = () => ({
  backMessage: null,
  status: 1,
});
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({}),
    },
    selectUserId: {
      type: Array,
      default: () => [],
    },
    oprationType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),

      ruleValidate: {
        backMessage: [{ required: true, message: "请输入", trigger: "blur" }],
        status: [{ required: true, message: "请选择", trigger: "change" }],
      },
    };
  },
  methods: {
    // 提交
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.oprationType == "singleRview") {
            memberFinanceApplyApi({
              ...this.formValidate,
              id: this.curRow.id,
            }).then((res) => {
              this.$message.success("操作成功");
              this.handleClose();
              this.$emit("success");
            });
          } else {
            memberBatchFinanceApplyApi({
              ...this.formValidate,
              ids: this.selectUserId.join(","),
            }).then((res) => {
              this.$message.success("操作成功");
              this.handleClose();
              this.$emit("success");
            });
          }
        }
      });
    },
    handlerOpen() {
      // console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false;
      this.formValidate = Object.assign({}, defaultObj());
      this.$refs.formValidate.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  .left-section {
    flex: 2;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .base-card {
      margin-bottom: 16px;
      .info-title {
        font-size: 14px;
        font-weight: 700;
        margin: 0 0 20px 0;
        &::before {
          content: "";
          display: inline-block;
          width: 5px;
          height: 14px;
          background-color: rgb(3, 158, 3);
          vertical-align: -2px;
          margin-right: 8px;
        }
      }
      .details-info {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }

  .right-section {
    position: relative;
    flex: 1;
    overflow: hidden;
    padding: 20px;

    .title {
      margin: 10px 0 10px;
      font-size: 18px;
      font-weight: 700;
    }

    .btn {
      display: flex;
      justify-content: center;
      gap: 10px;
      .submission {
        width: 100px;
      }
    }

    .form {
      .detail-description {
        font-size: 12px;
        color: #666;
        line-height: 1;
      }
      .inline-flex {
        display: inline-flex;
        gap: 10px;
        align-items: center;
        white-space: nowrap;
      }
    }
  }
}
</style>
