<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="tabs">
          <el-tabs
            v-model="tableFrom.queryType"
            @tab-click="seachList"
          >
            <el-tab-pane
              :label="item.name"
              :name="item.type.toString()"
              v-for="(item, index) in tabsItems"
              :key="index"
            />
          </el-tabs>
        </div>
        <div class="search-form">
          <el-form
            label-width="110px"
            inline
            size="small"
          >
            <el-form-item label="申请时间：">
              <el-date-picker
                style="width: 300px !important"
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="left"
                clearable
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="提现单号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.extractNumber"
                placeholder="订单号"
                class="selWidth"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="状态：">
              <el-select
                clearable
                v-model="tableFrom.status"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button
                @click="seachList"
                type="primary"
                >筛选</el-button
              >
              <el-button
                type="text"
                @click="reset"
                >重置筛选条件</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div
          v-if="tableFrom.queryType == 1"
          class="tips"
        >
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            style="margin-right: 6px"
          ></el-checkbox>
          <span class="mr10">已选 {{ selectId.length }}</span>
          <span class="mr10">
            <el-button
              @click="handlerRview('patchRview')"
              size="small"
              >批量审核</el-button
            ></span
          >
        </div>
        <div
          v-else
          class="statistics-data"
        >
          <div class="left">
            <div class="statistics">
              <span class="statistics-item"
                ><span>提现金额合计：</span
                ><span>{{ statisticsInfo }}</span></span
              >
            </div>
          </div>
        </div>
      </div>
      <el-table
        :key="tableFrom.queryType"
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :highlight-current-row="true"
      >
        <el-table-column
          v-if="tableFrom.queryType == 1"
          align="center"
          :selectable="checkSelectable"
          type="selection"
          width="55"
        >
        </el-table-column>
        <el-table-column
          prop="orderNo"
          label="提现单号"
        />
        <el-table-column label="会员">
          <template slot-scope="scope">
            <div class="user">
              <div class="user-info">
                <div
                  style="color: #3d71d2"
                  class="user-name"
                >
                  {{ scope.row.nickName }}
                </div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="price"
          label="提现金额/手续费"
        >
          <template slot-scope="scope">
            <div>提现金额：￥{{ scope.row.extractPrice }}</div>
            <div>手续费：￥{{ scope.row.fee }}</div>
          </template>
        </el-table-column>

        <el-table-column
          prop="createTime"
          label="申请时间"
        />
        <el-table-column
          prop="status"
          label="状态"
        >
          <template slot-scope="scope">
            <span>{{
              statusList.find((item) => item.value === scope.row.status).name
            }}</span>
          </template></el-table-column
        >
        <template v-if="tableFrom.queryType == 1">
          <el-table-column
            label="操作"
            min-width="150"
            fixed="right"
          >
            <template slot-scope="scope">
              <el-button
                v-if="scope.row.status == 0"
                @click="handlerRview('singleRview', scope.row)"
                type="text"
                size="small"
                >审核</el-button
              >
              <span v-if="scope.row.status == 1"
                >审核人：{{ scope.row.approveAdminName }}，{{
                  scope.row.approveTime
                }}</span
              >
              <div v-if="scope.row.status == -1">
                <div style="color: #3d71d2">{{ scope.row.failMsg }}</div>
                <div>
                  <span
                    >审核人：{{ scope.row.approveAdminName }}，{{
                      scope.row.failTime
                    }}</span
                  >
                </div>
              </div>
            </template>
          </el-table-column>
        </template>
        <template v-else>
          <el-table-column
            prop="createTime"
            label="付款流水号/原因"
          >
            <template slot-scope="scope">
              <span v-if="scope.row.status == 2"
                >付款流水号：{{ scope.row.paymentNo }}</span
              >
              <span
                style="color: #dd4130"
                v-if="scope.row.status == 3"
                >{{ scope.row.failMsg }}</span
              >
            </template>
          </el-table-column>
        </template>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <RviewDialog
      ref="rviewDialog"
      :oprationType="oprationType"
      :selectUserId="selectId"
      :curRow="curRow"
      @success="seachList"
    />
  </MainCard>
</template>

<script>
import { mapGetters } from 'vuex'
import RviewDialog from './RviewDialog'
import { memberFinanceApplyListApi } from '@/api/memberBalanceWithdrawal'
import { checkPermi } from '@/utils/permission' // 权限判断函数

export default {
  components: {
    RviewDialog
  },
  data() {
    return {
      statusList: [
        { name: '已拒绝', value: -1 },
        { name: '待审核', value: 0 },
        { name: '审核通过', value: 1 },
        { name: '提现成功', value: 2 },
        { name: '打款失败', value: 3 }
      ],
      tabsItems: [
        { name: '提现审核', type: '1' },
        { name: '提现记录', type: '2' }
      ],
      time: [],
      listLoading: true,
      oprationType: '',
      tableData: {
        data: [],
        total: 0
      },
      selectId: [],
      checkAll: false,
      isIndeterminate: false,
      statisticsInfo: '',
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        extractNumber: null,
        queryType: '1',
        status: null
      }
    }
  },
  computed: {
    ...mapGetters(['tabsItemType'])
  },
  mounted() {
    this.getList()
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberFinanceApplyListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    // 自定义选择逻辑
    checkSelectable(row, index) {
      if (row.status == 0) {
        return true
      }
      return false
    },
    handlerRview(type, row) {
      this.oprationType = type
      if (type == 'patchRview') {
        if (this.selectId.length == 0 && !row) {
          return this.$message.warning(`请选择用户`)
        }
      } else {
        this.curRow = row
      }
      this.$refs.rviewDialog.dialogVisible = true
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection()
    },
    handleSelectionChange(val) {
      this.selectId = val.map((item) => item.id)
      if (this.selectId.length == 0) {
        this.isIndeterminate = false
        this.checkAll = false
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectId.length == this.tableData.total) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        } else {
          if (this.selectId.length == this.tableFrom.limit) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        }
      }
    },
    getStatisticsInfo() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberCommissionTotalApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        extractNumber: null,
        queryType: '1',
        status: null
      }
      this.time = []
      this.getList()
    },

    seachList() {
      this.tableData.data = []
      this.tableFrom.page = 1
      this.getList()
    },

    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  padding-top: 6px;
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 20px;
  }
  .tips {
    margin-bottom: 16px;
  }
  .statistics-data {
    margin-bottom: 16px;
    .left {
      .statistics {
        display: flex;
        gap: 60px;
        .statistics-item {
          font-size: 14px;
          span {
            &:last-child {
              font-weight: bold;
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
