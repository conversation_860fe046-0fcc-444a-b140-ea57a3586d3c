<template>
  <MainCard>
    <div class="divBox">
      <div class="header">
        <div class="statistics">
          <div class="left">
            <div class="year">{{ tableFrom.year }}年度</div>
            <div class="statistics-value">
              累计销售额：{{ statisticsInfo.totalSalesAmount }}元，
              已累计奖励支出：{{ statisticsInfo.totalRewardAmount }}元
            </div>
          </div>
          <div class="right">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                历史年度查看<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="new Date().getFullYear()">{{
                  new Date().getFullYear()
                }}</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
        <div v-if="bonusConfigList.length > 0" class="tabs">
          <el-tabs v-model="tableFrom.rankType" @tab-click="handleTabClick">
            <el-tab-pane
              :label="item.name"
              :name="item.type.toString()"
              v-for="(item, index) in tabsItems"
              :key="index"
            />
          </el-tabs>
        </div>
        <template
          v-if="bonusConfigList.length > 0 && tableFrom.rankType !== 'year'"
        >
          <div v-if="curParams.enabled" class="ranking-des">
            <div class="condition">
              <div class="left">
                周排行榜入榜条件： ≥ {{ curParams.threshold }}元
              </div>
              <div class="right">
                <el-button @click="toParamsSettingPage(3)" type="text"
                  ><i class="el-icon-warning-outline"></i
                  >榜单奖励参数</el-button
                >
              </div>
            </div>
            <div class="rule">
              奖励规则：
              <span v-for="(item, index) in curParams.awards" :key="item.rank"
                >第{{ item.rank }}名 {{ item.amount }}元{{
                  index !== curParams.awards.length - 1 ? "，" : ""
                }}</span
              >
            </div>
          </div>
        </template>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <!-- <el-table-column label="SVIP会员">
          <template slot-scope="scope">
            <div class="user">
              <div class="user-info">
                <div class="user-name">{{ scope.row.nickname }}</div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column> -->
        <el-table-column prop="participantCount" label="周数" />
        <el-table-column prop="rankPeriod" label="时间" />
        <el-table-column prop="totalSalesAmount" label="销售额" />
        <el-table-column prop="participantCount" label="入榜人数" />
        <el-table-column prop="rewardStatus" label="奖励状态" />
        <el-table-column prop="isAutoReward" width="300" label="奖励发放">
          <template slot-scope="scope">
            <div class="reward-des">
              <div class="auto-reward">
                {{ scope.row.isAutoReward ? "自动发放" : "手动发放" }}
              </div>
              <div class="time">
                {{ scope.row.rewardTime }}
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>

<script>
import { rankingListApi, rankingStatisticsApi } from "@/api/rankingList";
import { memberSettingListApi } from "@/api/parameterSetting";
import { checkPermi } from "@/utils/permission"; // 权限判断函数

export default {
  data() {
    return {
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      tabsItems: [
        { name: "周榜", type: "week" },
        { name: "月榜", type: "month" },
        { name: "季度", type: "quarter" },
        { name: "年度", type: "year" },
      ],
      statisticsInfo: {},
      curParams: {},
      bonusConfigList: [],
      tableFrom: {
        page: 1,
        limit: 20,
        rankType: "week",
        year: new Date().getFullYear(),
      },
    };
  },
  mounted() {
    this.getMemberSetting();
    this.getRankingStatistics();
    this.getList();
  },
  methods: {
    checkPermi,
    handleCommand(data) {
      this.tableFrom.year = data;
      this.getRankingStatistics();
      this.getList();
    },
    toParamsSettingPage(type) {
      this.$store.dispatch("member/setTabsItemType", type).then(() => {
        this.$router.push("/member/parameterSetting");
      });
    },
    // 详情
    async getMemberSetting() {
      await memberSettingListApi()
        .then((res) => {
          if (res && res.bonusConfigList && res.bonusConfigList.length > 0) {
            this.bonusConfigList = res.bonusConfigList
              .slice(4, 7)
              .map((item) => {
                return JSON.parse(item.configValue);
              });
            this.curParams = this.bonusConfigList[0];
          }
          // for (const key in data) {
          //   data[key].forEach((element) => {
          //     element.configValue = JSON.parse(element.configValue);
          //   });
          // }
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    // 列表
    getList() {
      this.listLoading = true;
      rankingListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.records;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    // 统计
    getRankingStatistics() {
      rankingStatisticsApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },

    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        rankType: "week",
        year: new Date().getFullYear(),
      };
      this.getList();
    },
    handleTabClick(tab) {
      this.curParams = this.bonusConfigList[tab.index]
      this.getList();
    },
    seachList(val) {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  .header {
    .statistics {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
      .left {
        display: flex;
        align-items: flex-end;
        .year {
          padding-right: 10px;
          font-size: 18px;
          font-weight: 700;
          border-right: 1px solid #e2e2e2;
        }
        .statistics-value {
          padding-left: 10px;
          margin-bottom: 3px;
        }
      }
      .right {
        .el-dropdown-link:hover {
          cursor: pointer;
        }
      }
    }
  }
  .ranking-des {
    background-color: #f1f3f9;
    padding: 16px;
    margin-bottom: 16px;
    border-radius: 5px;
    .condition {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 2px;
      .right {
        .el-button--medium {
          padding-top: 0;
        }
        .el-icon-warning-outline {
          margin-right: 4px;
        }
      }
    }
  }
  .table-list {
    .reward-des {
      display: flex;
      line-height: 1;
      .auto-reward {
        padding-right: 10px;
        border-right: 1px solid #e2e2e2;
      }
      .time {
        padding-left: 10px;
      }
    }
  }
}
.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
