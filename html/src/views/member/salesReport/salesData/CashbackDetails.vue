<template>
  <el-dialog
    title="返现明细"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <el-table-column prop="orderNo" label="订单号">
          <template slot-scope="scope">
            <span style="color: #3d71d2">{{ scope.row.orderNo }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderAmount" label="销售额" />
        <el-table-column prop="commissionAmount" label="返现金额" />
        <el-table-column prop="orderTime" label="订单支付时间" />
        <el-table-column  label="买家">
          <template slot-scope="scope">
            <div>
              <div style="color: #3d71d2">{{ scope.row.buyerName }}</div>
              <!-- <div>{{ scope.row.userPhone }}</div> -->
            </div>
          </template></el-table-column
        >
      </el-table>
      <div class="pagination">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { salesDataDetailApi } from "@/api/salesData";
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      tableFrom: {
        page: 1,
        limit: 10,
        memberLevel: null,
      },
      listLoading: false,
      tableData: {
        data: [],
        total: 0,
      },
    };
  },
  methods: {
    handlerOpen() {
      this.getList();
    },
    // 列表
    getList() {
      this.listLoading = true;
      salesDataDetailApi(
        {
          ...this.tableFrom,
          memberLevel: 2,
        },
        this.curRow.uid
      )
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  min-height: 600px;
  .pagination {
    margin-top: 10px;
    text-align: right;
  }
}
</style>
