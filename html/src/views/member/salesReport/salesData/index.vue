<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="search-form">
          <el-form label-width="120px" inline size="small">
            <el-form-item label="昵称/手机号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.keywords"
                placeholder="昵称/手机号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="统计时间：">
              <el-date-picker
                size="small"
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="left"
                clearable
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button @click="seachList" size="small" type="primary"
                >筛选</el-button
              >
              <el-button type="text" size="small" @click="reset"
                >重置筛选条件</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <el-table-column label="SVIP会员">
          <template slot-scope="scope">
            <div class="user">
              <div class="user-info">
                <div style="color: #3d71d2" class="user-name">
                  {{ scope.row.nickname || '微信用户' }}
                </div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="salesAmount" label="销售额" />
        <el-table-column prop="orderCount" label="订单数" />
        <el-table-column prop="pendingBrokerageAmount" label="待结算返现" />
        <el-table-column prop="settledBrokerageAmount" label="已结算返现" />
        <el-table-column prop="selfPurchaseAmount" label="自购金额" />
        <el-table-column
          label="操作"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="showCashbackDetails(scope.row)"
              type="text"
              size="small"
              >返现明细</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <CashbackDetails :curRow="curRow" ref="cashbackDetails" />
  </MainCard>
</template>

<script>
import { salesDataApi } from "@/api/salesData";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import CashbackDetails from "./CashbackDetails.vue";

export default {
  components: { CashbackDetails },
  data() {
    return {
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      time: [],
      curRow: {},
      statisticsInfo: "",
      tableFrom: {
        page: 1,
        limit: 20,
        keywords: null,
        startTime: null,
        endTime: null,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.listLoading = true;
      if (this.time) {
        this.tableFrom.startTime = this.time[0];
        this.tableFrom.endTime = this.time[1];
      } else {
        this.tableFrom.startTime = null;
        this.tableFrom.endTime = null;
      }
      salesDataApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    // 显示返现明细
    showCashbackDetails(row) {
      this.curRow = { ...row };
      this.$refs.cashbackDetails.dialogVisible = true;
    },

    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        keywords: null,
        startTime: null,
        endTime: null,
      };
      this.time = [];
      this.getList();
    },

    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 16px;
  }
  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
    }
  }
}
.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
