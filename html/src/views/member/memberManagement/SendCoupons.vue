<template>
  <el-dialog
    title="批量发优惠券"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="800px"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div class="right-section">
        <div class="form">
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="120px"
            @submit.native.prevent
          >
            <el-form-item
              label="优惠券："
              class="proCoupon"
            >
              <div class="coupon-list">
                <el-tag
                  v-for="(tag, index) in formValidate.coupons"
                  :key="index"
                  class="mr10 mb10"
                  closable
                  :disable-transitions="false"
                  @close="handleCloseCoupon(tag)"
                >
                  {{ tag.name }}
                </el-tag>
                <!-- <span v-if="formValidate.couponIds == null">无</span> -->
                <el-button
                  type="text"
                  @click="addCoupon"
                  >选择优惠券</el-button
                >
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        class="submission"
        @click="handleSubmit('formValidate')"
        >发送</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { memberSendCoupon } from '@/api/menberSVIPManagement'
import { Debounce } from '@/utils/validate'
const defaultObj = () => ({
  coupons: [],
  couponIds: []
})
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({})
    },
    selectUserIds: {
      type: Array,
      default: () => []
    },
    userLevelList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      ruleValidate: {
        adjustReason: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          memberSendCoupon({
            couponIds: this.formValidate.couponIds.join(),
            userIds: this.selectUserIds.join()
          }).then((res) => {
            this.$message.success('操作成功')
            this.handleClose()
            this.$emit('success')
          })
        }
      })
    }),
    // 选择优惠券
    addCoupon() {
      const _this = this
      this.$modalCoupon(
        'wu',
        (this.keyNum += 1),
        this.formValidate.coupons,
        function (row) {
          _this.formValidate.couponIds = []
          _this.formValidate.coupons = [...row]
          row.map((item) => {
            _this.formValidate.couponIds.push(item.id)
          })
        },
        ''
      )
    },
    handleCloseCoupon(tag) {
      this.formValidate.coupons.splice(
        this.formValidate.coupons.indexOf(tag),
        1
      )
      this.formValidate.couponIds.splice(
        this.formValidate.couponIds.indexOf(tag.id),
        1
      )
    },
    handlerOpen() {
      // console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false
      this.formValidate = Object.assign({}, defaultObj())
      this.$refs.formValidate.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 300px;
}
</style>
