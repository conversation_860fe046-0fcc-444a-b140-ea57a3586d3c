<template>
  <el-dialog
    title="加标签"
    :visible.sync="addTagDialogVisible"
    width="700px"
    @open="handlerOpen"
    @close="handlerClose"
    z-index="5"
  >
    <div class="tag-box">
      <div class="header">
        <span>请点击添加标签</span>
        <el-button @click="addNewTag" class="button" type="text" size="small"
          >创建新标签?</el-button
        >
        <el-button @click="refresh" class="button" type="text" size="small"
          >刷新</el-button
        >
      </div>
      <div class="tag-list">
        <div
          :ref="`tagItem${item.id}`"
          @click="handlerSelect(item.id)"
          v-for="item in tagList"
          :label="item.name"
          :key="item.id"
          :class="`tag-item ${selectTagIds.includes(item.id) ? 'active' : ''}`"
        >
          <span> {{ item.name }}</span>
          <svg-icon class="select-flag" icon-class="tag_select_flag" />
        </div>
      </div>
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onCancel">取 消</el-button>
      <el-button type="primary" @click="onConfirm">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { memberManagementbatchAddTagApi } from "@/api/memberManagement";

export default {
  props: {
    tagList: {
      type: Array,
      default: () => [],
    },
    selectUserId: {
      type: Array,
      default: () => [],
    },
    curRow: {
      type: Object,
      default: () => ({}),
    },
    addType: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      addTagDialogVisible: false,
      selectTagIds: [],
    };
  },

  methods: {
    handlerOpen() {
      if (this.addType === "singleAdd") {
        if (this.curRow.tagId) {
          this.selectTagIds = this.curRow.tagId
            .split(",")
            .map((item) => Number(item));
        }
      }
    },
    onConfirm() {
      // if (this.selectTagIds.length == 0) {
      //   return this.$message.warning("请选择标签");
      // }
      let params = {};
      if (this.addType === "singleAdd") {
        params = {
          id: this.curRow.id || this.curRow.uid,
          tagId: this.selectTagIds.join(),
        };
      } else {
        params = {
          id: this.selectUserId.join(),
          tagId: this.selectTagIds.join(),
        };
      }
      memberManagementbatchAddTagApi(params).then(() => {
        this.$message.success("添加成功");
        this.addTagDialogVisible = false;
        this.selectTagIds = [];
        this.$emit("success");
      });
    },
    addNewTag() {
      window.open("/admin/#/member/memberTagManagement");
    },
    refresh() {
      this.$emit("refresh");
    },
    onCancel() {
      this.selectTagIds = [];
      this.addTagDialogVisible = false;
    },
    handlerClose() {
      this.selectTagIds = [];
    },
    handlerSelect(id) {
      if (this.selectTagIds.includes(id)) {
        this.selectTagIds = this.selectTagIds.filter((item) => item != id);
      } else {
        this.selectTagIds = [...this.selectTagIds, id];
      }
    },
  },
};
</script>

<style scoped lang="scss">
.tag-box {
  min-height: 300px;

  .header {
    margin-bottom: 20px;
    span {
      margin-right: 10px;
    }
    .button {
      font-size: 14px;
      padding: 0;
    }
  }
  .tag-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .tag-item {
      border: 1px solid #f0f0f0;
      background-color: #f0f0f0;
      padding: 10px 20px; // 保持一致的 padding
      border-radius: 4px;
      cursor: pointer;
      .select-flag {
        display: none;
      }
      &.active {
        position: relative;
        background-color: #ffe8e6;
        border-color: #dc5422;
        .select-flag {
          position: absolute;
          display: block;
          bottom: -2px;
          right: 0;
        }
      }
    }
  }
}
</style>
