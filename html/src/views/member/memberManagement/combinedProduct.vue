<template>
  <el-dialog
    title="商品组合查询"
    @opened="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
  >
    <div class="combined-product">
      <div class="product-info">
        <MyImage
          :imagePath="productInfo.thumbnailImage"
          :previewPath="productInfo.image"
          :size="46"
        />
        <span>{{ productInfo.storeName }}</span>
      </div>
      <div class="table-box">
        <div class="title">查询该产品有什么组合活动</div>
        <div class="table-list">
          <el-table
            ref="table"
            v-loading="tableData.loading"
            :data="tableData.data"
            :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
            style="width: 100%"
            size="mini"
            highlight-current-row
          >
            <el-table-column prop="name" label="组合名称"> </el-table-column>
            <el-table-column prop="combinationPrice" label="组合价">
            </el-table-column>
            <el-table-column prop="initialSales" label="虚拟销量">
            </el-table-column>
            <el-table-column label="售卖时间"
              ><template slot-scope="scope">
                <span v-if="scope.row.storeStatus == 2">
                  <span
                    >{{
                      parseTime(scope.row.startTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}
                    至
                    {{
                      parseTime(scope.row.endTime, "{y}-{m}-{d} {h}:{i}:{s}")
                    }}</span
                  >
                </span>
              </template></el-table-column
            >
            <el-table-column label="状态">
              <template slot-scope="scope">
                <span v-if="scope.row.storeStatus == 0"> 仓库中 </span>
                <span v-if="scope.row.storeStatus == 1"> 在售中 </span>
                <span v-if="scope.row.storeStatus == 2 && scope.row.stock == 0">
                  已售空
                </span>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryByProductId } from "@/api/store";
export default {
  data() {
    return {
      dialogVisible: false,
      tableData: {
        data: [],
        loading: false,
      },
    };
  },
  props: {
    productInfo: {
      type: Object,
      default: () => ({}),
    },
  },

  methods: {
    handlerOpen() {
      // 获取组合活动
      this.tableData.loading = true;
      queryByProductId({ productId: this.productInfo.id })
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.loading = false;
        })
        .catch((res) => {
          this.tableData.loading = false;
          this.$message.error(res.message);
        });
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.combined-product {
  height: 400px;
  overflow: auto;
  .product-info {
    display: flex;
    align-items: center;
    margin-bottom:  20px;
    gap: 10px;
    & > span {
      color: #333;
      font-weight: 700;
    }
  }
  .table-box {
    .title {
      margin-bottom: 10px;
    }
  }
}
</style>
