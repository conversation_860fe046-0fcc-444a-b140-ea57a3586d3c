<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="search-form">
          <el-form
            label-width="auto"
            inline
            size="small"
          >
            <div>
              <el-form-item label="成为会员时间：">
                <el-date-picker
                  size="small"
                  v-model="memberTime"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  align="left"
                  clearable
                >
                </el-date-picker>
              </el-form-item>
            </div>

            <el-form-item label="昵称/手机号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.keywords"
                placeholder="昵称/手机号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item
              label="会员等级："
              prop="cateIds"
            >
              <el-select
                clearable
                v-model="tableFrom.level"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in userLevelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.grade"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="会员标签：">
              <el-select
                clearable
                v-model="tableFrom.labelId"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in memberTagList"
                  :label="item.name"
                  :key="item.id"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="search-btn">
            <el-button
              @click="seachList"
              size="small"
              type="primary"
              >筛选</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="reset"
              >重置筛选条件</el-button
            >
          </div>
        </div>
        <div class="tips">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            style="margin-right: 6px"
          ></el-checkbox>
          <span class="mr10">已选 {{ selectId.length }}</span>

          <span class="mr10">
            <el-button
              @click="addUserTag('batchAdd')"
              size="small"
              >加标签</el-button
            ></span
          >
          <span class="mr10">
            <el-button
              @click="sendCoupons()"
              size="small"
              >发优惠券</el-button
            ></span
          >
          <!-- <span>
            <el-button @click="batchSet" size="small">批量设置</el-button></span
          > -->
        </div>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :highlight-current-row="true"
      >
        <el-table-column
          align="center"
          type="selection"
          width="55"
        >
        </el-table-column>

        <el-table-column
          label="用户"
          min-width="200"
        >
          <template slot-scope="scope">
            <div class="user">
              <el-avatar
                class="el-avatar"
                :size="36"
                :src="scope.row.avatar"
              >
                <svg-icon
                  class="user_avator"
                  icon-class="user_avator"
                />
              </el-avatar>
              <div class="user-info">
                <div class="user-name">
                  {{ scope.row.nickname || '微信用户' }}
                </div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="level"
          label="等级"
        >
          <template slot-scope="scope">
            <memberLevelIcon :level="scope.row.level" />
          </template>
        </el-table-column>
        <el-table-column
          prop="nowMoney"
          label="账号余额"
        />
        <el-table-column
          prop="payCount"
          label="购买次数"
        />
        <el-table-column
          prop="totalConsumeMoney"
          label="累计消费金额"
        />
        <el-table-column
          prop="createTime"
          label="注册时间"
        />

        <el-table-column
          label="操作"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="addUserTag('singleAdd', scope.row)"
              type="text"
              size="small"
              >加标签</el-button
            >
            <el-button
              type="text"
              @click="adjustLevel(scope.row)"
              class="mr10"
              size="small"
              >调整等级</el-button
            >
            <router-link
              :to="{
                path: '/member/memberSystem/memberDetails/' + scope.row.id
              }"
            >
              <el-button
                type="text"
                size="small"
                >查看</el-button
              >
            </router-link>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <!-- 加标签 -->
    <AddUserTag
      ref="addUserTag"
      :addType="addType"
      @refresh="getTagList"
      :selectUserId="selectId"
      :curRow="curRow"
      :tagList="memberTagList"
      @success="seachList"
    />
    <combinedProduct
      ref="combinedProduct"
      :productInfo="curProductInfo"
    />
    <!-- 调整等级 -->
    <AdjustLevel
      ref="adjustLevel"
      :userLevelList="userLevelList"
      :selectUserId="selectId"
      :curRow="curRow"
      @success="seachList"
    />
    <!-- 批量发优惠券 -->
    <SendCoupons
      ref="sendCoupons"
      :selectUserIds="selectId"
      :curRow="curRow"
      @success="seachList"
    />
  </MainCard>
</template>

<script>
import { restoreApi } from '@/api/store'
import { memberManagementListApi } from '@/api/memberManagement'
import { userLevelListApi } from '@/api/discountCoupon.js'
import { memberTagListApi } from '@/api/memberTag'
import AddUserTag from './AddUserTag.vue'
import AdjustLevel from './AdjustLevel.vue'
import SendCoupons from './SendCoupons.vue'
import combinedProduct from './combinedProduct.vue'
import { checkPermi } from '@/utils/permission' // 权限判断函数

export default {
  name: 'ProductList',
  components: { AddUserTag, combinedProduct, AdjustLevel, SendCoupons },
  data() {
    return {
      props: {
        children: 'child',
        label: 'name',
        value: 'id',
        emitPath: false
      },
      memberTime: [],
      curRow: {},
      addType: 'singleAdd',
      curUserId: null,
      memberTagList: [],
      userLevelList: [],
      // roterPre: roterPre,
      headeNum: [],
      isFold: true,
      selectId: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20,
        memberTimeStart: null,
        memberTimeEnd: null,
        keywords: null,
        level: null,
        labelId: null
      },
      checkAll: false,
      isIndeterminate: false,
      categoryList: [],
      curProductInfo: {},
      merCateList: [],
      objectUrl: process.env.VUE_APP_BASE_API,
      dialogVisible: false
    }
  },
  mounted() {
    this.getTagList()
    this.getUserLevelList()
    this.getList()
    this.checkedCities = this.$cache.local.has('goods_stroge')
      ? this.$cache.local.getJSON('goods_stroge')
      : this.checkedCities
  },
  methods: {
    checkPermi,
    // 标签列表
    getTagList() {
      memberTagListApi({ page: 1, limit: 999 }).then((res) => {
        this.memberTagList = res.list
      })
    },
    adjustLevel(row) {
      this.curRow = row
      this.$refs.adjustLevel.dialogVisible = true
    },
    sendCoupons() {
      if (this.selectId.length == 0) {
        return this.$message.warning(`请选择用户`)
      }
      this.$refs.sendCoupons.dialogVisible = true
    },
    // 获取会员等级字典
    getUserLevelList() {
      userLevelListApi().then((res) => {
        this.userLevelList = res
      })
    },
    // 列表
    getList() {
      this.listLoading = true
      if (this.memberTime) {
        this.tableFrom.memberTimeStart = this.memberTime[0]
        this.tableFrom.memberTimeEnd = this.memberTime[1]
      } else {
        this.tableFrom.memberTimeStart = null
        this.tableFrom.memberTimeEnd = null
      }
      memberManagementListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection()
    },
    handleRestore(id) {
      this.$modalSure('恢复商品').then(() => {
        restoreApi(id).then((res) => {
          this.$message.success('操作成功')
          this.getList()
        })
      })
    },

    addUserTag(type, row) {
      this.curRow = row
      this.addType = type
      if (this.selectId.length == 0 && !row) {
        return this.$message.warning(`请选择用户`)
      }
      this.$refs.addUserTag.addTagDialogVisible = true
    },

    handleSelectionChange(val) {
      this.selectId = val.map((item) => item.id)
      if (this.selectId.length == 0) {
        this.isIndeterminate = false
        this.checkAll = false
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectId.length == this.tableData.total) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        } else {
          if (this.selectId.length == this.tableFrom.limit) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        }
      }
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        memberTimeStart: null,
        memberTimeEnd: null,
        keywords: null,
        level: null,
        labelId: null
      }
      this.memberTime = []
      this.getList()
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },
    onUnfold(row) {
      row.isFold = !row.isFold
    },
    seachList() {
      this.tableData.data = []
      this.tableFrom.page = 1
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleCloseMod(item) {
      this.dialogVisible = item
      this.getList()
    },

    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    .tabs {
      margin: 10px 0;
    }
    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px;
    margin-bottom: 20px;
    .search-btn {
      margin-left: 110px;
    }
  }
  .tips {
    margin-bottom: 16px;
  }

  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-info {
        .user-name {
          color: #3d71d2;
          font-size: 15px;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
