<template>
  <div v-loading="fullscreenLoading" class="app-box">
    <div class="details-item">
      <div class="base-card basic-info">
        <div class="info-title">基本信息</div>
        <div class="user-info">
          <el-avatar class="el-avatar" :size="84" :src="detailsInfo.avatar">
            <svg-icon class="user_avator" icon-class="user_avator" />
          </el-avatar>
          <div class="user-info-list">
            <div class="user-name">
              <span>{{ detailsInfo.nickname || "微信用户" }}</span>
              <span>
                <memberLevelIcon
                  :level="detailsInfo.level"
                />
              </span>
            </div>
            <div class="list">
              <div class="item">
                <span>手机号：</span
                ><span class="info-value">{{ detailsInfo.phone }}</span>
              </div>
              <div class="item">
                <span>性别：</span
                ><span class="info-value">{{
                  detailsInfo.sex == 0 ? "男" : "女"
                }}</span>
              </div>
              <div class="item">
                <span>生日：</span
                ><span class="info-value">{{ detailsInfo.birthday }}</span>
              </div>
              <div class="item">
                <span>会员等级：</span
                ><span class="info-value">{{ detailsInfo.memberLevel }}</span>
              </div>
              <div class="item">
                <span>上级：</span
                ><span class="info-value">{{
                  detailsInfo.spreadNickname
                }}</span>
              </div>
              <div class="item">
                <span>地区：</span
                ><span class="info-value">{{ detailsInfo.region }}</span>
              </div>
              <div class="item">
                <span>注册时间：</span
                ><span class="info-value">{{ detailsInfo.createTime }}</span>
              </div>
              <div class="item">
                <span>升级VIP时间：</span
                ><span class="info-value">{{ detailsInfo.vipTime }}</span>
              </div>
              <div class="item">
                <span>升级SVIP时间：</span
                ><span class="info-value">{{ detailsInfo.svipTime }}</span>
              </div>
              <div class="item">
                <span>最近浏览：</span
                ><span class="info-value">{{ detailsInfo.lastViewTime }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="base-card tag">
        <div class="header">
          <span class="title">标签</span>
          <el-button
            @click="addUserTag()"
            class="el-button"
            size="small"
            type="text"
            >添加标签</el-button
          >
        </div>
        <div class="tag-list">
          <div
            v-for="item in memberTagList"
            :label="item.name"
            :key="item.id"
            class="tag-item"
          >
            <span> {{ item.name }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="details-item">
      <div class="base-card">
        <div class="info-title">资产信息</div>
        <div class="info-list">
          <div
            v-for="(item, index) in assetsData"
            :key="index"
            class="info-item"
          >
            <div class="info-title">{{ item.title }}</div>
            <div
              :style="`color:${(index == 0 || index == 1) && '#e74c3c'}`"
              class="info-value"
            >
              {{ item.value }}
            </div>
          </div>
        </div>
        <div v-if="detailsInfo.nowMoney" class="info-des">
          充值金余额：{{ rechargeBalance >= 0 ? rechargeBalance : "0.00" }}，
          可提现金额：{{ detailsInfo.withdrawablePrice || "0.00" }}
        </div>
      </div>
    </div>
    <div class="details-item">
      <div class="base-card">
        <div class="info-title">业绩概览</div>
        <div class="info-list">
          <div
            v-for="(item, index) in overviewData"
            :key="index"
            class="info-item"
          >
            <div class="info-title">{{ item.title }}</div>
            <div class="info-value overview">
              {{ item.value }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="details-item">
      <div class="base-card">
        <div class="info-title">订单记录</div>
        <div class="info-list">
          <div
            v-for="(item, index) in orderData"
            :key="index"
            class="info-item"
          >
            <div class="info-title">{{ item.title }}</div>
            <div :style="`color:${index == 0 && '#e74c3c'}`" class="info-value">
              {{ item.value }}
            </div>
          </div>
          <div class="info-item"></div>
          <div class="info-item"></div>
        </div>
        <div class="table">
          <el-table
            class="table-list"
            :data="tableData"
            ref="table"
            :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
            style="width: 100%"
            :highlight-current-row="true"
          >
            <el-table-column prop="orderId" label="订单号">
              <template slot-scope="scope">
                <span style="color: #3d71d2">{{ scope.row.orderId }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="下单时间" />
            <el-table-column prop="statusStr.value" label="订单状态" />
            <el-table-column prop="orderPrice" label="订单金额" />
            <el-table-column prop="payTypeStr" label="支付方式" />
            <el-table-column prop="payPrice" label="实付金额" />
          </el-table>
        </div>
      </div>
    </div>

    <AddUserTag
      ref="addUserTag"
      addType="singleAdd"
      @refresh="getTagList"
      :curRow="detailsInfo"
      :tagList="memberAllTagList"
      @success="getInfo"
    />
  </div>
</template>

<script>
import { memberManagementDetailsApi } from "@/api/memberManagement";
import { memberTagListApi } from "@/api/memberTag";
import { orderListApi } from "@/api/order";

import AddUserTag from "../memberManagement/AddUserTag.vue";
export default {
  components: { AddUserTag },
  data() {
    return {
      detailsInfo: {},
      tableData: [],
      assetsData: [
        { title: "账号余额 (元)", value: "0.00", key: "nowMoney" },
        { title: "可用优惠券", value: "0", key: "availableCoupons" },
        { title: "成长值", value: "0", key: "experience" },
        { title: "推广的普通用户", value: "0", key: "normalSpreadCount" },
        { title: "推广的vip用户", value: "0", key: "vipSpreadCount" },
      ],
      overviewData: [
        { title: "累计返现（元）", value: "0.00", key: "totalBrokerage" },
        { title: "累计奖励金（元）", value: "0.00", key: "totalBonus" },
        { title: "累计销售额（元）", value: "0.00", key: "totalSales" },
        { title: "累计充值金额", value: "0.00", key: "totalRecharge" },
        { title: "累计提现金额", value: "0.00", key: "totalWithdraw" },
      ],
      orderData: [
        { title: "累计消费金额（元）", value: "0.00", key: "totalConsume" },
        { title: "累计消费订单数", value: "0", key: "totalOrders" },
        { title: "最近下单时间", value: "--", key: "lastOrderTime" },
      ],
      memberTagList: [],
      memberAllTagList: [],
      fullscreenLoading: true,
    };
  },
  computed: {
    // 充值金余额
    rechargeBalance() {
      return (
        (
          this.detailsInfo.nowMoney - this.detailsInfo.withdrawablePrice
        ).toFixed(2) || "0.00"
      );
    },
  },
  mounted() {
    if (this.$route.params.id) {
      this.getInfo();
    }
  },
  methods: {
    // 已添加标签列表
    getTagList() {
      memberTagListApi({ page: 1, limit: 999 }).then((res) => {
        this.memberAllTagList = res.list;
        if (this.detailsInfo.tagId) {
          this.memberTagList = res.list.filter((item) =>
            this.detailsInfo.tagId.split(",").includes(item.id.toString())
          );
        }
      });
    },
    // 用户订单
    getuserOrderList(params) {
      orderListApi(params).then((res) => {
        this.tableData = res.list || [];
      });
    },
    addUserTag() {
      this.$refs.addUserTag.addTagDialogVisible = true;
    },
    // 详情
    getInfo() {
      memberManagementDetailsApi(this.$route.params.id)
        .then(async (res) => {
          this.detailsInfo = res;
          this.assetsData.forEach((item) => {
            if (res[item.key]) {
              item.value = res[item.key];
            }
          });
          this.overviewData.forEach((item) => {
            if (res[item.key]) {
              item.value = res[item.key];
            }
          });
          this.orderData.forEach((item) => {
            if (res[item.key]) {
              item.value = res[item.key];
            }
          });
          this.getTagList();
          this.getuserOrderList({ page: 1, limit: 999, uid: res.uid, type: 2 });
          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
  },
};
</script>
<style scoped lang="scss">
.app-box {
  padding: 16px;
  .details-item {
    margin-bottom: 12px;
    &:first-child {
      display: flex;
      flex-wrap: nowrap;
      gap: 12px;
      .basic-info {
        width: 70%;
        .user-info {
          display: flex;
          gap: 20px;
          .el-avatar {
            flex-shrink: 0;
            margin: 0 20px;
          }
          .user-info-list {
            .user-name {
              font-size: 18px;
              font-weight: 700;
              margin-bottom: 16px;
              display: flex;
              align-items: center;
              gap: 8px;
              white-space: nowrap;
            }
            .list {
              display: grid;
              grid-template-columns: repeat(3, 1fr);
              column-gap: 60px;
              row-gap: 10px;
              .item {
                display: flex;
                white-space: nowrap;
              }
            }
          }
          .el-avatar {
            background-color: #d8d8d8;
            display: flex;
            align-items: center;
            justify-content: center;
            .user_avator {
              font-size: 50px;
              color: white;
            }
          }
        }
      }
      .tag {
        flex: 1;
        .header {
          margin-bottom: 20px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          .title {
            font-size: 14px;
            font-weight: 700;
          }
          .el-button {
            padding: 0;
            font-size: 14px;
          }
        }
        .tag-list {
          display: flex;
          flex-wrap: wrap;
          gap: 10px;
          .tag-item {
            border: 1px solid #f0f0f0;
            background-color: #f0f0f0;
            padding: 10px 20px; // 保持一致的 padding
            border-radius: 4px;
            .select-flag {
              display: none;
            }
          }
        }
      }
    }
    .base-card {
      background-color: #ffffff;
      overflow: hidden;
      border-radius: 4px;
      padding: 20px;

      > .info-title {
        font-size: 14px;
        font-weight: 700;
        margin: 0 0 20px 0;
        &::before {
          content: "";
          display: inline-block;
          width: 5px;
          height: 14px;
          background-color: rgb(3, 158, 3);
          vertical-align: -2px;
          margin-right: 8px;
        }
      }
      > .info-list {
        display: flex;
        flex-wrap: nowrap;
        .info-item {
          flex: 1;
          .info-title {
            margin-bottom: 10px;
          }
          .info-value {
            font-size: 20px;
          }
          .overview {
            color: #dd3f20;
          }
        }
      }
      .table {
        margin-top: 10px;
      }
      .info-des {
        position: relative;
        color: #e74c3c;
        background-color: #ffe8e7;
        display: inline-block;
        padding: 7px 9px;
        font-size: 14px;
        border-radius: 3px;
        margin-top: 10px;
        z-index: 1;
        &::before {
          content: "";
          position: absolute;
          width: 0;
          height: 0;
          top: -10px;
          left: 13px;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          border-bottom: 20px solid #ffe8e7;
          transform: rotate(-25deg);
          z-index: -1;
        }
      }
    }
  }
}
</style>
