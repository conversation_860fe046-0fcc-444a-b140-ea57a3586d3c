<template>
  <div>
    <div class="search-box">
      <div class="search-form">
        <el-form label-width="110px" inline size="small">
          <el-form-item label="昵称/手机号：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.keywords"
              placeholder="昵称/手机号"
              class="selWidth"
              size="small"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="申请时间：">
            <el-date-picker
              style="width: 300px !important"
              size="small"
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="left"
              clearable
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="状态：">
            <el-select
              clearable
              v-model="tableFrom.status"
              placeholder="请选择"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button @click="seachList" size="small" type="primary"
              >筛选</el-button
            >
            <el-button type="text" size="small" @click="reset"
              >重置筛选条件</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="tips">
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          style="margin-right: 6px"
        ></el-checkbox>
        <span class="mr10">已选 {{ selectId.length }}</span>
        <span class="mr10">
          <el-button @click="handlerRview('patchRview')" size="small"
            >批量审核</el-button
          ></span
        >
      </div>
    </div>
    <el-table
      class="table-list"
      v-loading="listLoading"
      :data="tableData.data"
      ref="table"
      :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
      style="width: 100%"
      :highlight-current-row="true"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        align="center"
        type="selection"
        :selectable="checkSelectable"
        width="55"
      >
      </el-table-column>
      <el-table-column label="申请用户" min-width="120">
        <template slot-scope="scope">
          <div class="user">
            <el-avatar class="el-avatar" :size="36" src="scope.row.avatar">
              <svg-icon class="user_avator" icon-class="user_avator" />
            </el-avatar>
            <div class="user-info">
              <div style="color: #3d71d2" class="user-name">
                {{ scope.row.nickname }}
              </div>
              <div class="phone">{{ scope.row.phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="申请时间" />
      <el-table-column prop="spreadCount" label="成长值" />
      <el-table-column prop="spreadCount" label="申请资料" />
      <el-table-column prop="status" label="审核状态">
        <template slot-scope="scope">
          <span>{{
            statusList.find((item) => item.value == scope.row.status) &&
            statusList.find((item) => item.value == scope.row.status).name
          }}</span>
        </template></el-table-column
      >
      <el-table-column
        label="操作"
        min-width="150"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            @click="handlerRview('singleRview', scope.row)"
            type="text"
            size="small"
            >审核</el-button
          >
          <!-- <el-button type="text" class="mr10" size="small">调整等级</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
    <RviewDialog
      ref="rviewDialog"
      :oprationType="oprationType"
      :selectUserId="selectId"
      :curRow="curRow"
      @success="seachList"
    />
  </div>
</template>

<script>
// import { memberSVIPApplyListApi } from "@/api/menberSVIPManagement";
import { memberSVIPApplyApi } from "@/api/menberSVIPManagement";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import RviewDialog from "./RviewDialog.vue";

export default {
  components: { RviewDialog },

  data() {
    return {
      statusList: [
        { name: "已拒绝", value: -1 },
        { name: "待审核", value: 0 },
        { name: "审核通过", value: 1 },
        { name: "提现成功", value: 2 },
        { name: "打款失败", value: 3 },
      ],
      tabsItems: [
        { name: "提现审核", type: "1" },
        { name: "提现记录", type: "2" },
      ],
      time: [],
      listLoading: true,
      curRow: {},
      oprationType: "",
      tableData: {
        data: [],
        total: 0,
      },
      selectId: [],
      checkAll: false,
      isIndeterminate: false,
      statisticsInfo: "",
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        keywords: null,
        status: null,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    checkPermi,
    handlerRview(type, row) {
      this.oprationType = type;
      if (type == "patchRview") {
        if (this.selectId.length == 0 && !row) {
          return this.$message.warning(`请选择用户`);
        }
      } else {
        this.curRow = row;
      }
      this.$refs.rviewDialog.dialogVisible = true;
    },
    // 自定义选择逻辑
    checkSelectable(row, index) {
      if (row.status == 0) {
        return true;
      }
      return false;
    },
    // 列表
    getList() {
      this.listLoading = true;
      if (this.time) {
        this.tableFrom.startTime = this.time[0];
        this.tableFrom.endTime = this.time[1];
      } else {
        this.tableFrom.startTime = null;
        this.tableFrom.endTime = null;
      }
      memberSVIPApplyApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection();
    },
    handleSelectionChange(val) {
      this.curRow = [...val];
      this.selectId = val.map((item) => item.id);
      if (this.selectId.length == 0) {
        this.isIndeterminate = false;
        this.checkAll = false;
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectId.length == this.tableData.total) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        } else {
          if (this.selectId.length == this.tableFrom.limit) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        }
      }
    },
    getStatisticsInfo() {
      this.listLoading = true;
      if (this.time) {
        this.tableFrom.startTime = this.time[0];
        this.tableFrom.endTime = this.time[1];
      } else {
        this.tableFrom.startTime = null;
        this.tableFrom.endTime = null;
      }
      memberCommissionTotalApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        keywords: null,
        status: null,
      };
      this.time = [];
      this.getList();
    },

    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  padding-top: 6px;
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 16px;
  }
  .tips {
    margin-bottom: 16px;
  }
  .statistics-data {
    margin-bottom: 16px;
    .left {
      .statistics {
        display: flex;
        gap: 60px;
        .statistics-item {
          font-size: 14px;
          span {
            &:last-child {
              font-weight: bold;
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
