<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="tabs">
          <el-tabs v-model="tabType" @tab-click="seachList">
            <el-tab-pane
              :label="item.name"
              :name="item.type.toString()"
              v-for="(item, index) in tabsItems"
              :key="index"
            />
          </el-tabs>
        </div>
      </div>
      <SVIPList v-if="tabType == '1'" />
      <SVIPReview v-if="tabType == '2'" />
      <AdjustmentRecord v-if="tabType == '3'" />
    </div>
  </MainCard>
</template>

<script>
import AdjustmentRecord from "./AdjustmentRecords.vue";
import SVIPList from "./SVIPList.vue";
import SVIPReview from "./SVIPReview.vue";
export default {
  components: {
    SVIPList,
    SVIPReview,
    AdjustmentRecord,
  },
  data() {
    return {
      tabsItems: [
        { name: "SVIP列表", type: "1" },
        { name: "SVIP申请审核", type: "2" },
        { name: "调整记录", type: "3" },
      ],
      tabType: "1",
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  padding-top: 6px;
  .search-box {
    display: flex;
    flex-direction: column;
  }
}
</style>
