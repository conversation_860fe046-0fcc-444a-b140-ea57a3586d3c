<template>
  <el-dialog
    :title="oprationType == 'singleRview' ? 'SVIP申请审核' : '批量SVIP申请审核'"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    :width="oprationType == 'singleRview' ? '60%' : '40%'"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div
        v-if="oprationType == 'singleRview'"
        class="left-section"
      >
        <div class="base-card">
          <div class="info-title">申请人：{{ curRow.nickname }}</div>
          <div class="details-info">
            <el-row :gutter="20">
              <el-col :span="12"
                ><div>
                  <span>用户昵称：</span
                  ><span style="color: #3d71d2">{{ curRow.nickname }}</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>手机号：</span><span>{{ curRow.phone }}</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>申请时间：</span><span>{{ curRow.createTime }}</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>成长值：</span><span>{{ curRow.growth }}</span>
                </div></el-col
              >
            </el-row>
          </div>
        </div>
        <div class="base-card">
          <div class="info-title">申请资料信息：</div>
          <div class="details-info">
            <el-row :gutter="20">
              <el-col :span="12"
                ><div>
                  <span>真实姓名：</span
                  ><span>{{ getRealName(curRow.applyInfo) }}</span>
                </div></el-col
              >

              <el-col :span="12"
                ><div>
                  <span>身份证号：</span
                  ><span>{{ getIdCard(curRow.applyInfo) }}</span>
                </div></el-col
              >
            </el-row>
          </div>
        </div>
      </div>

      <div class="right-section">
        <div class="form">
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-position="top"
            label-width="100px"
            @submit.native.prevent
          >
            <el-form-item
              label="审核意见："
              prop="status"
            >
              <el-radio-group
                class="radio-group"
                v-model="formValidate.status"
              >
                <el-radio-button :label="1">审核通过</el-radio-button>
                <el-radio-button :label="-1">不通过</el-radio-button>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-if="formValidate.status == -1"
              label="拒绝原因："
              prop="backMessage"
            >
              <el-input
                :autosize="{ minRows: 10, maxRows: 14 }"
                type="textarea"
                v-model="formValidate.backMessage"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="handleClose">取 消</el-button>
      <el-button
        type="primary"
        @click="handleSubmit('formValidate')"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import {
  memberSvipApplyApi,
  memberBatchSvipApplyApi
} from '@/api/menberSVIPManagement'
const defaultObj = () => ({
  backMessage: null,
  status: 1
})
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({})
    },
    selectUserId: {
      type: Array,
      default: () => []
    },
    oprationType: {
      type: String,
      default: 'singleRview'
    }
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      memberLevelList: [
        { name: '普通会员', value: 0 },
        { name: 'vip', value: 1 },
        { name: 'svip', value: 2 }
      ],
      ruleValidate: {
        backMessage: [{ required: true, message: '请输入', trigger: 'blur' }],
        status: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  methods: {
    // 获取真实姓名
    getRealName(applyInfo) {
      if (!applyInfo) return ''
      try {
        const info = JSON.parse(applyInfo)
        return info.realName || ''
      } catch (error) {
        console.warn('解析申请信息失败:', error)
        return ''
      }
    },
    // 获取身份证号
    getIdCard(applyInfo) {
      if (!applyInfo) return ''
      try {
        const info = JSON.parse(applyInfo)
        return info.idCard || ''
      } catch (error) {
        console.warn('解析申请信息失败:', error)
        return ''
      }
    },
    // 提交
    handleSubmit(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.oprationType == 'singleRview') {
            memberSvipApplyApi({
              ...this.formValidate,
              id: this.curRow.id
            }).then((res) => {
              this.$message.success('操作成功')
              this.handleClose()
              this.$emit('success')
            })
          } else {
            memberBatchSvipApplyApi({
              ...this.formValidate,
              ids: this.selectUserId.join(',')
            }).then((res) => {
              this.$message.success('操作成功')
              this.handleClose()
              this.$emit('success')
            })
          }
        }
      })
    },
    handlerOpen() {
      // console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false
      this.formValidate = Object.assign({}, defaultObj())
      this.$refs.formValidate.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  height: 500px;
  .left-section {
    flex: 2;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .base-card {
      margin-bottom: 16px;
      .info-title {
        font-size: 14px;
        font-weight: 700;
        margin: 0 0 20px 0;
        &::before {
          content: '';
          display: inline-block;
          width: 5px;
          height: 14px;
          background-color: rgb(3, 158, 3);
          vertical-align: -2px;
          margin-right: 8px;
        }
      }
      .details-info {
        .el-col {
          margin-bottom: 10px;
        }
      }
    }
  }

  .right-section {
    position: relative;
    flex: 1;
    overflow: hidden;
    padding: 20px;

    .title {
      margin: 10px 0 10px;
      font-size: 18px;
      font-weight: 700;
    }

    .btn {
      display: flex;
      justify-content: center;
      gap: 10px;
      .submission {
        width: 100px;
      }
    }

    .form {
      .detail-description {
        font-size: 12px;
        color: #666;
        line-height: 1;
      }
      .inline-flex {
        display: inline-flex;
        gap: 10px;
        align-items: center;
        white-space: nowrap;
      }
    }
  }
}
</style>
