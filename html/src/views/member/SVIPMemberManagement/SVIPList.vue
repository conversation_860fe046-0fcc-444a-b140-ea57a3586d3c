<template>
  <div>
    <div class="search-box">
      <div class="search-form">
        <el-form label-width="110px" inline size="small">
          <el-form-item label="昵称/手机号：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.keywords"
              placeholder="昵称/手机号"
              class="selWidth"
              size="small"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="申请时间：">
            <el-date-picker
              style="width: 300px !important"
              size="small"
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="left"
              clearable
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button @click="seachList" size="small" type="primary"
              >筛选</el-button
            >
            <el-button type="text" size="small" @click="reset"
              >重置筛选条件</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <div class="tips">
        <router-link
          :to="{
            path: '/member/SVIPMemberManagement/creatSvip',
          }"
        >
          <el-button type="primary" size="small"
            >添加SVIP</el-button
          ></router-link
        >
        <el-upload
          class="excel-uploader"
          :action="uploadUrl"
          :headers="headers"
          :show-file-list="false"
          :before-upload="beforeExcelUpload"
          :on-success="handleExcelSuccess"
          :on-error="handleExcelError"
        >
          <el-button size="small">批量导入</el-button>
        </el-upload>
      </div>
      <!-- <div class="statistics-data">
        <div class="left">
          <div class="statistics">
            <span class="statistics-item"
              ><span>提现金额合计：</span
              ><span>{{ statisticsInfo }}</span></span
            >
          </div>
        </div>
      </div> -->
    </div>
    <el-table
      class="table-list"
      v-loading="listLoading"
      :data="tableData.data"
      ref="table"
      :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
      style="width: 100%"
      :highlight-current-row="true"
    >
      <!-- @selection-change="handleSelectionChange" -->
      <!-- <el-table-column align="center" type="selection" width="55">
      </el-table-column> -->

      <el-table-column min-width="120">
        <template slot="header">
          <div class="svip-name">
            <span>SVIP会员</span>
            <memberLevelIcon :level="3" />
          </div>
        </template>
        <template slot-scope="scope">
          <div class="user">
            <el-avatar class="el-avatar" :size="36" :src="scope.row.avatar">
              <svg-icon class="user_avator" icon-class="user_avator" />
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ scope.row.nickname }}</div>
              <div class="phone">{{ scope.row.phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="createTime" label="加入时间" />
      <el-table-column prop="spreadCount" label="下级VIP数" />
      <el-table-column prop="spreadCount" label="下级普通会员数" />
      <el-table-column prop="brokerageAmount" label="累计佣金&奖励 (元)" />
      <el-table-column
        label="操作"
        min-width="150"
        fixed="right"
        align="center"
      >
        <template slot-scope="scope">
          <el-button
            @click="adjustLevel(scope.row)"
            type="text"
            class="mr10"
            size="small"
            >调整等级</el-button
          >
          <router-link
            :to="{
              path: '/member/memberSystem/memberDetails/' + scope.row.uid,
            }"
          >
            <el-button type="text" size="small">详情</el-button></router-link
          >
        </template>
      </el-table-column>
    </el-table>
    <div class="block">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
    <AdjustLevel
      ref="adjustLevel"
      :selectUserId="selectId"
      :curRow="curRow"
      @success="seachList"
    />
  </div>
</template>

<script>
import {
  memberSVIPImportApi,
  memberSVIPListApi,
} from "@/api/menberSVIPManagement";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import AdjustLevel from "./AdjustLevel.vue";
import { getToken } from "@/utils/auth"; // 获取token
export default {
  components: { AdjustLevel },
  data() {
    return {
      statusList: [
        { name: "已拒绝", value: -1 },
        { name: "待审核", value: 0 },
        { name: "审核通过", value: 1 },
        { name: "提现成功", value: 2 },
        { name: "打款失败", value: 3 },
      ],
      uploadUrl: process.env.VUE_APP_BASE_API + "/admin/user/svip/import", // 上传地址
      headers: {
        Authorization: "Bearer " + getToken(),
      },
      tabsItems: [
        { name: "提现审核", type: "1" },
        { name: "提现记录", type: "2" },
      ],
      curRow: {},
      time: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      selectId: [],
      checkAll: false,
      isIndeterminate: false,
      statisticsInfo: "",
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        keywords: null,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    checkPermi,
    adjustLevel(row) {
      this.curRow = row;
      this.$refs.adjustLevel.dialogVisible = true;
    },
    // 文件上传之前的验证
    beforeExcelUpload(file) {
      const isExcel = /\.(xlsx|xls)$/.test(file.name.toLowerCase());
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isExcel) {
        this.$message.error("只能上传 Excel 文件!");
        return false;
      }
      if (!isLt5M) {
        this.$message.error("文件大小不能超过 5MB!");
        return false;
      }
      this.listLoading = true;
      return true;
    },

    // 上传成功
    handleExcelSuccess(response, file) {
      this.listLoading = false;
      if (response.code === 200) {
        this.$message.success("导入成功");
        this.getList(); // 刷新列表
      } else {
        this.$message.error(response.msg || "导入失败");
      }
    },
    // 上传失败
    handleExcelError(err) {
      this.listLoading = false;
      this.$message.error("导入失败，请重试");
      console.error("上传失败:", err);
    },
    // 列表
    getList() {
      this.listLoading = true;
      if (this.time) {
        this.tableFrom.startTime = this.time[0];
        this.tableFrom.endTime = this.time[1];
      } else {
        this.tableFrom.startTime = null;
        this.tableFrom.endTime = null;
      }
      memberSVIPListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection();
    },
    handleSelectionChange(val) {
      this.selectId = val.map((item) => item.id);
      if (this.selectId.length == 0) {
        this.isIndeterminate = false;
        this.checkAll = false;
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectId.length == this.tableData.total) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        } else {
          if (this.selectId.length == this.tableFrom.limit) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        }
      }
    },
    getStatisticsInfo() {
      this.listLoading = true;
      if (this.time) {
        this.tableFrom.startTime = this.time[0];
        this.tableFrom.endTime = this.time[1];
      } else {
        this.tableFrom.startTime = null;
        this.tableFrom.endTime = null;
      }
      memberCommissionTotalApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        keywords: null,
      };
      this.time = [];
      this.getList();
    },

    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  padding-top: 6px;
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 16px;
  }
  .tips {
    margin-bottom: 16px;
    display: flex;
    gap: 10px;
  }
  .statistics-data {
    margin-bottom: 16px;
    .left {
      .statistics {
        display: flex;
        gap: 60px;
        .statistics-item {
          font-size: 14px;
          span {
            &:last-child {
              font-weight: bold;
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .table-list {
    .svip-name {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-info {
        .user-name {
          color: #3d71d2;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
