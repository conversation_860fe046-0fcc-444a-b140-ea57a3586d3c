<template>
  <div>
    <div class="search-box">
      <div class="search-form">
        <el-form
          label-width="110px"
          inline
          size="small"
        >
          <el-form-item label="昵称/手机号：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.keywords"
              placeholder="昵称/手机号"
              class="selWidth"
              size="small"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="调整时间：">
            <el-date-picker
              style="width: 300px !important"
              size="small"
              v-model="time"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
              align="left"
              clearable
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button
              @click="seachList"
              size="small"
              type="primary"
              >筛选</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="reset"
              >重置筛选条件</el-button
            >
          </el-form-item>
        </el-form>
      </div>
    </div>
    <el-table
      class="table-list"
      v-loading="listLoading"
      :data="tableData.data"
      ref="table"
      :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
      style="width: 100%"
      :highlight-current-row="true"
    >
      <el-table-column min-width="120">
        <template slot="header">
          <div class="svip-name">
            <span>SVIP会员</span>
            <memberLevelIcon
              :level="3"
            />
          </div>
        </template>
        <template slot-scope="scope">
          <div class="user">
            <el-avatar
              class="el-avatar"
              :size="36"
              src="scope.row.avatar"
            >
              <svg-icon
                class="user_avator"
                icon-class="user_avator"
              />
            </el-avatar>
            <div class="user-info">
              <div class="user-name">{{ scope.row.nickname }}</div>
              <div class="phone">{{ scope.row.phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="动作"
      />
      <el-table-column
        prop="spreadCount"
        label="原因"
      />
      <el-table-column
        prop="spreadCount"
        label="下级人员关系"
      />
      <el-table-column
        prop="createTime"
        label="调整时间"
      />
      <el-table-column
        prop="createTime"
        label="操作人"
      />
    </el-table>
    <div class="block">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
import { memberSVIPApplyListApi } from '@/api/menberSVIPManagement'
import { checkPermi } from '@/utils/permission' // 权限判断函数

export default {
  data() {
    return {
      tabsItems: [
        { name: '提现审核', type: '1' },
        { name: '提现记录', type: '2' }
      ],
      time: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      selectId: [],
      checkAll: false,
      isIndeterminate: false,
      statisticsInfo: '',
      tableFrom: {
        page: 1,
        limit: 20,
        status: 1,
        startTime: null,
        endTime: null,
        keywords: null
      }
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberSVIPApplyListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection()
    },
    handleSelectionChange(val) {
      this.selectId = val.map((item) => item.id)
      if (this.selectId.length == 0) {
        this.isIndeterminate = false
        this.checkAll = false
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectId.length == this.tableData.total) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        } else {
          if (this.selectId.length == this.tableFrom.limit) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        }
      }
    },
    getStatisticsInfo() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberCommissionTotalApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        keywords: null,
        status: null
      }
      this.time = []
      this.getList()
    },

    seachList() {
      this.tableData.data = []
      this.tableFrom.page = 1
      this.getList()
    },

    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  padding-top: 6px;
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 16px;
  }
  .tips {
    margin-bottom: 16px;
  }
  .statistics-data {
    margin-bottom: 16px;
    .left {
      .statistics {
        display: flex;
        gap: 60px;
        .statistics-item {
          font-size: 14px;
          span {
            &:last-child {
              font-weight: bold;
              font-size: 16px;
            }
          }
        }
      }
    }
  }

  .table-list {
    .svip-name {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-info {
        .user-name {
          color: #3d71d2;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
