<template>
  <el-dialog
    title="调整等级"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="720px"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div class="left-section">
        <div class="user-info">
          <el-avatar
            class="el-avatar"
            :size="84"
            :src="curRow.avatar"
          >
            <svg-icon
              class="user_avator"
              icon-class="user_avator"
            />
          </el-avatar>
          <div class="user-name">
            <span> {{ curRow.nickname || '微信用户' }} </span>
            <span>
              <memberLevelIcon :level="3" />
            </span>
          </div>
          <div class="user-phone">
            <span class="info-value">{{ curRow.phone }}</span>
          </div>
        </div>
      </div>

      <div class="right-section">
        <div class="title">
          <svg-icon
            class="level"
            icon-class="level"
          />
        </div>
        <div class="form">
          <div class="des">
            SVIP调整等级是做
            <span style="color: #e03d35; font-weight: 700">降级处理</span>
            ，调整后将失去该等级的会员权益； 请核对后再操作！
          </div>
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="82px"
            @submit.native.prevent
          >
            <el-form-item
              label="调整等级："
              prop="memberLevel"
            >
              <el-select
                clearable
                v-model="formValidate.memberLevel"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in memberLevelList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
              label="原因："
              prop="adjustReason"
            >
              <el-input
                :autosize="{ minRows: 4, maxRows: 8 }"
                type="textarea"
                v-model="formValidate.adjustReason"
              ></el-input>
            </el-form-item>
            <div class="terminated">
              <div>
                <el-checkbox v-model="formValidate.unbindRelation">
                  <span>解除下级用户的关系</span>
                </el-checkbox>
              </div>
              <div class="detail-description">
                请谨慎操作，解除后不可恢复！<span
                  v-if="formValidate.unbindRelation"
                  style="color: #5873de"
                  >可将此SVIP的下级用户转移给：</span
                >
              </div>
            </div>
            <el-form-item
              label="转移给："
              v-if="formValidate.unbindRelation"
            >
              <div>
                <span>
                  <el-input
                    placeholder="请输入SVIP的手机号"
                    v-model="formValidate.transferPhone"
                  ></el-input>
                </span>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            class="submission"
            @click="handleSubmit('formValidate')"
            >确定</el-button
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { svipLevelAdjustApi } from '@/api/menberSVIPManagement'
import { Debounce } from '@/utils/validate'
const defaultObj = () => ({
  adjustReason: null,
  memberLevel: null,
  transferPhone: null,
  unbindRelation: false
})
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      memberLevelList: [
        { name: '普通会员', value: 0 },
        { name: 'vip', value: 1 },
        { name: 'svip', value: 2 }
      ],
      ruleValidate: {
        adjustReason: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          svipLevelAdjustApi({
            ...this.formValidate,
            uid: this.curRow.uid
          }).then((res) => {
            this.$message.success('操作成功')
            this.handleClose()
            this.$emit('success')
          })
        }
      })
    }),
    handlerOpen() {
      // console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false
      this.formValidate = Object.assign({}, defaultObj())
      this.$refs.formValidate.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  min-height: 460px;
  .left-section {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 6px;
      padding-top: 35px;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 50px;
          color: white;
        }
      }
      .user-name {
        font-size: 18px;
        font-weight: 700;
        display: flex;
        gap: 10px;
        align-items: center;
      }
      .user-phone {
        color: black;
      }
    }
  }
  .right-section {
    position: relative;
    flex: 2;
    overflow: hidden;
    padding: 20px 0 0 30px;
    display: flex;
    align-items: flex-start;
    gap: 10px;
    .title {
      display: flex;
      align-items: center;
      gap: 10px;
      .level {
        font-size: 33px;
        margin-top: 6px;
      }
    }
    .form {
      > .des {
        line-height: 1.6;
        margin-bottom: 20px;
      }
      padding-right: 40px;
      .terminated {
        .detail-description {
          font-size: 12px;
          color: #666;
          margin: 6px 0 10px;
        }
      }
      .inline-flex {
        display: inline-flex;
        gap: 10px;
        align-items: center;
        white-space: nowrap;
      }
    }
    .btn {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
