<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link
                :to="{
                  path: '/member/memberSystem/creatMemberSystem',
                }"
              >
                <el-button type="primary">新增会员等级</el-button></router-link
              >
            </template>
            <template v-slot:right>
              <el-button @click="toParamsSettingPage" type="text"
                ><i class="el-icon-warning-outline"></i> 成长值规则</el-button
              >
            </template>
          </MyHeaderSearch>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column label="会员等级" width="120" prop="icon">
          <template slot-scope="scope">
            <div>
              <img
                style="display: block"
                class="img"
                :src="scope.row.icon"
                :title="scope.row.name"
                alt=""
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column label="名称" prop="name"> </el-table-column>
        <el-table-column label="获得等级条件" prop="experienceSource">
        </el-table-column>
        <el-table-column label="会员权益" prop="name">
          <template slot-scope="scope">
            <div v-if="scope.row.birthCouponEnabled">生日券</div>
            <div v-if="scope.row.discountEnabled">
              会员折扣{{ scope.row.discount }}折
            </div>
            <div v-if="scope.row.commissionEnabled">返现奖励</div>
            <div v-if="scope.row.customerServiceEnabled">专属微信客服</div>
          </template>
        </el-table-column>
        <el-table-column label="启用状态" prop="status">
          <template slot-scope="scope">
            <span
              ><span class="status-icon"></span
              ><span style="vertical-align: middle">已启用</span></span
            >
            <!-- <span>已启用</span> -->
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="scope">
            <router-link
              :to="{
                path: '/member/memberSystem/creatMemberSystem/' + scope.row.id,
              }"
            >
              <el-button type="text" size="small" class="mr10">编辑</el-button>
            </router-link>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              v-hasPermi="['admin:product:rule:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>
<script>
import { memberLevelListApi, memberLevelDeleteApi } from "@/api/memberSystem";

export default {
  name: "StoreAttr",
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20,
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      listLoading: true,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    seachList() {
      this.getList();
    },

    // 列表
    getList() {
      this.listLoading = true;
      memberLevelListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    toParamsSettingPage() {
      this.$store.dispatch("member/setTabsItemType", 0).then(() => {
        this.$router.push("/member/parameterSetting");
      });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除id为" + id + "的会员等级吗？")
        .then(() => {
          memberLevelDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.fr {
  float: right;
}

.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  .status-icon {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0fa142;
    margin-right: 4px;
  }
}
</style>
