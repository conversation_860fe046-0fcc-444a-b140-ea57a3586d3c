<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="search-form">
          <el-form label-width="110px" inline size="small">
            <el-form-item label="昵称/手机号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.keywords"
                placeholder="昵称/手机号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="会员等级：" prop="cateIds">
              <el-select
                clearable
                v-model="tableFrom.level"
                placeholder="请选择"
              >
              <el-option
                  v-for="item in userLevelList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.grade"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button @click="seachList" size="small" type="primary"
                >筛选</el-button
              >
              <el-button type="text" size="small" @click="reset"
                >重置筛选条件</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 顶部统计信息 -->
      <div class="summary-box">
        <el-row :gutter="20">
          <el-col :span="4" v-for="(item, index) in summaryData" :key="index">
            <div class="summary-item">
              <div class="summary-title">{{ item.title }}</div>
              <div class="summary-value">{{ item.value }}</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <el-table-column label="用户" min-width="200">
          <template slot-scope="scope">
            <div class="user">
              <el-avatar class="el-avatar" :size="36" :src="scope.row.avatar">
                <svg-icon class="user_avator" icon-class="user_avator" />
              </el-avatar>
              <div class="user-info">
                <div class="user-name">
                  <span>{{ scope.row.nickname || '微信用户' }}</span
                  ><span
                    ><memberLevelIcon
                      :level="scope.row.level"
                  /></span>
                </div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="nowMoney" label="现有余额" />
        <el-table-column prop="rechargeAmount" label="充值金额" />
        <el-table-column prop="refundAmount" label="返现&奖励" />
        <el-table-column prop="consumeAmount" label="累计消费金额" />
        <el-table-column prop="withdrawalAmount" label="累计提现金额" />
        <el-table-column
          label="操作"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="viewDetails(scope.row)"
              type="text"
              size="small"
              class="mr10"
              >查看明细</el-button
            >
            <el-button
              @click="addSubtractAmount(scope.row)"
              type="text"
              size="small"
              >加减款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <AddSubtractAmount
      ref="addSubtractAmount"
      :selectUserId="selectId"
      :curRow="curRow"
      @success="seachList"
    />
  </MainCard>
</template>

<script>
import {
  memberBalanceListApi,
  memberBalanceStatisticsApi,
} from "@/api/memberBalance";
import { userLevelListApi } from '@/api/discountCoupon.js'

import AddSubtractAmount from "./AddSubtractAmount.vue";

export default {
  name: "ProductList",
  components: { AddSubtractAmount },
  data() {
    return {
      props: {
        children: "child",
        label: "name",
        value: "id",
        emitPath: false,
      },
      memberTime: [],
      curRow: {},
      addType: "singleAdd",
      curUserId: null,
      summaryData: [
        { title: "会员余额合计（元）", value: "0.00", key: "totalBalance" },
        { title: "VIP充值总金额", value: "0.00", key: "totalRecharge" },
        { title: "累计返现&奖励", value: "0.00", key: "totalRefund" },
        { title: "累计消费金额", value: "0.00", key: "totalConsume" },
        { title: "累计提现金额", value: "0.00", key: "totalWithdrawal" },
      ],
      // roterPre: roterPre,
      headeNum: [],
      userLevelList: [],
      isFold: true,
      selectId: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      tableFrom: {
        page: 1,
        limit: 20,
        keywords: null,
        level: null,
        labelId: null,
      },
      checkAll: false,
      isIndeterminate: false,
      categoryList: [],
      curProductInfo: {},
      merCateList: [],
      objectUrl: process.env.VUE_APP_BASE_API,
      dialogVisible: false,
    };
  },
  mounted() {
    this.getUserLevelList()
    this.getStatisticsList();
    this.getList();
    this.checkedCities = this.$cache.local.has("goods_stroge")
      ? this.$cache.local.getJSON("goods_stroge")
      : this.checkedCities;
  },
  methods: {
    // 统计列表
    getStatisticsList() {
      memberBalanceStatisticsApi().then((res) => {
        this.summaryData.forEach((item) => {
          item.value = res[item.key];
        });
      });
    },
    // 查看详情
    viewDetails(row) {
      this.$router.push({
        name: "BalanceDetails",
        params: { ...row },
      });
    },
    // 获取会员等级字典
    getUserLevelList() {
      userLevelListApi().then((res) => {
        this.userLevelList = res;
      });
    },
    // 列表
    getList() {
      this.listLoading = true;
      memberBalanceListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    addSubtractAmount(row) {
      this.curRow = row;
      this.$refs.addSubtractAmount.dialogVisible = true;
    },

    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        memberTimeStart: null,
        memberTimeEnd: null,
        keywords: null,
        level: null,
        labelId: null,
      };
      this.getList();
    },
    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    .tabs {
      margin: 10px 0;
    }
    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 20px;
  }
  .summary-box {
    margin-bottom: 20px;
    .summary-item {
      .summary-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 5px;
      }
      .summary-value {
        font-size: 20px;
        font-weight: bold;
        color: #e74c3c;
      }
    }
  }
  .tips {
    margin-bottom: 16px;
  }

  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-name {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: nowrap;
        span {
          &:first-child {
            font-size: 15px;
            color: #3d71d2;
          }
        }
      }
    }
  }
}
.selWidth {
  width: 350px !important;
}
</style>
