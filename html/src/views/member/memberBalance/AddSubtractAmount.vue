<template>
  <el-dialog
    title="加减款"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="54%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div class="left-section">
        <div class="user-info">
          <el-avatar class="el-avatar" :size="84" :src="curRow.avatar">
            <svg-icon class="user_avator" icon-class="user_avator" />
          </el-avatar>
          <div class="user-name">
            <span>{{ curRow.nickname }}</span
            ><span
              ><memberLevelIcon
                :level="curRow.level"
            /></span>
          </div>
          <div class="user-phone">
            <span class="info-value">{{ curRow.phone }}</span>
          </div>
        </div>
      </div>

      <div class="right-section">
        <div class="title">
          <img src="@/assets/imgs/add_subtrac_icon.png" alt="" />
          <span
            >加减款是通过增加或减少金额来调整会员账号余额；会员线下充
            值/退费需做备注。</span
          >
        </div>
        <div class="form">
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="120px"
            @submit.native.prevent
          >
            <el-form-item label="加减款金额：" prop="moneyValue">
              <el-input
                placeholder="输入负数则扣减"
                v-model.number="formValidate.moneyValue"
                @input="handleInput"
              />
              <div
                v-show="this.formValidate.moneyValue"
                class="capitalized-amount"
              >
                <span class="show-symbol">{{ showSymbol }}</span>
                <span
                  v-show="
                    this.formValidate.moneyValue != '-' &&
                    this.formValidate.moneyValue != '.'
                  "
                  >{{ upperCaseAmount }}</span
                >
              </div>
            </el-form-item>
            <el-form-item label="操作类型：" prop="operationType">
              <el-radio-group v-model="formValidate.operationType">
                <el-radio :label="1"> 会员充值 </el-radio>
                <el-radio :label="2"> 后台调整 </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="操作备注：">
              <el-input
                :autosize="{ minRows: 4, maxRows: 8 }"
                type="textarea"
                v-model="formValidate.remark"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            class="submission"
            @click="handleSubmit('formValidate')"
            >确定</el-button
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { memberBalanceUpdateApi } from "@/api/memberBalance";
import { Debounce } from "@/utils/validate";
const defaultObj = () => ({
  operationType: 2,
  moneyType: null,
  moneyValue: null,
  remark: null,
});
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    showSymbol() {
      return this.formValidate.moneyValue >= 0 ? "+" : "-";
    },
    upperCaseAmount() {
      return this.formValidate.moneyValue
        ? this.convertToUpperCase(this.formValidate.moneyValue)
        : "";
    },
  },
  watch: {
    "formValidate.moneyValue": {
      handler(val) {
        if (Number(val) > 0) {
          this.formValidate.moneyType = 1;
          return;
        }
        if (Number(val) < 0) {
          this.formValidate.moneyType = 2;
          return;
        }
        this.formValidate.moneyType = null;
      },
      immediate: true,
    },
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      ruleValidate: {
        moneyValue: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (isNaN(value) || value === null || value === "") {
                callback(new Error("请输入一个合法的数值"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        operationType: [
          { required: true, message: "请选择", trigger: "change" },
        ],
      },

      stats: [
        { label: "支付总金额", value: "¥341405.2", tooltip: "支付总金额说明" },
        { label: "优惠总金额", value: "¥37910.8", tooltip: "优惠总金额说明" },
        { label: "费效比", value: "11.10%", tooltip: "费效比说明" },
        { label: "用券笔单价", value: "48772.17", tooltip: "用券笔单价说明" },
        { label: "用券客数", value: "3", tooltip: "用券客数说明" },
        { label: "购买商品件数", value: "131", tooltip: "购买商品件数说明" },
      ],
    };
  },

  methods: {
    handleInput(value) {
      // 如果输入为空，直接返回空值
      if (!value) {
        this.formValidate.moneyValue = "";
        return;
      }
      // 处理负号：只允许开头有一个负号
      let newValue = value;
      if (value.indexOf("-") !== -1) {
        newValue = (value.startsWith("-") ? "-" : "") + value.replace(/-/g, "");
      }

      // 处理数字和小数点：只允许数字和一个小数点，且小数点后最多两位
      newValue = newValue.replace(/[^\d.-]/g, ""); // 只保留数字、小数点和负号

      // 处理多个小数点的情况
      const parts = newValue.split(".");
      if (parts.length > 2) {
        newValue = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数点后两位
      if (parts.length === 2) {
        newValue = parts[0] + "." + parts[1].slice(0, 2);
      }

      // 更新值
      this.formValidate.moneyValue = newValue;
    },
    convertToUpperCase(money) {
      if (!money && money !== 0) return "";
      const fraction = ["角", "分"];
      const digit = [
        "零",
        "壹",
        "贰",
        "叁",
        "肆",
        "伍",
        "陆",
        "柒",
        "捌",
        "玖",
      ];
      const unit = [
        ["元", "万", "亿"],
        ["", "拾", "佰", "仟"],
      ];
      // const head = money < 0 ? "负" : "";
      let n = Math.abs(money);

      let s = "";

      for (let i = 0; i < fraction.length; i++) {
        s += (
          digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]
        ).replace(/零./, "");
      }
      s = s || "整";
      n = Math.floor(n);

      for (let i = 0; i < unit[0].length && n > 0; i++) {
        let p = "";
        for (let j = 0; j < unit[1].length && n > 0; j++) {
          p = digit[n % 10] + unit[1][j] + p;
          n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
      }

      return (
        // head +
        s
          .replace(/(零.)*零元/, "元")
          .replace(/(零.)+/g, "零")
          .replace(/^整$/, "零元整")
      );
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          memberBalanceUpdateApi({
            ...this.formValidate,
            uid: this.curRow.id,
            integralType: this.formValidate.moneyType,
            moneyValue: Math.abs(this.formValidate.moneyValue),
          }).then((res) => {
            this.$message.success("操作成功");
            this.handleClose();
            this.$emit("success");
          });
        }
      });
    }),
    handlerOpen() {
      console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false;
      this.formValidate = Object.assign({}, defaultObj());
      this.$refs.formValidate.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  height: 500px;
  .left-section {
    flex: 1;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16px;
      padding-top: 40px;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 50px;
          color: white;
        }
      }
      .user-name {
        font-size: 18px;
        font-weight: 700;
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }
  }

  .right-section {
    position: relative;
    flex: 2;
    overflow: hidden;
    padding: 20px;

    .title {
      display: flex;
      align-items: center;
      margin: 10px 0 30px;
      gap: 10px;
      line-height: 1.4;
    }

    .form {
      padding-right: 40px;
      .capitalized-amount {
        font-size: 16px;
        font-weight: 700;
        height: 36px;
        background-image: url("~@/assets/imgs/line.png");
        background-position: left top;
        margin-top: 6px;
        padding-left: 10px;
        .show-symbol {
          font-size: 20px;
          line-height: 1;
        }
      }
    }
    .btn {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
