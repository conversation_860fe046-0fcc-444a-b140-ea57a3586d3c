<template>
  <div class="app-box">
    <div class="search-form">
      <el-form
        :label-width="isBalanceDetails ? '60px' : 'auto'"
        inline
        size="small"
      >
        <el-form-item
          v-if="!isBalanceDetails"
          label="昵称/手机号："
        >
          <el-input
            style="width: 200px !important"
            v-model="tableFrom.userKeywords"
            placeholder="昵称/手机号"
            class="selWidth"
            size="small"
            clearable
          >
          </el-input>
        </el-form-item>
        <el-form-item label="类型：">
          <el-select
            clearable
            v-model="tableFrom.billType"
            placeholder="请选择"
          >
            <el-option
              v-for="item in memberBalanceBillTypesList"
              :key="item.id"
              :label="item.title"
              :value="item.title"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间：">
          <el-date-picker
            size="small"
            v-model="time"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd HH:mm:ss"
            align="left"
            clearable
          >
          </el-date-picker>
        </el-form-item>
      </el-form>
      <div
        :style="`margin-left:${isBalanceDetails ? '60px' : '100px'}`"
        class="search-btn"
      >
        <el-button
          @click="getBalanceDetailList"
          size="small"
          type="primary"
          >筛选</el-button
        >
        <el-button
          type="text"
          size="small"
          @click="reset"
          >重置筛选条件</el-button
        >
      </div>
    </div>
    <div class="statistics">
      <div class="item">
        <span>收入合计：</span><span>{{ statistics.incomeTotal }}</span>
      </div>
      <div class="item">
        <span>支出合计：</span><span>{{ statistics.expenditureTotal }}</span>
      </div>
    </div>
    <el-table
      class="table-list"
      v-loading="listLoading"
      :data="tableData.data"
      ref="table"
      :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
      style="width: 100%"
      :highlight-current-row="true"
    >
      <el-table-column
        v-if="!isBalanceDetails"
        label="用户"
        min-width="120"
      >
        <template slot-scope="scope">
          <div class="user">
            <el-avatar
              class="el-avatar"
              :size="36"
              :src="scope.row.avatar"
            >
              <svg-icon
                class="user_avator"
                icon-class="user_avator"
              />
            </el-avatar>
            <div class="user-info">
              <div class="user-name">
                <span>{{ scope.row.nickname || '微信用户' }}</span
                ><span><memberLevelIcon :level="scope.row.level" /></span>
              </div>
              <div class="phone">{{ scope.row.phone }}</div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="时间"
      />
      <el-table-column
        prop="title"
        label="类型"
      />
      <el-table-column label="收入+">
        <template slot-scope="scope">
          <span>{{ scope.row.pm == 1 ? scope.row.number : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="支出-">
        <template slot-scope="scope">
          <span>{{ scope.row.pm == 0 ? scope.row.number : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="mark"
        label="备注"
      />
    </el-table>
    <div class="pagination">
      <el-pagination
        :page-sizes="[20, 40, 60, 80]"
        :page-size="tableFrom.limit"
        :current-page="tableFrom.page"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableData.total"
        @size-change="handleSizeChange"
        @current-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
import {
  memberBalanceDetailApi,
  memberBalanceBillTypesApi
} from '@/api/memberBalance'
export default {
  data() {
    return {
      time: [],
      statistics: {
        incomeTotal: 0.0,
        expenditureTotal: 0.0
      },
      memberBalanceBillTypesList: [],
      listLoading: false,
      tableFrom: {
        page: 1,
        limit: 20,
        billType: null,
        userKeywords: null,
        startTime: null,
        endTime: null,
        uid: null
      },
      tableData: {
        data: [],
        total: 0
      }
    }
  },
  props: {
    id: {
      type: Number,
      default: null
    },
    isBalanceDetails: {
      type: Boolean,
      default: false
    }
  },
  mounted() {
    this.memberBalanceBillTypes()
    this.getBalanceDetailList()
  },
  methods: {
    // 标签列表
    getBalanceDetailList() {
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      this.listLoading = true
      memberBalanceDetailApi({ ...this.tableFrom, uid: this.id })
        .then((res) => {
          this.statistics.incomeTotal = res.incomeTotal
          this.statistics.expenditureTotal = res.expenditureTotal
          this.tableData.data = res.page.list
          this.tableData.total = res.page.total
          this.listLoading = false
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    memberBalanceBillTypes() {
      memberBalanceBillTypesApi()
        .then((res) => {
          this.memberBalanceBillTypesList = res
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        billType: null,
        startTime: null,
        endTime: null
      }
      this.time = []
      this.getBalanceDetailList()
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getBalanceDetailList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getBalanceDetailList()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-box {
  .search-form {
    background-color: #f7f7f7;
    padding: 20px;
    margin-bottom: 16px;
  }
  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-name {
        display: flex;
        align-items: center;
        gap: 8px;
        flex-wrap: nowrap;
        span {
          &:first-child {
            font-size: 15px;
            color: #3d71d2;
          }
        }
      }
    }
  }
  .statistics {
    margin-bottom: 16px;
    display: flex;
    gap: 40px;
    font-size: 14px;
    .item {
      span:last-child {
        font-size: 16px;
        font-weight: bold;
        color: #333;
      }
    }
  }
  .pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>
