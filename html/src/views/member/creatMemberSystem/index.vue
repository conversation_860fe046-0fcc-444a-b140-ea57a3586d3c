<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item style="width: 40%" label="等级名称：" prop="name">
                <el-input v-model="formValidate.name" placeholder="请输入" />
              </el-form-item>
              <el-form-item style="width: 40%" prop="grade" label="会员等级：">
                <el-input v-model="formValidate.grade" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="等级图标：">
                <div class="upLoadPicBox">
                  <div
                    @click="modalPicTap('1')"
                    v-if="formValidate.icon"
                    class="pictrue"
                  >
                    <img :src="handlerImgUrl(formValidate.icon)" alt="" />
                  </div>
                  <div @click="modalPicTap('1')" v-else class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
              </el-form-item>

              <el-form-item label="获得条件：" prop="experience">
                <div class="inline-flex">
                  <span>成长值</span>
                  <div class="inline-flex">
                    <span>
                      <el-input
                        v-model="formValidate.experience"
                        placeholder="请输入"
                      />
                    </span>
                    <span>
                      <el-button @click="toParamsSettingPage(0)" type="text">会员成长值设置</el-button>
                    </span>
                  </div>
                </div>
                <div class="flex">
                  <span>包括</span>
                  <el-form-item>
                    <el-checkbox-group v-model="formValidate.experienceSource">
                      <el-checkbox label="自购消费">自购消费</el-checkbox>
                      <el-checkbox label="推广金额">推广金额</el-checkbox>
                      <el-checkbox label="会员充值">会员充值</el-checkbox>
                    </el-checkbox-group>
                  </el-form-item>
                </div>
              </el-form-item>
              <el-form-item style="margin-top: -10px">
                <div>
                  <el-checkbox v-model.number="formValidate.autoUpgrade">
                    <span>达到成长值自动升级</span>
                  </el-checkbox>
                </div>
                <div class="detail-description">
                  勾选后，系统自动调整会员等级，否则将以消息方式提示需申请后再调整会员等级
                </div>
              </el-form-item>
              <el-form-item>
                <div>
                  <el-checkbox v-model="formValidate.phoneRequired">
                    <span>注册信息 （获取手机号）</span>
                  </el-checkbox>
                </div>
                <div class="detail-description">
                  勾选后，所有等级会员都需填写，系统会在个人中心提示补充完善资料
                </div>
              </el-form-item>
              <el-form-item>
                <div class="inline-flex">
                  <div class="inline-flex">
                    <span>
                      <el-checkbox v-model="formValidate.couponEnabled">
                        <span>补充资料送优惠券</span>
                      </el-checkbox>
                    </span>
                    <span>
                      <div class="acea-row">
                        <el-tag
                          v-for="(tag, index) in formValidate.coupons"
                          :key="index"
                          class="mr10 mb10"
                          closable
                          :disable-transitions="false"
                          @close="handleCloseCoupon(tag)"
                        >
                          {{ tag.name }}
                        </el-tag>
                        <el-button
                          :disabled="!formValidate.couponEnabled"
                          @click="addCoupon(1)"
                          type="text"
                          >选择优惠券</el-button
                        >
                      </div>
                    </span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->
          <!-- 权益礼包 start -->
          <div class="base-card">
            <div class="info-title">权益礼包</div>
            <div class="form-info">
              <el-form-item required label="会员权限：">
                <div class="form-item-box">
                  <div class="left">
                    <el-checkbox v-model="formValidate.birthCouponEnabled">
                      <span>生日券</span>
                    </el-checkbox>
                  </div>
                  <div class="right">
                    <div>
                      <el-button
                        :disabled="!formValidate.birthCouponEnabled"
                        @click="addCoupon(2)"
                        type="text"
                        >选择生日券</el-button
                      >
                    </div>
                    <div
                      v-show="formValidate.birthCouponEnabled"
                      class="table-box"
                    >
                      <el-table
                        v-show="formValidate.birthCoupon.length > 0"
                        :data="formValidate.birthCoupon"
                        :header-cell-style="{
                          background: '#f5f5f5',
                          color: '#444',
                          lineHeight: 1,
                        }"
                        class="tabNumWidth"
                        size="mini"
                      >
                        <el-table-column label="优惠券活动名称" prop="name">
                        </el-table-column>
                        <el-table-column label="活动类型" prop="couponType">
                          <template slot-scope="scope">
                            <span>{{
                              couponList.find(
                                (item) => item.type == scope.row.couponType
                              ).title
                            }}</span>
                          </template>
                        </el-table-column>
                        <el-table-column label="优惠内容">
                          <template slot-scope="scope">
                            <span v-if="scope.row.couponType === 1">
                              订单满{{ scope.row.minPrice }}元,减{{
                                scope.row.money
                              }}元
                            </span>
                            <span v-if="scope.row.couponType === 2">
                              无门槛,减{{ scope.row.money }}元
                            </span>
                          </template>
                        </el-table-column>
                        <el-table-column
                          label="限定会员等级"
                          prop="memberLevels"
                        >
                        </el-table-column>
                      </el-table>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <el-checkbox v-model="formValidate.discountEnabled">
                      <span>会员折扣</span>
                    </el-checkbox>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>购买产品享</span>
                      <span>
                        <el-input
                          :disabled="!formValidate.discountEnabled"
                          v-model="formValidate.discount"
                        >
                          <template slot="append">折</template>
                        </el-input>
                      </span>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <el-checkbox v-model="formValidate.commissionEnabled">
                      <span>佣金返现</span>
                    </el-checkbox>
                  </div>
                  <div class="right">
                    <el-button
                    @click="toParamsSettingPage(2)"
                      :disabled="!formValidate.commissionEnabled"
                      type="text"
                      >佣金返现规则</el-button
                    >
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <el-checkbox v-model="formValidate.customerServiceEnabled">
                      <span>专属客服</span>
                    </el-checkbox>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>客服微信号</span>
                      <span>
                        <el-input
                          :disabled="!formValidate.customerServiceEnabled"
                          v-model="formValidate.customerServiceWechat"
                        />
                      </span>
                    </span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 权益礼包 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  memberLevelCreateApi,
  memberLevelUpdateApi,
  memberLevelInfoApi,
} from "@/api/memberSystem";
import { Debounce } from "@/utils/validate";

const defaultObj = {
  name: "",
  icon: "",
  grade: null,
  experience: null,
  autoUpgrade: true,
  phoneRequired: true,
  couponEnabled: false,
  birthCouponEnabled: false,
  discountEnabled: false,
  commissionEnabled: false,
  customerServiceEnabled: false,
  customerServiceWechat: null,
  discount: null,
  isShow: true,
  experienceSource: [],
  coupons: [],
  couponIds: [],
  birthCoupon: [],
  birthCouponIds: [],
};
export default {
  name: "ProductProductAdd",
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      couponList: [
        { title: "满减券", type: 1 },
        { title: "新人专享券", type: 2 },
        {
          title: "会员专享券",
          type: 3,
        },
      ],
      ruleValidate: {
        name: [{ required: true, message: "请输入等级名称", trigger: "blur" }],
        experience: [{ required: true, message: "请输入", trigger: "blur" }],
        grade: [{ required: true, message: "请输入会员等级", trigger: "blur" }],
      },
      fullscreenLoading: false,
    };
  },

  mounted() {
    if (this.$route.params.id) {
      this.getInfo();
    }
  },
  watch: {
    "formValidate.discountEnabled"(val) {
      if (!val) {
        this.formValidate.discount = null;
      }
    },
    "formValidate.customerServiceEnabled"(val) {
      if (!val) {
        this.formValidate.customerServiceWechat = null;
      }
    },
  },
  methods: {
    // 详情
    getInfo() {
      this.fullscreenLoading = true;
      memberLevelInfoApi(this.$route.params.id)
        .then(async (res) => {
          console.log(res);
          let info = res.userLevel;
          let memberCoupon = res.memberCoupon;
          let birthCoupon = res.birthCoupon;
          this.formValidate = {
            name: info.name,
            icon: info.icon,
            isShow: true,
            experience: info.experience,
            autoUpgrade: info.autoUpgrade,
            grade: info.grade,
            phoneRequired: info.phoneRequired,
            couponEnabled: info.couponEnabled,
            birthCouponEnabled: info.birthCouponEnabled,
            discountEnabled: info.discountEnabled,
            commissionEnabled: info.commissionEnabled,
            customerServiceEnabled: info.customerServiceEnabled,
            customerServiceWechat: info.customerServiceWechat,
            discount: info.discount,
            experienceSource: info.experienceSource
              ? info.experienceSource.split(",")
              : [],
            coupons: memberCoupon ? [memberCoupon] : [],
            couponId: info.couponId,
            birthCoupon: birthCoupon ? [birthCoupon] : [],
            birthCouponId: info.birthCouponId,
          };
          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    // 选择优惠券
    addCoupon(type) {
      const _this = this;
      this.$modalCoupon(
        "wu",
        (this.keyNum += 1),
        type == 1 ? this.formValidate.coupons : this.formValidate.birthCoupon,
        function (row) {
          if (row && row.length > 1) {
            this.$message.warning("只能选择一张优惠券");
            return;
          }
          if (row && row.length == 0) {
            _this.formValidate.couponId = null;
            _this.formValidate.birthCouponId = null;
            _this.formValidate.coupons = [];
            _this.formValidate.birthCoupon = [];
          }
          if (row.length > 0) {
            if (type == 1) {
              _this.formValidate.couponId = null;
              _this.formValidate.coupons = [...row];
              _this.formValidate.couponId = row[0].id;
            }
            if (type == 2) {
              _this.formValidate.birthCouponId = null;
              _this.formValidate.birthCoupon = [...row];
              _this.formValidate.birthCouponId = row[0].id;
            }
          }
        },
        ""
      );
    },
    toParamsSettingPage(type) {
      this.$store.dispatch("member/setTabsItemType", type).then(() => {
        this.$router.push("/member/parameterSetting");
      });
    },
    handleCloseCoupon(tag) {
      this.formValidate.coupons.splice(
        this.formValidate.coupons.indexOf(tag),
        1
      );
      this.formValidate.couponIds.splice(
        this.formValidate.couponIds.indexOf(tag.id),
        1
      );
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    modalPicTap(type) {
      const _this = this;
      this.$modalUpload(
        function (img) {
          if (type === "1") {
            _this.formValidate.icon = img[0].attDir;
          }
        },
        2,
        "content"
      );
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      const experienceSource = this.formValidate.experienceSource.join(",");
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.params.id
            ? memberLevelUpdateApi({
                id: this.$route.params.id,
                ...this.formValidate,
                experienceSource,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/member/memberSystem" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : memberLevelCreateApi({
                ...this.formValidate,
                experienceSource,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/member/memberSystem" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          this.$message.warning("请填写完整信息！");
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
    line-height: 1;
  }
  .mt10 {
    margin-top: 10px;
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    white-space: nowrap;
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
  }
  .form-info {
    padding-left: 80px;
    .upLoadPicBox {
      display: inline-flex;
    }
  }

  .form-item-box {
    display: flex;
    .left {
      flex: 0 0 120px;
    }
    .right {
      flex: 1;
    }
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
