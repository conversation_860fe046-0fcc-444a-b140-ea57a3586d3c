<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="search-form">
          <el-form
            label-width="auto"
            inline
            size="small"
          >
            <div>
              <el-form-item label="订单支付时间：">
                <el-date-picker
                  size="small"
                  v-model="time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  align="left"
                  clearable
                >
                </el-date-picker>
              </el-form-item>
            </div>
            <el-form-item label="SVIP手机号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.mobile"
                placeholder="SVIP手机号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="订单号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.linkId"
                placeholder="订单号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>

            <el-form-item label="结算状态：">
              <el-select
                clearable
                v-model="tableFrom.status"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="search-btn">
            <el-button
              @click="seachList"
              size="small"
              type="primary"
              >筛选</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="reset"
              >重置筛选条件</el-button
            >
          </div>
        </div>
        <div class="statistics-data">
          <div class="left">
            <div class="des">
              只统计已结算状态的数据，退款/退款订单不计入销售额内
            </div>
            <div class="statistics">
              <span class="statistics-item"
                ><span>返现金额：</span
                ><span>￥{{ statisticsInfo.settledCommission }}</span></span
              >
              <span class="statistics-item"
                ><span>销售额合计：</span
                ><span>￥{{ statisticsInfo.salesTotal }}</span></span
              >
            </div>
          </div>
          <div class="right">
            <el-button
              @click="toParamsSettingPage(2)"
              type="text"
              ><i class="el-icon-warning-outline"></i>佣金返现参数</el-button
            >
          </div>
        </div>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <el-table-column
          prop="linkId"
          label="订单号"
        >
          <template slot-scope="scope">
            <span style="color: #3d71d2">{{ scope.row.linkId }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="orderAmount"
          label="销售额"
        />
        <el-table-column
          prop="price"
          label="返现金额"
        />
        <el-table-column
          prop="status"
          label="结算状态"
        >
          <template slot-scope="scope">
            <span>{{
              statusList.find((item) => item.value === scope.row.status).name
            }}</span>
          </template></el-table-column
        >
        <el-table-column
          prop="createTime"
          label="订单支付时间"
        />
        <el-table-column
          prop="status"
          label="分销人员"
        >
          <template slot-scope="scope">
            <div>
              <div style="color: #3d71d2">{{ scope.row.userName }}</div>
              <div>{{ scope.row.userPhone }}</div>
            </div>
          </template></el-table-column
        >
        <el-table-column
          prop="mark"
          label="备注"
        />
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>

<script>
import { mapGetters } from 'vuex'
import {
  memberCommissionListApi,
  memberCommissionTotalApi
} from '@/api/memberCashbackAndBonus'
import { checkPermi } from '@/utils/permission' // 权限判断函数

export default {
  name: 'ProductList',
  data() {
    return {
      statusList: [
        { name: '待结算', value: 1 },
        { name: '已结算', value: 2 },
        { name: '已失效', value: 3 }
      ],
      time: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      statisticsInfo: {},
      tableFrom: {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        linkId: null,
        mobile: null,
        status: null
      }
    }
  },
  computed: {
    ...mapGetters(['tabsItemType'])
  },
  mounted() {
    this.getStatisticsInfo()
    this.getList()
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberCommissionListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    toParamsSettingPage(type) {
      this.$store.dispatch('member/setTabsItemType', type).then(() => {
        this.$router.push('/member/parameterSetting')
      })
    },
    getStatisticsInfo() {
      this.listLoading = true
      if (this.time) {
        this.tableFrom.startTime = this.time[0]
        this.tableFrom.endTime = this.time[1]
      } else {
        this.tableFrom.startTime = null
        this.tableFrom.endTime = null
      }
      memberCommissionTotalApi(this.tableFrom)
        .then((res) => {
          this.statisticsInfo = res
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        startTime: null,
        endTime: null,
        linkId: null,
        mobile: null,
        status: null
      }
      this.time = []
      this.getList()
    },

    seachList() {
      this.tableData.data = []
      this.tableFrom.page = 1
      this.getStatisticsInfo()
      this.getList()
    },

    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    .tabs {
      margin: 10px 0;
    }
    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px;
    margin-bottom: 20px;
    .search-btn {
      margin-left: 110px;
    }
  }
  .statistics-data {
    display: flex;
    width: 100%;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .left {
      .des {
        color: #888888;
        margin-bottom: 6px;
      }
      .statistics {
        display: flex;
        gap: 60px;

        .statistics-item {
          font-size: 14px;
          span {
            &:last-child {
              font-weight: bold;
              font-size: 16px;
            }
          }
        }
      }
    }
    .right {
      .el-icon-warning-outline {
        margin-right: 4px;
      }
    }
  }

  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
    }
  }
}

.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
