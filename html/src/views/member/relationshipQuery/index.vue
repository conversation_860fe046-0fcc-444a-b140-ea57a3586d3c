<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="search-form">
          <el-form label-width="120px" inline size="small">
            <el-form-item label="昵称/手机号：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.extractNumber"
                placeholder="昵称/手机号"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="所属上级：">
              <el-input
                style="width: 200px !important"
                v-model="tableFrom.extractNumber"
                placeholder="所属上级"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item>
              <el-button @click="seachList" size="small" type="primary"
                >筛选</el-button
              >
              <el-button type="text" size="small" @click="reset"
                >重置筛选条件</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        :highlight-current-row="true"
      >
        <el-table-column label="用户">
          <template slot-scope="scope">
            <div class="user">
              <div class="user-info">
                <div style="color: #3d71d2" class="user-name">
                 
                  <span>{{ scope.row.nickname || '微信用户' }}</span
                  ><span
                    ><memberLevelIcon
                      :level="scope.row.memberLevelCode"
                  /></span>
                </div>
                <div class="phone">{{ scope.row.phone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="memberLevel" label="会员等级" />
        <el-table-column label="所属上级">
          <template slot-scope="scope">
            <div class="user">
              <div class="user-info">
                <div class="user-name">
                  <span>{{ scope.row.spreadNickname }}</span
                  ><span
                    ><memberLevelIcon
                      :level="scope.row.level"
                  /></span>
                </div>
                <div class="phone">{{ scope.row.spreadPhone }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="spreadChangeTime" label="关系更新时间" />
        <el-table-column prop="spreadStatus" label="关系状态">
          <template slot-scope="scope">
            <span>{{ scope.row.spreadStatus == "0" ? "无效" : "有效" }}</span>
          </template></el-table-column
        >
        <el-table-column
          label="操作"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template slot-scope="scope">
            <el-button
              @click="openChangeRecord(scope.row)"
              type="text"
              size="small"
              >变更记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <ChangeRecord
      ref="changeRecord"
      :addType="addType"
      @refresh="getTagList"
      :selectUserId="selectId"
      :curRow="curRow"
      :tagList="memberTagList"
      @success="seachList"
    />
  </MainCard>
</template>

<script>
import { memberSpreadRecordApplyListApi } from "@/api/relationshipQuery";
import { checkPermi } from "@/utils/permission"; // 权限判断函数
import ChangeRecord from "./ChangeRecord.vue";

export default {
  components: { ChangeRecord },
  data() {
    return {
      statusList: [
        { name: "已拒绝", value: -1 },
        { name: "待审核", value: 0 },
        { name: "审核通过", value: 1 },
        { name: "提现成功", value: 2 },
        { name: "打款失败", value: 3 },
      ],
      listLoading: true,
      tableData: {
        data: [],
        total: 0,
      },
      curRow: {},
      statisticsInfo: "",
      tableFrom: {
        page: 1,
        limit: 20,
        keywords: null,
        spreadUid: null,
      },
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    checkPermi,
    // 列表
    getList() {
      this.listLoading = true;
      memberSpreadRecordApplyListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    openChangeRecord(row) {
      this.curRow = row;
      this.$refs.changeRecord.dialogVisible = true;
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        keywords: null,
        spreadUid: null,
      };
      this.getList();
    },

    seachList() {
      this.tableData.data = [];
      this.tableFrom.page = 1;
      this.getList();
    },

    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;

    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px 0;
    margin-bottom: 16px;
  }
  .table-list {
    .user {
      display: flex;
      gap: 10px;
      align-items: center;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 26px;
          color: white;
        }
      }
      .user-name {
        display: flex;
        gap: 6px;
        align-items: center;
      }
    }
  }
}
.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}

.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
</style>
