<template>
  <el-dialog
    title="变更记录"
    :visible.sync="dialogVisible"
    width="800px"
    @opened="handlerOpen"
    @close="handlerClose"
    z-index="5"
  >
    <div class="dialog-content">
      <div class="relationship">
        <div class="subordinate user-info-box">
          <div class="user">
            <el-avatar
              class="el-avatar"
              :size="36"
              :src="spreadRecordApplyDetail.user.avatar"
            >
              <svg-icon class="user_avator" icon-class="user_avator" />
            </el-avatar>
            <div class="user-info">
              <div style="color: #3d71d2" class="user-name">
                {{ spreadRecordApplyDetail.user.nickname }}
              </div>
              <div class="phone">{{ spreadRecordApplyDetail.user.phone }}</div>
            </div>
          </div>
        </div>
        <div class="binding-relationship">
          <div class="relationship-status">
            <svg-icon
              class="user_avator"
              :icon-class="
                curRow.spreadStatus == 0 ? 'not_binding_icon' : 'binding_icon'
              "
            />
            <span
              :style="`color:${curRow.spreadStatus == 0 ? '#DD4130' : '#333'}`"
              >{{ curRow.spreadStatus == 0 ? "脱落关系" : "已绑定" }}</span
            >
          </div>
        </div>
        <div
          :style="`opacity:${curRow.spreadStatus == 0 ? '.5' : '1'}`"
          class="superiors user-info-box"
        >
          <div class="user">
            <el-avatar class="el-avatar" :size="36">
              <span>上级</span>
            </el-avatar>
            <div class="user-info">
              <div class="user-name">
                <span>{{ spreadRecordApplyDetail.currentSpread.nickname }}</span
                ><span
                  ><memberLevelIcon
                    :level="spreadRecordApplyDetail.currentSpread.level"
                /></span>
              </div>
              <div class="phone">
                {{ spreadRecordApplyDetail.currentSpread.phone }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <el-table
        class="table-list"
        v-loading="listLoading"
        :data="spreadRecordApplyDetail.changeRecords"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        size="mini"
        :highlight-current-row="true"
      >
        <el-table-column label="当前所属上级">
          <template slot-scope="scope">
            <div style="color: #3d71d2" class="user">
              <div v-if="scope.row.newSpreadNickname" class="user-info">
                <div class="user-name">
                  <span>{{ scope.row.newSpreadNickname }}</span
                  ><span><memberLevelIcon :level="scope.row.level" /></span>
                </div>
                <div class="phone">{{ scope.row.newSpreadPhone }}</div>
              </div>
              <div v-else>--</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="changeTime" label="更新时间" />
        <el-table-column prop="changeMessage" label="变更信息" />
      </el-table>
    </div>
  </el-dialog>
</template>

<script>
import { memberSpreadRecordApplyDetailApi } from "@/api/relationshipQuery";

export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      listLoading: false,
      spreadRecordApplyDetail: {
        changeRecords: [],
        currentSpread: {},
        user: {},
      },
    };
  },

  methods: {
    handlerOpen() {
      this.getSpreadRecordApplyDetail();
    },
    getSpreadRecordApplyDetail() {
      this.listLoading = true;
      memberSpreadRecordApplyDetailApi(this.curRow.uid)
        .then((res) => {
          this.spreadRecordApplyDetail = res;
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    handlerClose() {},
  },
};
</script>

<style scoped lang="scss">
.dialog-content {
  height: 400px;
  .relationship {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 30px;
    .user-info-box {
      .user {
        display: flex;
        gap: 10px;
        align-items: center;
        .el-avatar {
          background-color: #d8d8d8;
          display: flex;
          align-items: center;
          justify-content: center;
          .user_avator {
            font-size: 26px;
            color: white;
          }
        }
      }
    }
    .superiors {
      .user {
        .el-avatar {
          background-color: #d9e3f8;
          span {
            color: #3d71d2;
            font-size: 12px;
          }
        }
      }
    }
    .binding-relationship {
      position: relative;
      width: 200px;
      height: 3px;
      background-color: #e9e9e9;
      .relationship-status {
        position: absolute;
        display: inline-flex;
        left: 50%;
        top: -10px;
        transform: translateX(-50%);
        flex-direction: column;
        align-items: center;
        gap: 4px;
        background-color: #fff;
      }
    }
  }
}
</style>
