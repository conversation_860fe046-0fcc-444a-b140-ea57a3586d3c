<template>
  <MainCard
    v-loading="isLoading"
    :cardType="2"
  >
    <div
      v-if="!isLoading"
      class="app-box"
    >
      <el-form
        ref="formValidate"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 成长值设置 start -->
          <div
            class="base-card"
            id="setting0"
          >
            <div class="title-info">
              <div>
                <div class="title">成长值设置</div>
                <div class="des">
                  会员成长值针对会员体系成长设置，对自购商品、会员充值、推广金额成长规则的设置
                </div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">自购商品</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>每消费</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[0].configValue
                            .number
                        "
                      />
                      <span>元，获得</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[0].configValue.ratio
                        "
                      />
                      <span>成长值</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">会员充值</span></div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>每充值</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[1].configValue
                            .number
                        "
                      />
                      <span>元，获得</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[1].configValue.ratio
                        "
                      />
                      <span>成长值</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">推广金额</span></div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>推广下线每消费</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[2].configValue
                            .number
                        "
                      />
                      <span>元，获得</span>
                      <el-input
                        v-model="
                          formValidate.experienceConfigList[2].configValue.ratio
                        "
                      />
                      <span>成长值</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 成长值设置 end -->
          <!-- 提现设置 start -->
          <div
            class="base-card"
            id="setting1"
          >
            <div class="title-info">
              <div>
                <div class="title">提现设置</div>
                <div class="des">
                  只有佣金提成&奖励金可以提现，会员充值不支持线上提现
                </div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">提现金额限制</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>每笔提现金额范围</span>
                      <el-input
                        v-model="
                          formValidate.withdrawConfigList[0].configValue
                            .min_amount
                        "
                      >
                        <template slot="append">元</template>
                      </el-input>
                      <span>-</span>
                      <el-input
                        v-model="
                          formValidate.withdrawConfigList[0].configValue
                            .max_amount
                        "
                      >
                        <template slot="append">元</template>
                      </el-input>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">提现等级限制</span></div>
                  <div class="right">
                    <el-checkbox-group
                      v-model="
                        formValidate.withdrawConfigList[1].configValue.levels
                      "
                    >
                      <el-checkbox label="SVIP会员">SVIP会员</el-checkbox>
                      <el-checkbox label="VIP会员">VIP会员</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">提现手续费</span></div>
                  <div class="right">
                    <div class="inline-flex">
                      <span>设置手续费</span>
                      <el-input
                        v-model="
                          formValidate.withdrawConfigList[2].configValue
                            .fee_rate
                        "
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </div>
                    <div class="des">
                      向分销人员收取提现手续费，提现时自动从提现金额中扣除，填0表示不收取
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">每月提现次数</span></div>
                  <div class="right">
                    <el-radio-group
                      v-model="
                        formValidate.withdrawConfigList[3].configValue
                          .is_limited
                      "
                    >
                      <el-radio :label="false"> 不限次数 </el-radio>
                      <el-radio :label="true">
                        <span class="inline-flex">
                          <span>限制次数</span><span>，最多可提现次数</span
                          ><el-input
                            v-model="
                              formValidate.withdrawConfigList[3].configValue
                                .max_times
                            "
                          >
                            <template slot="append">次</template>
                          </el-input>
                        </span>
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 提现设置 end -->
          <!-- 佣金返现设置 start -->
          <div
            class="base-card"
            id="setting2"
          >
            <div class="title-info">
              <div>
                <div class="title">佣金返现设置</div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">SVIP自购返现</span></div>
                  <div class="right">
                    <el-radio-group
                      v-model="
                        formValidate.commissionConfigList[0].configValue.enabled
                      "
                    >
                      <el-radio :label="true"> 有返现 </el-radio>
                      <el-radio :label="false"> 无返现 </el-radio>
                    </el-radio-group>
                    <div class="des">
                      <div>开启后，SVIP会员自己购买将获得佣金返现</div>
                      <div>
                        关闭后，所有SVIP自己购买，自己将不会获得佣金返现，但订单金额会计入销售金额里
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">佣金返现规则</span></div>
                  <div class="right">
                    <div class="inline-flex">
                      <span>普通会员购买，返现</span>
                      <el-input
                        v-model="
                          formValidate.commissionConfigList[1].configValue[1]
                            .purchase
                        "
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"></div>
                  <div class="right">
                    <div class="inline-flex">
                      <span>VIP会员购买，返现</span>
                      <el-input
                        v-model="
                          formValidate.commissionConfigList[1].configValue[2]
                            .purchase
                        "
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"></div>
                  <div class="right">
                    <div class="inline-flex">
                      <span>SVIP会员自购，返现</span>
                      <el-input
                        v-model="
                          formValidate.commissionConfigList[1].configValue[3]
                            .purchase
                        "
                      >
                        <template slot="append">%</template>
                      </el-input>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 佣金返现设置 end -->
          <!-- 奖励金设置 start -->
          <div
            class="base-card"
            id="setting3"
          >
            <div class="title-info">
              <div>
                <div class="title">奖励金设置</div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">升级奖励金</span></div>
                  <div class="right">
                    <div class="flex-colomn">
                      <el-checkbox
                        v-model="
                          formValidate.bonusConfigList[0].configValue.enabled
                        "
                      >
                        <div class="inline-flex">
                          <span>推广新用户首单购买，给予vip等级的首单奖励</span
                          ><el-input
                            v-model="
                              formValidate.bonusConfigList[0].configValue.ratio
                            "
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </div>
                      </el-checkbox>
                      <div
                        style="margin-top: -10px"
                        class="des"
                      >
                        关闭后，所有SVIP自己购买，自己将不会获得佣金返现，但订单金额会计入销售金额里
                      </div>
                      <el-checkbox
                        v-model="
                          formValidate.bonusConfigList[1].configValue.enabled
                        "
                      >
                        <div class="inline-flex">
                          <span>推广普通会员升级为vip后，一次奖励</span
                          ><el-input
                            v-model="
                              formValidate.bonusConfigList[1].configValue.amount
                            "
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </div>
                      </el-checkbox>
                      <el-checkbox
                        v-model="
                          formValidate.bonusConfigList[2].configValue.enabled
                        "
                      >
                        <div class="inline-flex">
                          <span>推广vip会员升级为Svip后，一次奖励</span
                          ><el-input
                            v-model="
                              formValidate.bonusConfigList[2].configValue.amount
                            "
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </div>
                      </el-checkbox>
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left"><span class="name">销售榜单奖励</span></div>
                  <div class="right">
                    <div class="flex-colomn">
                      <div>
                        <span>榜单排名展示数量：</span>
                        <el-select
                          clearable
                          v-model="
                            formValidate.bonusConfigList[3].configValue
                              .display_count
                          "
                          placeholder="请选择"
                        >
                          <el-option value="TOP10">TOP10</el-option>
                          <el-option value="TOP20">TOP20</el-option>
                        </el-select>
                      </div>
                      <div>
                        <el-tabs tab-position="left">
                          <el-tab-pane
                            v-for="item in menuItems"
                            :key="item.key"
                          >
                            <div slot="label">
                              <div class="menu-item">
                                <span>{{ item.name }}</span>
                              </div>
                            </div>
                            <div class="meun-content">
                              <div class="inline-flex">
                                <span>{{ item.name }}入榜门槛值</span>
                                <el-input
                                  v-model="
                                    formValidate.bonusConfigList[item.key]
                                      .configValue.threshold
                                  "
                                >
                                  <template slot="append">元</template>
                                </el-input>
                              </div>
                              <div
                                style="margin-left: 20px"
                                class="inline-flex"
                              >
                                <span>奖励前</span>
                                <el-input
                                  v-model.number="
                                    formValidate.bonusConfigList[item.key]
                                      .configValue.reward_count
                                  "
                                >
                                  <template slot="append">名</template>
                                </el-input>
                              </div>
                              <div class="des">
                                只有达到最低销量的人员才能进入排行榜统计中，才能获得奖励金
                              </div>

                              <el-table
                                class="table-list"
                                :data="
                                  formValidate.bonusConfigList[item.key]
                                    .configValue.awards || []
                                "
                                ref="table"
                                :header-cell-style="{
                                  background: '#f5f5f5',
                                  color: '#444',
                                  lineHeight: '1'
                                }"
                                style="width: 100%"
                                size="mini"
                                :highlight-current-row="true"
                              >
                                <el-table-column
                                  prop="rank"
                                  label="名次"
                                />
                                <el-table-column
                                  prop="amount"
                                  label="奖励金（元）"
                                >
                                  <template slot-scope="scope">
                                    <el-input
                                      style="width: 60%"
                                      v-model="scope.row.amount"
                                      size="mini"
                                    />
                                  </template>
                                </el-table-column>
                              </el-table>
                            </div>
                          </el-tab-pane>
                        </el-tabs>
                      </div>
                      <div>
                        <el-checkbox
                          v-model="
                            formValidate.bonusConfigList[7].configValue.weekly
                          "
                        >
                          <span
                            >榜单奖励自动发放，
                            （不勾选的话，需要去销售报表→销售榜单里面手动发放）</span
                          >
                        </el-checkbox>
                      </div>
                    </div>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 佣金返现设置 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button
          class="submission"
          @click="getMemberSetting"
          >重置</el-button
        >
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  memberSettingListApi,
  saveMemberSettingForm
} from '@/api/parameterSetting'
import { Debounce } from '@/utils/validate'
import { mapGetters } from 'vuex'
export default {
  data() {
    return {
      formValidate: {
        experienceConfigList: [{}, {}, {}],
        withdrawConfigList: [{}, {}, {}, {}],
        commissionConfigList: [{}, {}],
        bonusConfigList: [{}, {}, {}, {}, {}, {}, {}, {}]
      },
      menuItems: [
        { key: 4, name: '周排行榜' },
        { key: 5, name: '月排行榜' },
        { key: 6, name: '季排行榜' }
      ],
      ruleValidate: {
        name: [{ required: true, message: '请输入标签名称', trigger: 'blur' }]
      },
      isLoading: true
    }
  },
  computed: {
    ...mapGetters(['tabsItemType']),
    curScrollId() {
      return 'setting' + this.tabsItemType
    }
  },
  mounted() {
    this._init()
  },
  watch: {
    curScrollId: {
      handler(val) {
        this.scrollToSection(val)
      }
    },
    // 分别监听每个排行榜的奖励人数变化
    'formValidate.bonusConfigList.4.configValue.reward_count': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== '' && !isNaN(newVal)) {
          this.updateAwardsData(4)
        }
      }
    },
    'formValidate.bonusConfigList.5.configValue.reward_count': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== '' && !isNaN(newVal)) {
          this.updateAwardsData(5)
        }
      }
    },
    'formValidate.bonusConfigList.6.configValue.reward_count': {
      handler(newVal, oldVal) {
        if (newVal !== oldVal && newVal !== '' && !isNaN(newVal)) {
          this.updateAwardsData(6)
        }
      }
    }
  },
  methods: {
    async _init() {
      await this.getMemberSetting()
      this.scrollToSection(this.curScrollId)
    },
    // 详情
    async getMemberSetting() {
      await memberSettingListApi()
        .then((res) => {
          this.isLoading = false
          const data = res
          for (const key in data) {
            data[key].forEach((element) => {
              element.configValue = JSON.parse(element.configValue)
            })
          }
          this.formValidate = data
          // 初始化表格数据
          this.$nextTick(() => {
            this.menuItems.forEach((item) => {
              this.updateAwardsData(item.key)
            })
          })
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.isLoading = true
      const data = JSON.parse(JSON.stringify(this.formValidate))
      for (const key in data) {
        data[key].forEach((element) => {
          element.configValue = JSON.stringify(element.configValue)
        })
      }
      saveMemberSettingForm(data)
        .then(async (res) => {
          this.$message.success('保存成功')
          this.isLoading = false
        })
        .catch((res) => {
          this.isLoading = false
        })
    }),
    // 滚动到指定section
    scrollToSection(sectionId, offset = 16) {
      this.$nextTick(() => {
        const targetElement = document.getElementById(sectionId)
        if (targetElement) {
          const elementPosition = targetElement.offsetTop
          const offsetPosition = elementPosition - offset
          const scrollArea = document.querySelector('.scroll')
          scrollArea.scrollTo({
            top: offsetPosition,
            behavior: 'smooth'
          })
        }
      })
    },
    // 更新奖励数据
    updateAwardsData(itemKey) {
      if (
        !this.formValidate.bonusConfigList[itemKey] ||
        !this.formValidate.bonusConfigList[itemKey].configValue
      ) {
        return
      }

      const rewardCount =
        parseInt(
          this.formValidate.bonusConfigList[itemKey].configValue.reward_count
        ) || 0
      const currentAwards =
        this.formValidate.bonusConfigList[itemKey].configValue.awards || []

      // 如果reward_count为0或空，不清空现有数据，直接返回
      if (rewardCount === 0) {
        return
      }

      // 如果数量没有变化，不需要更新
      if (currentAwards.length === rewardCount) {
        return
      }

      const newAwards = []
      for (let i = 0; i < rewardCount; i++) {
        newAwards.push({
          rank: `${i + 1}`,
          // 更精确地保留原有数据
          amount:
            currentAwards[i] &&
            typeof currentAwards[i] === 'object' &&
            currentAwards[i].hasOwnProperty('amount')
              ? currentAwards[i].amount
              : ''
        })
      }

      this.$set(
        this.formValidate.bonusConfigList[itemKey].configValue,
        'awards',
        newAwards
      )
    }
  }
}
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }

  /* .form-info {
    padding-left: 80px;
  } */

  .title-info {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    display: flex;
    .des {
      font-weight: normal;
      font-size: 12px;
      margin-top: 8px;
      color: #888888;
    }
    &::before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      margin-right: 8px;
      transform: translateY(1px);
    }
  }
  .form-item-box {
    display: flex;
    .left {
      flex: 0 0 120px;
      .name {
        position: relative;
        &::before {
          content: '';
          position: absolute;
          display: inline-block;
          width: 36px;
          height: 5px;
          border-radius: 2.5px;
          background-color: #dd412f;
          opacity: 0.31;
          bottom: 0;
          left: 0;
        }
      }
    }
    .right {
      flex: 1;
      ::v-deep .el-tabs__item {
        padding: 0;
      }
      ::v-deep .el-tabs__content {
        min-height: 100%;
        /* overflow-y: auto; */
        padding-right: 20px;
      }
      .menu-item {
        display: flex;
        padding-right: 30px;
      }
      .meun-content {
        padding-left: 20px;
        min-height: 100%;
        /* overflow-y: auto; */
        .des {
          color: #3f5ad2;
        }
        .table-list {
          border: 1px solid #dfe6ec;
        }
      }
      .des {
        font-size: 12px;
        color: #888888;
      }
    }
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    white-space: nowrap;
  }
  .flex-colomn {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: 16px;
  }
  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
