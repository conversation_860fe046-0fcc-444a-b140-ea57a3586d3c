<template>
  <MainCard :cardType="3">
    <div class="app-box">
      <div class="base-card basic-info">
        <div class="user-info">
          <el-avatar class="el-avatar" :size="84" :src="userInfo.avatar">
            <svg-icon class="user_avator" icon-class="user_avator" />
          </el-avatar>
          <div class="user-name">
            <span>{{ userInfo.nickname || '微信用户' }}</span
            ><span
              ><memberLevelIcon
                :level="userInfo.level"
            /></span>
          </div>
          <div class="user-phone">
            <span class="info-value">{{ userInfo.phone }}</span>
          </div>
        </div>
        <div class="info-details">
          <div class="title">账号余额(元)</div>
          <div class="balance-box">
            <div class="balance-value">{{ balanceInfo.balance }}</div>
            <div class="balance-des">
              充值金余额：{{ Number(balanceInfo.rechargeAmount) || "0.00" }}，
              返现&奖励金(可提现)：{{
                Number(balanceInfo.refundAmount) || "0.00"
              }}
            </div>
          </div>
          <div class="summary-box">
            <el-row>
              <el-col
                :span="4"
                v-for="(item, index) in summaryData"
                :key="index"
              >
                <div class="summary-item">
                  <div class="summary-title">{{ item.title }}</div>
                  <div class="summary-value">
                    {{ Number(item.value) || "0.00" }}
                  </div>
                </div>
              </el-col>
            </el-row>
          </div>
        </div>
      </div>
      <div class="base-card">
        <BalanceDetailsCom :isBalanceDetails="true" :id="this.$route.params.id" />
      </div>
    </div>
  </MainCard>
</template>

<script>
import {
  memberBalanceInfoApi,
  memberBalanceDetailApi,
} from "@/api/memberBalance";
import BalanceDetailsCom from "../balanceDetailsCom/index.vue";

export default {
  components: { BalanceDetailsCom },
  data() {
    return {
      balanceInfo: {},
      time: [],
      summaryData: [
        { title: "累计充值金", value: "0.00", key: "totalRechargeAmount" },
        { title: "累计佣金返现", value: "0.00", key: "totalRefundAmount" },
        { title: "累计奖励金", value: "0.00", key: "totalActualAmount" },
        { title: "累计余额消费", value: "0.00", key: "totalConsumeAmount" },
        { title: "累计提现", value: "0.00", key: "totalWithdrawalAmount" },
      ],
      tableFrom: {
        page: 1,
        limit: 20,
        billType: null,
        startTime: null,
        endTime: null,
        uid: null,
      },
      tableData: {
        data: [],
        total: 0,
      },
      fullscreenLoading: false,
    };
  },
  computed: {
    userInfo() {
      return this.$route.params;
    },
  },
  mounted() {
    if (this.$route.params.id) {
      this.getBalanceInfo();
    }
  },
  methods: {
    // 详情
    getBalanceInfo() {
      memberBalanceInfoApi(this.$route.params.id)
        .then((res) => {
          this.summaryData.forEach((item) => {
            item.value = Number(res[item.key]) || "0.00";
          });
          this.balanceInfo = res;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 16px;
    .search-form {
      background-color: #f7f7f7;
      padding: 20px;
      margin-bottom: 16px;
    }

    .user-info {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 14px;
      padding: 20px 60px;
      border-right: 1px solid #e8e8e8;
      .el-avatar {
        background-color: #d8d8d8;
        display: flex;
        align-items: center;
        justify-content: center;
        .user_avator {
          font-size: 50px;
          color: white;
        }
      }
      .user-name {
        font-size: 18px;
        font-weight: 700;
        display: flex;
        align-items: center;
        gap: 8px;
      }
      .user-phone {
        font-size: 14px;
      }
    }
    .info-details {
      flex: 1;
      padding: 20px 30px;
      line-height: 1;
      .title {
        margin-bottom: 10px;
      }
      .balance-box {
        .balance-value {
          font-size: 24px;
          font-weight: bold;
          color: #e74c3c;
          margin-bottom: 12px;
        }
        .balance-des {
          position: relative;
          color: #e74c3c;
          background-color: #ffe8e7;
          display: inline-block;
          padding: 7px 9px;
          font-size: 14px;
          margin-bottom: 16px;
          border-radius: 3px;
          z-index: 1;
          &::before {
            content: "";
            position: absolute;
            width: 0;
            height: 0;
            top: -10px;
            left: 13px;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            border-bottom: 20px solid #ffe8e7;
            transform: rotate(-25deg);
            z-index: -1;
          }
        }
      }
      .summary-box {
        .summary-item {
          .summary-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 12px;
          }
          .summary-value {
            font-size: 20px;
          }
        }
      }
    }
    .info-title {
      font-size: 14px;
      font-weight: 700;
      margin: 0 0 20px 0;
      &::before {
        content: "";
        display: inline-block;
        width: 5px;
        height: 14px;
        background-color: rgb(3, 158, 3);
        vertical-align: -2px;
        margin-right: 8px;
      }
    }
  }
  .basic-info {
    display: flex;
    padding: 0;
  }
}
</style>
