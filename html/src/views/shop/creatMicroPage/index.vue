<template>
  <div
    v-loading="fullscreenLoading"
    class="app-box"
  >
    <div class="left">
      <div class="page-area">
        <div class="page-content">
          <div class="header-img">
            <img
              :src="headerImg"
              alt=""
            />

            <div class="header-title">{{ formValidate.pageName }}</div>
          </div>
          <div
            v-show="formValidate.contentType === 1"
            class="img-content"
          >
            <img
              v-for="item in formValidate.imageUrls"
              :src="item"
              :key="item"
            />
          </div>
          <div
            v-show="formValidate.contentType === 2"
            class="rich-text-content"
          >
            <div v-html="formValidate.content"></div>
            <!-- <div v-show="!formValidate.content">这里是一个富文本</div> -->
          </div>

          <!-- <div class="rich-text-content">
            {{ formValidate.content }}
          </div> -->
        </div>
      </div>
      <div class="menu">
        <div
          :class="`item ${curActive === item.id ? 'active' : ''}`"
          v-for="item in menuList"
          :key="item.id"
          @click="scrollToSection(item.id, item.top, item.height)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <div class="right">
      <div class="page-setting">
        <div class="content">
          <div class="title">页面设置</div>
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="auto"
            @submit.native.prevent
          >
            <el-form-item
              label="页面名称："
              prop="pageName"
            >
              <el-input
                v-model="formValidate.pageName"
                maxlength="249"
              />
            </el-form-item>
            <el-form-item
              label="页面内容："
              prop="contentType"
            >
              <el-radio-group
                class="radio-group"
                v-model="formValidate.contentType"
              >
                <el-radio-button :label="1">图片模式</el-radio-button>
                <el-radio-button :label="2">富文本模式</el-radio-button>
              </el-radio-group>
              <el-form-item
                v-show="formValidate.contentType === 1"
                prop="imageUrls"
              >
                <div class="detail-description">
                  <div>上传图片说明：</div>
                  <div>1、支持上传长图，自动切割成多张小图并生成HTML；</div>
                  <div>2、建议图片宽度 750 像素，鼠标拖拽调整顺序。</div>
                </div>
                <div class="acea-row">
                  <div
                    v-for="(item, index) in formValidate.imageUrls"
                    :key="index"
                    class="pictrue"
                    draggable="true"
                    @dragstart="handleDragStart($event, item)"
                    @dragover.prevent="handleDragOver($event, item)"
                    @dragenter="handleDragEnter($event, item)"
                    @dragend="handleDragEnd($event, item, 'imageUrls')"
                  >
                    <MyImage
                      :imagePath="handlerImgUrl(item)"
                      :previewPath="item"
                      :size="60"
                    />
                    <i
                      class="el-icon-error btndel"
                      @click="handleRemove(index)"
                    />
                  </div>
                  <div
                    v-if="formValidate.imageUrls.length < 10"
                    class="upLoadPicBox"
                    @click="modalPicTap('2')"
                  >
                    <div class="upLoad">
                      <i
                        style="font-size: 16px"
                        class="el-icon-plus cameraIconfont"
                      />
                    </div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                v-show="formValidate.contentType === 2"
                prop="content"
              >
                <MyEditer :htmlContent.sync="formValidate.content" />
              </el-form-item>
            </el-form-item>
          </el-form>
        </div>

        <div class="footer-btn">
          <el-button
            type="primary"
            class="submission"
            @click="handleSubmit('formValidate')"
            >保存</el-button
          >
          <el-button
            class="submission"
            @click="$router.go(-1)"
            >取消</el-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  microPageSaveApi,
  microPageInfoApi,
  microPageUpdateApi
} from '@/api/microPage'
import MyEditer from '@/components/myEditer/index'
import { Debounce } from '@/utils/validate'
export default {
  components: { MyEditer },
  data() {
    return {
      curActive: 'menuItem1',
      boxSelectionHeight: 0,
      boxSelectionTop: 0,
      homePageImg: require('@/assets/imgs/home_page.jpg'),
      headerImg: require('@/assets/imgs/header_img.png'),
      fullscreenLoading: false,
      menuList: [
        {
          title: '页面设置',
          id: 'menuItem1'
        }
      ],
      formValidate: {
        pageName: null,
        contentType: 1,
        content: null,
        imageUrls: []
      },
      ruleValidate: {
        pageName: [{ required: true, message: '请输入', trigger: 'blur' }],
        contentType: [{ required: true, message: '请选择', trigger: 'change' }]
      }
    }
  },
  mounted() {
    if (this.$route.params.id) {
      this.getInfo()
    }
    this.scrollToSection(
      this.curActive,
      this.menuList[0].top,
      this.menuList[0].height
    )
  },
  methods: {
    scrollToSection(sectionId, top, height) {
      this.curActive = sectionId
      this.boxSelectionTop = top
      this.boxSelectionHeight = height
      this.$nextTick(() => {
        const targetElement = document.getElementById(sectionId)
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center' // 或 'center', 'end'
          })
        }
      })
    },
    // 详情
    getInfo() {
      this.fullscreenLoading = true
      microPageInfoApi(this.$route.params.id)
        .then(async (res) => {
          let info = res

          this.formValidate = {
            pageName: info.pageName,
            contentType: info.contentType,
            content: info.content
              ? (() => {
                  const match = info.content.match(
                    /<body[^>]*>([\s\S]*)<\/body>/
                  )
                  return match && match[1] ? match[1].trim() : ''
                })()
              : '',
            imageUrls: info.imageUrls ? JSON.parse(info.imageUrls) : []
          }
          this.fullscreenLoading = false
        })
        .catch((res) => {
          this.fullscreenLoading = false
          this.$message.error(res.message)
        })
    },
    handleRemove(index) {
      this.formValidate.imageUrls.splice(index, 1)
    },
    // 移动
    handleDragStart(e, item) {
      this.dragging = item
    },
    handleDragEnd(e, item, type) {
      this.dragging = null
    },
    handleDragOver(e) {},
    handleDragEnter(e, item) {
      e.dataTransfer.effectAllowed = 'move'
      if (item === this.dragging) {
        return
      }
      const newItems = [...this.formValidate.imageUrls]
      const src = newItems.indexOf(this.dragging)
      const dst = newItems.indexOf(item)
      newItems.splice(dst, 0, ...newItems.splice(src, 1))
      this.formValidate.imageUrls = newItems
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          const content = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
<meta charset="utf-8">
<title>${this.formValidate.pageName}</title>
<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0" name="viewport">     
<meta content="yes" name="apple-mobile-web-app-capable">
<meta content="black" name="apple-mobile-web-app-status-bar-style">
<meta name="wap-font-scale" content="no">

<style>
*{margin:0;padding:0; -webkit-box-sizing: border-box; box-sizing:border-box; outline:0;}
html{-webkit-text-size-adjust: 100%;-webkit-user-select: none;-webkit-touch-callout: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);-webkit-text-size-adjust:none; overflow-x:hidden}
body {-webkit-text-size-adjust:none;letter-spacing: -0.01em;font-size:16px; padding: 16px; line-height: 1.6;}
a,a:visited {text-decoration: none; color:#333}
a,button,input{-webkit-tap-highlight-color:rgba(255,0,0,0);}
ul,ol,li,dl,dt,dd {list-style-type:none; list-style:none}
img { border: 0; -ms-interpolation-mode: bicubic; vertical-align: middle; font-size:0}
p { padding-top: 0.6rem;}
</style>
</head>

<body>
 ${this.formValidate.contentType === 2 ? this.formValidate.content : null}
</body>
</html>`
          this.$route.params.id
            ? microPageUpdateApi({
                ...this.formValidate,
                imageUrls:
                  this.formValidate.contentType === 1
                    ? JSON.stringify(this.formValidate.imageUrls)
                    : null,

                // content:
                //   this.formValidate.contentType === 2
                //     ? this.formValidate.content
                //     : null,
                content,
                id: this.$route.params.id
              }).then(async (res) => {
                this.$message.success('更新成功')
                this.$router.push({ name: 'microPage' })
              })
            : microPageSaveApi({
                ...this.formValidate,
                imageUrls:
                  this.formValidate.contentType === 1
                    ? JSON.stringify(this.formValidate.imageUrls)
                    : null,
                // content:
                //   this.formValidate.contentType === 2
                //     ? this.formValidate.content
                //     : null
                content
              })
                .then(async (res) => {
                  this.$message.success('保存成功')
                  this.$router.push({ name: 'microPage' })
                })
                .catch((res) => {})
        } else {
          this.$message.warning('请填写完整信息！')
        }
      })
    }),
    // 点击商品图
    modalPicTap(tit) {
      const _this = this
      if (_this.isDisabled) return
      this.$modalUpload(
        function (img) {
          if (tit === '2') {
            img.map((item) => {
              _this.formValidate.imageUrls.push(item.attDir)
            })
          }
        },
        tit,
        'content'
      )
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.app-box {
  display: flex;
  height: 100%;
  > div {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
  }
  .left {
    position: relative;
    .page-area {
      overflow-y: auto;
      padding-top: 20px;
      height: 100%;
      .page-content {
        position: relative;
        width: 378px;
        margin: auto;
        background-color: #fff;
        min-height: calc(100% - 20px);
        .header-img {
          .header-title {
            position: absolute;
            top: 37px;
            left: 33px;
            font-size: 15px;
            font-weight: 700;
            width: 60%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
        }
        .img-content {
          min-height: 200px;
          border: 1px dashed #5d8ae6;

          img {
            width: 100%;
            display: block;
          }
        }
        .rich-text-content {
          min-height: 200px;
          border: 1px dashed #5d8ae6;
          padding: 0 20px;
          width: 100%;
          overflow-x: auto;

          /* 重置预览富文本样式 */
          ::v-deep ol,
          ::v-deep ul {
            margin: 0;
            padding: 0 0 0 20px; /* 默认的缩进 */
            list-style-position: outside; /* 默认的列表符号位置 */
          }
          /* 使用深度选择器重置标题标签的样式 */
          ::v-deep h1,
          ::v-deep h2,
          ::v-deep h3,
          ::v-deep h4,
          ::v-deep h5,
          ::v-deep h6 {
            margin: 0.67em 0 !important; /* 默认的 h1 上下边距 */
            font-size: 2em; /* 默认的 h1 字体大小 */
            font-weight: bold; /* 默认的加粗 */
          }
          ::v-deep h2 {
            font-size: 1.5em; /* 默认的 h2 字体大小 */
          }

          ::v-deep h3 {
            font-size: 1.17em; /* 默认的 h3 字体大小 */
          }
          ::v-deep li {
            list-style-type: disc; /* 默认无序列表样式 */
            margin-left: 1em;
          }
          ::v-deep P {
            display: block;
            margin-top: 1em;
            margin-bottom: 1em;
            margin-left: 0;
            margin-right: 0;
            padding-top: 0;
            padding-right: 0;
            padding-bottom: 0;
            padding-left: 0;
          }
          /* 如果是有序列表 */
          ::v-deep ol li {
            list-style-type: decimal; /* 默认有序列表样式 */
          }
        }
        .box-selection {
          position: absolute;
          width: 100%;
          left: 0;
          border: 2px dashed #5d8ae6;
        }
      }
    }
    .menu {
      position: absolute;
      width: 96px;
      top: 20px;
      right: 30px;
      text-align: center;
      .item {
        background-color: #fff;
        padding: 10px 0;
        margin-bottom: 10px;
        box-shadow: 0px 1px 5px 0px rgba(180, 180, 180, 0.3);
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
          color: #fff;
        }
        &.active {
          background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
          color: #fff;
        }
      }
    }
  }
  .right {
    background-color: #fff;
    padding: 20px;
    height: 100%;
    .page-setting {
      height: 100%;
      display: flex;
      flex-direction: column;
      gap: 20px;
      .content {
        flex: 1;
        overflow-y: auto;
        .title {
          margin-bottom: 18px;
          font-size: 18px;
          font-weight: 700;
        }
        .radio-group {
          margin-bottom: 20px;
        }
        .detail-description {
          color: #777777;
          margin-bottom: 20px;
          line-height: 2;
        }
        .acea-row {
          margin-bottom: 20px;
        }
        .pictrue {
          width: 60px;
          height: 60px;
          border: 1px dotted rgba(0, 0, 0, 0.1);
          margin-right: 10px;
          position: relative;
          cursor: pointer;
        }
        .btndel {
          position: absolute;
          z-index: 1;
          width: 20px !important;
          height: 20px !important;
          left: 46px;
          top: -4px;
        }
      }
      .footer-btn {
        text-align: right;
      }
    }
  }
}
</style>
<style>
.mce-container,
.mce-container *,
.mce-widget,
.mce-widget *,
.mce-reset,
.mce-reset {
  white-space: normal !important;
}
.mce-panel {
  box-sizing: border-box !important;
}
</style>
