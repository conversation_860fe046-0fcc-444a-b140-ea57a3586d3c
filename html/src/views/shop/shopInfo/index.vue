<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="180px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="base-card">
            <div class="info-title">基本信息</div>
            <div class="form-info">
              <el-form-item
                style="width: 50%"
                label="店铺名称："
                prop="storeName"
              >
                <el-input
                  v-model="formValidate.storeName"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item
                style="width: 50%"
                prop="grade"
                label="店铺理念："
              >
                <el-input
                  v-model="formValidate.storeCategory"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item label="小程序顶部logo图标：">
                <div class="upLoadPicBox">
                  <div
                    @click="modalPicTap('1')"
                    v-if="formValidate.miniAppLogo"
                    class="pictrue pictrue1"
                  >
                    <img
                      :src="handlerImgUrl(formValidate.miniAppLogo)"
                      alt=""
                    />
                  </div>
                  <div
                    @click="modalPicTap('1')"
                    v-else
                    class="upLoad"
                  >
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  固定尺寸：224x60像素，格式限定png、svg
                </div>
              </el-form-item>
              <el-form-item label="小程序登录logo：">
                <div class="upLoadPicBox">
                  <div
                    @click="modalPicTap('2')"
                    v-if="formValidate.miniAppLoginLogo"
                    class="pictrue"
                  >
                    <img
                      :src="handlerImgUrl(formValidate.miniAppLoginLogo)"
                      alt=""
                    />
                  </div>
                  <div
                    @click="modalPicTap('2')"
                    v-else
                    class="upLoad"
                  >
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  建议尺寸：144x144 像素，格式支持png、svg
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button
          class="submission"
          @click="getInfo"
          >取消</el-button
        >
      </div>
    </template>
  </MainCard>
</template>

<script>
import { storeInfoApi, storeInfoUpdateApi } from '@/api/shopInfo'
import { Debounce } from '@/utils/validate'

const defaultObj = {
  miniAppLoginLogo: null,
  miniAppLogo: null,
  storeCategory: null,
  storeName: null
}
export default {
  name: 'ProductProductAdd',
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        storeName: [
          { required: true, message: '请输入店铺名称', trigger: 'blur' }
        ]
      },
      fullscreenLoading: false
    }
  },

  mounted() {
    this.getInfo()
  },
  methods: {
    // 详情
    getInfo() {
      this.fullscreenLoading = true
      storeInfoApi()
        .then(async (res) => {
          this.formValidate = {
            ...res
          }
          this.fullscreenLoading = false
        })
        .catch((res) => {
          this.fullscreenLoading = false
          this.$message.error(res.message)
        })
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },
    modalPicTap(type) {
      const _this = this
      this.$modalUpload(
        function (img) {
          if (type === '1') {
            _this.formValidate.miniAppLogo = img[0].attDir
          }
          if (type === '2') {
            _this.formValidate.miniAppLoginLogo = img[0].attDir
          }
        },
        2,
        'content'
      )
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true
          storeInfoUpdateApi({
            ...this.formValidate
          })
            .then(async (res) => {
              this.$message.success('保存成功')
              this.fullscreenLoading = false
            })
            .catch((res) => {
              this.fullscreenLoading = false
            })
        } else {
          this.$message.warning('请填写完整信息！')
        }
      })
    })
  }
}
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
    line-height: 1;
    margin-top: 16px;
  }

  .form-info {
    padding-left: 80px;
    .upLoadPicBox {
      display: inline-flex;
      .pictrue1 {
        width: 224px !important;
        height: 60px !important;
      }
    }
  }

  .form-item-box {
    display: flex;
    .left {
      flex: 0 0 120px;
    }
    .right {
      flex: 1;
    }
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
