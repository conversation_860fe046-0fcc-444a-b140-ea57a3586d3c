<template>
  <div class="home-page-mgt">
    <div class="left">
      <div class="page-area">
        <div class="page-content">
          <img
            :src="homePageImg"
            alt=""
          />
          <div
            :id="curActive"
            :style="`height: ${boxSelectionHeight}px; top: ${boxSelectionTop}px`"
            class="box-selection"
          ></div>
        </div>
      </div>
      <div class="menu">
        <div
          :class="`item ${curActive === item.id ? 'active' : ''}`"
          v-for="item in menuList"
          :key="item.id"
          @click="scrollToSection(item.id, item.top, item.height)"
        >
          {{ item.title }}
        </div>
      </div>
    </div>
    <div class="right">
      <BannerConfig v-if="curActive === 'menuItem1'" />
      <PopularBestseller v-if="curActive === 'menuItem2'" />
      <HotSellingRecommendation v-if="curActive === 'menuItem3'" />
    </div>
  </div>
</template>

<script>
import BannerConfig from './BannerConfig'
import PopularBestseller from './PopularBestseller.vue'
import HotSellingRecommendation from './HotSellingRecommendation.vue'
export default {
  components: {
    BannerConfig,
    PopularBestseller,
    HotSellingRecommendation,
  },
  data() {
    return {
      curActive: 'menuItem1',
      boxSelectionHeight: 0,
      boxSelectionTop: 0,
      homePageImg: require('@/assets/imgs/home_page.jpg'),
      menuList: [
        {
          title: 'Banner轮播图',
          id: 'menuItem1',
          top: 127,
          height: 158
        },
        { title: '人气爆款', id: 'menuItem2', top: 553, height: 242 },
        { title: '热销推荐', id: 'menuItem3', top: 792, height: 312 }
      ]
    }
  },
  mounted() {
    this.scrollToSection(
      this.curActive,
      this.menuList[0].top,
      this.menuList[0].height
    )
  },
  methods: {
    scrollToSection(sectionId, top, height) {
      this.curActive = sectionId
      this.boxSelectionTop = top
      this.boxSelectionHeight = height
      this.$nextTick(() => {
        const targetElement = document.getElementById(sectionId)
        if (targetElement) {
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'center' // 或 'center', 'end'
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.home-page-mgt {
  display: flex;
  height: 100%;
  > div {
    flex: 1;
    overflow-y: auto;
  }
  .left {
    position: relative;
    .page-area {
      height: 100%;
      overflow-y: auto;
      .page-content {
        position: relative;
        width: 378px;
        margin: auto;
        margin-top: 20px;
        background-color: #fff;
        .box-selection {
          position: absolute;
          width: 100%;
          left: 0;
          border: 2px dashed #5d8ae6;
        }
      }
    }
    .menu {
      position: absolute;
      width: 96px;
      top: 20px;
      right: 30px;
      text-align: center;
      .item {
        background-color: #fff;
        padding: 10px 0;
        margin-bottom: 10px;
        box-shadow: 0px 1px 5px 0px rgba(180, 180, 180, 0.3);
        border-radius: 2px;
        cursor: pointer;
        &:hover {
          background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
          color: #fff;
        }
        &.active {
          background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
          color: #fff;
        }
      }
    }
  }
  .right {
    background-color: #fff;
    padding: 20px;
  }
}
</style>
