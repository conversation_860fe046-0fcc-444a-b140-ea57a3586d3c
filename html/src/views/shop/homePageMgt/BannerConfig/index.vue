<template>
  <div class="banner-editor">
    <div class="content">
      <div class="banner-title">Banner轮播图</div>
      <div>
        <div class="description">
          图片比例采用 2.39 : 1（建议尺寸：750像素 x 312像素）；
        </div>
        <div class="description des2">最多上传 5 张轮播图，支持调整顺序；</div>
        <el-form
          label-position="left"
          ref="formValidate"
          :model="formData"
          @submit.native.prevent
        >
          <div class="banner-list">
            <div
              v-for="(item, index) in formData.bannerList"
              :key="index"
              class="banner-item"
            >
              <div class="left">
                <div class="upLoadPic">
                  <div v-if="item.imageUrl" class="pictrue">
                    <MyImage
                      :imagePath="handlerImgUrl(item.imageUrl)"
                      :previewPath="item.imageUrl"
                      :size="76"
                    />
                    <!-- <img
                      :src="handlerImgUrl(item.imageUrl)"
                      alt=""
                    /> -->
                    <div class="replace-img" @click="modalPicTap('1', index)">
                      更换图片
                    </div>
                  </div>
                  <div @click="modalPicTap('1', index)" v-else class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
              </div>
              <div v-loading="listLoading" class="right">
                <div class="banner-actions">
                  <div class="radio-group">
                    <el-form-item>
                      <el-radio-group v-model="isSelected">
                        <el-radio :label="1">小程序内跳转</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                  <div class="opration-btn">
                    <el-button
                      type="text"
                      @click="moveUp(index)"
                      v-show="index !== 0"
                    >
                      <svg-icon
                        class="opreation-btn"
                        icon-class="move_top"
                      ></svg-icon>

                      <!-- <i
                        style="font-size: 18px"
                        class="el-icon-arrow-up"
                      ></i> -->
                    </el-button>
                    <el-button
                      type="text"
                      @click="moveDown(index)"
                      v-show="index !== formData.bannerList.length - 1"
                    >
                      <svg-icon
                        class="opreation-btn"
                        icon-class="move_down"
                      ></svg-icon>
                      <!-- <i
                        style="font-size: 18px"
                        class="el-icon-arrow-down"
                      ></i> -->
                    </el-button>
                    <el-button type="text" @click="removeBanner(index)">
                      <!-- <i
                        style="font-size: 18px"
                        class="el-icon-delete"
                      ></i> -->
                      <svg-icon
                        class="opreation-btn"
                        icon-class="delete"
                      ></svg-icon>
                    </el-button>
                  </div>
                </div>
                <div class="link-input">
                  <div class="text"><span>*</span> 链接</div>
                  <el-form-item
                    :prop="`bannerList[${index}].jumpUrl`"
                    :rules="[
                      {
                        required: true,
                        message: '请选择跳转链接',
                        trigger: 'change',
                        validator: (rule, value, callback) => {
                          if (!value || value === '') {
                            callback(new Error('请选择跳转链接'));
                          } else {
                            callback();
                          }
                        },
                      },
                    ]"
                  >
                    <!-- 已选择的链接显示 -->
                    <div v-if="item.selectedLink" class="selected-link">
                      <div class="selected-info">
                        <span class="link-type">{{
                          getLinkTypeText(item.selectedLink.type)
                        }}</span>
                        <span class="link-name">{{
                          item.selectedLink.name
                        }}</span>
                      </div>
                      <el-button
                        type="text"
                        size="small"
                        @click="clearSelection(index)"
                        class="clear-btn"
                      >
                        <i class="el-icon-close"></i>
                      </el-button>
                    </div>
                    <!-- 配置跳转链接按钮 -->
                    <el-dropdown
                      v-else
                      placement="bottom"
                      @command="handleDropdownCommand($event, index)"
                    >
                      <el-button size="mini" type="primary">
                        配置跳转链接<i
                          class="el-icon-arrow-down el-icon--right"
                        ></i>
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item command="product"
                          ><div class="dropdown-item">
                            <span>商品</span>
                            <i class="el-icon-arrow-right"></i></div
                        ></el-dropdown-item>
                        <el-dropdown-item command="activity"
                          ><div class="dropdown-item">
                            <span>活动</span>
                            <i class="el-icon-arrow-right"></i></div
                        ></el-dropdown-item>
                        <el-dropdown-item command="microPage"
                          ><div class="dropdown-item">
                            <span>微页面</span>
                            <i class="el-icon-arrow-right"></i></div
                        ></el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
                  </el-form-item>
                </div>
              </div>
            </div>
          </div>
        </el-form>
        <div class="add-banner-button">
          <el-button
            type="primary"
            @click="addBanner"
            :disabled="formData.bannerList.length >= 5"
          >
            <i class="el-icon-plus"></i> 添加图片 ({{
              formData.bannerList.length
            }}
            / 5)
          </el-button>
        </div>
      </div>
    </div>
    <div class="footer-btn">
      <el-button
        type="primary"
        class="submission"
        @click="handleSubmit('formValidate')"
        >保存</el-button
      >
      <el-button class="submission" @click="getConfigList">重置</el-button>
    </div>

    <!-- 链接选择器组件 -->
    <LinkSelector ref="linkSelector" @confirm="handleLinkConfirm" />
  </div>
</template>

<script>
import { systemBannerListApi, systemBannerSaveApi } from "@/api/homeMgt";
import { Debounce } from "@/utils/validate";
import LinkSelector from "./LinkSelector/index.vue";

export default {
  components: {
    LinkSelector,
  },
  data() {
    return {
      formData: {
        bannerList: [
          {
            imageUrl: "",
            jumpType: 1,
            jumpUrl: "",
            selectedLink: null,
          },
        ],
      },
      isSelected: 1,
      listLoading: false,
      currentBannerIndex: null, // 当前操作的banner索引
    };
  },
  mounted() {
    this.getConfigList();
  },
  methods: {
    addBanner() {
      if (this.formData.bannerList.length < 5) {
        this.formData.bannerList.push({
          imageUrl: "",
          jumpType: 1,
          jumpUrl: "",
          selectedLink: null,
        });
      }
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    modalPicTap(type, index) {
      const _this = this;
      this.$modalUpload(
        function (img) {
          if (type === "1") {
            _this.formData.bannerList[index].imageUrl = img[0].attDir;
          }
        },
        2,
        "content"
      );
    },
    // 获取banner轮播图配置
    getConfigList() {
      this.listLoading = true;
      systemBannerListApi()
        .then((res) => {
          this.listLoading = false;
          if (res.length) {
            this.formData.bannerList = res.map((item) => {
              const bannerItem = {
                imageUrl: item.imageUrl,
                jumpType: item.jumpType,
                jumpUrl: item.jumpUrl,
                id: item.id,
                selectedLink: null,
              };

              // 如果有jumpUrl，创建selectedLink对象
              if (item.jumpUrl) {
                try {
                  const jumpData = JSON.parse(item.jumpUrl);
                  const linkType = this.getLinkTypeByJumpType(item.jumpType);
                  bannerItem.selectedLink = {
                    type: linkType,
                    id: jumpData.id,
                    name: jumpData.name,
                    jumpType: item.jumpType,
                    jumpUrl: item.jumpUrl,
                  };
                } catch (e) {
                  console.error("解析jumpUrl失败:", e);
                  // 如果解析失败，使用旧的方式
                  const linkType = this.getLinkTypeByJumpType(item.jumpType);
                  bannerItem.selectedLink = {
                    type: linkType,
                    name: this.getNameFromJumpUrl(item.jumpUrl, linkType),
                    jumpType: item.jumpType,
                    jumpUrl: item.jumpUrl,
                  };
                }
              }

              return bannerItem;
            });
          } else {
            this.formData.bannerList = [
              {
                imageUrl: "",
                jumpType: 1,
                jumpUrl: "",
                selectedLink: null,
              },
            ];
          }
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          // 准备提交数据，只包含后端需要的字段
          const submitData = this.formData.bannerList.map((item) => {
            const bannerData = {
              imageUrl: item.imageUrl,
              jumpType: item.jumpType,
              jumpUrl: item.jumpUrl,
            };
            // 如果有id，说明是更新操作
            if (item.id) {
              bannerData.id = item.id;
            }
            return bannerData;
          });

          systemBannerSaveApi(submitData)
            .then(async (res) => {
              this.$message.success("保存成功");
              this.getConfigList();
            })
            .catch((res) => {});
        } else {
          this.$message.warning("请填写完整信息！");
        }
      });
    }),
    removeBanner(index) {
      this.formData.bannerList.splice(index, 1);
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.formData.bannerList[index];
        this.$set(
          this.formData.bannerList,
          index,
          this.formData.bannerList[index - 1]
        );
        this.$set(this.formData.bannerList, index - 1, temp);
      }
    },
    moveDown(index) {
      if (index < this.formData.bannerList.length - 1) {
        const temp = this.formData.bannerList[index];
        this.$set(
          this.formData.bannerList,
          index,
          this.formData.bannerList[index + 1]
        );
        this.$set(this.formData.bannerList, index + 1, temp);
      }
    },

    // 处理下拉菜单命令
    handleDropdownCommand(command, index) {
      this.currentBannerIndex = index;
      this.$refs.linkSelector.show(command);
    },

    // 处理链接选择确认
    handleLinkConfirm(linkData) {
      if (this.currentBannerIndex !== null) {
        this.$set(
          this.formData.bannerList[this.currentBannerIndex],
          "selectedLink",
          linkData
        );
        this.$set(
          this.formData.bannerList[this.currentBannerIndex],
          "jumpUrl",
          linkData.jumpUrl
        );
        this.$set(
          this.formData.bannerList[this.currentBannerIndex],
          "jumpType",
          linkData.jumpType
        );
        this.currentBannerIndex = null;
      }
    },

    // 清除选择
    clearSelection(index) {
      this.$set(this.formData.bannerList[index], "selectedLink", null);
      this.$set(this.formData.bannerList[index], "jumpUrl", "");
      this.$set(this.formData.bannerList[index], "jumpType", 1);
    },

    // 获取链接类型文本
    getLinkTypeText(type) {
      const typeMap = {
        product: "商品",
        activity: "活动",
        microPage: "微页面",
      };
      return typeMap[type] || "";
    },

    // 根据跳转类型获取链接类型
    getLinkTypeByJumpType(jumpType) {
      const typeMap = {
        1: "product",
        2: "activity",
        3: "microPage",
      };
      return typeMap[jumpType] || "product";
    },

    // 从jumpUrl中提取名称（兼容旧数据）
    getNameFromJumpUrl(jumpUrl, linkType) {
      return `已选择的${this.getLinkTypeText(linkType)}`;
    },
  },
};
</script>

<style lang="scss" scoped>
.banner-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  .content {
    flex: 1;
    overflow-y: auto;
    .opreation-btn {
      color: #666;
      &:hover {
        color: #7c7c7c;
      }
    }
    .banner-title {
      margin-bottom: 18px;
      font-size: 18px;
      font-weight: 700;
    }
    .description {
      font-size: 14px;
      color: #6b7280;
    }
    .des2 {
      margin-top: 8px;
    }
    .banner-list {
      margin: 30px 0 20px;
      .el-form-item {
        margin: 0;
      }
      .banner-item {
        display: flex;
        align-items: center;
        border: 1px solid #efefef;
        background-color: #fdfdfd;
        border-radius: 4px;
        padding: 16px 16px 16px 0;
        .left {
          flex: 0 0 110px;
          display: flex;
          align-items: center;
          justify-content: center;
          .upLoadPic {
            width: 76px;
            height: 76px;
            border-radius: 4px;
            border: 1px dashed #d5d5d5;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            .pictrue {
              position: relative;
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              &:hover {
                .replace-img {
                  display: block;
                }
              }
              img {
                width: 100%;
                height: auto;
              }
              .replace-img {
                position: absolute;
                display: none;
                bottom: 0;
                height: 20px;
                width: 100%;
                background-color: black;
                text-align: center;
                line-height: 20px;
                color: white;
                font-size: 12px;
                &:hover {
                  background-color: #f0f0f0;
                  color: #e03d35;
                }
              }
            }
            .upLoad {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
          }
        }
        .right {
          flex: 1;
          .banner-actions {
            display: flex;
            justify-content: space-between;
          }

          .link-input {
            display: flex;
            align-items: center;
            gap: 10px;
            .text {
              white-space: nowrap;
              span {
                display: inline-block;
                margin-top: 4px;
                color: #dd3f31;
                font-size: 16px;
                vertical-align: middle;
              }
            }
          }
        }
        & + .banner-item {
          margin-top: 12px;
        }
      }
    }

    .add-banner-button {
      text-align: center;
    }
  }

  .footer-btn {
    text-align: right;
  }
}
::v-deep .el-dropdown-menu__item {
  i {
    margin-right: 0;
  }
}
.dropdown-item {
  width: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selected-link {
  display: flex;
  align-items: center;
  background-color: #fbebea;
  border: 1px solid #f3bbb8;
  border-radius: 6px;
  padding: 4px 10px;
  line-height: 1;
  .selected-info {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #de5045;
    font-size: 14px;
    .link-type {
      border-radius: 2px;
      color: #de5045;
      padding-right: 6px;
      border-right: 1px solid #de5045;
    }
  }

  .clear-btn {
    padding: 4px;
    font-size: 14px;
    margin-left: 10px;
    background-color: #f7d6d4;
    border-radius: 50%;
    .el-icon-close {
      color: #c5362c;
      padding: 2px;
      &:hover {
        color: #ffffff;
      }
    }
    &:hover {
      background-color: #c5362c;
    }
  }
}
</style>
