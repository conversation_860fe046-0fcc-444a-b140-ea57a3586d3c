<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="60%"
    top="50px"
    :before-close="handleClose"
    class="link-selector-dialog"
  >
    <div class="selector-content">
      <!-- 搜索区域 -->
      <div
        class="search-box"
        v-if="currentType !== ''"
      >
        <el-form
          inline
          size="small"
        >
          <el-form-item
            v-if="currentType === 'product'"
            label="产品名称："
          >
            <el-input
              v-model="searchForm.keywords"
              placeholder="商品名称、商品ID"
              clearable
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item
            v-if="currentType === 'activity'"
            label="活动名称："
          >
            <el-input
              v-model="searchForm.keywords"
              placeholder="请输入活动名称"
              clearable
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-form-item
            v-if="currentType === 'microPage'"
            label="页面名称："
          >
            <el-input
              v-model="searchForm.keywords"
              placeholder="请输入页面名称"
              clearable
              @keyup.enter.native="handleSearch"
            />
          </el-form-item>
          <el-button
            type="primary"
            size="small"
            @click="handleSearch"
            >搜索</el-button
          >
          <el-button
            size="small"
            @click="handleReset"
            >重置</el-button
          >
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        v-loading="loading"
        :key="currentType"
        :data="tableData"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        ref="table"
      >
        <!-- 商品表格 -->
        <template v-if="currentType === 'product'">
          <el-table-column
            type="selection"
            align="center"
            width="55"
            :selectable="(row) => true"
          />
          <el-table-column
            prop="id"
            label="ID"
            width="80"
          />
          <el-table-column
            label="商品信息"
            min-width="300"
          >
            <template slot-scope="scope">
              <div class="product-info">
                <div class="product-image">
                  <img
                    :src="handlerImgUrl(scope.row.image)"
                    alt=""
                  />
                </div>
                <div class="product-details">
                  <div class="product-name">{{ scope.row.storeName }}</div>
                  <div class="product-price">¥{{ scope.row.price }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="stock"
            label="库存"
            width="100"
          />
          <el-table-column
            prop="sales"
            label="销量"
            width="100"
          />
          <el-table-column
            label="状态"
            width="100"
          >
            <template>
              <!-- <span :class="scope.row.isShow ? 'status-on' : 'status-off'">
                {{ scope.row.isShow ? '上架' : '下架' }}
              </span> -->
              <span>在售中</span>
            </template>
          </el-table-column>
        </template>

        <!-- 活动表格 -->
        <template v-if="currentType === 'activity'">
          <el-table-column
            type="selection"
            align="center"
            width="55"
          />
          <el-table-column
            prop="id"
            label="ID"
            width="80"
          />
          <el-table-column
            label="活动信息"
            min-width="400"
          >
            <template slot-scope="scope">
              <div class="activity-info">
                <div class="activity-image">
                  <img
                    :src="handlerImgUrl(scope.row.image)"
                    alt=""
                  />
                </div>
                <div class="activity-details">
                  <div class="activity-name">{{ scope.row.name }}</div>
                  <div class="activity-time">
                    {{
                      parseTime(scope.row.startTime, '{y}-{m}-{d} {h}:{i}:{s}')
                    }}
                    至
                    {{
                      parseTime(scope.row.stopTime, '{y}-{m}-{d} {h}:{i}:{s}')
                    }}
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="hits"
            label="点击量"
            width="100"
          />
          <el-table-column
            label="状态"
            width="100"
          >
            <template slot-scope="scope">
              <span :class="scope.row.status ? 'status-on' : 'status-off'">
                {{ scope.row.status ? '进行中' : '已结束' }}
              </span>
            </template>
          </el-table-column>
        </template>

        <!-- 微页面表格 -->
        <template v-if="currentType === 'microPage'">
          <el-table-column
            type="selection"
            align="center"
            width="55"
          />
          <el-table-column
            prop="id"
            label="ID"
            width="80"
          />
          <el-table-column
            prop="pageName"
            label="页面名称"
            min-width="200"
          />
          <el-table-column
            label="页面状态"
            width="120"
          >
            <template slot-scope="scope">
              <span
                :class="
                  scope.row.pageStatus == '1' ? 'status-on' : 'status-off'
                "
              >
                {{ scope.row.pageStatus == '1' ? '已发布' : '未发布' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="lastUpdateUser"
            label="最后编辑人"
            width="120"
          />
          <el-table-column
            prop="updateTime"
            label="更新时间"
            width="180"
          />
        </template>
      </el-table>

      <!-- 分页 -->
      <div
        class="pagination-wrapper"
        v-if="tableData.length > 0"
      >
        <el-pagination
          :page-sizes="[10, 20, 50, 100]"
          :page-size="pagination.limit"
          :current-page="pagination.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="pagination.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        @click="handleConfirm"
        :disabled="!selectedItem"
        >确定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import { productLstApi } from '@/api/store'
import { activityListApi } from '@/api/activity'
import { microPageListApi } from '@/api/microPage'
import { parseTime } from '@/utils/index'

export default {
  name: 'LinkSelector',
  data() {
    return {
      dialogVisible: false,
      currentType: '', // 'product', 'activity', 'microPage'
      loading: false,
      tableData: [],
      selectedItem: null,
      searchForm: {
        keywords: ''
      },
      pagination: {
        page: 1,
        limit: 20,
        total: 0
      }
    }
  },
  computed: {
    dialogTitle() {
      const titleMap = {
        product: '选择商品',
        activity: '选择活动',
        microPage: '选择微页面'
      }
      return titleMap[this.currentType] || '选择'
    }
  },
  methods: {
    // 显示弹窗
    show(type) {
      this.currentType = type
      this.dialogVisible = true
      this.resetData()
      this.loadData()
    },

    // 重置数据
    resetData() {
      this.selectedItem = null
      this.searchForm.keywords = ''
      this.pagination.page = 1
      this.pagination.limit = 20
      this.pagination.total = 0
      this.tableData = []
    },

    // 加载数据
    async loadData() {
      this.loading = true
      try {
        let response
        const params = {
          page: this.pagination.page,
          limit: this.pagination.limit
        }

        switch (this.currentType) {
          case 'product':
            response = await productLstApi({
              ...params,
              type: 1,
              keywords: this.searchForm.keywords
            })
            this.tableData = response.list || []
            this.pagination.total = response.total || 0
            break
          case 'activity':
            response = await activityListApi({
              ...params,
              name: this.searchForm.keywords
            })
            this.tableData = response.list || []
            this.pagination.total = response.total || 0
            break
          case 'microPage':
            response = await microPageListApi(params)
            this.tableData = response.list || []
            this.pagination.total = response.total || 0
            break
        }
      } catch (error) {
        console.error('加载数据失败:', error)
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },

    // 搜索
    handleSearch() {
      this.pagination.page = 1
      this.loadData()
    },

    // 重置搜索
    handleReset() {
      this.searchForm.keywords = ''
      this.pagination.page = 1
      this.loadData()
    },

    // 分页大小变化
    handleSizeChange(val) {
      this.pagination.limit = val
      this.pagination.page = 1
      this.loadData()
    },

    // 当前页变化
    handleCurrentChange(val) {
      this.pagination.page = val
      this.loadData()
    },

    // 行点击
    handleRowClick(row) {
      // 如果点击的是已选中的行，则取消选择
      if (this.selectedItem && this.selectedItem.id === row.id) {
        this.selectedItem = null
        this.$refs.table.clearSelection()
      } else {
        // 否则选中当前行
        this.selectedItem = row
        this.$refs.table.clearSelection()
        this.$refs.table.toggleRowSelection(row, true)
      }
    },

    // 选择变化
    handleSelectionChange(selection) {
      // 确保只能选择一个
      if (selection.length > 1) {
        // 如果选择了多个，只保留最后一个
        const lastSelected = selection[selection.length - 1]
        this.$refs.table.clearSelection()
        this.$refs.table.toggleRowSelection(lastSelected, true)
        this.selectedItem = lastSelected
      } else if (selection.length === 1) {
        this.selectedItem = selection[0]
      } else {
        this.selectedItem = null
      }
    },

    // 确认选择
    handleConfirm() {
      if (!this.selectedItem) {
        this.$message.warning('请选择一项')
        return
      }

      let result = {
        type: this.currentType,
        id: this.selectedItem.id,
        name: '',
        jumpType: 1,
        jumpUrl: ''
      }

      switch (this.currentType) {
        case 'product':
          result.name = this.selectedItem.storeName
          result.jumpType = 1
          result.jumpUrl = JSON.stringify({
            id: this.selectedItem.id,
            name: this.selectedItem.storeName
          })
          break
        case 'activity':
          result.name = this.selectedItem.name
          result.jumpType = 2
          result.jumpUrl = JSON.stringify({
            id: this.selectedItem.id,
            name: this.selectedItem.name
          })
          break
        case 'microPage':
          result.name = this.selectedItem.pageName
          result.jumpType = 3
          result.jumpUrl = JSON.stringify({
            id: this.selectedItem.id,
            name: this.selectedItem.pageName
          })
          break
      }

      this.$emit('confirm', result)
      this.handleClose()
    },

    // 关闭弹窗
    handleClose() {
      this.dialogVisible = false
      this.resetData()
    },

    // 处理图片URL
    handlerImgUrl(url) {
      if (!url) return ''
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },

    // 格式化时间
    parseTime
  }
}
</script>

<style lang="scss" scoped>
.link-selector-dialog {
  ::v-deep .el-dialog {
    /* max-height: 90vh; */
    display: flex;
    flex-direction: column;
  }

  ::v-deep .el-dialog__body {
    flex: 1;
    /* overflow: hidden; */
    padding: 10px 20px;
  }
}

.selector-content {
  /* height: 60vh; */
  display: flex;
  flex-direction: column;

  .search-box {
    margin-bottom: 20px;
    padding: 16px 16px 0;
    background-color: #f8f9fa;
    border-radius: 4px;
  }

  .el-table {
    flex: 1;
  }

  .pagination-wrapper {
    margin-top: 20px;
    text-align: right;
  }
}

.product-info {
  display: flex;
  align-items: center;

  .product-image {
    width: 50px;
    height: 50px;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  .product-details {
    flex: 1;

    .product-name {
      font-weight: 500;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .product-price {
      color: #e74c3c;
      font-weight: bold;
    }
  }
}

.activity-info {
  display: flex;
  align-items: center;

  .activity-image {
    width: 50px;
    height: 50px;
    margin-right: 12px;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 4px;
    }
  }

  .activity-details {
    flex: 1;

    .activity-name {
      font-weight: 500;
      margin-bottom: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .activity-time {
      color: #666;
      font-size: 12px;
    }
  }
}

.status-on {
  color: #67c23a;
}

.status-off {
  color: #f56c6c;
}

::v-deep .el-table__row {
  cursor: pointer;

  &:hover {
    background-color: #f5f7fa;
  }
}

::v-deep .current-row {
  background-color: #ecf5ff;
}
</style>
