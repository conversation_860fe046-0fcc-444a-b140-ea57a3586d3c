<template>
  <div class="hot-selling-recommendation">
    <div class="content">
      <div class="title">热销推荐</div>
      <div class="description">
        设置产品为推荐商品在首页展示，支持调整顺序；
      </div>
      <div class="description des2">热销关键词长度建议4——10个汉字；</div>
      <div class="display-number">
        <span class="label">首页显示数量：</span>
        <el-input-number
          v-model="displayCount"
          controls-position="right"
          :min="1"
        ></el-input-number>
      </div>

      <el-table
        v-loading="listLoading"
        :data="productItems"
        size="mini"
        highlight-current-row
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
      >
        <el-table-column label="热销产品">
          <template slot-scope="scope">
            <div class="product-item">
              <MyImage
                :imagePath="handlerImgUrl(scope.row.productImage)"
                :previewPath="scope.row.productImage"
                :size="46"
              />
              <div class="product-info">
                <div class="product-name">{{ scope.row.productName }}</div>
                <div class="product-price">¥{{ scope.row.productPrice }}</div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="热销关键词">
          <template slot-scope="scope">
            <el-input
              v-model="scope.row.hotKeyword"
              placeholder="请输入关键词"
            ></el-input>
          </template>
        </el-table-column>
        <el-table-column align="right" label="操作">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="moveUp(scope.$index)"
              v-show="scope.$index !== 0"
            >
              <svg-icon class="opreation-btn" icon-class="move_top"></svg-icon>
              <!-- <i class="el-icon-arrow-up"></i> -->
            </el-button>
            <el-button
              type="text"
              @click="moveDown(scope.$index)"
              v-show="scope.$index !== productItems.length - 1"
            >
              <svg-icon class="opreation-btn" icon-class="move_down"></svg-icon>
              <!-- <i class="el-icon-arrow-down"></i> -->
            </el-button>
            <el-button type="text" @click="removeProduct(scope.$index)">
              <!-- <i class="el-icon-delete"></i> -->
              <svg-icon class="opreation-btn" icon-class="delete"></svg-icon>
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="add-product">
        <el-button
          plain
          type="primary"
          :disabled="productItems.length >= 6"
          @click="changeGood"
        >
          <i class="el-icon-plus"></i> 添加产品 ({{ productItems.length }} / 6)
        </el-button>
      </div>
    </div>

    <div class="footer-btn">
      <el-button type="primary" class="submission" @click="saveData"
        >保存</el-button
      >
      <el-button class="submission" @click="getConfigList()">重置</el-button>
    </div>
  </div>
</template>

<script>
import {
  systemHotProductListApi,
  systemHotProductSaveApi,
} from "@/api/hotSellingRecommendation";
import { Debounce } from "@/utils/validate";

export default {
  data() {
    return {
      displayCount: 6,
      listLoading: false,
      productItems: [],
      fit: "cover",
    };
  },
  mounted() {
    this.getConfigList();
  },
  methods: {
    changeGood() {
      const _this = this;
      this.$modalGoodList(
        function (row) {
          if (!row) return;
          if (row.length > 6) {
            _this.$message.warning("最多只能选择6个产品");
            return;
          }
          console.log("row", row);
          _this.productItems = JSON.parse(JSON.stringify(row)).map((item) => ({
            ...item,
            productName: item.storeName,
            productImage: item.image,
            productPrice: item.price,
            hotKeyword: "",
          }));
        },
        "many",
        _this.productItems
      );
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 提交
    saveData: Debounce(function () {
      this.listLoading = true;
      systemHotProductSaveApi({
        displayCount: this.displayCount,
        productList: this.productItems,
      })
        .then(async (res) => {
          this.$message.success("保存成功");
          this.getConfigList();
          this.listLoading = false;
        })
        .catch((res) => {
          this.listLoading = false;
        });
    }),
    // 获取配置
    getConfigList() {
      this.listLoading = true;
      systemHotProductListApi()
        .then((res) => {
          this.listLoading = false;
          this.displayCount = res.displayCount || 6;
          if (res.productList && res.productList.length) {
            this.productItems = res.productList.map((item) => ({
              ...item,
              id: item.productId,
              image: item.productImage,
              storeName: item.productName,
              price: item.productPrice,
            }));
          }
        })
        .catch((res) => {
          this.listLoading = false;
          this.$message.error(res.message);
        });
    },
    removeProduct(index) {
      this.productItems.splice(index, 1);
    },
    moveUp(index) {
      if (index > 0) {
        const temp = this.productItems[index];
        this.$set(this.productItems, index, this.productItems[index - 1]);
        this.$set(this.productItems, index - 1, temp);
      }
    },
    moveDown(index) {
      if (index < this.productItems.length - 1) {
        const temp = this.productItems[index];
        this.$set(this.productItems, index, this.productItems[index + 1]);
        this.$set(this.productItems, index + 1, temp);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.hot-selling-recommendation {
  display: flex;
  flex-direction: column;
  height: 100%;

  .content {
    flex: 1;
    overflow-y: auto;
    .opreation-btn {
      color: #666;
      &:hover {
        color: #7c7c7c;
      }
    }
    .title {
      margin-bottom: 18px;
      font-size: 18px;
      font-weight: 700;
    }

    .description {
      font-size: 14px;
      color: #6b7280;
    }
    .des2 {
      margin-top: 8px;
    }

    .display-number {
      margin: 18px 0;
      .label {
        font-size: 14px;
        color: #6b7280;
      }
      .el-select {
        width: 120px;
        margin-right: 10px;
      }

      .default-value {
        color: #999;
      }
    }

    .product-item {
      display: flex;
      align-items: center;

      .product-info {
        margin-left: 10px;
        font-size: 14px;
        line-height: 1.2;
      }
    }

    .add-product {
      text-align: center;
      margin-top: 20px;
    }
  }

  .footer-btn {
    text-align: right;
  }
}
</style>
