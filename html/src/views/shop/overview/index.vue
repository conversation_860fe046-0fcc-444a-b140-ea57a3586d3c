<template>
  <div class="overview-container">
    <div class="header">
      <!-- 顶部用户信息 -->
      <div class="user-info-card card">
        <div class="user-info">
          <el-avatar :size="44" icon="el-icon-user-solid"></el-avatar>
          <div class="info-text">
            <div class="title">{{ realName }}</div>
            <div class="sub-title">
              <span class="time"> 登录时间：{{ lastTime }}</span>
              <span class="sub-title"
                ><router-link to="/maintain/user"
                  ><el-button type="text">个人中心</el-button></router-link
                ></span
              >
            </div>
          </div>
        </div>
        <div class="pending-items">
          <el-row :gutter="20">
            <el-col :span="8">
              <router-link to="/order/index">
                <div class="grid-content first-grid-content">
                  <div class="label">待发货订单</div>
                  <div class="value blue">{{ indexData.pendingShipNum }}</div>
                </div>
              </router-link>
            </el-col>
            <el-col :span="8">
              <router-link to="/order/afterSales">
                <div class="grid-content">
                  <div class="label">待处理售后</div>
                  <div class="value">{{ indexData.pendingAfterSaleNum }}</div>
                </div>
              </router-link>
            </el-col>
            <el-col :span="8">
              <router-link to="/store/comment">
                <div class="grid-content">
                  <div class="label">待回复评价</div>
                  <div class="value">{{ indexData.pendingReplyNum }}</div>
                </div>
              </router-link>
            </el-col>
          </el-row>
        </div>
      </div>
      <!-- 快捷入口 -->
      <div class="quick-access-card card">
        <div class="quick-access">
          <div class="section-title main-title">快捷入口</div>
          <div class="quick-cards">
            <router-link
              v-for="(item, index) in quickAccess"
              :key="index"
              :to="item.path"
              :style="`background-color: ${item.bgcColor};`"
              class="quick-card"
            >
              <div class="left">
                <svg-icon class="svg-icon" :icon-class="item.icon"></svg-icon>
                <span>{{ item.name }}</span>
              </div>
              <div class="right"><i class="el-icon-arrow-right"></i></div>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时概况 -->
    <div class="real-time-overview-card card">
      <div class="real-time-overview">
        <div class="section-title">
          <span class="main-title">实时概况</span>
          <span class="update-time">更新于：{{ updateTime }}</span>
        </div>
        <el-row :gutter="20">
          <el-col :span="6" v-for="(item, index) in statistics" :key="index">
            <div
              :class="`stat-card ${
                index == 3 || index == 6 ? 'no-border' : ''
              }`"
            >
              <div class="top">
                <div class="stat-title">{{ item.label }}</div>
                <div v-show="item.isShowRight">昨日：{{ item.yesterday }}</div>
              </div>
              <div class="bottom">
                <div class="stat-value">
                  {{ item.value }}
                  <span class="unit">{{ item.unit }}</span>
                </div>
                <div v-show="item.isShowRight" class="stat-compare">
                  <span :class="['trend', item.trend >= 0 ? 'up' : 'down']">
                    <span class="trend-value"
                      ><span v-show="item.trend != 0">{{
                        item.trend >= 0 ? "+" : "-"
                      }}</span
                      >{{
                        Math.abs(item.trend) &&
                        Number(Math.abs(item.trend)).toFixed(2)
                      }}</span
                    >
                    <i
                      v-show="item.trend != 0"
                      :class="
                        item.trend >= 0 ? 'el-icon-top' : 'el-icon-bottom'
                      "
                    ></i>
                  </span>
                </div>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 经营趋势图表 -->
    <div class="trend-chart-card card">
      <div class="trend-chart">
        <visit-chart ref="visitChart" />
        <!-- <div class="chart-container" ref="trendChart"></div> -->
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { homeInfoApi } from "@/api/overview";
import visitChart from "./visitChart";
import { mapGetters } from "vuex";

export default {
  components: {
    visitChart,
  },
  name: "ShopOverview",
  data() {
    return {
      updateTime: "--",
      indexData: {},
      quickAccess: [
        {
          name: "订单查询",
          path: "/order/index",
          icon: "overview_icon1",
          bgcColor: "#FEF3E7",
        },
        {
          name: "发布商品",
          path: "/store/index",
          icon: "overview_icon2",
          bgcColor: "#E8F7EF",
        },
        {
          name: "创建活动",
          path: "/marketing/activityManagement",
          icon: "overview_icon3",
          bgcColor: "#E8F7EF",
        },
        {
          name: "提现审核",
          path: "/member/memberBalanceWithdrawal/wechatWithdrawal",
          icon: "overview_icon4",
          bgcColor: "#EBF0FA",
        },
        {
          name: "SVIP申请审核",
          path: "/member/SVIPMemberManagement",
          icon: "overview_icon5",
          bgcColor: "#EBF0FA",
        },
        {
          name: "销售报表",
          path: "/member/salesReport/salesData",
          icon: "overview_icon6",
          bgcColor: "#FEF3E7",
        },
      ],
      statistics: [
        {
          label: "支付金额(元)",
          value: 0.0,
          unit: "元",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: true,
        },
        {
          label: "退款金额(元)",
          value: 0.0,
          unit: "元",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: true,
        },
        {
          label: "访问数量",
          value: 0.0,
          unit: "",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: true,
        },
        {
          label: "新增会员数",
          value: 0.0,
          unit: "",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: true,
        },
        {
          label: "支付订单数",
          value: 0.0,
          unit: "",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: true,
        },
        {
          label: "佣金返现",
          value: 0.0,
          unit: "元",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: false,
        },
        {
          label: "访问-支付转化率",
          value: 0.0,
          unit: "",
          yesterday: 0.0,
          trend: 0.0,
          isShowRight: false,
        },
      ],
      chartTimeRange: "day",
      chart: null,
    };
  },
  computed: {
    ...mapGetters(["realName", "lastTime"]),
  },
  mounted() {
    this.getIndexData();
    // this.initChart();
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
    }
  },
  methods: {
    // 获取数据
    getIndexData() {
      homeInfoApi().then((res) => {
        this.indexData = res;
        this.updateTime = res.updateTime;
        const mapData = [
          { today: res.sales, yesterday: res.yesterdaySales },
          {
            today: res.todayRefundAmount,
            yesterday: res.yesterdayRefundAmount,
          },
          {
            today: res.pageviews,
            yesterday: res.yesterdayPageviews,
          },
          {
            today: res.newUserNum,
            yesterday: res.yesterdayNewUserNum,
          },
          { today: res.orderNum, yesterday: res.yesterdayOrderNum },
          { today: res.todayBrokerageAmount },
          { today: res.visitPayRate },
        ];

        this.statistics = this.statistics.map((item, index) => {
          if (index <= 4) {
            return {
              ...item,
              value: Number(mapData[index].today),
              yesterday: Number(mapData[index].yesterday),
              trend:
                Math.abs(Number(mapData[index].today)) -
                Math.abs(Number(mapData[index].yesterday)),
            };
          }
          return {
            ...item,
            value: mapData[index].today,
          };
        });
      });
    },
    initChart() {
      this.chart = echarts.init(this.$refs.trendChart);

      const option = {
        tooltip: {
          trigger: "axis",
        },
        legend: {
          data: ["上架金额", "下架金额", "上架订单数", "下架订单数"],
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          boundaryGap: false,
          data: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
        },
        yAxis: {
          type: "value",
        },
        series: [
          {
            name: "上架金额",
            type: "line",
            data: [0.2, 0.4, 0.6, 0.8, 0.6, 0.4, 0.2],
          },
          {
            name: "下架金额",
            type: "line",
            data: [0.3, 0.5, 0.7, 0.9, 0.7, 0.5, 0.3],
          },
          {
            name: "上架订单数",
            type: "line",
            data: [0.1, 0.3, 0.5, 0.7, 0.5, 0.3, 0.1],
          },
          {
            name: "下架订单数",
            type: "line",
            data: [0.4, 0.6, 0.8, 1.0, 0.8, 0.6, 0.4],
          },
        ],
      };

      this.chart.setOption(option);
    },
  },
  watch: {
    chartTimeRange() {
      // 这里可以根据时间范围重新获取数据并更新图表
      // this.initChart();
    },
  },
};
</script>

<style lang="scss" scoped>
.overview-container {
  display: flex;
  flex-direction: column;
  padding: 16px 16px 0;
  .header {
    display: flex;
    gap: 16px;
  }
  .card {
    margin-bottom: 16px;
    border-radius: 5px;
    background-color: #fff;
    padding: 20px;
    .main-title {
      font-weight: 700 !important;
    }
  }

  .user-info-card {
    flex: 1;
    background: url("~@/assets/imgs/overview_bg.png") 0 / cover no-repeat;
    .user-info {
      display: flex;
      align-items: center;
      margin: 10px 0 30px 0;

      ::v-deep.el-avatar {
        background-color: #f2f2f2;
        color: #999;
      }
      .info-text {
        margin-left: 15px;

        .title {
          font-size: 20px;
          font-weight: 500;
          color: #333;
        }

        .sub-title {
          color: #666;
          font-size: 14px;
          .time {
            margin-right: 10px;
          }
          a {
            color: #409eff;
            text-decoration: none;
          }
        }
      }
    }
    .pending-items {
      .grid-content {
        border-left: 1px solid #ebebeb;
        padding-left: 20px;

        .label {
          margin-bottom: 10px;
          font-size: 14px;
          font-weight: medium;
        }
        .value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
        }
      }
      .first-grid-content {
        border-left: none;
        padding-left: 0;

        .blue {
          color: #3f74e1;
        }
      }
    }
  }

  .quick-access-card {
    flex: 1;
    .quick-access {
      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
        color: #333;
      }
      .quick-cards {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 16px 20px;
        .quick-card {
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-radius: 5px;
          background-color: #fef3e7;
          padding: 16px;
          .left {
            display: flex;
            gap: 6px;
            align-items: center;
            .svg-icon {
              font-size: 22px;
            }
          }
          span {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
  }

  .real-time-overview-card {
    padding-bottom: 0;
    .real-time-overview {
      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
        color: #333;
        display: flex;
        align-items: flex-end;
        gap: 16px;

        .update-time {
          font-size: 12px;
          color: #999;
        }
      }

      .stat-card {
        border-right: 1px solid #ebebeb;
        padding-right: 16px;
        margin-bottom: 30px;
        &.no-border {
          border-right: none;
        }
        .top {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .stat-title {
          font-size: 14px;
          margin-bottom: 5px;
        }

        .stat-value {
          font-size: 24px;
          font-weight: bold;
          margin: 10px 0;
          color: #333;

          .unit {
            font-size: 14px;
            color: #999;
            margin-left: 5px;
          }
        }

        .stat-compare {
          font-size: 12px;
          color: #999;
          display: flex;
          justify-content: space-between;
          align-items: center;
          .trend {
            margin-left: 10px;
            display: flex;
            align-items: center;
            font-weight: 700;
            font-size: 18px;
            i {
              vertical-align: middle;
            }
            .trend-value {
              margin-right: 10px;
            }
            &.up {
              color: #f56c6c;
            }

            &.down {
              color: #67c23a;
            }
          }
        }
      }
    }
  }

  .trend-chart-card {
    .trend-chart {
      .section-title {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 20px;
        color: #333;
        display: flex;
        justify-content: space-between;
        align-items: center;

        .chart-tabs {
          .el-radio-button {
            margin-left: 10px;
          }
        }
      }

      .chart-container {
        height: 400px;
        border-radius: 5px;
      }
    }
  }
}
</style>
