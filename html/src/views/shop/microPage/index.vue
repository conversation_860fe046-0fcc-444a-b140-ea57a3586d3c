<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link
                :to="{
                  path: '/shop/microPage/creatMicroPage',
                }"
              >
                <el-button type="primary">新增页面</el-button></router-link
              >
            </template>
          </MyHeaderSearch>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column label="id" width="80" prop="id"> </el-table-column>
        <el-table-column width="380" label="页面名称" prop="pageName">
        </el-table-column>
        <el-table-column label="页面状态" prop="pageStatus">
          <template slot-scope="scope">
            <span>{{ scope.row.pageStatus == "0" ? "未发布" : "已发布" }}</span>
          </template>
        </el-table-column>
        <el-table-column label="最后一次编辑" prop="lastUpdateUser">
        </el-table-column>
        <el-table-column label="更新时间" prop="updateTime"> </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <router-link
              :to="{
                path: '/shop/microPage/creatMicroPage/' + scope.row.id,
              }"
            >
              <el-button type="text" size="small" class="mr10">编辑</el-button>
            </router-link>

            <el-button
              @click="handerStatus(scope.row.pageStatus, scope.row.id)"
              type="text"
              size="small"
              >{{
                scope.row.pageStatus == 0 ? "立即发布" : "取消发布"
              }}</el-button
            >
            <el-button
              type="text"
              :disabled="scope.row.pageStatus == 1"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>
<script>
import {
  microPageListApi,
  microPageDeleteApi,
  microPageStatusApi,
} from "@/api/microPage";

export default {
  name: "StoreAttr",
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20,
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      listLoading: true,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    seachList() {
      this.getList();
    },

    // 列表
    getList() {
      this.listLoading = true;
      microPageListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 改变发售状态
    handerStatus(status, id) {
      this.$modalSure((status == "0" ? "发布" : "取消发布") + "该微页面？")
        .then(() => {
          microPageStatusApi({ id, status: status == "0" ? 1 : 0 })
            .then(() => {
              this.$message.success("操作成功");
              this.getList();
            })
            .catch(() => {});
        })
        .catch(() => {
          console.log(123);
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除id为" + id + "的微页面吗？")
        .then(() => {
          microPageDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.fr {
  float: right;
}

.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
</style>
