<template>
  <div class="login-container">
    <!-- :style="
      backgroundImages
        ? { backgroundImage: 'url(' + backgroundImages + ')' }
        : { backgroundImage: 'url(' + backgroundImageMo + ')' }
    " -->
    <div class="sys-logo-box">
      <svg-icon
        class="sys-logo"
        icon-class="logo"
      />
      <svg-icon
        class="sys-name"
        icon-class="sys_name"
      />
    </div>
    <div class="container">
      <!-- <template v-if="fullWidth>768">
        <swiper :options="swiperOption" class="swiperPross">
          <swiper-slide v-for="(item,index) in swiperList" :key="index" class="swiperPic">
            <img :src="item.pic">
          </swiper-slide>
          <div slot="pagination" class="swiper-pagination" />
        </swiper>
      </template> -->
      <div class="login-title">账号密码登录</div>
      <div class="form-box">
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="loginRules"
          class="login-form"
          autocomplete="on"
          hide-required-asterisk
          label-position="top"
          @keyup.enter="handleLogin"
        >
          <el-form-item
            prop="account"
            label="账号"
          >
            <el-input
              ref="account"
              v-model="loginForm.account"
              placeholder="请输入登录账号"
              name="username"
              type="text"
              tabindex="1"
              autocomplete="on"
            >
              <template slot="prefix">
                <svg-icon icon-class="user_name" />
              </template>
            </el-input>
          </el-form-item>

          <el-form-item
            prop="pwd"
            label="密码"
          >
            <el-input
              :key="passwordType"
              ref="pwd"
              v-model="loginForm.pwd"
              :type="passwordType"
              placeholder="请输入登录密码"
              name="pwd"
              tabindex="2"
              auto-complete="on"
            >
              <template slot="prefix">
                <svg-icon icon-class="user_password" />
              </template>
            </el-input>
            <span
              class="show-pwd"
              @click="showPwd"
            >
              <svg-icon
                :icon-class="passwordType === 'password' ? 'eye' : 'eye-open'"
              />
            </span>
          </el-form-item>

          <!-- <el-form-item
            prop="code"
            class="captcha"
          >
            <div class="captcha">
              <el-input
                ref="username"
                v-model="loginForm.code"
                style="width: 218px"
                prefix-icon="el-icon-message"
                placeholder="验证码"
                name="username"
                type="text"
                tabindex="3"
                autocomplete="on"
              />
              <div
                class="imgs"
                @click="getCaptcha()"
              >
                <img :src="captchatImg" />
              </div>
            </div>
          </el-form-item> -->
        </el-form>
        <div class="login-opreation">
          <el-button
            class="login-btn"
            :loading="loading"
            type="primary"
            @click.native.prevent="handleLogin"
            >登 录
          </el-button>

          <!-- 分割线效果 -->
          <div class="divider-container">
            <div class="divider-line"></div>
            <div class="divider-text">成都养乐啵文化传媒有限公司</div>
            <div class="divider-line"></div>
          </div>
          <!-- <div
              class="acea-row footer"
              @click="onWechat"
            >
              <div class="wechat mr10">
                <img src="../../assets/imgs/weixin.png" />
              </div>
              <span>微信</span>
            </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { validUsername } from '@/utils/validate'
import { getLoginPicApi, captchaApi, codeCheckApi } from '@/api/user'
import { getStoreStaff } from '@/libs/public'
import { getWXCodeByUrl, loginByWxCode } from '@/libs/wechat'
import { getWechatConfig } from '@/api/wxApi'
import { getToken, removeToken, setToken } from '@/utils/auth'
import Cookies from 'js-cookie'
export default {
  name: 'Login',
  data() {
    const validateUsername = (rule, value, callback) => {
      if (!validUsername(value)) {
        callback(new Error('Please enter the correct user name'))
      } else {
        callback()
      }
    }
    const validatePassword = (rule, value, callback) => {
      if (value.length < 6 || value.length > 12) {
        callback(new Error('密码位数为6-12位'))
      } else {
        callback()
      }
    }
    return {
      captchatImg: '',
      swiperList: [],
      loginLogo: '',
      backgroundImages: '',
      backgroundImageMo: require('@/assets/imgs/bg.jpg'),
      fullWidth: document.body.clientWidth,
      swiperOption: {
        pagination: {
          el: '.pagination'
        },
        autoplay: {
          enabled: true,
          disableOnInteraction: false,
          delay: 3000
        }
      },
      loginForm: {
        account: '', // admin
        pwd: '',
        key: '',
        code: '',
        wxCode: ''
      },
      loginRules: {
        account: [{ required: true, trigger: 'blur', message: '请输入账号' }], // validator: validateUsername
        pwd: [{ required: true, trigger: 'blur', message: '请输入密码' }],
        code: [
          { required: true, message: '请输入正确的验证码', trigger: 'blur' }
        ]
      },
      passwordType: 'password',
      capsTooltip: false,
      loading: false,
      showDialog: false,
      redirect: undefined,
      otherQuery: {}
    }
  },
  watch: {
    fullWidth(val) {
      // 为了避免频繁触发resize函数导致页面卡顿，使用定时器
      if (!this.timer) {
        // 一旦监听到的screenWidth值改变，就将其重新赋给data里的screenWidth
        this.screenWidth = val
        this.timer = true
        const that = this
        setTimeout(function () {
          // 打印screenWidth变化的值
          that.timer = false
        }, 400)
      }
    },
    $route: {
      handler: function (route) {
        const query = route.query
        if (query) {
          this.redirect = query.redirect
          this.otherQuery = this.getOtherQuery(query)
        }
      },
      immediate: true
    }
  },
  created() {
    const _this = this
    document.onkeydown = function (e) {
      if (_this.$route.path.indexOf('login') !== -1) {
        const key = window.event.keyCode
        if (key === 13) {
          _this.handleLogin()
        }
      }
    }
    // window.addEventListener("resize", this.handleResize);
  },
  mounted() {
    // this.getInfo();
    // if (this.loginForm.account === '') {
    //   this.$refs.account.focus()
    // } else if (this.loginForm.pwd === '') {
    //   this.$refs.pwd.focus()
    // }
    // this.getCaptcha()
    // this.agentWeiXinLogin()
  },

  beforeDestroy: function () {
    // window.removeEventListener("resize", this.handleResize);
  },
  methods: {
    agentWeiXinLogin() {
      // 判断是否需要微信公众号登陆
      const _isWechat = this.$wechat.isWeixin()
      if (_isWechat) {
        let code = this.$route.query.code
        let state = this.$route.query.state
        let wxAuthPath = location.origin + '/login'
        //  如果没有code 去获取
        if (null == code) {
          getWXCodeByUrl(wxAuthPath, 'step1')
        }
        // 如果有state=step1 根据code去登陆
        if (state === 'step1') {
          loginByWxCode(code)
            .then((res) => {
              sessionStorage.setItem('token', res.token)
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery
              })
            })
            .catch((err) => {
              // 如果登陆失败，那么输入账号登陆，重新获取code传递给后端做绑定
              getWXCodeByUrl(wxAuthPath, 'step2')
            })
        } else if (state === 'step2') {
          this.loginForm.wxCode = code
        }
      }
    },
    onWechat() {
      let url = this.$route.query.redirect
        ? this.$route.query.redirect
        : '/dashboard'
      this.$wechat.oAuth(url, 'login')
    },
    handleResize(event) {
      this.fullWidth = document.body.clientWidth
    },
    getInfo() {
      getLoginPicApi().then((res) => {
        this.swiperList = res.banner
        this.loginLogo = res.loginLogo
        this.backgroundImages = res.backgroundImage
      })
    },
    checkCapslock(e) {
      const { key } = e
      this.capsTooltip = key && key.length === 1 && key >= 'A' && key <= 'Z'
    },
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
      } else {
        this.passwordType = 'password'
      }
      this.$nextTick(() => {
        this.$refs.pwd.focus()
      })
    },
    handleLogin() {
      const code = this.$route.query.code
      this.$refs.loginForm.validate((valid) => {
        if (valid) {
          this.loading = true
          if (this.$wechat.isWeixin()) {
            this.loginForm.wxCode = code
          }
          this.$store
            .dispatch('user/login', this.loginForm)
            .then(() => {
              this.$router.push({
                path: this.redirect || '/',
                query: this.otherQuery
              })
              this.loading = false
            })
            .catch((err) => {
              this.loading = false
              if (this.$wechat.isPhone()) this.$dialog.error(err.message)
              // this.getCaptcha()
            })
        } else {
          return false
        }
      })
    },
    getCaptcha() {
      captchaApi()
        .then((data) => {
          this.captchatImg = data.code
          this.loginForm.key = data.key
        })
        .catch(({ message }) => {
          this.$message.error(message)
        })
    },
    getOtherQuery(query) {
      return Object.keys(query).reduce((acc, cur) => {
        if (cur !== 'redirect') {
          acc[cur] = query[cur]
        }
        return acc
      }, {})
    }
  }
}
</script>

<style lang="scss" scoped>
$screen-md: 768px;
$font-size-base: 14px;
$animation-time: 0.3s;
$animation-time-quick: 0.15s;
$transition-time: 0.2s;
$ease-in-out: ease-in-out;
$subsidiary-color: #ffffff;

.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f2f3f4;
  background: url('~@/assets/imgs/login_bg.png') no-repeat left top;
  .sys-logo-box {
    position: absolute;
    top: 20px;
    left: 50px;
    display: flex;
    align-items: center;
    .sys-logo {
      font-size: 44px;
      margin-top: -4px;
    }
    .sys-name {
      font-size: 112px;
      height: 23px;
    }
  }
  .container {
    border-radius: 9px;
    background-color: #fff;
    padding: 80px 60px 0;
    width: 420px;
    height: 580px;
    box-sizing: border-box;
    box-shadow: 0 4px 14px rgba(0, 0, 0, 0.04);

    .login-title {
      font-size: 28px;
      margin-bottom: 60px;
    }
    .form-box {
      ::v-deep.el-form-item--medium .el-form-item__label {
        line-height: 18px;
      }
      ::v-deep.el-input--medium .el-input__inner {
        height: 44px;
      }
      ::v-deep.el-input__prefix {
        display: flex;
        align-items: center;
        padding-left: 4px;
      }
      .login-opreation {
        .login-btn {
          width: 100%;
          font-size: 16px;
          font-weight: 700;
          margin-top: 28px;
          height: 44px;
        }
        .divider-container {
          display: flex;
          align-items: center;
          margin-top: 84px;
          width: 100%;
          .divider-line {
            flex: 1;
            height: 1px;
            &:first-child {
              background: linear-gradient(to right, transparent, #d0d0d0);
            }
            &:last-child {
              background: linear-gradient(to left, transparent, #d0d0d0);
            }
          }
          .divider-text {
            padding: 0 8px;
            color: #ccc;
            font-size: 13px;
            white-space: nowrap;
          }
        }
      }
    }
  }

  &-top {
    padding: 32px 0;
    &-logo {
      img {
        max-height: 75px;
      }
    }
    &-desc {
      font-size: $font-size-base;
      color: $subsidiary-color;
    }
  }
}

.login-container-top {
  padding: 20px 0 !important;
  box-sizing: border-box !important;
  display: flex;
  justify-content: center;
}
</style>

<style lang="scss" scoped>
.captcha {
  display: flex;
  align-items: flex-start;
}
$bg: #2d3a4b;
$dark_gray: #889aa4;
$light_gray: #eee;
.imgs {
  img {
    height: 36px;
  }
}
.login-form {
  position: relative;
  max-width: 100%;
  margin: 0 auto;
  overflow: hidden;
}

.show-pwd {
  position: absolute;
  right: 10px;
  top: 11px;
  font-size: 16px;
  color: $dark_gray;
  cursor: pointer;
  user-select: none;
  ::v-deep.svg-icon {
    vertical-align: 0.3em;
  }
}
</style>
