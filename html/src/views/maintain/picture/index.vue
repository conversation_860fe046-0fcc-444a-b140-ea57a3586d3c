<template>
  <MainCard>
    <div style="height: calc(100vh - 130px)">
      <upload-index :pictureType="pictureType"></upload-index>
    </div>
  </MainCard>
</template>

<script>
import UploadIndex from '@/components/uploadPicture/index.vue'

export default {
  name: 'index',
  data() {
    return {
      pictureType: 'maintain'
    }
  },
  components: { UploadIndex }
}
</script>

<style scoped></style>
