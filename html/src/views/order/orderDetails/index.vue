<template>
  <MainCard v-loading="fullscreenLoading" :cardType="3">
    <div class="app-box">
      <div class="base-card order-status">
        <div class="header">
          <span>订单号：{{ orderInfo.orderId }}</span>
          <span>下单时间：{{ orderInfo.createTime }}</span>
        </div>
        <div class="main">
          <div class="left">
            <template
              v-if="
                (this.orderInfo.statusStr &&
                  this.orderInfo.statusStr.value === '已取消') ||
                (this.orderInfo.statusStr &&
                  this.orderInfo.statusStr.value === '全额退款') ||
                (this.orderInfo.statusStr &&
                  this.orderInfo.statusStr.value === '部分退款') ||
                isCancelOrder
              "
            >
              <div>订单已取消</div>
            </template>
            <template v-else-if="orderFlow.completeStatus === 1">
              <div>交易完成</div>
              <div v-if="remainingWarrantyDays > 0">
                系统自动确认收货，售后期（剩余
                {{ remainingWarrantyDays }} 天）
              </div>
              <div v-else>系统自动确认收货，售后期已结束</div>
            </template>

            <template v-else-if="orderFlow.deliveryStatus === 1">
              <div>已发货，等待买家确认收货</div>
              <div>
                买家如果在{{
                  this.orderSettingInfo.tradeConfigList &&
                  this.orderSettingInfo.tradeConfigList[2].configValue.value
                }}天内未确认收货，交易将自动完成
              </div>
              <div>
                <Logistics :deliveryId="orderInfo.deliveryId" />
              </div>
            </template>
            <template v-else-if="orderFlow.payStatus === 1">
              <div>等待发货</div>
              <div>买家已付款，请尽快发货</div>
              <div class="opration-btn">
                <Print :orderId="$route.query.id" />
                <ConfirmShipment
                  :orderId="$route.params.id"
                  @success="$router.go(-1)"
                />
              </div>
            </template>
            <template v-else-if="orderFlow.orderStatus === 1">
              <div>等待买家付款</div>
              <div>
                付款剩余：<span class="count-down">{{ minutes }}</span
                >分<span class="count-down">{{ seconds }}</span
                >秒，超时订单将自动取消
              </div>
              <!-- <div>
                <el-button @click="cancelOrder" size="small"
                  >取消订单</el-button
                >
              </div> -->
            </template>

            <template
              v-if="
                this.orderInfo.aftersaleType != 0 && remainingWarrantyDays > 0
              "
            >
              <div>
                <span
                  >买家申请
                  <span style="color: #dd422e">{{
                    returnTypeName(this.orderInfo.aftersaleType)
                  }}</span>
                  ，请审核</span
                ><el-button
                  @click="$router.push('/order/afterSales')"
                  style="margin-left: 10px"
                  size="small"
                  type="primary"
                  >去处理</el-button
                >
              </div>
            </template>
          </div>

          <div class="right">
            <div class="status-steps">
              <div
                v-for="(item, index) in steps"
                :key="item.title"
                class="step-item"
              >
                <div class="item-left">
                  <svg-icon
                    v-show="item.isFinished"
                    class="order_finished"
                    icon-class="order_finished"
                  />
                  <div v-show="!item.isFinished" class="step-no">
                    {{ index + 1 }}
                  </div>
                </div>
                <div class="item-right">
                  <div class="title">
                    <div class="step-title">{{ item.title }}</div>
                    <div
                      v-if="index != steps.length - 1"
                      :style="`background:${
                        item.isFinished && steps[index + 1].isFinished
                          ? 'linear-gradient(90deg, #dd3f31 0%, #dc5620 100%)'
                          : '#d8d8d8'
                      }`"
                      class="step-line"
                    ></div>
                  </div>
                  <div class="description">
                    <div>{{ item.des1 }}</div>
                    <div v-show="item.des2">{{ item.des2 }}</div>
                    <div v-show="item.des3">{{ item.des3 }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="base-card order-info">
        <div class="info-title">订单信息</div>
        <div class="info-content">
          <el-row>
            <el-col :span="12">
              <div class="content-title">收货人信息</div>
              <div class="content-value">
                <span>收货人：</span><span>{{ orderInfo.realName }}</span>
              </div>
              <div class="content-value">
                <span>联系电话：</span><span>{{ orderInfo.userPhone }}</span>
              </div>
              <div class="content-value">
                <span>收货地址：</span><span>{{ orderInfo.userAddress }}</span>
              </div>
            </el-col>
            <el-col :span="12">
              <div class="content-title">买家留言</div>
              <div class="content-value">
                <span>买家：</span
                ><span class="seller"
                  ><span>{{ orderInfo.buyerName }}</span
                  ><span>
                    <memberLevelIcon
                      :level="Number(orderInfo.buyerLevel)"
                      /></span
                ></span>
              </div>
              <div class="content-value">
                <span>留言：</span><span>{{ orderInfo.mark || "--" }}</span>
              </div>
              <div class="content-value"></div>
            </el-col>
          </el-row>
          <el-row style="margin-top: 20px">
            <el-col :span="12">
              <div class="content-title">付款信息</div>
              <div class="content-value">
                <span>实付金额：</span
                ><span>{{ "￥" + orderInfo.payPrice }}</span>
              </div>
              <div class="content-value">
                <span>付款方式：</span><span>{{ orderInfo.payTypeStr }}</span>
              </div>
              <div class="content-value">
                <span>付款时间：</span><span>{{ orderInfo.createTime }}</span>
              </div>
            </el-col>
            <el-col
              v-if="
                orderFlow.completeStatus === 1 || orderFlow.deliveryStatus === 1
              "
              :span="12"
            >
              <div class="content-title">物流信息</div>
              <div class="content-value">
                <span>快递公司：</span><span>{{ orderInfo.deliveryName }}</span>
              </div>
              <div class="content-value">
                <span>快递单号：</span><span>{{ orderInfo.deliveryId }}</span>
              </div>
              <div class="content-value">
                <span>物流跟踪：</span>
                <Logistics
                  buttonType="link"
                  :deliveryId="orderInfo.deliveryId"
                />
              </div>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="base-card store-info">
        <div class="info-title">商品信息</div>
        <div class="table">
          <el-table
            ref="table"
            :data="orderInfo.orderInfo"
            :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
            style="width: 100%"
            size="mini"
            highlight-current-row
          >
            <el-table-column label="商品" prop="name">
              <template slot-scope="scope">
                <div class="store">
                  <MyImage
                    :imagePath="handlerImgUrl(scope.row.info.image)"
                    :previewPath="scope.row.info.image"
                    :size="40"
                  />
                  <div class="item-info">
                    <div class="store-name">
                      {{ scope.row.info.productName }}
                    </div>
                    <div class="specification">
                      {{ scope.row.info.sku }}
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="数量" prop="info.payNum"> </el-table-column>
            <el-table-column label="单价(元)" prop="info.price">
              <template slot-scope="scope">
                <div>
                  {{ "￥" + scope.row.info.price }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="会员折扣" prop="info.memberDiscountAmount">
              <template slot-scope="scope">
                <div>
                  {{ "-￥" + scope.row.info.memberDiscountAmount }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="优惠减">
              <template slot-scope="scope">
                <div>
                  {{
                    "-￥" +
                    (
                      scope.row.info.discountAmount -
                      scope.row.info.memberDiscountAmount
                    ).toFixed(2)
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="小计" prop="info.subtotalAmount">
              <template slot-scope="scope">
                <div>
                  {{ "￥" + scope.row.info.subtotalAmount }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="利润" prop="memberCount">
              <template slot-scope="scope">
                <div>
                  {{
                    "￥" +
                    Number(
                      scope.row.info.subtotalAmount - scope.row.info.cost
                    ).toFixed(2)
                  }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="price-info">
          <div class="left">
            <div><span>商品总额：</span></div>
            <div><span>商品优惠后合计：</span></div>
            <div><span>运费：</span></div>
            <div><span>订单金额：</span></div>
          </div>
          <div class="right">
            <div>
              <span>{{ "￥" + orderInfo.totalPrice }}</span>
            </div>
            <div>
              <span>{{ "￥" + orderInfo.proTotalPrice }}</span>
            </div>
            <div>
              <span>{{ "￥" + orderInfo.payPostage }}</span>
            </div>
            <div>
              <span style="color: #dc4a2a; font-size: 18px">{{
                "￥" + orderInfo.payPrice
              }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { orderInfoApi } from "@/api/order";
import { orderSettingListApi } from "@/api/orderSetting";
import Print from "./Print.vue";
import ConfirmShipment from "./ConfirmShipment.vue";
import Logistics from "../logistics";

import moment from "moment";
export default {
  components: {
    Print,
    ConfirmShipment,
    Logistics,
  },
  data() {
    return {
      fullscreenLoading: false,
      isCancelOrder: false,
      steps: [
        {
          title: "下单",
          des1: "",
          des2: "",
          des3: "",
          isFinished: false,
        },
        {
          title: "付款",
          des1: "",
          des2: "",
          des3: "",
          isFinished: false,
        },
        {
          title: "发货",
          des1: "",
          des2: "",
          des3: "",
          isFinished: false,
        },
        {
          title: "交易完成",
          des1: "",
          des2: "",
          des3: "",
          isFinished: false,
        },
      ],
      minutes: 0,
      seconds: 0,
      timer: null,
      orderSettingInfo: [],
      orderInfo: {},
      orderFlow: {},
      orderPlacedAt: "", //下单时间
      timeout: 30, // 订单超时时间分钟
      tableData: {
        data: [],
        total: 0,
      },
    };
  },
  computed: {
    remainingWarrantyDays() {
      if (
        this.orderFlow.completeStatus === 1 &&
        this.orderSettingInfo.tradeConfigList &&
        this.orderFlow.completeTime
      ) {
        const completeDate = this.orderFlow.completeTime.split(" ")[0];
        const daysPassed = moment().diff(moment(completeDate), "days");
        const remaining =
          this.orderSettingInfo.tradeConfigList[3].configValue.value -
          daysPassed;
        return remaining > 0 ? remaining : 0;
      }
      return null;
    },
  },
  mounted() {
    this._init();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    async _init() {
      this.fullscreenLoading = true;
      if (this.$route.params.id) {
        await this.getInfo();
      }
      // 获取订单设置
      await this.getOrderSetting();
      this.fullscreenLoading = false;
      // 判断是否超时
      if (this.orderInfo.statusStr.value === "待付款") {
        this.checkExpiration();
      }
    },
    // 订单设置
    async getOrderSetting() {
      await orderSettingListApi()
        .then((res) => {
          const data = {
            tradeConfigList: res.tradeConfigList,
            printConfigList: res.printConfigList,
          };
          for (const key in data) {
            data[key].forEach((element) => {
              element.configValue = JSON.parse(element.configValue);
            });
          }
          this.orderSettingInfo = data;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    returnTypeName(val) {
      if (val == 1) {
        return "退货退款";
      } else if (val === 2) {
        return "换货";
      } else if (val === 3) {
        return "未发货退款";
      }
      return "仅退款";
    },
    // 计时器
    startTimer() {
      this.timer = setInterval(() => {
        const now = moment();
        const endTime = moment(this.orderPlacedAt).add(this.timeout, "minutes");
        const duration = moment.duration(endTime.diff(now));

        this.minutes = duration.minutes();
        this.seconds = duration.seconds();

        if (duration.asSeconds() <= 0) {
          clearInterval(this.timer);
          this.isCancelOrder = true;
        }
      }, 1000);
    },
    //判断是否超时
    checkExpiration() {
      this.orderPlacedAt = this.orderInfo.createTime;
      // this.orderPlacedAt = "2025-06-29 15:20:00";
      this.timeout = Number(
        this.orderSettingInfo.tradeConfigList[1].configValue.value
      );
      const endTime = moment(this.orderPlacedAt).add(this.timeout, "minutes");
      const now = moment();
      if (now.isAfter(endTime)) {
        this.isCancelOrder = true;
        return;
      }
      this.startTimer();
    },
    // 取消订单
    cancelOrder() {
      clearInterval(this.timer);
      // 取消逻辑
    },
    // 详情
    async getInfo() {
      await orderInfoApi({ orderNo: this.$route.params.id })
        .then(async (res) => {
          this.orderInfo = res;
          this.orderFlow = res.orderFlow;
          // 改变流程图状态
          this.steps.forEach((item) => {
            switch (item.title) {
              case "下单":
                if (this.orderFlow.orderStatus === 1) {
                  const time = this.orderFlow.orderTime
                    ? this.orderFlow.orderTime.split(" ")
                    : [];
                  item.des1 = time[0];
                  item.des2 = time[1];
                  item.des3 = this.orderFlow.orderUser;
                  item.isFinished = true;
                }
                break;
              case "付款":
                if (this.orderFlow.payStatus === 1) {
                  const time = this.orderFlow.payTime
                    ? this.orderFlow.payTime.split(" ")
                    : [];
                  item.des1 = time[0];
                  item.des2 = time[1];
                  item.des3 = this.orderFlow.payType;
                  item.isFinished = true;
                }
                break;
              case "发货":
                if (this.orderFlow.deliveryStatus === 1) {
                  const time = this.orderFlow.deliveryTime
                    ? this.orderFlow.deliveryTime.split(" ")
                    : [];
                  item.des1 = time[0];
                  item.des2 = time[1];
                  item.des3 = this.orderFlow.expressName;
                  item.isFinished = true;
                }
                break;
              case "交易完成":
                if (this.orderFlow.completeStatus === 1) {
                  const time = this.orderFlow.completeTime
                    ? this.orderFlow.completeTime.split(" ")
                    : [];
                  item.des1 = time[0];
                  item.des2 = time[1];
                  item.isFinished = true;
                }
                break;
            }
          });
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px;
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
  .order-status {
    padding: 0;
    .header {
      height: 50px;
      color: #666666;
      line-height: 50px;
      border-bottom: #e8e8e8 1px solid;
      padding-left: 20px;
      font-size: 14px;
      > span {
        margin-right: 40px;
      }
    }
    .main {
      display: flex;
      .left {
        flex: 1;
        border-right: #e8e8e8 1px solid;
        padding: 30px;
        .opration-btn {
          display: flex;
        }
        .count-down {
          display: inline-block;
          background: linear-gradient(85deg, #dd3f31 0%, #dc5620 100%);
          color: white;
          padding: 0 2px;
        }
        > div {
          & + div {
            margin-top: 10px;
          }
          font-size: 14px;
          &:first-child {
            font-size: 24px;
          }
        }
      }
      .right {
        flex: 2;
        padding: 0 60px 0;
        display: flex;
        align-items: center;
        justify-content: center;
        .status-steps {
          flex: 1;
          display: flex;
          gap: 10px;
          padding: 20px 0;
          .step-item {
            flex: 1;
            display: flex;
            gap: 8px;
            .item-left {
              .order_finished {
                font-size: 20px;
                margin-top: -2px;
              }
              .step-no {
                width: 20px;
                height: 20px;
                background-color: #e8e8e8;
                border-radius: 50%;
                text-align: center;
                line-height: 20px;
                color: #888;
                margin-top: -2px;
              }
            }
            .item-right {
              font-size: 14px;
              flex: 1;
              .title {
                color: #000;
                display: flex;
                gap: 10px;
                align-items: center;
                .step-line {
                  flex: 1;
                  height: 1.5px;
                  background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
                }
              }
              .description {
                color: #333;
                > div {
                  margin-top: 10px;
                }
              }
            }
          }
        }
      }
    }
  }
  .order-info {
    .info-content {
      font-size: 14px;
      padding: 0 10px;
      .content-title {
        position: relative;
        color: #000;
        font-weight: 700;
        margin-bottom: 14px;

        &::before {
          content: "";
          position: absolute;
          display: inline-block;
          width: 36px;
          height: 5px;
          border-radius: 2.5px;
          background-color: #dd412f;
          opacity: 0.31;
          bottom: 0;
          left: 0;
        }
      }
      .content-value {
        margin-top: 10px;
        display: flex;
        gap: 10px;
        .seller {
          color: #628be5;
          display: inline-flex;
          align-items: center;
          gap: 10px;
        }
        .link {
          color: #3f6fd6;
          cursor: pointer;
          &:hover {
            color: #628be5;
          }
        }
      }
    }
  }
  .store-info {
    font-size: 15px;
    .table {
      .store {
        display: flex;
        align-items: center;
        gap: 10px;
        .store-name {
          font-size: 15px;
          color: #000;
        }
        .specification {
          font-size: 12px;
        }
      }
    }
    .price-info {
      margin-top: 18px;
      display: flex;
      justify-content: flex-end;
      gap: 4px;
      text-align: right;
      margin-right: 20px;
      .left,
      .right {
        > div {
          margin-bottom: 10px;
        }
      }
    }
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
</style>
