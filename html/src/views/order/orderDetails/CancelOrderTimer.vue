<template>
  <div>
    <p>等待买家付款</p>
    <p>付款剩余：&zwnj;**{{ minutes }} 分 {{ seconds }} 秒**&zwnj;，超时订单将自动取消</p>
    <button @click="cancelOrder">取消订单</button>
  </div>
</template>

<script>
import moment from 'moment';

export default {
  props: {
    orderPlacedAt: String,
    timeout: {
      type: Number,
      default: 30
    }
  },
  data() {
    return {
      minutes: 0,
      seconds: 0,
      timer: null
    };
  },
  mounted() {
    this.startTimer();
  },
  beforeDestroy() {
    clearInterval(this.timer);
  },
  methods: {
    startTimer() {
      const endTime = moment(this.orderPlacedAt).add(this.timeout, 'minutes');
      this.timer = setInterval(() => {
        const now = moment();
        const duration = moment.duration(endTime.diff(now));
        this.minutes = duration.minutes();
        this.seconds = duration.seconds();
        if (endTime.isBefore(now) || endTime.isSame(now)) {
          clearInterval(this.timer);
          this.$emit('order-cancelled');
        }
      }, 1000);
    },
    cancelOrder() {
      clearInterval(this.timer);
      this.$emit('order-cancelled');
    }
  }
};
</script>
