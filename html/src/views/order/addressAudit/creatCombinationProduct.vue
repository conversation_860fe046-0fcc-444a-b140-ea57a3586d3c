<template>
  <el-form
    :model="formValidate"
    :rules="rules"
    ref="formValidate"
    label-width="120px"
    class="demo-formValidate"
    v-loading="loading"
  >
    <el-form-item
      label="搭配组合名称："
      prop="name"
    >
      <el-input
        type="text"
        v-model="formValidate.name"
      ></el-input>
    </el-form-item>

    <el-form-item
      label="搭配组合商品："
      prop="productIds"
    >
      <div class="acea-row">
        <template v-if="formValidate.productIds.length">
          <div
            class="pictrue"
            v-for="(item, index) in formValidate.productIds"
            :key="index"
          >
            <img :src="item.image" />
            <i
              class="el-icon-error btndel"
              @click="handleRemove(index)"
            />
          </div>
        </template>
        <div
          class="upLoadPicBox"
          @click="changeGood"
        >
          <div class="upLoad">
            <i class="el-icon-camera cameraIconfont" />
          </div>
        </div>
      </div>
    </el-form-item>
    <el-form-item
      label="组合优惠价："
      prop="combinationPrice"
    >
      <el-input
        type="text"
        v-model="formValidate.combinationPrice"
      ></el-input>
    </el-form-item>
    <el-form-item>
      <el-button
        size="mini"
        type="primary"
        @click="submitForm('formValidate')"
        :loading="loadingbtn"
        >提交</el-button
      >
      <el-button
        size="mini"
        @click="resetForm('formValidate')"
        >重置</el-button
      >
    </el-form-item>
  </el-form>
</template>

<script>
// import { replyCreatApi, replyEditApi, replyInfoApi } from '@/api/store'
import { productCombinationCreateApi, productCombinationUpdateApi} from '@/api/storeMatch'
import { Debounce } from '@/utils/validate'
const defaultObj = {
  name: '',
  nacombinationPriceme: null,
  productIds: []
}
export default {
  name: 'creatComment',
  props: {
    isCreate: {
      type: Number,
      default: 0
    },
    editData: { type: Object, dfault: () => ({}) }
  },
  data() {
    return {
      loadingbtn: false,
      loading: false,
      pics: [],
      image: '',
      formValidate: Object.assign({}, defaultObj),
      rules: {
        productIds: [
          {
            required: true,
            message: '请至少选择一个商品',
            trigger: 'change',
            type: 'array'
          }
        ],
        name: [
          { required: true, message: '请填写搭配组合名称', trigger: 'blur' }
        ],
        nacombinationPriceme: [
          { required: true, message: '请填写组合优惠价格', trigger: 'blur' }
        ]
      }
    }
  },
  watch: {
    isCreate: {
      handler: function (val) {
        if (val === 0) {
          this.$nextTick(() => {
            this.resetForm('formValidate')
          })
        } else {
          console.log(this.editData)
          this.formValidate = Object.assign({}, this.editData)
          this.formValidate.productIds = this.editData.productDetailList.map(
            (item) => {
              return { id: item.id, name: item.storeName, image: item.image }
            }
          )
        }
      },
      immediate: true
    }
  },
  methods: {
    changeGood() {
      const _this = this
      this.$modalGoodList(
        function (row) {
          _this.formValidate.productIds = row
        },
        'many',
        _this.formValidate.productIds
      )
    },

    handleRemove(i) {
      this.formValidate.productIds.splice(i, 1)
    },
    submitForm: Debounce(function (formName) {
      const productIds = this.formValidate.productIds.map((item) => item.id)
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loadingbtn = true
          if (this.isCreate === 0) {
            // 新增组合
            productCombinationCreateApi({ ...this.formValidate, productIds })
              .then(() => {
                this.$message.success('新增成功')
                setTimeout(() => {
                  // this.clear();
                  this.$emit('getList')
                }, 600)
                this.loadingbtn = false
              })
              .catch(() => {
                this.loadingbtn = false
              })
          } else {
            // 修改组合
            productCombinationUpdateApi({ ...this.formValidate, productIds })
              .then(() => {
                this.$message.success('修改成功')
                setTimeout(() => {
                  // this.clear();
                  this.$emit('getList')
                }, 600)
                this.loadingbtn = false
              })
              .catch(() => {
                this.loadingbtn = false
              })
          }
        } else {
          return false
        }
      })
    }),
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.productScore {
  ::v-deep.el-rate {
    line-height: 2.4;
  }
}
.pictrue {
  width: 60px;
  height: 60px;
  border: 1px dotted rgba(0, 0, 0, 0.1);
  margin-right: 10px;
  position: relative;
  cursor: pointer;
  img {
    width: 100%;
    height: 100%;
  }
}
.btndel {
  position: absolute;
  z-index: 1;
  width: 20px !important;
  height: 20px !important;
  left: 46px;
  top: -4px;
}
</style>
