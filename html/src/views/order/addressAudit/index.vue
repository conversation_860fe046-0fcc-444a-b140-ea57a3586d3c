<template>
  <div class="divBox">
    <el-card class="box-card">
      <div
        slot="header"
        class="clearfix"
      >
        <!-- <el-tabs
          v-model="tableFrom.type"
          @tab-click="seachList"
        >
          <el-tab-pane
            :label="item.name + '(' + item.count + ')'"
            :name="item.type.toString()"
            v-for="(item, index) in headeNum"
            :key="index"
          />
        </el-tabs> -->

        <div class="container">
          <el-form
            inline
            size="small"
          >
            <el-form-item label="商品名称：">
              <el-input
                v-model="tableFrom.storeName"
                placeholder="请输入商品名称"
                class="selWidth"
                clearable
              >
                <el-button
                  slot="append"
                  icon="el-icon-search"
                  @click="seachList"
                />
              </el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="acea-row">
          <!-- <el-button
            size="small"
            type="primary"
            @click="add"
            v-hasPermi="['admin:product:rule:save']"
            >组合商品</el-button
          > -->
          <!-- <el-button
            size="small"
            @click="handleDeleteAll"
            v-hasPermi="['admin:product:rule:delete']"
            >批量删除</el-button
          > -->
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        style="width: 100%"
        size="mini"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column
          type="selection"
          width="55"
        /> -->
        <el-table-column
          label="订单号"
          min-width="210"
        >
          <template slot-scope="scope">
            <span
              style="display: block"
              v-text="scope.row.orderId"
            />
            <span
              v-show="scope.row.isDel"
              style="color: #ed4014; display: block"
              >用户已删除</span
            >
          </template>
        </el-table-column>
        <el-table-column
          prop="orderType"
          label="订单类型"
          min-width="110"
        />
        <el-table-column
          prop="realName"
          label="收货人"
          min-width="100"
        />
        <el-table-column
          label="商品信息"
          min-width="400"
        >
          <template slot-scope="scope">
            <el-popover
              trigger="hover"
              placement="right"
              :open-delay="800"
            >
              <div
                v-if="scope.row.productList && scope.row.productList.length"
                slot="reference"
              >
                <div
                  v-for="(val, i) in scope.row.productList"
                  :key="i"
                  class="tabBox acea-row row-middle"
                  style="flex-wrap: inherit"
                >
                  <div class="demo-image__preview mr10">
                    <el-image
                      :src="val.info.image"
                      :preview-src-list="[val.info.image]"
                    />
                  </div>
                  <div class="text_overflow">
                    <span class="tabBox_tit mr10"
                      >{{ val.info.productName + ' | '
                      }}{{ val.info.sku ? val.info.sku : '-' }}</span
                    >
                    <span class="tabBox_pice">{{
                      '￥' + val.info.price
                        ? val.info.price + ' x ' + val.info.payNum
                        : '-'
                    }}</span>
                  </div>
                </div>
              </div>
              <div
                class="pup_card"
                v-if="scope.row.productList && scope.row.productList.length"
              >
                <div
                  v-for="(val, i) in scope.row.productList"
                  :key="i"
                  class="tabBox acea-row row-middle"
                  style="flex-wrap: inherit"
                >
                  <div class="">
                    <span class="tabBox_tit mr10"
                      >{{ val.info.productName + ' | '
                      }}{{ val.info.sku ? val.info.sku : '-' }}</span
                    >
                    <span class="tabBox_pice">{{
                      '￥' + val.info.price
                        ? val.info.price + ' x ' + val.info.payNum
                        : '-'
                    }}</span>
                  </div>
                </div>
              </div>
            </el-popover>
          </template>
        </el-table-column>
        <el-table-column
          prop="payPrice"
          label="实际支付"
          min-width="80"
        />
        <el-table-column
          label="支付方式"
          min-width="80"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.payTypeStr }}</span>
          </template>
        </el-table-column>

        <el-table-column
          label="订单状态"
          min-width="100"
        >
          <template slot-scope="scope">
            <div>
              <div
                v-if="
                  scope.row.refundStatus === 1 || scope.row.refundStatus === 2
                "
                class="refunding"
              >
                <template>
                  <el-popover
                    trigger="hover"
                    placement="left"
                    :open-delay="800"
                  >
                    <b
                      style="color: #f124c7"
                      slot="reference"
                      >{{ scope.row.statusStr.value }}</b
                    >
                    <div class="pup_card flex-column">
                      <span>退款原因：{{ scope.row.refundReasonWap }}</span>
                      <span
                        >备注说明：{{ scope.row.refundReasonWapExplain }}</span
                      >
                      <span>退款时间：{{ scope.row.refundReasonTime }}</span>
                      <span class="acea-row">
                        退款凭证：
                        <template v-if="scope.row.refundReasonWapImg">
                          <div
                            v-for="(
                              item, index
                            ) in scope.row.refundReasonWapImg.split(',')"
                            :key="index"
                            class="demo-image__preview"
                            style="
                              width: 35px;
                              height: auto;
                              display: inline-block;
                            "
                          >
                            <el-image
                              :src="item"
                              :preview-src-list="[item]"
                            />
                          </div>
                        </template>
                        <span
                          v-else
                          style="display: inline-block"
                          >无</span
                        >
                      </span>
                    </div>
                  </el-popover>
                </template>
              </div>
              <span v-else>{{ scope.row.statusStr.value }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="下单时间"
          min-width="150"
        />
        <el-table-column
          prop="remark"
          label="订单备注"
          min-width="150"
        />
        <el-table-column
          label="操作"
          min-width="120"
          align="center"
          fixed="right"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              v-hasPermi="['admin:product:rule:delete']"
              >审核地址</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
      <el-dialog
        :title="isCreate === 0 ? '组合商品' : '编辑组合商品'"
        :visible.sync="dialogVisible"
        width="700px"
        z-index="4"
      >
        <creatCombinationProduct
          :key="timer"
          :is-create="isCreate"
          :edit-data="editData"
          @getList="seachList"
        />
      </el-dialog>
    </el-card>
  </div>
</template>
<script>
import { auditAddressListApi } from '@/api/order'
import creatCombinationProduct from './creatCombinationProduct.vue'
import {
  storeCombinationListApi,
  productCombinationStatusUpdateApi,
  productCombinationDeleteApi
} from '@/api/storeMatch'
export default {
  name: 'StoreAttr',
  components: { creatCombinationProduct },
  data() {
    return {
      formDynamic: {
        ruleName: '',
        ruleValue: []
      },
      headeNum: [],
      tableFrom: {
        page: 1,
        limit: 20,
        type: 2,
        storeName: null
      },
      tableData: {
        data: [],
        loading: false,
        total: 0
      },
      listLoading: true,
      selectionList: [],
      multipleSelectionAll: [],
      idKey: 'id',
      nextPageFlag: false,
      keyNum: 0,
      dialogVisible: false,
      isCreate: 0, // 0添加 1编辑
      editData: {},
      timer: ''
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    seachList() {
      this.tableFrom.page = 1
      this.dialogVisible = false
      this.getList()
    },
    // 获取地址审核表单头数量
    goodHeade() {
      productHeadersApi()
        .then((res) => {
          this.headeNum = res
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    handleSelectionChange(val) {
      this.selectionList = val
      setTimeout(() => {
        this.changePageCoreRecordData()
      }, 50)
    },
    // 启用禁用组合
    onchangeIsShow(row) {
      console.log(row.status, 8686868)
      row.status
        ? productCombinationStatusUpdateApi(row.id, 1)
            .then(() => {
              this.$message.success('启用成功')
              this.getList()
            })
            .catch(() => {
              row.status = !row.status
            })
        : productCombinationStatusUpdateApi(row.id, 0)
            .then(() => {
              this.$message.success('禁用成功')
              this.getList()
            })
            .catch(() => {
              row.status = !row.status
            })
    },
    // 设置选中的方法
    setSelectRow() {
      if (!this.multipleSelectionAll || this.multipleSelectionAll.length <= 0) {
        return
      }
      // 标识当前行的唯一键的名称
      const idKey = this.idKey
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })
      this.$refs.table.clearSelection()
      for (var i = 0; i < this.tableData.data.length; i++) {
        if (selectAllIds.indexOf(this.tableData.data[i][idKey]) >= 0) {
          // 设置选中，记住table组件需要使用ref="table"
          this.$refs.table.toggleRowSelection(this.tableData.data[i], true)
        }
      }
    },
    // 记忆选择核心方法
    changePageCoreRecordData() {
      // 标识当前行的唯一键的名称
      const idKey = this.idKey
      const that = this
      // 如果总记忆中还没有选择的数据，那么就直接取当前页选中的数据，不需要后面一系列计算
      if (this.multipleSelectionAll.length <= 0) {
        this.multipleSelectionAll = this.selectionList
        return
      }
      // 总选择里面的key集合
      const selectAllIds = []
      this.multipleSelectionAll.forEach((row) => {
        selectAllIds.push(row[idKey])
      })
      const selectIds = []
      // 获取当前页选中的id
      this.selectionList.forEach((row) => {
        selectIds.push(row[idKey])
        // 如果总选择里面不包含当前页选中的数据，那么就加入到总选择集合里
        if (selectAllIds.indexOf(row[idKey]) < 0) {
          that.multipleSelectionAll.push(row)
        }
      })
      const noSelectIds = []
      // 得到当前页没有选中的id
      this.tableData.data.forEach((row) => {
        if (selectIds.indexOf(row[idKey]) < 0) {
          noSelectIds.push(row[idKey])
        }
      })
      noSelectIds.forEach((id) => {
        if (selectAllIds.indexOf(id) >= 0) {
          for (let i = 0; i < that.multipleSelectionAll.length; i++) {
            if (that.multipleSelectionAll[i][idKey] == id) {
              // 如果总选择中有未被选中的，那么就删除这条
              that.multipleSelectionAll.splice(i, 1)
              break
            }
          }
        }
      })
    },
    add() {
      this.dialogVisible = true
      this.isCreate = 0
      this.timer = new Date().getTime()
    },
    // 列表
    getList() {
      this.listLoading = true
      auditAddressListApi(this.tableFrom)
        .then((res) => {
          const list = res.list
          this.tableData.data = list
          this.tableData.total = res.total
          for (var i = 0; i < list.length; i++) {
            list[i].ruleValue = JSON.parse(list[i].ruleValue)
          }
          this.$nextTick(function () {
            this.setSelectRow() // 调用跨页选中方法
          })
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    pageChange(page) {
      this.changePageCoreRecordData()
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.changePageCoreRecordData()
      this.tableFrom.limit = val
      this.getList()
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure()
        .then(() => {
          productCombinationDeleteApi(id).then(() => {
            this.$message.success('删除成功')
            this.tableData.data.splice(idx, 1)
          })
        })
        .catch(() => {})
    },
    // handleDeleteAll() {
    //   if (!this.multipleSelectionAll.length)
    //     return this.$message.warning('请选择商品规格')
    //   const data = []
    //   this.multipleSelectionAll.map((item) => {
    //     data.push(item.id)
    //   })
    //   this.ids = data.join(',')
    //   this.$modalSure()
    //     .then(() => {
    //       attrDeleteApi(this.ids).then(() => {
    //         this.$message.success('删除成功')
    //         this.getList()
    //       })
    //     })
    //     .catch(() => {})
    // },
    onEdit(val) {
      this.dialogVisible = true
      this.isCreate = 1
      this.editData = { ...val }
    }
  }
}
</script>

<style scoped lang="scss">
.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 35px;
}
.fr {
  float: right;
}
</style>
