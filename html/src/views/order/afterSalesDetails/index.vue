<template>
  <MainCard v-loading="fullscreenLoading" :cardType="3">
    <div class="app-box">
      <div class="base-card after-sale-status">
        <div class="header">
          <span>退款单号：{{ afterSalesInfo.refundNo }}</span>
          <span
            >订单编号：<span style="color: #396fd4">{{
              afterSalesInfo.orderNo
            }}</span></span
          >
        </div>
        <div class="main">
          <div class="left">
            <div class="info-title">订单信息</div>
            <div class="store-list">
              <div
                v-for="(item, index) in afterSalesInfo.productList"
                :key="index"
                class="store-item"
              >
                <MyImage
                  :imagePath="handlerImgUrl(item.productImage)"
                  :previewPath="item.productImage"
                  :size="40"
                />
                <div class="item-info">
                  <div class="store-name">
                    {{ item.productName }}
                  </div>
                  <div class="specification">
                    <span>{{ item.skuName }}</span
                    ><span> x {{ item.skuNum }}</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="details-info">
              <div class="item">
                <span>售后类型：</span
                ><span>{{ afterSalesInfo.aftersaleTypeDesc }}</span>
              </div>
              <div class="item">
                <span>买家：</span
                ><span class="seller-part"
                  ><span style="color: #628be5">{{
                    afterSalesInfo.nickname
                  }}</span
                  ><span
                    ><memberLevelIcon
                      :level="Number(afterSalesInfo.buyerLevel)" /></span
                ></span>
              </div>
              <div class="item">
                <span>收货人：</span
                ><span>{{ afterSalesInfo.receiverName }}</span
                ><span style="margin-left: 10px">{{
                  afterSalesInfo.receiverPhone
                }}</span>
              </div>
              <div class="item">
                <span>物流信息：</span
                ><span>{{ afterSalesInfo.expressName }}</span
                ><span style="margin-left: 10px">{{
                  afterSalesInfo.expressNo
                }}</span>
              </div>
              <div class="item">
                <span>支付方式：</span
                ><span>{{ afterSalesInfo.payTypeDesc }}</span>
              </div>
              <div class="item">
                <span>申请金额：</span
                ><span>{{ "￥" + afterSalesInfo.orderAmount }}</span>
              </div>
              <div class="item">
                <span>运费：</span
                ><span>{{ "￥" + afterSalesInfo.shippingFee }}</span>
              </div>
            </div>
            <div class="details-info">
              <div class="item">
                <span>售后原因：</span
                ><span>{{ afterSalesInfo.aftersaleReason }}</span>
              </div>
              <div class="item">
                <span>问题描述：</span
                ><span>{{ afterSalesInfo.aftersaleExplain }}</span>
              </div>
              <div class="item">
                <span>证明材料：</span>
                <div>
                  <div
                    class="aftersaleImg-list"
                    v-if="afterSalesInfo.aftersaleImages"
                  >
                    <MyImage
                      v-for="(
                        item, index
                      ) in afterSalesInfo.aftersaleImages.split(',')"
                      :key="index"
                      :imagePath="item"
                      :previewPath="item"
                      :size="70"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="right">
            <div class="top">
              <AfterSalesStatus
                v-if="!fullscreenLoading"
                :aftersaleFlow="aftersaleFlow"
              />
            </div>
            <div class="operation-area">
              <div v-if="isShowHandlerAfterSales" class="operation">
                <div class="status-title">买家发起售后，等待审核处理</div>
                <div class="operation-btn">
                  <el-button
                    size="small"
                    @click="handleAfterSales('agree')"
                    type="primary"
                    >同意</el-button
                  >
                  <el-button @click="handleAfterSales('refuse')" size="small"
                    >拒绝</el-button
                  >
                </div>
              </div>
              <div v-if="isShowAfterSalesFinish" class="operation">
                <div class="status-title">售后完成</div>
                <div class="status-des-info">
                  <div>
                    <span>处理方式：</span
                    ><span>{{
                      afterSalesType[aftersaleFlow.finalAftersaleType]
                    }}</span>
                  </div>
                  <div>
                    <span>退款金额：￥</span
                    ><span>{{ aftersaleFlow.refundAmount }}</span>
                  </div>
                </div>
                <div class="operation-btn">
                  <el-button size="small" type="text">查看钱款去向</el-button>
                </div>
              </div>
              <div v-if="isShowReShipment" class="operation">
                <div class="status-title">买家寄回商品</div>
                <div class="status-des-info">
                  <div>
                    <span>快递单号：</span
                    ><span>{{ aftersaleFlow.reshippingExpressNo }}</span>
                  </div>
                  <div>
                    <span>快递公司：</span
                    ><span>{{ aftersaleFlow.returnExpressCompany }}</span>
                  </div>
                  <div>
                    <span>物流信息：</span><span class="link">查看</span>
                  </div>
                  <div></div>
                </div>
                <div class="operation-btn">
                  <el-button size="small" type="primary">重新发货</el-button>
                </div>
              </div>

              <div v-if="isShowRefund" class="operation">
                <div class="status-title">买家退回商品</div>
                <div class="status-des-info">
                  <div>
                    <span>快递单号：</span
                    ><span>{{ aftersaleFlow.reshippingExpressNo }}</span>
                  </div>
                  <div>
                    <span>快递公司：</span
                    ><span>{{ aftersaleFlow.returnExpressCompany }}</span>
                  </div>
                  <div>
                    <span>物流信息：</span><span class="link">查看</span>
                  </div>
                  <div></div>
                </div>
                <div class="operation-btn">
                  <el-button
                    size="small"
                    @click="handleAfterSales('agree')"
                    type="primary"
                    >退款</el-button
                  >
                  <el-button @click="handleAfterSales('refuse')" size="small"
                    >拒绝</el-button
                  >
                </div>
              </div>

              <HandleAfterSales
                ref="handleAfterSales"
                :handleType="handleType"
                :afterSalesInfo="afterSalesInfo"
                @success="getInfo()"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="base-card after-sale-log">
        <div class="info-title">售后日志</div>
        <div v-if="afterSalesInfo.aftersaleLogs" class="log-steps">
          <div
            v-for="(item, index) in afterSalesInfo.aftersaleLogs.toReversed()"
            :key="item.id"
            class="step-item"
          >
            <div class="left">
              <div class="time">
                {{ item.createTime && item.createTime.split(" ")[0] }}
              </div>
              <div class="time">
                {{ item.createTime && item.createTime.split(" ")[1] }}
              </div>
              <div
                v-if="index !== afterSalesInfo.aftersaleLogs.length - 1"
                class="icon"
              >
                <svg-icon class="step_arrow" icon-class="step_arrow" />
              </div>
            </div>
            <div class="right">
              <div class="des-list">
                <div
                  v-if="index === 0"
                  style="font-weight: 700"
                  class="des-item"
                >
                  <span class="value">{{ afterSalesInfo.nickname }}</span>
                  <span class="value">发起了售后申请,等待商家处理</span>
                </div>
                <div
                  v-if="item.changeMessage"
                  style="font-weight: 700"
                  class="des-item"
                >
                  <span class="value">{{ item.changeMessage }}</span>
                </div>
                <div v-if="index === 0" class="des-item">
                  <span class="title">售后方式：</span
                  ><span class="value">{{
                    afterSalesInfo.aftersaleTypeDesc
                  }}</span>
                </div>
                <div v-if="index === 0" class="des-item">
                  <span class="title">售后原因：</span
                  ><span class="value">{{
                    afterSalesInfo.aftersaleReason
                  }}</span>
                </div>
                <div v-if="index === 1" class="des-item">
                  <span class="title">处理描述：</span
                  ><span class="value">{{
                    afterSalesInfo.processingExplain
                  }}</span>
                </div>
                <div v-if="index === 1" class="des-item">
                  <span class="title">补偿退款：</span
                  ><span class="value">{{ item.refundPrice }}</span>
                </div>
                <div
                  v-if="index === 2"
                  style="font-weight: 700"
                  class="des-item"
                >
                  <span class="value">{{ item.aftersaleStatusDesc }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainCard>
</template>

<script>
import { aftersaleDetailApi } from "@/api/afterSales";
import Print from "./Print.vue";
import AfterSalesStatus from "./AfterSalesStatus.vue";
import HandleAfterSales from "./HandleAfterSales.vue";
export default {
  components: {
    Print,
    AfterSalesStatus,
    HandleAfterSales,
  },
  data() {
    return {
      fullscreenLoading: true,
      handleType: "agree",
      afterSalesType: {
        1: "仅退款",
        2: "换货",
        3: "小额补偿",
      },

      isCancelOrder: false,
      orderSettingInfo: [],
      afterSalesInfo: {},
      aftersaleFlow: {},
    };
  },
  mounted() {
    this._init();
  },
  computed: {
    // 是否显示处理售后模块
    isShowHandlerAfterSales() {
      return (
        this.aftersaleFlow.applyStatus &&
        !this.aftersaleFlow.processStatus &&
        !this.aftersaleFlow.returnStatus &&
        !this.aftersaleFlow.completeStatus
      );
    },
    // 是否显示售后完成状态
    isShowAfterSalesFinish() {
      return this.aftersaleFlow.completeStatus;
    },
    // 是否显示重新发货状态
    isShowReShipment() {
      return (
        this.afterSalesInfo.aftersaleTypeDesc === "换货" &&
        this.aftersaleFlow.returnStatus &&
        !this.aftersaleFlow.processStatus &&
        !this.aftersaleFlow.applyStatus &&
        !this.aftersaleFlow.completeStatus
      );
    },
    // 是否显示退款
    isShowRefund() {
      return (
        this.afterSalesInfo.aftersaleTypeDesc === "退货退款" &&
        this.aftersaleFlow.returnStatus &&
        !this.aftersaleFlow.processStatus &&
        !this.aftersaleFlow.applyStatus &&
        !this.aftersaleFlow.completeStatus
      );
    },
  },
  methods: {
    async _init() {
      if (this.$route.params.id) {
        await this.getInfo();
      }
      this.fullscreenLoading = false;
    },
    handleAfterSales(type) {
      this.handleType = type;
      this.$refs.handleAfterSales.dialogVisible = true;
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 详情
    async getInfo() {
      await aftersaleDetailApi(this.$route.params.id)
        .then(async (res) => {
          this.afterSalesInfo = res;
          this.aftersaleFlow = res.aftersaleFlow;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px;
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
  .after-sale-status {
    padding: 0;
    .header {
      height: 50px;
      color: #666666;
      line-height: 50px;
      border-bottom: #e8e8e8 1px solid;
      padding-left: 20px;
      font-size: 14px;
      > span {
        margin-right: 40px;
      }
    }
    .main {
      display: flex;
      .left {
        flex: 1;
        border-right: #e8e8e8 1px solid;
        padding: 20px;
        .store-list {
          padding-left: 13px;
          border-bottom: #e8e8e8 1px solid;
          .store-item {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 12px;
            .store-name {
              font-size: 15px;
              color: #000;
              margin-bottom: 6px;
            }
            .specification {
              font-size: 12px;
              color: #666;
            }
          }
        }
        .details-info {
          font-size: 14px;
          padding: 12px 0;
          &:nth-last-of-type(2) {
            border-bottom: #e8e8e8 1px solid;
          }
          .item {
            + .item {
              margin-top: 12px;
            }
            display: flex;
            > span {
              &:first-child {
                flex-shrink: 0;
                width: 80px;
                color: #666666;
                text-align: right;
              }
            }
            .seller-part {
              display: flex;
              align-items: center;
              gap: 10px;
            }
          }
          .aftersaleImg-list {
            display: flex;
            gap: 10px;
          }
        }
      }
      .right {
        flex: 2;
        .top {
          padding: 40px 0 40px 50px;
        }
        .operation-area {
          border-top: #e8e8e8 1px solid;
          padding: 30px;
          .operation {
            .status-title {
              font-size: 24px;
              margin-bottom: 10px;
            }
            .status-des-info {
              margin-bottom: 10px;
              > div {
                margin-bottom: 4px;
              }
            }
          }
        }
      }
    }
  }
  .after-sale-log {
    .log-steps {
      font-size: 14px;
      padding: 0 10px;
      .step-item {
        margin-bottom: 40px;
        display: flex;
        align-items: flex-start;
        gap: 20px;
        .left {
          .time {
            margin-bottom: 8px;
            font-size: 12px;
            color: #666666;
          }
          .icon {
            margin-top: 20px;
            text-align: center;
            .step_arrow {
              font-size: 26px;
            }
          }
        }
        .right {
          .des-list {
            .des-item {
              margin-bottom: 10px;
            }
          }
        }
      }
    }
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
</style>
