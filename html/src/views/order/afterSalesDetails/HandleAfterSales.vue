<template>
  <el-dialog
    :title="handleType === 'agree' ? '同意售后' : '售后处理'"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="50%"
    top="80px"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div class="right-section">
        <div class="form">
          <!-- 同意售后 -->
          <el-form
            v-if="handleType === 'agree'"
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="120px"
            @submit.native.prevent
          >
            <el-form-item label="售后类型：">
              <div style="font-weight: 700">
                {{ afterSalesInfo.aftersaleTypeDesc }}
              </div>
            </el-form-item>
            <el-form-item label="售后类型：" prop="processingMethod">
              <div>
                <el-radio-group v-model="formValidate.processingMethod">
                  <el-radio-button :label="1">仅退款</el-radio-button>
                  <template
                    v-if="afterSalesInfo.aftersaleTypeDesc !== '仅退款'"
                  >
                    <el-radio-button :label="2">换货</el-radio-button>
                    <el-radio-button :label="3">小额补偿</el-radio-button>
                  </template>
                </el-radio-group>
              </div>
              <div class="des" v-show="formValidate.processingMethod == 1">
                仅退款：不退货仅退款，退款去向为原路返回
              </div>
              <div class="des" v-show="formValidate.processingMethod == 2">
                换货：由买家退回原商品，重新发货
              </div>
              <div class="des" v-show="formValidate.processingMethod == 3">
                小额补偿：直接补偿到用户的余额账户
              </div>
            </el-form-item>
            <el-form-item prop="processingExplain" label="处理描述：">
              <el-input
                :autosize="{ minRows: 4, maxRows: 8 }"
                type="textarea"
                v-model="formValidate.processingExplain"
              ></el-input>
            </el-form-item>
            <el-form-item
              v-show="formValidate.processingMethod == 2"
              prop="processingExplain"
              label="退货地址："
            >
              <el-radio-group v-model="formValidate.storeStatus">
                <div class="radio-group">
                  <el-radio :label="1">
                    <span v-if="addressConfigList.length > 0" class="item">
                      <span>{{ addressConfigList[0].configValue.name }}</span>
                      <span>{{ addressConfigList[0].configValue.mobile }}</span>
                      <span>{{
                        addressConfigList[0].configValue.address
                      }}</span>
                    </span>
                  </el-radio>
                  <!-- <el-radio :label="0">
                    <span class="item">
                      <span>唐女士</span>
                      <span>13945203994</span>
                      <span>甘肃省庆阳市西峰区南街街道</span>
                    </span>
                  </el-radio> -->
                </div>
              </el-radio-group>
            </el-form-item>
            <el-form-item
              v-show="formValidate.processingMethod == 1"
              label="申请金额："
            >
              <div>{{ afterSalesInfo.orderAmount }}</div>
            </el-form-item>
            <el-form-item
              v-show="
                formValidate.processingMethod == 1 ||
                formValidate.processingMethod == 3
              "
              :label="
                formValidate.processingMethod == 1 ? '实退金额：' : '补偿金额：'
              "
              prop="refundPrice"
            >
              <el-input
                v-model.number="formValidate.refundPrice"
                @input="handleInput"
              />
              <div
                v-show="this.formValidate.refundPrice"
                class="capitalized-amount"
              >
                <span class="show-symbol">{{ showSymbol }}</span>
                <span
                  v-show="
                    this.formValidate.refundPrice != '-' &&
                    this.formValidate.refundPrice != '.'
                  "
                  >{{ upperCaseAmount }}</span
                >
              </div>
            </el-form-item>
          </el-form>
          <!-- 拒绝售后 -->
          <el-form
            v-else
            ref="formValidate"
            :rules="ruleValidate"
            :model="formRejectValidate"
            label-width="120px"
            @submit.native.prevent
          >
            <el-form-item label="售后类型：">
              <div style="font-weight: 700">
                {{ afterSalesInfo.aftersaleTypeDesc }}
              </div>
            </el-form-item>
            <el-form-item label="申请金额：">
              <div>{{ afterSalesInfo.orderAmount }}</div>
            </el-form-item>
            <el-form-item prop="rejectReason" label="拒绝理由：">
              <el-input
                :autosize="{ minRows: 4, maxRows: 8 }"
                type="textarea"
                v-model="formValidate.rejectReason"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="btn">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            class="submission"
            @click="handleSubmit('formValidate')"
            >确定</el-button
          >
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { aftersaleProcessApi, aftersaleRejectApi } from "@/api/afterSales";
import { orderSettingListApi } from "@/api/orderSetting";
import { Debounce } from "@/utils/validate";
const defaultObj = () => ({
  processingMethod: 1,
  refundPrice: null,
  processingExplain: null,
});
export default {
  props: {
    handleType: {
      type: String,
      default: "agree",
    },
    afterSalesInfo: {
      type: Object,
      default: () => ({}),
    },
  },
  computed: {
    showSymbol() {
      return this.formValidate.refundPrice >= 0 ? "+" : "-";
    },
    upperCaseAmount() {
      return this.formValidate.refundPrice
        ? this.convertToUpperCase(this.formValidate.refundPrice)
        : "";
    },
  },

  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      addressConfigList: [],
      formRejectValidate: { rejectReason: null },
      ruleValidate: {
        refundPrice: [
          { required: true, message: "请输入", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              if (isNaN(value) || value === null || value === "") {
                callback(new Error("请输入一个合法的数值"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        processingMethod: [
          { required: true, message: "请选择", trigger: "change" },
        ],
        processingExplain: [
          { required: true, message: "请输入", trigger: "blur" },
        ],
        rejectReason: [{ required: true, message: "请输入", trigger: "blur" }],
      },
    };
  },

  methods: {
    handleInput(value) {
      // 如果输入为空，直接返回空值
      if (!value) {
        this.formValidate.refundPrice = "";
        return;
      }
      // 处理负号：只允许开头有一个负号
      let newValue = value;
      if (value.indexOf("-") !== -1) {
        newValue = (value.startsWith("-") ? "-" : "") + value.replace(/-/g, "");
      }

      // 处理数字和小数点：只允许数字和一个小数点，且小数点后最多两位
      newValue = newValue.replace(/[^\d.-]/g, ""); // 只保留数字、小数点和负号

      // 处理多个小数点的情况
      const parts = newValue.split(".");
      if (parts.length > 2) {
        newValue = parts[0] + "." + parts.slice(1).join("");
      }

      // 限制小数点后两位
      if (parts.length === 2) {
        newValue = parts[0] + "." + parts[1].slice(0, 2);
      }

      // 更新值
      this.formValidate.refundPrice = newValue;
    },
    convertToUpperCase(money) {
      if (!money && money !== 0) return "";
      const fraction = ["角", "分"];
      const digit = [
        "零",
        "壹",
        "贰",
        "叁",
        "肆",
        "伍",
        "陆",
        "柒",
        "捌",
        "玖",
      ];
      const unit = [
        ["元", "万", "亿"],
        ["", "拾", "佰", "仟"],
      ];
      // const head = money < 0 ? "负" : "";
      let n = Math.abs(money);

      let s = "";

      for (let i = 0; i < fraction.length; i++) {
        s += (
          digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]
        ).replace(/零./, "");
      }
      s = s || "整";
      n = Math.floor(n);

      for (let i = 0; i < unit[0].length && n > 0; i++) {
        let p = "";
        for (let j = 0; j < unit[1].length && n > 0; j++) {
          p = digit[n % 10] + unit[1][j] + p;
          n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, "").replace(/^$/, "零") + unit[0][i] + s;
      }

      return (
        // head +
        s
          .replace(/(零.)*零元/, "元")
          .replace(/(零.)+/g, "零")
          .replace(/^整$/, "零元整")
      );
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.handleType === "agree") {
            //同意售后
            aftersaleProcessApi({
              ...this.formValidate,
              aftersaleId: this.afterSalesInfo.aftersaleId,
            }).then((res) => {
              this.$message.success("操作成功");
              this.handleClose();
              this.$emit("success");
            });
          } else {
            // 拒绝售后
            aftersaleRejectApi({
              ...this.formRejectValidate,
              aftersaleId: this.afterSalesInfo.aftersaleId,
            }).then((res) => {
              this.$message.success("操作成功");
              this.handleClose();
              this.$emit("success");
            });
          }
        }
      });
    }),
    handlerOpen() {
      this.getOrderSetting();
    },
    // 订单设置
    getOrderSetting() {
      orderSettingListApi()
        .then((res) => {
          const addressConfigList = res.addressConfigList;
          addressConfigList.forEach((element) => {
            element.configValue = JSON.parse(element.configValue);
          });
          this.addressConfigList = addressConfigList;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    handleClose(done) {
      this.dialogVisible = false;
      this.formValidate = Object.assign({}, defaultObj());
      this.$refs.formValidate.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  height: 500px;

  .right-section {
    position: relative;
    flex: 2;
    overflow: hidden;
    padding: 20px;

    .title {
      margin: 10px 0 30px;
    }

    .form {
      padding-right: 60px;
      .capitalized-amount {
        font-size: 16px;
        font-weight: 700;
        height: 36px;
        background-color: #f6e1e1;
        margin-top: 6px;
        padding-left: 10px;
        .show-symbol {
          font-size: 20px;
        }
      }
      .des {
        font-size: 12px;
        color: #555555;
      }
      .radio-group {
        display: flex;
        flex-direction: column;
        gap: 20px;
        align-items: flex-start;
        .item {
          > span {
            margin-right: 16px;
          }
        }
      }
    }
    .btn {
      position: absolute;
      bottom: 20px;
      right: 20px;
    }
  }
}
</style>
