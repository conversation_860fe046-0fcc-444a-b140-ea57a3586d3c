<template>
  <div class="status-steps">
    <div
      v-for="(item, index) in curSteps"
      :key="item.title"
      class="step-item"
    >
      <div class="item-left">
        <svg-icon
          v-show="item.isFinished"
          class="order_finished"
          icon-class="order_finished"
        />
        <div
          v-show="!item.isFinished"
          class="step-no"
        >
          {{ index + 1 }}
        </div>
      </div>
      <div class="item-right">
        <div class="title">
          <div class="step-title">{{ item.title }}</div>
          <div
            v-if="index != curSteps.length - 1"
            :style="`background:${
              item.isFinished && curSteps[index + 1].isFinished
                ? 'linear-gradient(90deg, #dd3f31 0%, #dc5620 100%)'
                : '#d8d8d8'
            }`"
            class="step-line"
          ></div>
        </div>
        <div class="description">
          <div>{{ item.des1 }}</div>
          <div v-show="item.des2">{{ item.des2 }}</div>
          <div v-show="item.des3">{{ item.des3 }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    aftersaleFlow: {
      type: Object,
      default: () => ({ aftersaleType: 1 })
      // aftersaleType 售后类型（1:退货退款,2:换货,3:未发货退款,4:仅退款）
    }
  },
  data() {
    return {
      curSteps: [],
      allAfterStatusSteps: {
        1: [
          {
            title: '买家申请售后',
            des1: '',
            des2: '',
            isFinished: false
          },
          {
            title: '处理退货退款',
            des1: '',
            des2: '',
            isFinished: false
          },
          {
            title: '买家退回商品',
            des1: '',
            des2: '',
            isFinished: false
          },
          {
            title: '交易完成',
            des1: '',
            des2: '',
            isFinished: false
          }
        ],
        2: [
          {
            title: '买家申请售后',
            des1: '',
            isFinished: false
          },
          {
            title: '处理换货',
            des1: '',
            isFinished: false
          },
          {
            title: '买家寄回商品',
            des1: '',
            isFinished: false
          },
          {
            title: '交易完成',
            des1: '',
            isFinished: false
          }
        ],
        4: [
          {
            title: '买家申请售后',
            des1: '',
            isFinished: false
          },
          {
            title: '处理退款',
            des1: '',
            isFinished: false
          },
          {
            title: '交易完成',
            des1: '',
            isFinished: false
          }
        ]
      }
    }
  },

  watch: {
    aftersaleFlow: {
      handler(curFlow) {
        this.curSteps = this.allAfterStatusSteps[curFlow.aftersaleType]
        this.curSteps.forEach((item) => {
          // 退货退款
          if (curFlow.aftersaleType == 1) {
            switch (item.title) {
              case '交易完成':
                if (curFlow.completeStatus === 1) {
                  const time = curFlow.completeTime
                    ? curFlow.completeTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '买家退回商品':
                if (curFlow.returnStatus === 1) {
                  const time = curFlow.returnTime
                    ? curFlow.returnTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '处理退货退款':
                if (curFlow.processStatus === 1) {
                  const time = curFlow.processTime
                    ? curFlow.processTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '买家申请售后':
                if (curFlow.applyStatus === 1) {
                  const time = curFlow.applyTime
                    ? curFlow.applyTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
            }
          } else if (curFlow.aftersaleType == 2) {
            // 换货
            switch (item.title) {
              case '交易完成':
                if (curFlow.completeStatus === 1) {
                  const time = curFlow.completeTime
                    ? curFlow.completeTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '买家寄回商品':
                if (curFlow.returnStatus === 1) {
                  const time = curFlow.returnTime
                    ? curFlow.returnTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '处理换货':
                if (curFlow.processStatus === 1) {
                  const time = curFlow.processTime
                    ? curFlow.processTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '买家申请售后':
                if (curFlow.applyStatus === 1) {
                  const time = curFlow.applyTime
                    ? curFlow.applyTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
            }
          } else if (curFlow.aftersaleType == 4) {
            // 退款
            switch (item.title) {
              case '交易完成':
                if (curFlow.completeStatus === 1) {
                  const time = curFlow.completeTime
                    ? curFlow.completeTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '处理退款':
                if (curFlow.processStatus === 1) {
                  const time = curFlow.processTime
                    ? curFlow.processTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
              case '买家申请售后':
                if (curFlow.applyStatus === 1) {
                  const time = curFlow.applyTime
                    ? curFlow.applyTime.split(' ')
                    : []
                  item.des1 = time[0]
                  item.des2 = time[1]
                  item.isFinished = true
                }
                break
            }
          }
        })
      },
      immediate: true
    }
  },
  method: {}
}
</script>

<style scoped lang="scss">
.status-steps {
  width: 100%;
  display: flex;
  gap: 10px;
  .step-item {
    flex: 1;
    display: flex;
    gap: 8px;
    .item-left {
      .order_finished {
        font-size: 20px;
        margin-top: -2px;
      }
      .step-no {
        width: 20px;
        height: 20px;
        background-color: #e8e8e8;
        border-radius: 50%;
        text-align: center;
        line-height: 20px;
        color: #888;
        margin-top: -2px;
      }
    }
    .item-right {
      font-size: 14px;
      flex: 1;
      .title {
        color: #000;
        display: flex;
        gap: 10px;
        align-items: center;
        .step-line {
          flex: 1;
          height: 1.5px;
          background: linear-gradient(90deg, #dd3f31 0%, #dc5620 100%);
        }
      }
      .description {
        color: #333;
        > div {
          margin-top: 10px;
        }
      }
    }
  }
}
</style>
