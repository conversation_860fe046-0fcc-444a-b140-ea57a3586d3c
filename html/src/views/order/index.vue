<template>
  <MainCard>
    <div class="app-box divBox">
      <div class="search-form">
        <el-form
          label-width="auto"
          inline
          size="small"
        >
          <el-form-item label="订单下单时间：">
            <span class="inline-flex">
              <el-date-picker
                style="width: 360px"
                v-model="time"
                type="datetimerange"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                align="left"
                clearable
              >
              </el-date-picker>
              <el-radio-group
                v-model="tableFrom.dateLimit"
                type="button"
              >
                <el-radio-button label="today">今天</el-radio-button>
                <el-radio-button label="yesterday">昨日</el-radio-button>
                <el-radio-button label="lately7">近7天</el-radio-button>
                <el-radio-button label="lately30">近30天</el-radio-button>
              </el-radio-group>
            </span>
          </el-form-item>
          <el-form-item label="付款方式：">
            <el-select
              clearable
              v-model="tableFrom.payType"
              placeholder="请选择"
            >
              <el-option
                label="微信支付"
                value="weixin"
              ></el-option>
              <el-option
                label="余额支付"
                value="yue"
              ></el-option>
              <el-option
                label="线下支付"
                value="offline"
              ></el-option>
              <el-option
                label="支付宝支付"
                value="alipay"
              ></el-option>
              <el-option
                label="其他支付"
                value="other"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="订单编号：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.orderNo"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="收货人：">
            <el-input
              style="width: 200px !important"
              placeholder="收货人姓名/手机号"
              v-model="tableFrom.receiverKeyword"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="订单类型：">
            <el-select
              clearable
              v-model="tableFrom.type"
              placeholder="请选择"
            >
              <el-option
                label="普通订单"
                :value="0"
              ></el-option>
              <el-option
                label="送礼"
                :value="1"
              ></el-option>
              <el-option
                label="全部订单"
                :value="2"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商品名称：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.storeName"
              clearable
            >
            </el-input>
          </el-form-item>
          <el-form-item label="发货单号：">
            <el-input
              style="width: 200px !important"
              v-model="tableFrom.expressNo"
              clearable
            >
            </el-input>
          </el-form-item>
        </el-form>
        <div class="search-btn">
          <el-button
            @click="getList()"
            size="small"
            type="primary"
            >筛选</el-button
          >
          <el-button
            type="text"
            size="small"
            @click="reset"
            >重置筛选条件</el-button
          >
        </div>
      </div>
      <div class="tabs">
        <el-tabs
          v-model="tableFrom.status"
          @tab-click="seachList"
        >
          <el-tab-pane
            :label="item.name"
            :name="item.type"
            v-for="item in tabsItems"
            :key="item.typedex"
          />
        </el-tabs>
      </div>
      <div class="table-list">
        <div class="table-header">
          <div
            style="width: 30%"
            class="item"
          >
            商品信息
          </div>
          <div
            style="width: 10%; text-align: center"
            class="item"
          >
            实收金额
          </div>
          <div
            style="width: 20%"
            class="item"
          >
            买家/收货人
          </div>
          <div
            style="width: 15%; text-align: center"
            class="item"
          >
            订单状态
          </div>
          <div
            style="width: 15%; text-align: center"
            class="item"
          >
            订单类型
          </div>
          <div
            style="width: 10%; text-align: right"
            class="item"
          >
            操作
          </div>
        </div>
        <div
          class="order-info"
          v-loading="listLoading"
        >
          <div
            v-for="(item, index) in tableData.data"
            :key="index"
            class="info-item"
          >
            <el-table
              :data="[item]"
              ref="table"
              border
              :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
              style="width: 100%"
              :highlight-current-row="true"
            >
              <el-table-column
                prop="add_time"
                label="时间"
              >
                <template slot="header">
                  <div class="info-item-header">
                    <div class="left">
                      <div>
                        订单号：<span class="order-no">
                          {{ item.orderId }}</span
                        >
                      </div>
                      <div>
                        下单时间：<span>{{ item.createTime }}</span>
                      </div>
                      <div>{{ item.payTypeStr }}</div>
                    </div>
                    <div class="right">
                      <router-link
                        :to="{
                          path: `/order/orderList/orderDetails/${item.orderId}?id=${item.id}`
                        }"
                        >订单详情</router-link
                      >
                    </div>
                  </div>
                </template>
                <template slot-scope="scope">
                  <div class="item-content">
                    <div
                      style="width: 30%"
                      class="item store-list"
                    >
                      <template
                        v-if="
                          scope.row.productList && scope.row.productList.length
                        "
                      >
                        <div
                          v-for="(item1, index1) in scope.row.productList"
                          :key="index1"
                          class="store-item"
                        >
                          <MyImage
                            :imagePath="handlerImgUrl(item1.info.image)"
                            :previewPath="item1.info.image"
                            :size="40"
                          />
                          <div class="item-info">
                            <div class="store-name flex">
                              <div class="left">
                                {{ item1.info.productName }}
                              </div>
                              <div class="right">{{ item1.info.price }}</div>
                            </div>
                            <div class="specification flex">
                              <div class="left">{{ item1.info.sku }}</div>
                              <div class="right">
                                {{ 'x' + item1.info.payNum }}
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                    <div
                      style="width: 10%; text-align: center"
                      class="item"
                    >
                      {{ '￥' + scope.row.payPrice }}
                    </div>
                    <div
                      style="width: 20%"
                      class="item"
                    >
                      <div class="seller-part">
                        <span class="seller"
                          ><span>买家：</span
                          ><span style="color: #628be5">{{
                            scope.row.buyerName
                          }}</span></span
                        >
                        <span>
                          <memberLevelIcon
                            :level="Number(scope.row.buyerLevel)"
                        /></span>
                      </div>
                      <div>
                        <span>收货人：</span>
                        <span class="consignee"
                          >{{ scope.row.realName }}（{{
                            scope.row.userPhone
                          }}）</span
                        >
                      </div>
                    </div>
                    <div
                      style="width: 15%; text-align: center"
                      class="item"
                    >
                      {{ scope.row.statusStr.value }}
                    </div>
                    <div
                      style="width: 15%; text-align: center"
                      class="item"
                    >
                      {{ scope.row.orderType }}
                    </div>
                    <div
                      style="width: 10%"
                      class="item"
                    >
                      <div
                        v-if="scope.row.statusStr.value === '待发货'"
                        class="opration-btn"
                      >
                        <Print :orderId="item.id" />
                        <ConfirmShipment
                          :orderId="item.orderId"
                          @success="getList()"
                        />
                      </div>
                      <div
                        v-else-if="
                          scope.row.statusStr.value === '待收货' ||
                          scope.row.statusStr.value === '已完成'
                        "
                        class="opration-btn"
                      >
                        <!-- <el-button size="small">物流跟踪</el-button> -->
                        <span>
                          <Logistics :deliveryId="scope.row.deliveryId" />
                        </span>
                      </div>
                      <div
                        v-else
                        style="text-align: center"
                      >
                        无
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div
            class="not-data"
            v-if="!tableData.data.length"
          >
            暂无数据
          </div>
        </div>
      </div>

      <div class="pagination">
        <el-pagination
          :page-sizes="[10, 20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>

<script>
import { orderListApi } from '@/api/order'
import Print from './orderDetails/Print.vue'
import ConfirmShipment from './orderDetails/ConfirmShipment.vue'
import Logistics from './logistics'
export default {
  components: {
    Print,
    ConfirmShipment,
    Logistics
  },
  data() {
    return {
      time: [],
      statistics: {
        incomeTotal: 0.0,
        expenditureTotal: 0.0
      },
      listLoading: false,
      tabsItems: [
        { name: '全部', type: 'All' },
        { name: '待付款', type: 'Unpaid' },
        { name: '待发货', type: 'Unshipped' },
        { name: '已发货', type: 'Shipped' },
        { name: '已完成', type: 'Completed' },
        { name: '已取消', type: 'Closed' }
        // { name: "已关闭", type: "Closed" },
      ],
      tableFrom: {
        page: 1,
        limit: 10,
        type: 2,
        dateLimit: '',
        payType: '',
        // billType: null,
        orderTimeStart: null,
        orderTimeEnd: null,
        orderNo: null,
        receiverKeyword: null,
        storeName: null,
        expressNo: null,
        status: 'All'
        // uid: null,
      },
      tableData: {
        data: [],
        total: 0
      }
    }
  },
  props: {
    id: {
      type: Number,
      default: null
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    seachList() {
      this.tableFrom.page = 1
      this.getList()
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },
    // 列表
    getList() {
      if (this.time) {
        this.tableFrom.orderTimeStart = this.time[0]
        this.tableFrom.orderTimeEnd = this.time[1]
      } else {
        this.tableFrom.orderTimeStart = null
        this.tableFrom.orderTimeEnd = null
      }
      this.listLoading = true
      orderListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list || []
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 10,
        type: 2,
        dateLimit: '',
        payType: '',
        orderTimeStart: null,
        orderTimeEnd: null,
        orderNo: null,
        receiverKeyword: null,
        storeName: null,
        expressNo: null,
        status: 'All'
      }
      this.time = []
      this.getList()
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    }
  }
}
</script>

<style lang="scss" scoped>
.app-box {
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    white-space: nowrap;
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px 20px;
    margin-bottom: 10px;
    .search-btn {
      margin-left: 110px;
    }
  }
  .table-list {
    ::v-deep.el-table--medium td {
      padding: 0;
    }
    .table-header {
      display: flex;
      height: 36px;
      background-color: #f5f5f5;
      color: #444;
      border-radius: 5px 5px 0 0;
      border-bottom: 1px solid #e0e0e0;
      align-items: center;
      padding: 0 10px;
    }
    .order-info {
      .info-item {
        margin-top: 12px;
        ::v-deep.el-table--mini td {
          padding: 0;
        }
        ::v-deep .el-table td .cell {
          padding-right: 0;
          padding-left: 0;
        }
        .info-item-header {
          display: flex;
          justify-content: space-between;
          .left {
            display: flex;
            gap: 30px;
            .order-no {
              color: #3f6fd6;
            }
          }
          .right {
            color: #3f6fd6;
            cursor: pointer;
            &:hover {
              color: #628be5;
            }
          }
        }
        .item-content {
          display: flex;
          .item {
            /* padding-right: 10px; */
            & + .item {
              padding: 10px;
              border-left: 1px solid #e0e0e0;
            }
            .seller-part {
              display: flex;
              align-items: center;
              gap: 10px;
            }

            .consignee {
              color: #000;
            }
          }
          .store-list {
            .store-item {
              & + .store-item {
                border-top: 1px solid #e0e0e0;
              }
              display: flex;
              gap: 10px;
              align-items: center;
              padding: 10px;
              .item-info {
                flex: 1;
                .flex {
                  display: flex;
                  justify-content: space-between;
                  align-content: center;
                }
                .store-name {
                  color: #000;
                }
                .specification {
                  font-size: 12px;
                }
              }
            }
          }
          .opration-btn {
            .el-button + .el-button {
              margin-left: 0px !important;
            }
            display: flex;
            flex-direction: column;
            gap: 10px;
            /* text-align: center; */
          }
        }
      }
    }
    .not-data {
      text-align: center;
      padding: 20px 0;
      color: #888888;
      border-bottom: 1px solid #dfe6ec;
    }
  }
  .pagination {
    margin-top: 16px;
    text-align: right;
  }
}
</style>
