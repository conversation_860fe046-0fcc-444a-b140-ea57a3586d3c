<template>
  <MainCard v-loading="isLoading" :cardType="2">
    <div v-if="!isLoading" class="app-box">
      <el-form
        ref="formValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 地址管理 start -->
          <div class="base-card">
            <div class="title-info">
              <div>
                <div class="title">地址管理</div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item
                v-for="(item, index) in formValidate.addressConfigList"
                :key="index"
              >
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">{{ item.configName }}：</span>
                  </div>
                  <div class="right">
                    <el-table
                      class="table-list"
                      v-loading="listLoading"
                      :data="[item.configValue]"
                      ref="table"
                      :header-cell-style="{
                        color: '#444',
                        lineHeight: 1,
                      }"
                      style="width: 100%"
                      size="mini"
                      :highlight-current-row="true"
                    >
                      <el-table-column label="收货人">
                        <template slot-scope="scope">
                          <el-form-item>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.name"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="电话" width="140">
                        <template slot-scope="scope">
                          <el-form-item>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.mobile"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="邮编">
                        <template slot-scope="scope">
                          <el-form-item>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.postCode"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column label="省、市、区">
                        <template slot-scope="scope">
                          <el-form-item>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.province"
                              placeholder="省"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.city"
                              class="selWidth"
                              placeholder="市"
                              size="small"
                            >
                            </el-input>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.area"
                              placeholder="区"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>

                      <el-table-column label="详细地址" width="200">
                        <template slot-scope="scope">
                          <el-form-item>
                            <el-input
                              style="width: 100%"
                              v-model="scope.row.address"
                              class="selWidth"
                              size="small"
                            >
                            </el-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 地址管理 end -->
          <!-- 交易设置 start -->
          <div class="base-card">
            <div class="title-info">
              <div>
                <div class="title">交易设置</div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">付款减库存预占时间：</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>拍下后</span>
                      <el-input
                        v-model="
                          formValidate.tradeConfigList[0].configValue.value
                        "
                      >
                        <template slot="append">分钟</template>
                      </el-input>
                      <span>内未付款，库存自动释放，方便其他消费者下单；</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">待付款订单取消时间：</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>拍下未付款订单</span>
                      <el-input
                        v-model="
                          formValidate.tradeConfigList[1].configValue.value
                        "
                      >
                        <template slot="append">分钟</template>
                      </el-input>
                      <span>内未付款，自动取消订单；</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">发货后自动确认收货时间：</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>发货后</span>
                      <el-input
                        v-model="
                          formValidate.tradeConfigList[2].configValue.value
                        "
                      >
                        <template slot="append">天</template>
                      </el-input>
                      <span>自动确认收货；</span>
                    </span>
                    <div class="des">{{ getAutoConfirmText() }}</div>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">买家申请售后限制：</span>
                  </div>
                  <div class="right">
                    <span class="inline-flex">
                      <span>买家确认收货</span>
                      <el-input
                        v-model="
                          formValidate.tradeConfigList[3].configValue.value
                        "
                      >
                        <template slot="append">天</template>
                      </el-input>
                      <span>后，不支持买家申请售后；</span>
                    </span>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 交易设置 end -->
          <!-- 打单设置 start -->
          <div class="base-card">
            <div class="title-info">
              <div>
                <div class="title">打单设置</div>
              </div>
            </div>
            <div class="form-info">
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">支持打印商品清单：</span>
                  </div>
                  <div class="right">
                    <el-radio-group
                      v-model="
                        formValidate.printConfigList[0].configValue.enabled
                      "
                    >
                      <el-radio :label="1"> 开启 </el-radio>
                      <el-radio :label="0"> 关闭 </el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div class="form-item-box">
                  <div class="left">
                    <span class="name">物流查询AppKey：</span>
                  </div>
                  <div class="right">
                    <el-input
                      v-model="
                        formValidate.printConfigList[1].configValue.api_key
                      "
                    >
                    </el-input>
                  </div>
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 打单设置 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="getOrderSetting">重置</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import { orderSettingListApi, saveOrderSettingForm } from "@/api/orderSetting";
import { Debounce } from "@/utils/validate";

export default {
  data() {
    return {
      formValidate: {
        addressConfigList: [{}, {}],
        tradeConfigList: [{}, {}, {}, {}],
        printConfigList: [{}, {}],
      },
      isLoading: true,
      // 订单流程信息，包含交易完成时间
      orderFlow: {
        completeTime: null // 这里应该从API获取实际的交易完成时间
      }
    };
  },

  computed: {
    // 计算自动确认收货的截止时间
    autoConfirmTime() {
      if (this.formValidate.tradeConfigList && 
          this.formValidate.tradeConfigList[2] && 
          this.formValidate.tradeConfigList[2].configValue && 
          this.orderFlow.completeTime) {
        
        const days = this.formValidate.tradeConfigList[2].configValue.value;
        const completeTime = new Date(this.orderFlow.completeTime);
        const autoConfirmTime = new Date(completeTime.getTime() + days * 24 * 60 * 60 * 1000);
        
        return this.formatDateTime(autoConfirmTime);
      }
      return '';
    },

    // 计算剩余天数
    remainingDays() {
      if (this.formValidate.tradeConfigList && 
          this.formValidate.tradeConfigList[2] && 
          this.formValidate.tradeConfigList[2].configValue && 
          this.orderFlow.completeTime) {
        
        const days = parseInt(this.formValidate.tradeConfigList[2].configValue.value);
        const completeTime = new Date(this.orderFlow.completeTime);
        const autoConfirmTime = new Date(completeTime.getTime() + days * 24 * 60 * 60 * 1000);
        const now = new Date();
        const diffTime = autoConfirmTime.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        return Math.max(0, diffDays);
      }
      return 0;
    },

    // 获取配置的天数
    autoConfirmDays() {
      if (this.formValidate.tradeConfigList && 
          this.formValidate.tradeConfigList[2] && 
          this.formValidate.tradeConfigList[2].configValue) {
        return this.formValidate.tradeConfigList[2].configValue.value;
      }
      return 0;
    }
  },

  mounted() {
    this.getOrderSetting();
  },

  methods: {
    // 格式化日期时间
    formatDateTime(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      const hours = String(d.getHours()).padStart(2, '0');
      const minutes = String(d.getMinutes()).padStart(2, '0');
      const seconds = String(d.getSeconds()).padStart(2, '0');
      
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    },

    // 格式化日期（只显示年月日）
    formatDate(date) {
      if (!date) return '';
      const d = new Date(date);
      const year = d.getFullYear();
      const month = String(d.getMonth() + 1).padStart(2, '0');
      const day = String(d.getDate()).padStart(2, '0');
      
      return `${year}-${month}-${day}`;
    },

    // 获取自动确认收货的提示文本
    getAutoConfirmText() {
      if (this.remainingDays > 0) {
        return `买家如果在${this.autoConfirmDays}天内未确认收货，交易将自动完成。剩余${this.remainingDays}天，预计${this.formatDate(this.autoConfirmTime)}自动确认收货`;
      } else if (this.remainingDays === 0) {
        return `买家如果在${this.autoConfirmDays}天内未确认收货，交易将自动完成。今天将自动确认收货`;
      } else {
        return `买家确认收货期限已过，交易已自动完成`;
      }
    },

    // 详情
    getOrderSetting() {
      orderSettingListApi()
        .then((res) => {
          this.isLoading = false;
          const data = {
            addressConfigList: res.addressConfigList,
            tradeConfigList: res.tradeConfigList,
            printConfigList: res.printConfigList,
          };
          for (const key in data) {
            data[key].forEach((element) => {
              element.configValue = JSON.parse(element.configValue);
            });
          }
          this.formValidate = data;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.isLoading = true;
      const data = JSON.parse(JSON.stringify(this.formValidate));
      for (const key in data) {
        data[key].forEach((element) => {
          element.configValue = JSON.stringify(element.configValue);
        });
      }
      saveOrderSettingForm(data)
        .then(async (res) => {
          this.$message.success("保存成功");
          this.isLoading = false;
        })
        .catch((res) => {
          this.isLoading = false;
        });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px;
  }

  /* .form-info {
    padding-left: 80px;
  } */

  .title-info {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    display: flex;
    .des {
      font-weight: normal;
      font-size: 12px;
      margin-top: 8px;
      color: #888888;
    }
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      margin-right: 8px;
      transform: translateY(1px);
    }
  }
  .form-item-box {
    display: flex;
    .left {
      flex: 0 0 180px;
      .name {
        position: relative;
        &::before {
          content: "";
          position: absolute;
          display: inline-block;
          width: 36px;
          height: 5px;
          border-radius: 2.5px;
          background-color: #dd412f;
          opacity: 0.31;
          bottom: 0;
          left: 0;
        }
      }
    }
    .right {
      flex: 1;
      .table-list {
        border: 1px solid #dcdfe6;
        border-radius: 4px;
      }
      .des {
        font-size: 12px;
        color: #888888;
      }
    }
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    white-space: nowrap;
  }
  .flex-colomn {
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    gap: 16px;
  }
  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
