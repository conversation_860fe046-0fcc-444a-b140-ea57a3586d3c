<template>
  <el-button @click="handlePrint" size="small"
    >打单</el-button
  >
</template>

<script>
import { printHiddenContent } from "@/utils/printUtil";
import { getPrintOrderApi } from "@/api/order";
export default {
  directives: { print },
  props: {
    orderId: {
      type: String,
      default: null,
    },
  },
  mounted() {
    // this.getPrintOrder();
  },
  methods: {
    async handlePrint() {
      const html = await this.getPrintOrder();
      const content = html;
      printHiddenContent(content);
    },
    // 获取打印单
    async getPrintOrder() {
      const res = await getPrintOrderApi({ orderId: this.orderId }).catch(
        (res) => {
          this.$message.error(res.message);
        }
      );
      return res;
    },
  },
};
</script>

<style>
@page {
  margin: 0; /* 隐藏页眉页脚的关键 */
}

/* 打印样式优化（可选） */
@media print {
  #printContent {
    width: 100%;
    opacity: 1;
    /* 可添加打印时的样式，如边距、字体大小等 */
  }
  /* 隐藏打印时不需要的元素（如按钮） */
  button {
    display: none;
  }
}
</style>
