<template>
  <el-button @click="handleShipment" type="primary" size="small">确认发货</el-button>
</template>

<script>
import { batchDeliveryApi } from "@/api/order";
export default {
  props: {
    orderId: {
      type: String,
      default: null,
    },
  },
  methods: {
    async handleShipment() {
      await batchDeliveryApi({ orderIds: this.orderId }).catch((res) => {
        this.$message.error(res.message);
      });
      this.$message.success("发货成功");
      this.$emit("success");
    },
  },
};
</script>

<style></style>
