<template>
  <el-dialog
    title="物流跟踪"
    top="50px"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">123</div>
  </el-dialog>
</template>

<script>
import { getDeliveryPath } from "@/api/order";
export default {
  props: {
    deliveryId: {
      type: String,
      default:null,
    },
  },
  data() {
    return {
      dialogVisible: false,
      pathInfos: [],
    };
  },
  methods: {
    handlerOpen() {
      this.getList();
    },
    // 列表
    getList() {
      getDeliveryPath({
        // waybillId: '73563073369976',
        waybillId: this.deliveryId,
      })
        .then((res) => {
         this.pathInfos = res.pathInfos
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  min-height: 600px;
}
</style>
