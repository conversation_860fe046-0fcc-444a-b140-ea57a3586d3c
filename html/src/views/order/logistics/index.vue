<template>
  <span class="logistics">
    <el-button
      @click="showLogisticsInfo()"
      v-if="buttonType === 'button'"
      size="small"
      >物流跟踪</el-button
    >
    <span @click="showLogisticsInfo()" v-else class="link">查看</span>
    <LogisticsInfo :deliveryId="deliveryId" ref="logisticsInfo" />
  </span>
</template>

<script>
import LogisticsInfo from "./LogisticsInfo.vue";
export default {
  components: { LogisticsInfo },
  props: {
    buttonType: {
      type: String,
      default: "button",
    },
    deliveryId: {
      type: String,
      default: null,
    },
  },
  methods: {
    // 显示物流
    showLogisticsInfo(row) {
      this.$refs.logisticsInfo.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.link {
  color: #3A6CDC;
  &:hover {
    color: #5080e6;
  }
}
</style>
