<template>
  <el-dialog
    v-if="dialogVisible"
    title="运费模板"
    @open="handlerOpen"
    :visible.sync="dialogVisible"
    width="1000px"
    :before-close="handleClose"
  >
    <div>
      <el-form
        ref="ruleForm"
        :model="ruleForm"
        label-width="120px"
        :rules="rules"
      >
        <el-form-item style="width: 60%" label="模板名称：" prop="name">
          <el-input
            v-model="ruleForm.name"
            class="withs"
            placeholder="请输入模板名称"
          />
        </el-form-item>
        <el-form-item label="计费方式：" prop="type">
          <el-radio-group
            v-model="ruleForm.type"
            @change="changeRadio(ruleForm.type)"
          >
            <el-radio :label="1">按件数</el-radio>
            <!-- <el-radio :label="2">按重量</el-radio>
            <el-radio :label="3">按体积</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="配送区域：" prop="region">
          <div class="table-box">
            <el-table
              v-loading="listLoading"
              :data="ruleForm.region"
              fit
              highlight-current-row
              style="width: 100%"
              size="mini"
              class="tempBox"
            >
              <el-table-column label="可配送区域" min-width="260">
                <template slot-scope="scope">
                  <div class="area-config">
                    <div class="left">
                      {{ scope.row.cityNames }}
                    </div>
                    <div class="right">
                      <i
                        @click="selectCity(scope.row.cityNames, scope.$index)"
                        class="el-icon-edit"
                      ></i
                      ><i
                        @click="confirmEdit(ruleForm.region, scope.$index)"
                        class="el-icon-delete"
                      ></i>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column
                min-width="120px"
                align="center"
                label="运费（元）"
                prop="firstPrice"
              >
                <template slot-scope="scope">
                  <el-form-item
                    :rules="rules.firstPrice"
                    :prop="'region.' + scope.$index + '.firstPrice'"
                  >
                    <el-input-number
                      v-model="scope.row.firstPrice"
                      controls-position="right"
                      :min="0"
                    />
                  </el-form-item>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-form-item>
        <el-form-item>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-edit"
            @click="addRegion(ruleForm.region)"
          >
            添加配送区域
          </el-button>
        </el-form-item>
        <el-form-item label="是否启用：">
          <el-switch
            v-model="ruleForm.status"
            :active-value="1"
            :inactive-value="0"
          />
        </el-form-item>
        <el-form-item style="width: 60%" label="排序">
          <el-input
            v-model="ruleForm.sort"
            class="withs"
            placeholder="请输入排序"
          />
        </el-form-item>
      </el-form>
      <SelectCity
        :cityList="cityList"
        :curSelectCityNames="curSelectCityNames"
        @selectResult="handlerSelectResult"
        ref="SelectCity"
      />
    </div>
    <span slot="footer" class="dialog-footer">
      <el-button @click="onClose('ruleForm')">取 消</el-button>
      <el-button
        type="primary"
        :loading="loading"
        @click="onsubmit('ruleForm')"
        v-hasPermi="['admin:shipping:templates:update']"
        >确 定</el-button
      >
    </span>
  </el-dialog>
</template>

<script>
import * as logistics from "@/api/logistics";
import { Loading } from "element-ui";
import { Debounce } from "@/utils/validate";
import SelectCity from "./selectCity.vue";
const defaultRole = {
  name: "",
  type: 1,
  appoint: false,
  sort: 0,
  status: 1,
  region: [
    {
      first: 0,
      firstPrice: 0,
      renewal: 0,
      renewalPrice: 0,
      city_ids: [],
      cityNames: "",
    },
  ],
  curHanlerItemIndex: null,

  undelivery: 0,
  free: [],
  undelives: {},
  city_id3: [],
};
const kg = "重量（kg）";
const m = "体积（m³）";
const statusMap = [
  {
    title: "首件",
    title2: "续件",
    title3: "包邮件数",
  },
  {
    title: `首件${kg}`,
    title2: `续件${kg}`,
    title3: `包邮${kg}`,
  },
  {
    title: `首件${m}`,
    title2: `续件${m}`,
    title3: `包邮${m}`,
  },
];
export default {
  name: "CreatTemplates",
  components: { SelectCity },
  data() {
    return {
      loading: false,
      rules: {
        name: [{ required: true, message: "请输入模板名称", trigger: "blur" }],
        free: [
          {
            type: "array",
            required: true,
            message: "请至少添加一个地区",
            trigger: "change",
          },
        ],
        appoint: [
          { required: true, message: "请选择是否指定包邮", trigger: "change" },
        ],
        undelivery: [
          {
            required: true,
            message: "请选择是否指定区域不配送",
            trigger: "change",
          },
        ],
        type: [
          { required: true, message: "请选择计费方式", trigger: "change" },
        ],
        region: [
          { required: true, message: "请选择活动区域", trigger: "change" },
        ],
        city_id3: [
          {
            type: "array",
            required: true,
            message: "请至少选择一个地区",
            trigger: "change",
          },
        ],
        first: [{ required: true, message: "请输入", trigger: "blur" }],
        renewal: [{ required: true, message: "请输入", trigger: "blur" }],
        firstPrice: [
          { required: true, message: "请输入运费", trigger: "blur" },
        ],
        renewalPrice: [
          { required: true, message: "请输入续费", trigger: "blur" },
        ],
      },
      nodeKey: "city_id",
      props: {
        // children: 'child',
        children: "child1",
        label: "name",
        value: "cityId",
        multiple: true,
      },
      dialogVisible: false,
      ruleForm: Object.assign({}, defaultRole),
      listLoading: false,
      curHanlerItemIndex: null,
      curSelectCityNames: null,
      cityList: [],
      columns: {
        title: "首件",
        title2: "续件",
        title3: "包邮件数",
      },
      tempId: 0,
      type: 0, // 0添加 1编辑
    };
  },
  mounted() {
    setTimeout(() => {
      let cityList = JSON.parse(sessionStorage.getItem("cityList"));
      this.cityList = cityList;
    }, 1000);
  },
  methods: {
    handlerOpen() {
      this.getCityList();
    },
    handlerSelectResult(selectData) {
      if (selectData && selectData.length > 0) {
        if (selectData.length === this.cityList.length) {
          this.ruleForm.region[this.curHanlerItemIndex].cityNames = "全国";
        } else {
          this.ruleForm.region[this.curHanlerItemIndex].cityNames =
            selectData.join();
        }
      }
    },
    selectCity(names, index) {
      this.curHanlerItemIndex = index;
      this.curSelectCityNames = names;
      this.$refs.SelectCity.dialogVisible = true;
    },

    onClose(formName) {
      this.dialogVisible = false;
      this.$refs[formName].resetFields();
    },
    confirmEdit(row, index) {
      row.splice(index, 1);
    },
    handleClose() {
      // this.$refs['ruleForm'].resetFields()
      this.dialogVisible = false;
      this.ruleForm = {
        name: "",
        type: 1,
        appoint: false,
        sort: 0,
        region: [
          {
            first: 0,
            firstPrice: 0,
            renewal: 0,
            renewalPrice: 0,
            city_ids: [],
            cityNames: "",
          },
        ],
        undelivery: 0,
        free: [],
        undelives: {},
        city_id3: [],
      };
    },
    changeRadio(num) {
      this.columns = Object.assign({}, statusMap[num - 1]);
    },
    // 添加配送区域
    addRegion(region) {
      region.push(
        Object.assign(
          {},
          {
            first: 0,
            firstPrice: 0,
            renewal: 0,
            renewalPrice: 0,
            city_ids: [],
            cityNames: "",
          }
        )
      );
    },

    // 列表
    async getCityList() {
      await logistics
        .cityListTree()
        .then((res) => {
          this.cityList = res;
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },

    onsubmit: Debounce(function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true;
          const param = {
            appoint: this.ruleForm.appoint,
            name: this.ruleForm.name,
            sort: this.ruleForm.sort,
            type: this.ruleForm.type,
            status: this.ruleForm.status,
            // 配送区域及运费
            shippingTemplatesRegionRequestList: [],
            // // 指定包邮设置
            // shippingTemplatesFreeRequestList: []
          };

          if (this.ruleForm.region.length > 0) {
            this.ruleForm.region.forEach((item) => {
              if (item.cityNames === "全国") {
                const cityId = this.cityList
                  .map((item1) => item1.cityId)
                  .join();
                param.shippingTemplatesRegionRequestList.push({
                  ...item,
                  cityId,
                  title: "全国",
                });
              } else {
                const cityId = this.cityList
                  .filter((item1) =>
                    item.cityNames.split(",").includes(item1.name)
                  )
                  .map((item2) => item2.cityId)
                  .join();
                param.shippingTemplatesRegionRequestList.push({
                  ...item,
                  cityId,
                  title: item.cityNames,
                });
              }
            });
          }
          logistics.shippingSave(param).then((res) => {
            this.$message.success("操作成功");
            this.dialogVisible = false;
            setTimeout(() => {
              this.$emit("getList");
            }, 600);
            this.loading = false;
          });
        } else {
          return false;
        }
      });
    }),
    clear() {
      this.ruleForm.name = "";
      this.ruleForm.sort = 0;
    },
  },
};
</script>

<style scoped lang="scss">
.withs {
  width: 50%;
}
.noBox {
  ::v-deep.el-form-item__content {
    margin-left: 0 !important;
  }
}
.tempBox {
  ::v-deep.el-input-number--mini {
    width: 100px !important;
  }
}
.table-box {
  border: 1px solid #dcdfe6;
  padding: 10px;
  border-radius: 4px;
  .area-config {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 40px;
    .right {
      flex-shrink: 0;
      i {
        cursor: pointer;
        &:hover {
          color: #dc3c2e;
        }
      }
      i:first-child {
        margin-right: 20px;
      }
    }
  }
}
</style>
