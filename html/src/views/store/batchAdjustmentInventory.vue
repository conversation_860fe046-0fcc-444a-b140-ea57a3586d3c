<template>
  <el-dialog
    title="补充库存"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="500px"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <div class="right-section">
        <div class="form">
          <el-form
            ref="formValidate"
            :rules="ruleValidate"
            :model="formValidate"
            label-width="80px"
            @submit.native.prevent
          >
            <el-form-item
              label="库存："
              prop="quantity"
              class="proCoupon"
            >
              <el-input v-model="formValidate.quantity" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
    <div slot="footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button
        type="primary"
        class="submission"
        @click="handleSubmit('formValidate')"
        >保存</el-button
      >
    </div>
  </el-dialog>
</template>

<script>
import { batchOperationStock } from '@/api/store'
import { Debounce } from '@/utils/validate'
const defaultObj = () => ({
  quantity: null
})
export default {
  props: {
    selectStoreId: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      formValidate: Object.assign({}, defaultObj()),
      ruleValidate: {
        quantity: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  methods: {
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          batchOperationStock({
            ...this.formValidate,
            operationType: 'set',
            productIds: this.selectStoreId
          }).then((res) => {
            this.$message.success('操作成功')
            this.handleClose()
            this.$emit('success')
          })
        }
      })
    }),

    handlerOpen() {
      // console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false
      this.formValidate = Object.assign({}, defaultObj())
      this.$refs.formValidate.resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.content {
  height: 200px;
}
</style>
