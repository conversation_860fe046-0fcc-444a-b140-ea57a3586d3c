<template>
  <MainCard>
    <div class="divBox relative">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link :to="{ path: '/store/list/creatProduct' }">
                <el-button
                  type="primary"
                  class="mr10"
                  >发布产品</el-button
                >
              </router-link>
            </template>
          </MyHeaderSearch>
        </div>
        <div class="search-form">
          <el-form
            inline
            label-width="auto"
            size="small"
          >
            <el-form-item
              label="产品分类："
              prop="cateIds"
            >
              <el-select
                clearable
                v-model="tableFrom.cateId"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in merCateList"
                  :key="item.id.toString()"
                  :label="item.name"
                  :value="item.id.toString()"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="产品名称：">
              <el-input
                style="width: 100px"
                v-model="tableFrom.keywords"
                placeholder="商品名称、商品ID"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="分组标签：">
              <el-select
                clearable
                v-model="tableFrom.tag"
                placeholder="请选择"
              >
                <el-option
                  label="人气爆款"
                  :value="1"
                >
                </el-option>
                <el-option
                  label="热销推荐"
                  :value="2"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="search-btn">
            <el-button
              @click="seachList"
              size="small"
              type="primary"
              >筛选</el-button
            >
            <el-button
              size="small"
              @click="exports"
              >导出</el-button
            >

            <el-button
              type="text"
              size="small"
              @click="reset"
              >重置筛选条件</el-button
            >
          </div>
        </div>
        <div class="tabs">
          <el-tabs
            v-model="tableFrom.type"
            @tab-click="seachList"
          >
            <el-tab-pane
              :label="item.name + '(' + item.count + ')'"
              :name="item.type.toString()"
              v-for="(item, index) in headeNum"
              :key="index"
            />
          </el-tabs>
        </div>
        <div class="tips">
          <el-checkbox
            :indeterminate="isIndeterminate"
            v-model="checkAll"
            @change="handleCheckAllChange"
            style="margin-right: 6px"
          ></el-checkbox>
          <span class="mr10">已选 {{ selectStoreId.length }}</span>
          <span
            v-if="tableFrom.type == 2"
            class="mr10"
          >
            <el-button
              @click="handlerBatch('上架')"
              size="small"
              >上架</el-button
            ></span
          >
          <span
            v-if="tableFrom.type == 1 || tableFrom.type == 3"
            class="mr10"
          >
            <el-button
              @click="handlerBatch('下架')"
              size="small"
              >下架</el-button
            ></span
          >
          <span
            v-if="tableFrom.type == 3"
            class="mr10"
          >
            <el-button
              @click="handlerBatch('补充库存')"
              size="small"
              >补充库存</el-button
            ></span
          >

          <!-- <span v-if="tableFrom.type != 5" class="mr10">
            <el-button @click="handlerBatch('加入回收站')" size="small"
              >加入回收站</el-button
            ></span
          > -->
          <!-- <span>
            <el-button @click="batchSet" size="small">批量设置</el-button></span
          > -->
        </div>
      </div>
      <el-table
        v-loading="listLoading"
        :data="tableData.data"
        ref="table"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        @selection-change="handleSelectionChange"
        :highlight-current-row="true"
      >
        <el-table-column
          align="center"
          type="selection"
          width="55"
        >
        </el-table-column>

        <el-table-column
          label="产品名"
          min-width="280"
        >
          <template slot-scope="scope">
            <div class="store-info">
              <div class="store-img">
                <MyImage
                  :imagePath="scope.row.thumbnailImage"
                  :previewPath="scope.row.image"
                  :size="46"
                />
                <span>{{ scope.row.storeName }}</span>
              </div>
            </div>
            <div class="store-specification">
              <span
                @click="onUnfold(scope.row)"
                class="specification-num"
              >
                <span>{{ scope.row.attrValue.length }}种规格</span>
                <i
                  :class="
                    scope.row.isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
                  "
                ></i>
              </span>
              <div
                class="specification-list"
                v-if="!scope.row.isFold"
              >
                <div
                  v-for="(item, index) in scope.row.attrValue"
                  :key="index"
                  class="item"
                >
                  <MyImage
                    :imagePath="handlerImgUrl(item.image)"
                    :previewPath="item.image"
                    :size="30"
                  />
                  <span>{{ item.attrName }}</span
                  ><span>¥{{ item.price }}</span>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          prop="price"
          label="销售价(元)"
          min-width="90"
        />
        <el-table-column
          prop="stock"
          label="库存"
          min-width="90"
        />
        <el-table-column
          prop="popularity"
          label="虚拟销量"
          min-width="90"
        />
        <el-table-column
          prop="sales"
          label="实际销量"
          min-width="90"
        />
        <el-table-column
          label="创建时间"
          min-width="120"
          align="center"
        >
          <template slot-scope="scope">
            <span>{{ scope.row.addTime | formatDate }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="状态"
          min-width="120"
        >
          <template slot-scope="scope">
            <span v-if="tableFrom.type == 1"> 在售中 </span>
            <span v-if="tableFrom.type == 2">
              <span v-if="scope.row.storeStatus == 2">
                定时开售：{{
                  parseTime(scope.row.saleTime, '{y}-{m}-{d} {h}:{i}:{s}')
                }}
              </span>
              <span v-else> 仓库中 </span>
            </span>
            <span v-if="tableFrom.type == 3"> 已售空 </span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="200"
          fixed="right"
        >
          <template slot-scope="scope">
            <router-link
              :to="{ path: '/store/list/creatProduct/' + scope.row.id }"
            >
              <el-button
                type="text"
                size="small"
                class="mr10"
                >编辑</el-button
              >
            </router-link>
            <el-button
              @click="openCombinedProduct(scope.row)"
              type="text"
              size="small"
              >组合</el-button
            >
            <el-button
              @click="showMarketingCampaign(scope.row)"
              type="text"
              size="small"
              >营销</el-button
            >
            <el-button
              v-if="tableFrom.type == '2'"
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, tableFrom.type)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <el-dialog
      title="批量设置"
      :visible.sync="batchSetDialogVisible"
      width="700px"
      z-index="5"
    >
      <batchSetting
        v-if="batchSetDialogVisible"
        :selectStoreId="selectStoreId"
        @getList="seachList"
      />
    </el-dialog>

    <!-- 批量补充库存 -->
    <batchAdjustmentInventory
      ref="batchAdjustmentInventory"
      :selectStoreId="selectStoreId"
      @success="_init()"
    />
    <!-- 组合活动 -->
    <combinedProduct
      ref="combinedProduct"
      :productInfo="curProductInfo"
    />
    <!-- 营销活动 -->
    <marketingCampaign
      ref="marketingCampaign"
      :productInfo="curProductInfo"
    />
  </MainCard>
</template>

<script>
import {
  productLstApi,
  productDeleteApi,
  categoryApi,
  putOnShellApi,
  offShellApi,
  productHeadersApi,
  productBatchOffShell,
  productBatchPutOnShell,
  productBatchRecycle,
  productBatchSetting,
  productExportApi,
  restoreApi,
  productExcelApi
} from '@/api/store'
import batchSetting from './batchSetting.vue'
import batchAdjustmentInventory from './batchAdjustmentInventory'
import combinedProduct from './combinedProduct.vue'
import marketingCampaign from './marketingCampaign'

import { checkPermi } from '@/utils/permission' // 权限判断函数

const apiMap = {
  上架: productBatchPutOnShell,
  下架: productBatchOffShell,
  加入回收站: productBatchRecycle
}
export default {
  name: 'ProductList',
  components: {
    batchSetting,
    combinedProduct,
    batchAdjustmentInventory,
    marketingCampaign
  },
  data() {
    return {
      props: {
        children: 'child',
        label: 'name',
        value: 'id',
        emitPath: false
      },
      batchSetDialogVisible: false,
      // roterPre: roterPre,
      headeNum: [],
      isFold: true,
      selectStoreId: [],
      listLoading: true,
      tableData: {
        data: [],
        total: 0
      },
      tableFrom: {
        page: 1,
        limit: 20,
        cateId: '',
        tag: null,
        keywords: '',
        type: '1'
      },
      checkAll: false,
      isIndeterminate: false,
      categoryList: [],
      curProductInfo: {},
      merCateList: [],
      objectUrl: process.env.VUE_APP_BASE_API,
      dialogVisible: false
    }
  },
  mounted() {
    this._init()
    this.getCategorySelect()
    this.checkedCities = this.$cache.local.has('goods_stroge')
      ? this.$cache.local.getJSON('goods_stroge')
      : this.checkedCities
  },
  methods: {
    checkPermi,
    _init() {
      this.goodHeade()
      this.getList()
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection()
    },
    handleRestore(id) {
      this.$modalSure('恢复商品').then(() => {
        restoreApi(id).then((res) => {
          this.$message.success('操作成功')
          this._init()
        })
      })
    },
    // 产品关联的组合活动列表
    openCombinedProduct(row) {
      this.$refs.combinedProduct.dialogVisible = true
      this.curProductInfo = { ...row }
    },
    // 产品关联的营销活动列表
    showMarketingCampaign(row) {
      this.$refs.marketingCampaign.dialogVisible = true
      this.curProductInfo = { ...row }
    },
    batchSet() {
      if (this.selectStoreId.length == 0) {
        return this.$message.warning(`请选择需要批量设置的产品`)
      }
      this.batchSetDialogVisible = true
    },
    handleSelectionChange(val) {
      this.selectStoreId = val.map((item) => item.id)
      if (this.selectStoreId.length == 0) {
        this.isIndeterminate = false
        this.checkAll = false
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectStoreId.length == this.tableData.total) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        } else {
          if (this.selectStoreId.length == this.tableFrom.limit) {
            this.isIndeterminate = false
            this.checkAll = true
          } else {
            this.isIndeterminate = true
          }
        }
      }
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        cateId: '',
        tag: null,
        keywords: '',
        type: '1'
      }
      this.getList()
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },
    onUnfold(row) {
      row.isFold = !row.isFold
    },
    seachList() {
      this.tableData.data = []
      this.tableFrom.page = 1
      this.getList()
    },
    handleClose() {
      this.dialogVisible = false
    },
    handleCloseMod(item) {
      this.dialogVisible = item
      this._init()
    },
    // 复制
    onCopy() {
      this.dialogVisible = true
    },
    // 导出
    exports() {
      productExcelApi({
        cateId: this.tableFrom.cateId,
        keywords: this.tableFrom.keywords,
        type: this.tableFrom.type
      }).then((res) => {
        window.location.href = res.fileName
      })
    },
    // 获取商品表单头数量
    goodHeade() {
      productHeadersApi()
        .then((res) => {
          this.headeNum = res
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    // 商户分类；
    getCategorySelect() {
      categoryApi({ status: -1, type: 1 })
        .then((res) => {
          this.merCateList = res
        })
        .catch((res) => {
          this.$message.error(res.message)
        })
    },
    // 批量处理
    handlerBatch(typeName) {
      if (this.selectStoreId.length == 0) {
        return this.$message.warning(`请选择需要${typeName}的产品`)
      }
      if (typeName == '补充库存') {
        this.$refs.batchAdjustmentInventory.dialogVisible = true
        return
      } else {
        this.$modalSure(`要${typeName}选中的产品吗`).then(() => {
          const apifun = apiMap[typeName]
          apifun(this.selectStoreId).then(() => {
            this.$message.success('操作成功')
            this._init()
          })
        })
      }
    },
    // 列表
    getList() {
      this.listLoading = true
      productLstApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list.map((item) => {
            return { ...item, isFold: true }
          })
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch((res) => {
          this.listLoading = false
          this.$message.error(res.message)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    // 删除
    handleDelete(id, type) {
      this.$modalSure(`删除 id 为 ${id} 的商品`).then(() => {
        const deleteFlag = type == 5 ? 'delete' : 'recycle'
        productDeleteApi(id, deleteFlag).then(() => {
          this.$message.success('删除成功')
          this._init()
        })
      })
    },
    onchangeIsShow(row) {
      row.isShow
        ? putOnShellApi(row.id)
            .then(() => {
              this.$message.success('上架成功')
              this._init()
            })
            .catch(() => {
              row.isShow = !row.isShow
            })
        : offShellApi(row.id)
            .then(() => {
              this.$message.success('下架成功')
              this._init()
            })
            .catch(() => {
              row.isShow = !row.isShow
            })
    }
  }
}
</script>

<style scoped lang="scss">
.el-table__body {
  width: 100%;
  table-layout: fixed !important;
}
.demo-table-expand {
  ::v-deep label {
    width: 82px;
  }
}
.demo-table-expand {
  ::v-deep .el-form-item__content {
    width: 77%;
  }
}
.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 30px;
}
.relative {
  position: relative;
}
.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    .tabs {
      margin: 10px 0;
    }
    /* gap: 10px; */
  }
  .search-form {
    background-color: #f7f7f7;
    padding: 20px;
    .search-btn {
      margin-left: 82px;
    }
  }
  .tips {
    margin-bottom: 16px;
  }
  .store-info {
    .store-img {
      display: flex;
      align-items: center;
      gap: 10px;
      & > span {
        color: #333;
      }
    }
  }
  .store-specification {
    margin-top: 6px;

    .specification-num {
      cursor: pointer;
      color: #dc3c2e;
      &:hover {
        color: #df5c51;
      }
    }
    .specification-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      .item {
        background-color: #f6f6f6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        overflow: hidden;
        padding: 4px;
      }
    }
  }
}
</style>
