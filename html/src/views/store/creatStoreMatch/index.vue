<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 组合商品基本信息 start -->
          <div class="store-info base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item
                style="width: 60%"
                label="组合标签："
                prop="comboTags"
              >
                <el-input
                  v-model="formValidate.comboTags"
                  placeholder="请输入"
                />
                <div class="detail-description">
                  例如：滋补组合、疏肝健脾组合
                </div>
              </el-form-item>
              <el-form-item style="width: 60%" label="组合名称：" prop="name">
                <el-input v-model="formValidate.name" placeholder="请输入" />
              </el-form-item>
              <el-form-item style="width: 60%" label="组合描述：">
                <el-input
                  v-model="formValidate.description"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item style="width: 60%" label="初始虚拟销量：">
                <el-input
                  v-model="formValidate.initialSales"
                  maxlength="249"
                  placeholder="请输入"
                />
              </el-form-item>
              <el-form-item label="优惠叠加：">
                <div class="flex">
                  <el-switch
                    v-model="formValidate.allowDiscount"
                    :active-value="1"
                    :inactive-value="0"
                  />
                  <span class="detail-description">开启后，可使用满减券</span>
                </div>
              </el-form-item>
              <el-form-item label="上架时间：">
                <el-radio-group v-model="formValidate.storeStatus">
                  <!-- @change="onChangeSpec(formValidate.specType)" -->
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      gap: 20px;
                      align-items: flex-start;
                      margin-top: 10px;
                    "
                  >
                    <el-radio :label="1">
                      <span class="inline-flex">
                        <span>立即上架开售</span>
                      </span>
                    </el-radio>
                    <el-radio :label="2">
                      <span class="inline-flex">
                        <span>定时开售</span>
                        <span class="inline-flex">
                          <el-form-item>
                            <el-date-picker
                              :disabled="formValidate.storeStatus != 2"
                              v-model="formValidate.startTime"
                              type="datetime"
                              placeholder="售卖开始时间"
                            >
                            </el-date-picker>
                          </el-form-item>
                          <span>至</span>
                          <el-form-item>
                            <el-date-picker
                              v-model="formValidate.endTime"
                              :disabled="formValidate.storeStatus != 2"
                              type="datetime"
                              placeholder="售卖结束时间"
                            >
                            </el-date-picker>
                          </el-form-item>
                        </span>
                      </span>
                    </el-radio>
                    <el-radio :label="0">
                      <span class="inline-flex">
                        <span>暂不售卖，放入仓库</span>
                      </span>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
            </div>
          </div>
          <!-- 组合商品基本信息 end -->

          <!-- 组合规则 start -->
          <div class="store-specification base-card">
            <div class="info-title">组合规则</div>
            <div class="form-info">
              <!-- 组合规则 -->
              <el-form-item
                label="组合产品："
                prop="productItems"
                class="specification-table"
              >
                <div class="table-box">
                  <el-table
                    :data="formValidate.productItems"
                    class="tabNumWidth"
                    size="mini"
                  >
                    <el-table-column label="产品信息" min-width="200">
                      <template slot-scope="scope">
                        <div class="store-img">
                          <img
                            style="width: 40px; height: 40px"
                            :src="handlerImgUrl(scope.row.image)"
                          /><span>{{ scope.row.name }}</span>
                        </div>
                      </template>
                    </el-table-column>

                    <el-table-column label="销售单价" prop="price">
                    </el-table-column>
                    <el-table-column label="数量" min-width="90">
                      <template slot-scope="scope">
                        <el-input
                          maxlength="9"
                          min="1"
                          v-model="scope.row.quantity"
                          class="priceBox"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column
                      key="3"
                      align="center"
                      label="操作"
                      min-width="80"
                    >
                      <template slot-scope="scope">
                        <el-button
                          type="text"
                          class="submission"
                          @click="delAttrTable(scope.$index)"
                          >删除</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                  <div>
                    <el-button @click="changeGood" type="text"
                      ><i class="el-icon-plus"></i> 选择产品</el-button
                    >
                    <span class="detail-description"
                      >至少选择2个，最多选择3个</span
                    >
                  </div>
                </div>
              </el-form-item>
              <el-form-item
                v-if="formValidate.productItems.length > 0"
                style="width: 40%"
                prop="combinationPrice"
                label="组合价："
              >
                <div class="flex">
                  <el-input
                    :disabled="!!formValidate.freightType"
                    v-model="formValidate.combinationPrice"
                  >
                    <template slot="prepend">¥</template>
                  </el-input>
                  <span>原价小计：￥{{ oldTotalPrice }}，</span>
                  <span style="color: red">省：￥{{ preferentialPrice }}</span>
                </div>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  productCombinationCreateApi,
  productCombinationUpdateApi,
  productCombinationDetailApi,
} from "@/api/storeMatch";
import { Debounce } from "@/utils/validate";

const defaultObj = {
  comboTags: "",
  name: "",
  description: "",
  initialSales: null,
  allowDiscount: 1,
  storeStatus: 1,
  startTime: null,
  endTime: null,
  combinationPrice: 0,
  productItems: [],
};
export default {
  name: "ProductProductAdd",
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        comboTags: [
          { required: true, message: "请输入组合标签", trigger: "blur" },
        ],
        name: [{ required: true, message: "请输入组合名称", trigger: "blur" }],
        combinationPrice: [
          { required: true, message: "请输入组合优惠价", trigger: "blur" },
          {
            validator: (rule, value, callback) => {
              this.$nextTick(() => {
                if (this.formValidate.combinationPrice > this.oldTotalPrice) {
                  callback(new Error("组合价不能大于原价"));
                } else {
                  callback();
                }
              });
            },
            trigger: ["change"],
          },
        ],
        productItems: [
          { required: true, message: "请选择组合产品", trigger: "change" },
          {
            validator: (rule, value, callback) => {
              this.$nextTick(() => {
                if (this.formValidate.productItems.length < 1) {
                  callback(new Error("请至少选择2个产品"));
                } else if (this.formValidate.productItems.length > 3) {
                  callback(new Error("最多只能选择3个产品"));
                } else {
                  callback();
                }
              });
            },
            trigger: ["blur", "change"],
          },
        ],
      },
      fullscreenLoading: false,
      tempRoute: {},
    };
  },
  computed: {
    oldTotalPrice() {
      return this.formValidate.productItems.reduce(function (total, item) {
        return total + item.price * item.quantity;
      }, 0);
    },
    preferentialPrice() {
      return this.oldTotalPrice - this.formValidate.combinationPrice;
    },
  },
  watch: {
    "formValidate.freightType": {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.postage = null;
        } else {
          this.formValidate.tempId = null;
        }
      },
      immediate: false,
    },
    "formValidate.storeStatus": {
      handler: function (val) {
        if (val !== 2) {
          this.formValidate.startTime = null;
          this.formValidate.endTime = null;
        }
      },
      immediate: false,
    },
  },

  mounted() {
    if (this.$route.params.id) {
      // this.setTagsViewTitle();
      this.getInfo();
    }
  },
  methods: {
    changeGood() {
      const _this = this;
      this.$modalGoodList(
        function (row) {
          if (!row) return;
          console.log(row, 7585858);
          _this.formValidate.productItems = JSON.parse(JSON.stringify(row)).map(
            (item) => ({
              ...item,
              quantity: 1,
              productId: item.id,
              name: item.storeName,
            })
          );
        },
        "many",
        _this.formValidate.productItems
      );
    },

    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },

    setTagsViewTitle() {
      const title = this.isDisabled ? "商品详情" : "编辑商品";
      const route = Object.assign({}, this.tempRoute, {
        title: `${title}-${this.$route.params.id}`,
      });
      this.$store.dispatch("tagsView/updateVisitedView", route);
    },

    // 删除表格中的属性
    delAttrTable(index) {
      this.formValidate.productItems.splice(index, 1);
    },

    // 详情
    getInfo() {
      this.fullscreenLoading = true;

      productCombinationDetailApi(this.$route.params.id)
        .then(async (res) => {
          // this.isAttr = true;
          let info = res;
          // 处理商品明细回传值
          const productItems = info.productItems.map((item) => {
            return {
              ...item,
              id: item.productId,
              storeName: item.name,
            };
          });
          this.formValidate = {
            comboTags: info.comboTags,
            name: info.name,
            description: info.description,
            initialSales: info.initialSales,
            allowDiscount: info.allowDiscount,
            storeStatus: info.storeStatus,
            startTime: info.startTime,
            endTime: info.endTime,
            // saleTime: this.parseTime(info.saleTime, "{y}-{m}-{d} {h}:{i}:{s}"),
            combinationPrice: info.combinationPrice,
            productItems,
          };

          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      // 定时开售字段转时间戳
      // let saleTime = 0;
      // if (this.formValidate.saleTime) {
      //   saleTime = parseInt(
      //     new Date(this.formValidate.saleTime).getTime().toString().slice(0, -3)
      //   );
      // } else {
      //   saleTime = 0;
      // }

      const productIds = this.formValidate.productItems.map((item) => item.id);

      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.params.id
            ? productCombinationUpdateApi({
                ...this.formValidate,
                productIds,
                id: this.$route.params.id,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/store/match" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : productCombinationCreateApi({
                ...this.formValidate,
                productIds,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/store/match" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          if (
            !this.formValidate.comboTags ||
            !this.formValidate.name ||
            !this.formValidate.combinationPrice
          ) {
            this.$message.warning("请填写完整信息！");
          }
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .tabPic {
    width: 40px !important;
    height: 40px !important;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .tabNumWidth {
    ::v-deep.el-input-number--medium {
      width: 121px !important;
    }
    ::v-deep.el-input-number__increase {
      width: 20px !important;
      font-size: 12px !important;
    }
    ::v-deep.el-input-number__decrease {
      width: 20px !important;
      font-size: 12px !important;
    }
    ::v-deep.el-input-number--medium .el-input__inner {
      padding-left: 25px !important;
      padding-right: 25px !important;
    }
    ::v-deep thead {
      line-height: normal !important;
    }
    ::v-deep .el-table .cell {
      line-height: normal !important;
    }
  }
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }
  .form-info {
    padding-left: 80px;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
  }

  .specification-table {
    .table-box {
      border: 1px solid #dcdfe6;
      padding: 10px;
      border-radius: 4px;
    }
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }

  .store-img {
    display: flex;
    align-items: center;
    gap: 10px;
    & > span {
      color: #333;
    }
  }
  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
