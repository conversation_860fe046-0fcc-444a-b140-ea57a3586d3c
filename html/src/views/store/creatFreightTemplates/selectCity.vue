<template>
  <el-dialog
    title="选择地区"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
  >
    <div class="area-list">
      <div
        v-for="(item, index) in cityListArea"
        :key="index"
        class="item"
      >
        <el-checkbox
          :indeterminate="isIndeterminates[`isIndeterminates${index}`]"
          v-model="checkArea[`checkArea${index}`]"
          @change="handleCheckAllChange(index)"
          ><span class="area-name">{{
            `${getAreaName(index)}`
          }}</span></el-checkbox
        >
        <el-checkbox-group
          style="flex: 1"
          v-model="checkCity[`checkCity${index}`]"
          @change="handleCheckedCitiesChange(index)"
        >
          <div class="city-list">
            <el-checkbox
              class="city-name"
              v-for="city in item"
              :label="city.name"
              :key="city.name"
              >{{ city.name }}</el-checkbox
            >
          </div>
        </el-checkbox-group>
      </div>
    </div>

    <span
      slot="footer"
      class="dialog-footer"
    >
      <div class="footer-opration">
        <el-checkbox
          @change="handlerAllSelect"
          v-model="checkAll"
          >全选</el-checkbox
        >
        <span class="right">
          <span>已选择{{ selectedTotal }}个区域</span>
          <el-button
            @click="handlerSelected"
            type="primary"
            >确 认</el-button
          ></span
        >
      </div>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      cityArea0: [],
      cityArea1: [],
      cityArea2: [],
      isIndeterminates: {
        isIndeterminates0: false,
        isIndeterminates1: false,
        isIndeterminates2: false
      }, // 区域不确定态
      checkCity: { checkCity0: [], checkCity1: [], checkCity2: [] }, // 具体省份
      cityListArea: [],
      checkArea: { checkArea0: false, checkArea1: false, checkArea2: false }, // 区域选中状态
      checkAll: false, // 所有选中态
      isIndeterminateAll: false, // 所有不确定态
    }
  },
  props: {
    curSelectCityNames: {
      type: String
    },
    cityList: {
      type: Array,
      default: () => []
    }
  },
  mounted() {},
  computed: {
    selectedTotal() {
      return Object.values(this.checkCity)
        .map((item) => item.length)
        .reduce((sum, number) => sum + number, 0)
    }
  },

  methods: {
    handlerAllSelect() {
      this.isIndeterminates = {
        isIndeterminates0: false,
        isIndeterminates1: false,
        isIndeterminates2: false
      }
      if (this.checkAll) {
        this.checkArea = {
          checkArea0: true,
          checkArea1: true,
          checkArea2: true
        }
        this.checkCity = {
          checkCity0: this.cityArea0.map((item) => item.name),
          checkCity1: this.cityArea1.map((item) => item.name),
          checkCity2: this.cityArea2.map((item) => item.name)
        }
      } else {
        this.checkArea = {
          checkArea0: false,
          checkArea1: false,
          checkArea2: false
        }
        this.checkCity = {
          checkCity0: [],
          checkCity1: [],
          checkCity2: []
        }
      }
    },
    handlerSelected() {
      const selectCity = Object.values(this.checkCity).flat()
      this.$emit('selectResult', selectCity)
      this.handleClose()
    },
    handlerOpen() {
      // 拆分为三个区域的数据
      this.cityArea1 = this.cityList.filter(function (item) {
        return (
          item.name === '新疆维吾尔自治区' ||
          item.name === '西藏自治区' ||
          item.name === '青海省'
        )
      })

      this.cityArea2 = this.cityList.filter(function (item) {
        return (
          item.name === '香港特别行政区' ||
          item.name === '澳门特别行政区' ||
          item.name === '台湾'
        )
      })

      this.cityArea0 = this.cityList.filter((item) => {
        return !this.cityArea2.includes(item) && !this.cityArea1.includes(item)
      })
      this.cityListArea = [this.cityArea0, this.cityArea1, this.cityArea2]
      this.$nextTick(() => {
        if (this.curSelectCityNames) {
          if (this.curSelectCityNames === '全国') {
            this.checkAll = true
            this.handlerAllSelect()
          } else {
            const curSelectCityArr = this.curSelectCityNames.split(',')
            ;[0, 1, 2].forEach((item, index) => {
              this.checkCity['checkCity' + index] = this['cityArea' + index]
                .filter((item) => curSelectCityArr.includes(item.name))
                .map((item) => item.name)
              this.handleCheckedCitiesChange(index)
            })
          }
        }
      })
    },
    handleCheckAllChange(index) {
      this.isIndeterminates['isIndeterminates' + index] = false
      if (this.checkArea['checkArea' + index] === true) {
        this.checkCity['checkCity' + index] = this['cityArea' + index].map(
          (item) => item.name
        )
      } else {
        this.checkCity['checkCity' + index] = []
      }
      this.checkAll = this.selectedTotal == this.cityList.length
    },
    handleCheckedCitiesChange(index) {
      this.checkAll = this.selectedTotal == this.cityList.length
      if (
        this.checkCity['checkCity' + index].length > 0 &&
        this.checkCity['checkCity' + index].length <
          this['cityArea' + index].length
      ) {
        this.checkArea['checkArea' + index] = false
        this.isIndeterminates['isIndeterminates' + index] = true
      }
      if (
        this.checkCity['checkCity' + index].length ==
        this['cityArea' + index].length
      ) {
        this.checkArea['checkArea' + index] = true
        this.isIndeterminates['isIndeterminates' + index] = false
      }
      if (this.checkCity['checkCity' + index].length == 0) {
        this.checkArea['checkArea' + index] = false
        this.isIndeterminates['isIndeterminates' + index] = false
      }
    },
    getAreaName(index) {
      return ['一、二区', '三区  ', '港台区 '][index]
    },
    handleClose(done) {
      // 全部取消选中以及不确定态
      this.checkCity = {
        checkCity0: [],
        checkCity1: [],
        checkCity2: []
      }
      this.checkArea = {
        checkArea0: false,
        checkArea1: false,
        checkArea2: false
      }
      this.isIndeterminates = {
        isIndeterminates0: false,
        isIndeterminates1: false,
        isIndeterminates2: false
      }
      this.checkAll = false
      this.isIndeterminateAll = false
      this.$nextTick(() => {
        this.dialogVisible = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.area-list {
  .item {
    margin-bottom: 20px;
    display: flex;
    gap: 80px;
    .area-name {
      display: inline-block;
      width: 40px;
    }
    .city-list {
      display: grid;
      grid-template-columns: 1fr 1fr 1fr;
      gap: 12px;
    }
  }
}
.footer-opration {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .right {
    span {
      margin-right: 10px;
    }
  }
}
</style>
