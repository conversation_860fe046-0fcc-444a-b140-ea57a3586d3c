<template>
  <el-dialog
    title="营销活动查询"
    @opened="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="70%"
    :before-close="handleClose"
  >
    <div class="combined-product">
      <div class="product-info">
        <MyImage
          :imagePath="productInfo.thumbnailImage"
          :previewPath="productInfo.image"
          :size="46"
        />
        <span>{{ productInfo.storeName }}</span>
      </div>
      <div class="table-box">
        <div class="title">查询该产品有什么优惠</div>
        <div class="table-list">
          <el-table
            ref="table"
            v-loading="tableData.loading"
            :data="tableData.data"
            :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
            style="width: 100%"
            size="mini"
            highlight-current-row
          >
            <el-table-column
              prop="activityName"
              label="优惠活动名称"
            >
            </el-table-column>
            <el-table-column
              prop="activityType"
              label="活动类型"
            >
            </el-table-column>
            <el-table-column
              prop="discountContent"
              label="优惠内容"
            >
            </el-table-column>
            <el-table-column
              prop="status"
              label="状态"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              fixed="right"
            >
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="small"
                  @click="stopSending(scope.row.activityId)"
                  >停发</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { queryCouponsByProductId, stopCoupon } from '@/api/store'
export default {
  data() {
    return {
      dialogVisible: false,
      tableData: {
        data: [],
        loading: false
      }
    }
  },
  props: {
    productInfo: {
      type: Object,
      default: () => ({})
    }
  },

  methods: {
    handlerOpen() {
      // 获取营销优惠券
      this.queryCouponsByProductId()
    },
    queryCouponsByProductId() {
      this.tableData.loading = true
      queryCouponsByProductId(this.productInfo.id)
        .then((res) => {
          this.tableData.data = res.couponList
          this.tableData.loading = false
        })
        .catch((res) => {
          this.tableData.loading = false
          this.$message.error(res.message)
        })
    },
    // 停发优惠券
    stopSending(id) {
      this.$modalSure(`停发？`).then(() => {
        stopCoupon({ couponId: id, productId: this.productInfo.id }).then(
          () => {
            this.$message.success('操作成功')
            this.queryCouponsByProductId()
          }
        )
      })
    },
    handleClose(done) {
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.combined-product {
  min-height: 400px;
  overflow: auto;
  .product-info {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    gap: 10px;
    & > span {
      color: #333;
      font-weight: 700;
    }
  }
  .table-box {
    .title {
      margin-bottom: 10px;
    }
  }
}
</style>
