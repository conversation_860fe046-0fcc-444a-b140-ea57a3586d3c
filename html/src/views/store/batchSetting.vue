<template>
  <div>
    <el-form
      :model="formValidate"
      :rules="rules"
      ref="formValidate"
      label-width="120px"
      class="demo-formValidate"
      v-loading="loading"
    >
      <el-form
        :model="formValidate"
        :rules="rules"
        ref="formValidate"
        label-width="120px"
        class="demo-formValidate"
        v-loading="loading"
      >
      </el-form>
      <el-form-item label="虚拟销量：">
        <el-input-number
          v-model="formValidate.ficti"
          :min="0"
          placeholder="请输入排序"
        />
      </el-form-item>
      <el-form-item label="运费模板：">
        <div class="uniform-postage">
          <span>
            <el-select
              v-model="formValidate.tempId"
              placeholder="请选择"
              class="mr20"
              style="width: 100%"
            >
              <el-option
                v-for="item in shippingList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </span>
          <span>
            <el-button class="mr15" @click="addTem" type="text"
              >运费模板</el-button
            >
          </span>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button
          size="small"
          type="primary"
          @click="submitForm('formValidate')"
          :loading="loadingbtn"
          >提交</el-button
        >
        <el-button size="small" @click="resetForm('formValidate')"
          >重置</el-button
        >
      </el-form-item>
    </el-form>
    <CreatTemplates ref="addTemplates" @getList="getShippingList" />
  </div>
</template>

<script>
import { productBatchSetting } from "@/api/store";
import CreatTemplates from "@/views/systemSetting/logistics/shippingTemplates/creatTemplates";
import { shippingTemplatesList } from "@/api/logistics";
import { Debounce } from "@/utils/validate";
const defaultObj = {
  ficti: null,
  tempId: null,
};
export default {
  components: { CreatTemplates },
  props: {
    selectStoreId: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      loadingbtn: false,
      formValidate: Object.assign({}, defaultObj),
      rules: {},
      tempData: {
        page: 1,
        limit: 9999,
      },
      shippingList: [], // 运费模板
    };
  },
  mounted() {
    this.getShippingList();
  },
  methods: {
    // 运费模板；
    getShippingList() {
      shippingTemplatesList(this.tempData).then((res) => {
        this.shippingList = res.list;
      });
    },
    // 运费模板
    addTem() {
      this.$refs.addTemplates.dialogVisible = true;
      this.$refs.addTemplates.getCityList();
    },
    submitForm: Debounce(function (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loadingbtn = true;
          productBatchSetting({ ...this.formValidate, ids: this.selectStoreId })
            .then(() => {
              this.$message.success("设置成功");
              setTimeout(() => {
                // this.clear();
                this.$emit("getList");
              }, 600);
              this.loadingbtn = false;
            })
            .catch(() => {
              this.loadingbtn = false;
            });
        } else {
          return false;
        }
      });
    }),
    resetForm(formName) {
      this.$refs[formName].resetFields();
    },
  },
};
</script>

<style scoped lang="scss">
.uniform-postage {
  display: flex;
  gap: 10px;
}
</style>
