<template>
  <div class="steps">
    <div
      v-for="(item, index) in stepsTitle"
      :key="index"
      :class="`step-items step-items${index + 1} ${
        curStep == 1 ? 'active' : ''
      }`"
    >
      <span class="step-num">{{ index + 1 }}</span> <span>{{ item }}</span>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    stepsTitle: {
      type: Array,
      default: () => [],
    },
    curStep: {
      type: Number,
      default: 1,
    },
  },
  data() {
    return {};
  },
  methods: {},
};
</script>

<style lang="scss" scoped>
.steps {
  display: flex;
  /* justify-content: center; */
  gap: 16px;
  .step-items {
    color: white;
    background-color: #e03d35;
    padding: 0 40px;
    height: 40px;
    line-height: 40px;
    .step-num {
      font-style: italic;
      margin-right: 4px;
      font-size: 14px;
    }
    &.step-items1 {
      position: relative;
      border-radius: 4px 0 0 4px;
      &::before {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        /* background-color: pink; */
        z-index: 1;
        top: 0;
        right: -16px;
        border-top: 20px solid transparent;
        border-bottom: 20px solid transparent;
        border-left: 16px solid #e03d35;
      }
    }
    &.step-items2 {
      padding: 0 60px;
      position: relative;
      color: #333;
      background-color: #fff;
      border-radius: 0 4px 4px 0;
      &::before {
        content: "";
        position: absolute;
        width: 0;
        height: 0;
        /* background-color: pink; */
        z-index: 1;
        top: 0;
        left: 0;
        border-top: 20px solid transparent;
        border-bottom: 20px solid transparent;
        border-left: 16px solid #f5f5f5;
      }
      &.active {
        background: linear-gradient(to right, #dc3c2e, #dc5620);
        color: white;
      }
    }
  }
}
</style>
