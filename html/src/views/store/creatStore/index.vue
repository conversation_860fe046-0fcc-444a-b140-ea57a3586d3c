<template>
  <MainCard :cardType="2">
    <!-- <template v-slot:header>
     
    </template> -->
    <div class="app-box">
      <!-- <div slot="header" class="clearfix">
        <el-steps :active="currentTab" align-center finish-status="success">
          <el-step title="商品信息" />
          <el-step title="商品详情" />
          
          <el-step title="其他设置" />
          <el-step title="规格设置" />
        </el-steps>
      </div> -->
      <div class="steps">
        <Steps
          :steps-title="['编辑基本信息', '编辑商品详情图']"
          :curStep="currentTab"
        />
      </div>
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div v-show="currentTab === 0">
          <!-- 商品基本信息 start -->
          <div class="store-info base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item
                style="width: 60%"
                label="商品名称："
                prop="storeName"
              >
                <el-input
                  v-model="formValidate.storeName"
                  maxlength="249"
                  placeholder="商品简短名称"
                  :disabled="isDisabled"
                />
              </el-form-item>
              <el-form-item
                style="width: 60%"
                label="商品标题："
                prop="storeInfo"
              >
                <el-input
                  v-model="formValidate.storeInfo"
                  maxlength="249"
                  placeholder="商品详情页显示的标题"
                  :disabled="isDisabled"
                />
              </el-form-item>
              <el-form-item label="商品图：" prop="sliderImages">
                <div class="acea-row">
                  <div
                    v-for="(item, index) in formValidate.sliderImages"
                    :key="index"
                    class="pictrue store-img"
                    draggable="true"
                    @dragstart="handleDragStart($event, item)"
                    @dragover.prevent="handleDragOver($event, item)"
                    @dragenter="handleDragEnter($event, item)"
                    @dragend="handleDragEnd($event, item, 'sliderImages')"
                  >
                    <MyImage
                      :imagePath="handlerImgUrl(item)"
                      :previewPath="item"
                      :size="58"
                    />
                    <!-- <img :src="handlerImgUrl(item)" /> -->
                    <i
                      v-if="!isDisabled"
                      class="el-icon-error btndel"
                      @click="handleRemove(index)"
                    />
                    <span v-show="index === 0" class="master-image-flag"
                      >主图</span
                    >
                  </div>
                  <div
                    v-if="formValidate.sliderImages.length < 10 && !isDisabled"
                    class="upLoadPicBox"
                    @click="modalPicTap('2')"
                  >
                    <div class="upLoad">
                      <i
                        style="font-size: 16px"
                        class="el-icon-plus cameraIconfont"
                      />
                    </div>
                  </div>
                </div>
                <div class="detail-description">
                  建议尺寸：800*800像素，可拖拽图片调整顺序，最多6张
                </div>
              </el-form-item>
              <el-form-item label="主图视频：">
                <div
                  class="upLoadPicBox"
                  @click="modalPicTap('3')"
                  :disabled="isDisabled"
                >
                  <div v-if="formValidate.videoLink" class="pictrue">
                    <video :src="formValidate.videoLink" />
                  </div>
                  <div v-else class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  添加主图视频，建议时长9-30秒，视频宽高和商品图一致
                </div>
              </el-form-item>
              <el-form-item label="商品分类：" prop="cateIds">
                <el-select
                  style="width: 24%"
                  multiple
                  :disabled="isDisabled"
                  v-model="formValidate.cateIds"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in merCateList"
                    :key="item.id.toString()"
                    :label="item.name"
                    :value="item.id.toString()"
                  >
                  </el-option>
                </el-select>
              </el-form-item>
              <el-form-item style="width: 60%" label="商品卖点：">
                <el-input
                  v-model="formValidate.keyword"
                  placeholder="在产品详情页标题下面展示卖点信息，建议40字以内"
                  :disabled="isDisabled"
                />
              </el-form-item>
              <!-- <el-form-item label="分组标签：">
                <el-checkbox-group
                  :disabled="isDisabled"
                  v-model="formValidate.tag"
                >
                  <el-checkbox label="1">人气爆款</el-checkbox>
                  <el-checkbox label="2">热销推荐</el-checkbox>
                </el-checkbox-group>
              </el-form-item> -->
              <el-form-item style="width: 30%" label="初始虚拟销量：">
                <el-input
                  v-model="formValidate.popularity"
                  maxlength="249"
                  placeholder="请输入"
                  :disabled="isDisabled"
                />
              </el-form-item>
            </div>
          </div>
          <!-- 商品基本信息 end -->

          <!-- 价格库存 start -->
          <div class="store-specification base-card">
            <div class="info-title">价格库存</div>
            <div class="form-info">
              <el-form-item label="商品规格：" required>
                <div class="area-box">
                  <el-form-item label="规格单位：">
                    <el-select
                      :disabled="isDisabled"
                      v-model="formValidate.unitName"
                      placeholder="请选择"
                      @change="changeUnit"
                    >
                      <el-option label="盒" value="盒"> </el-option>
                      <el-option label="袋" value="袋"> </el-option>
                      <el-option label="罐" value="罐"> </el-option>
                      <el-option label="箱" value="箱"> </el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="规格值：">
                    <div class="input-list">
                      <div
                        class="item"
                        v-for="(item, index) in addRuleFormList"
                        :key="item.key"
                      >
                        <el-autocomplete
                          class="inline-input"
                          v-model="item.attrName"
                          :fetch-suggestions="querySearch"
                          placeholder="请选择"
                          @blur="handlerBlur"
                          @select="handlerBlur"
                        ></el-autocomplete>
                        <div
                          @click="delAttrTable(index, item.attrName)"
                          class="delete-btn"
                        >
                          <i class="el-icon-close"></i>
                        </div>
                      </div>
                      <div class="item">
                        <el-button
                          @click="addAttrTable"
                          type="text"
                          :disabled="isDisabled || !formValidate.unitName"
                        >
                          添加规格值</el-button
                        >
                      </div>
                    </div>
                  </el-form-item>
                </div>
              </el-form-item>
              <!-- 规格明细 -->
              <el-form-item
                v-if="attrValueList.length > 0"
                label="规格明细："
                class="specification-table"
              >
                <div class="table-box">
                  <el-table
                    :data="attrValueList"
                    class="tabNumWidth"
                    size="mini"
                  >
                    <el-table-column
                      align="left"
                      prop="attrName"
                      :label="`规格${
                        formValidate.unitName
                          ? `(${formValidate.unitName})`
                          : ''
                      }`"
                      min-width="180"
                    ></el-table-column>

                    <el-table-column align="center" label="图片" min-width="80">
                      <template slot-scope="scope">
                        <div
                          class="upLoadPicBox"
                          @click="modalPicTap('1', 'duo', scope.$index)"
                        >
                          <div v-if="scope.row.image" class="pictrue tabPic">
                            <img :src="handlerImgUrl(scope.row.image)" />
                          </div>
                          <div v-else class="upLoad tabPic">
                            <i
                              style="font-size: 16px"
                              class="el-icon-plus cameraIconfont"
                            />
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column
                      v-for="(item, iii) in attrObjInput"
                      :key="iii"
                      :label="item.title"
                      align="center"
                      min-width="120"
                    >
                      <template slot-scope="scope">
                        <el-input
                          :disabled="isDisabled"
                          maxlength="9"
                          min="0.01"
                          v-model="scope.row[iii]"
                          class="priceBox"
                        />
                      </template>
                    </el-table-column>
                    <el-table-column align="center" label="销量" min-width="80">
                      <template>
                        {{ 0 }}
                      </template>
                    </el-table-column>
                  </el-table>
                  <div class="detail-description">
                    默认商品主图为规格值图片，点击图片可以修改规格图片
                  </div>
                </div>
              </el-form-item>
              <el-form-item style="width: 30%" label="销售价：" prop="price">
                <el-input
                  :disabled="attrValueList.length > 0"
                  v-model="formValidate.price"
                >
                  <template slot="prepend">¥</template>
                </el-input>
              </el-form-item>
              <el-form-item style="width: 30%" label="库存：" prop="stock">
                <el-input :disabled="isDisabled" v-model="formValidate.stock">
                </el-input>
              </el-form-item>
            </div>
          </div>
          <!-- 价格库存 end -->

          <!-- 其他信息 start -->
          <div class="store-other base-card">
            <div class="info-title">其他信息</div>
            <div class="form-info">
              <!-- <el-form-item
                prop="deliveryType"
                label="配送方式："
              >
                <el-checkbox-group
                  v-model="formValidate.deliveryType"
                  size="small"
                  @change="onChangeGroup"
                  :disabled="isDisabled"
                >
                  <el-checkbox :label="0"> 快递发货 </el-checkbox>
                </el-checkbox-group>
              </el-form-item> -->
              <el-form-item label="快递运费：" prop="freightType">
                <el-radio-group
                  v-model="formValidate.freightType"
                  :disabled="isDisabled"
                >
                  <div style="display: flex; flex-direction: column; gap: 20px">
                    <el-radio :label="0">
                      <span class="inline-flex">
                        <span>统一邮费</span>
                        <span>
                          <el-input
                            :disabled="!!formValidate.freightType || isDisabled"
                            v-model="formValidate.postage"
                          >
                            <template slot="prepend">¥</template>
                          </el-input>
                        </span>
                      </span>
                    </el-radio>
                    <el-radio :label="1">
                      <div class="inline-flex">
                        <span>运费模板</span>
                        <el-form-item>
                          <div class="inline-flex">
                            <span>
                              <el-select
                                :disabled="
                                  !formValidate.freightType || isDisabled
                                "
                                v-model="formValidate.tempId"
                                placeholder="请选择"
                                class="mr20"
                                style="width: 100%"
                              >
                                <el-option
                                  v-for="item in shippingList"
                                  :key="item.id"
                                  :label="item.name"
                                  :value="item.id"
                                />
                              </el-select>
                            </span>
                            <span>
                              <el-button
                                :disabled="
                                  !formValidate.freightType || isDisabled
                                "
                                v-show="!isDisabled"
                                class="mr15"
                                @click="addTem"
                                type="text"
                                >运费模板</el-button
                              >
                            </span>
                          </div>
                        </el-form-item>
                      </div>
                      <div style="margin-top: 10px" class="detail-description">
                        运费模板支持按地区设置运费，支持满额包邮设置
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="上架时间：">
                <el-radio-group
                  v-model="formValidate.storeStatus"
                  :disabled="isDisabled"
                >
                  <!-- @change="onChangeSpec(formValidate.specType)" -->
                  <div
                    style="
                      display: flex;
                      flex-direction: column;
                      gap: 20px;
                      align-items: flex-start;
                      margin-top: 10px;
                    "
                  >
                    <el-radio :label="1">
                      <span class="inline-flex">
                        <span>立即上架开售</span>
                      </span>
                    </el-radio>
                    <el-radio :label="2">
                      <span class="inline-flex">
                        <span>定时开售</span>
                        <span>
                          <el-form-item>
                            <el-date-picker
                              :disabled="
                                formValidate.storeStatus != 2 || isDisabled
                              "
                              v-model="saleTime"
                              type="datetime"
                              placeholder="请选择上架售卖时间"
                            >
                            </el-date-picker>
                          </el-form-item>
                        </span>
                      </span>
                    </el-radio>
                    <el-radio :label="0">
                      <span class="inline-flex">
                        <span>暂不售卖，放入仓库</span>
                      </span>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="售后服务：">
                <el-checkbox
                  :disabled="isDisabled"
                  v-model="formValidate.supportExchange"
                >
                  支持买家申请换货
                </el-checkbox>
                <el-checkbox
                  :disabled="isDisabled"
                  v-model="formValidate.supportReturn7Days"
                >
                  支持7天无理由退货
                </el-checkbox>
              </el-form-item>
              <!-- <el-form-item label="限购：">
                <div>
                  <el-checkbox
                    :disabled="isDisabled"
                    v-model="formValidate.limitPurchaseCount"
                  >
                    <span class="inline-flex">
                      <span>限制每个人可购买数量</span>
                      <span>
                        <el-input
                          :disabled="
                            !formValidate.limitPurchaseCount || isDisabled
                          "
                          placeholder="请输入内容"
                          v-model="formValidate.maxPurchaseCount"
                        >
                          <template slot="append">个</template>
                        </el-input>
                      </span>
                    </span>
                  </el-checkbox>
                </div>
                <div style="margin-top: 10px">
                  <el-checkbox
                    :disabled="isDisabled"
                    v-model="formValidate.limitSpecificUsers"
                  >
                    <span class="inline-flex">
                      <span>只允许特定用户购买</span>
                      <span>
                        <el-form-item>
                          <el-select
                            :disabled="
                              !formValidate.limitSpecificUsers || isDisabled
                            "
                            multiple
                            v-model="formValidate.allowedLevels"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in userList"
                              :key="item.uid"
                              :label="item.nickname"
                              :value="item.uid"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </span>
                    </span>
                  </el-checkbox>
                </div>
              </el-form-item> -->
              <el-form-item label="限购：">
                <div class="inline-flex">
                  <el-checkbox v-model="formValidate.limitSpecificUsers">
                  </el-checkbox>
                  <span>限购人群</span>
                </div>
                <div style="margin-left: 24px">
                  <div>可购买该商品的会员等级</div>
                  <el-select
                    :disabled="!formValidate.limitSpecificUsers"
                    v-model="formValidate.allowedLevels"
                    multiple
                    placeholder="会员等级"
                  >
                    <el-option
                      v-for="item in userLevelList"
                      :key="item.grade"
                      :label="item.name"
                      :value="item.grade"
                    >
                    </el-option>
                  </el-select>
                </div>
              </el-form-item>
              <!-- <el-form-item>
                <div style="margin-top: 10px">
                  <el-checkbox
                    :disabled="isDisabled"
                    v-model="formValidate.limitSpecificUsers"
                  >
                    <span class="inline-flex">
                      <span>只允许特定用户购买</span>
                      <span>
                        <el-form-item>
                          <el-select
                            :disabled="
                              !formValidate.limitSpecificUsers || isDisabled
                            "
                            multiple
                            v-model="formValidate.allowedLevels"
                            placeholder="请选择"
                          >
                            <el-option
                              v-for="item in userList"
                              :key="item.uid"
                              :label="item.nickname"
                              :value="item.uid"
                            >
                            </el-option>
                          </el-select>
                        </el-form-item>
                      </span>
                    </span>
                  </el-checkbox>
                </div>
              </el-form-item> -->
            </div>
          </div>
          <!-- 其他信息 end -->
        </div>
        <div v-show="currentTab == 1" class="store-details base-card">
          <div class="info-title">商品详情</div>
          <div class="form-info">
            <div class="details-left">
              <el-form-item label="选择详情图：">
                <div
                  v-if="!isDisabled"
                  class="upLoadPicBox"
                  @click="modalPicTap('4')"
                >
                  <div class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  <div>商品详情图，建议宽度不小于750像素</div>
                  <div>建议切割为多张图片顺序上传，后期增加自动切图功能</div>
                </div>
              </el-form-item>
            </div>
            <div class="details-right">
              <div class="details-row">
                <div class="preview-imgs">
                  <div class="preview-top">商品详情效果预览</div>
                  <div class="preview-main">
                    <div v-show="!detailsImages.length" class="not-upLoad">
                      <div class="icon">
                        <svg-icon
                          class="not_preview"
                          icon-class="not_preview"
                        />
                      </div>
                      <div class="preview-des">
                        <div>产品详情图展示</div>
                        <div>建议宽度不小于750像素</div>
                      </div>
                    </div>
                    <div
                      v-show="detailsImages.length"
                      v-for="(item, index) in detailsImages"
                      :key="item"
                      class="details-img"
                      draggable="true"
                      @dragstart="handleDragStart($event, item)"
                      @dragover.prevent="handleDragOver($event, item)"
                      @dragenter="handleDragEnterDetails($event, item)"
                      @dragend="handleDragEnd($event, item)"
                    >
                      <img :src="item" />
                      <i
                        v-if="!isDisabled"
                        class="el-icon-error deleteBtn"
                        @click="handleRemoveDetailsImg(index)"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 其他设置-->
        <div v-show="currentTab === 2">
          <el-form-item label="排序：">
            <el-input-number
              v-model="formValidate.sort"
              :min="0"
              placeholder="请输入排序"
              :disabled="isDisabled"
            />
          </el-form-item>
          <el-form-item label="积分：">
            <el-input-number
              v-model="formValidate.giveIntegral"
              :min="0"
              placeholder="请输入排序"
              :disabled="isDisabled"
            />
          </el-form-item>
          <el-form-item label="虚拟销量：">
            <el-input-number
              v-model="formValidate.ficti"
              :min="0"
              placeholder="请输入排序"
              :disabled="isDisabled"
            />
          </el-form-item>
          <el-form-item label="商品推荐：">
            <el-checkbox-group
              v-model="checkboxGroup"
              size="small"
              @change="onChangeGroup"
              :disabled="isDisabled"
            >
              <el-checkbox
                v-for="(item, index) in recommend"
                :key="index"
                :label="item.value"
                >{{ item.name }}</el-checkbox
              >
            </el-checkbox-group>
          </el-form-item>

          <el-form-item label="活动优先级：">
            <div class="color-list acea-row row-middle">
              <div
                :disabled="isDisabled"
                class="color-item"
                :class="activity[item]"
                v-for="item in formValidate.activity"
                :key="item"
                draggable="true"
                @dragstart="handleDragStart($event, item)"
                @dragover.prevent="handleDragOver($event, item)"
                @dragenter="handleDragEnterFont($event, item)"
                @dragend="handleDragEnd($event, item)"
              >
                {{ item }}
              </div>
              <div class="tip">可拖动按钮调整活动的优先展示顺序</div>
            </div>
          </el-form-item>
          <el-form-item label="优惠券：" class="proCoupon">
            <div class="acea-row">
              <el-tag
                v-for="(tag, index) in formValidate.coupons"
                :key="index"
                class="mr10 mb10"
                :closable="!isDisabled"
                :disable-transitions="false"
                @close="handleCloseCoupon(tag)"
              >
                {{ tag.name }}
              </el-tag>
              <!-- <span v-if="formValidate.couponIds == null">无</span> -->
              <el-button v-if="!isDisabled" class="mr15" @click="addCoupon"
                >选择优惠券</el-button
              >
            </div>
          </el-form-item>
          <el-form-item label="折扣：" class="proCoupon">
            <div class="acea-row">
              <el-tag
                v-for="(tag, index) in formValidate.coupons"
                :key="index"
                class="mr10 mb10"
                :closable="!isDisabled"
                :disable-transitions="false"
                @close="handleCloseCoupon(tag)"
              >
                {{ tag.name }}
              </el-tag>
              <span v-if="formValidate.couponIds == null">无</span>
              <el-button v-if="!isDisabled" class="mr15" @click="addCoupon"
                >选择折扣</el-button
              >
            </div>
          </el-form-item>
        </div>
      </el-form>
      <CreatTemplates ref="addTemplates" @getList="getShippingList" />
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          v-if="currentTab > 0"
          class="submission priamry_border"
          @click="handleSubmitUp"
          >上一步</el-button
        >
        <el-button
          v-if="currentTab < 1"
          type="primary"
          class="submission"
          @click="handleSubmitNest('formValidate')"
          >下一步</el-button
        >
        <el-button
          v-show="(currentTab < 2 || $route.params.id) && !isDisabled"
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >提交</el-button
        >
      </div>
    </template>
  </MainCard>
</template>

<script>
import Tinymce from "@/components/Tinymce/index";
import Steps from "./steps.vue";
import {
  templateListApi,
  productCreateApi,
  categoryApi,
  productDetailApi,
  productUpdateApi,
  getProductSkuList,
} from "@/api/store";
import { userLevelListApi } from "@/api/discountCoupon.js";
import { marketingSendApi } from "@/api/marketing";
import { shippingTemplatesList } from "@/api/logistics";
import { goodDesignList } from "@/api/systemGroup";
import { clearTreeData } from "@/utils/ZBKJIutil";
import CreatTemplates from "@/views/systemSetting/logistics/shippingTemplates/creatTemplates";
import Templates from "../../appSetting/wxAccount/wxTemplate/index";
import { Debounce } from "@/utils/validate";
import { userListApi } from "@/api/user";

const attrObjInput = {
  // attrName: {
  //   title: "规格名称",
  // },

  // otPrice: {
  //   title: "原价",
  // },
  // stock: {
  //   title: "库存",
  // },

  price: {
    title: "销售价(元)",
  },
  cost: {
    title: "成本价",
  },
  quantity: {
    title: "数量",
  },
  // sort: {
  //   title: "排序",
  // },
};

const defaultObj = {
  image: "",
  thumbnailImage: "",
  storeStatus: 1,
  sliderImages: [],
  videoLink: "",
  sliderImage: "",
  storeName: "",
  price: null,
  storeInfo: "",
  saleTime: null,
  popularity: null,
  stock: null,
  // tag: [],
  keyword: "",
  cateIds: [], // 商品分类id
  cateId: null, // 商品分类id传值
  unitName: "",
  sort: 0,
  maxPurchaseCount: "",
  deliveryType: [0],
  allowedLevels: [],
  productSkuList: [],
  giveIntegral: 0,
  freightType: 1,
  supportExchange: true,
  limitPurchaseCount: false,
  limitSpecificUsers: false,
  postage: null,
  ficti: 0,
  isShow: false,
  isBenefit: false,
  isNew: false,
  isGood: false,
  isHot: false,
  isBest: false,
  tempId: "",
  attrValue: [
    {
      image: "",
      price: 0,
      cost: 0,
      otPrice: 0,
      stock: 0,
      barCode: "",
      weight: 0,
      volume: 0,
    },
  ],

  attr: [],
  selectRule: "",
  isSub: false,
  content: "",
  specType: true,
  id: 0,
  couponIds: [],
  coupons: [],
  userLevelList: [],
  activity: ["默认", "秒杀", "砍价", "拼团"],
};
const objTitle = {
  price: {
    title: "价格(元)",
  },
  otPrice: {
    title: "原价",
  },
  stock: {
    title: "库存",
  },
  cost: {
    title: "成本价",
  },
  barCode: {
    title: "商品编号",
  },
  weight: {
    title: "重量（KG）",
  },
  volume: {
    title: "体积(m³)",
  },
};
export default {
  name: "ProductProductAdd",
  components: { Templates, CreatTemplates, Tinymce, Steps },
  data() {
    return {
      isDisabled: this.$route.params.isDisabled === "1" ? true : false,
      activity: { 默认: "red", 秒杀: "blue", 砍价: "green", 拼团: "yellow" },
      props2: {
        children: "child",
        label: "name",
        value: "id",
        multiple: true,
        emitPath: false,
      },
      /* 开售时间 */
      saleTime: null,
      state1: "",
      isSaleTime: true,
      checkboxGroup: [],
      recommend: [],
      userList: [],
      tabs: [],
      detailsImages: [],
      fullscreenLoading: false,
      props: { multiple: true },
      active: 0,
      attrValueList: [],
      attrObjInput: Object.assign({}, attrObjInput),
      OneattrValue: [Object.assign({}, defaultObj.attrValue[0])], // 单规格
      ManyAttrValue: [Object.assign({}, defaultObj.attrValue[0])], // 多规格
      ruleList: [],
      addRuleFormList: [],
      merCateList: [], // 商户分类筛选
      shippingList: [], // 运费模板
      formThead: Object.assign({}, objTitle),
      formValidate: Object.assign({}, defaultObj),
      formDynamics: {
        ruleName: "",
        ruleValue: [],
      },
      tempData: {
        page: 1,
        limit: 9999,
      },
      manyTabTit: {},
      manyTabDate: {},
      grid2: {
        xl: 12,
        lg: 12,
        md: 12,
        sm: 24,
        xs: 24,
      },
      // 规格数据
      formDynamic: {
        attrsName: "",
        attrsVal: "",
      },
      isBtn: false,
      manyFormValidate: [],
      currentTab: 0,
      isChoice: "",
      grid: {
        xl: 8,
        lg: 8,
        md: 12,
        sm: 24,
        xs: 24,
      },
      ruleValidate: {
        storeName: [
          { required: true, message: "请输入商品名称", trigger: "blur" },
        ],
        cateIds: [
          {
            required: true,
            message: "请选择商品分类",
            trigger: "change",
            type: "array",
            min: "1",
          },
        ],
        unitName: [{ required: true, message: "请输入单位", trigger: "blur" }],
        storeInfo: [
          { required: true, message: "请输入商品标题", trigger: "blur" },
        ],
        price: [{ required: true, message: "请输入单价", trigger: "blur" }],
        stock: [{ required: true, message: "请输入库存量", trigger: "blur" }],
        tempId: [
          { required: true, message: "请选择运费模板", trigger: "change" },
        ],
        image: [{ required: true, message: "请上传商品图", trigger: "change" }],
        deliveryType: [
          { required: true, message: "请选择配送方式", trigger: "change" },
        ],
        freightType: [{ required: true, message: "请选择", trigger: "change" }],
        sliderImages: [
          {
            required: true,
            message: "请上传商品图",
            type: "array",
            trigger: "change",
          },
        ],
        specType: [
          { required: true, message: "请选择商品规格", trigger: "change" },
        ],
      },
      attrInfo: {},
      tableFrom: {
        page: 1,
        limit: 9999,
        keywords: "",
      },
      tempRoute: {},
      keyNum: 0,
      isAttr: false,
      showAll: false,
      videoLink: "",
    };
  },
  computed: {
    attrValue() {
      const obj = Object.assign({}, defaultObj.attrValue[0]);
      delete obj.image;
      return obj;
    },
    oneFormBatch() {
      const obj = [Object.assign({}, defaultObj.attrValue[0])];
      delete obj[0].barCode;
      return obj;
    },
    sliderImagesLength() {
      return this.formValidate.sliderImages.length;
    },
  },
  watch: {
    "formValidate.attr": {
      handler: function (val) {
        if (this.formValidate.specType && this.isAttr) this.watCh(val); //重要！！！
      },
      immediate: false,
      deep: true,
    },
    "formValidate.limitPurchaseCount": {
      handler: function (val) {
        if (val === false) {
          this.formValidate.maxPurchaseCount = null;
        }
      },
      immediate: false,
      deep: true,
    },
    "formValidate.limitSpecificUsers": {
      handler: function (val) {
        if (val === false) {
          this.formValidate.allowedLevels = [];
        }
      },
      immediate: false,
      deep: true,
    },
    "formValidate.storeStatus": {
      handler: function (val) {
        if (val !== 2) {
          this.saleTime = null;
        }
      },
      immediate: false,
      deep: true,
    },
    "formValidate.freightType": {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.postage = null;
        } else {
          this.formValidate.tempId = null;
        }
      },
      immediate: false,
      deep: true,
    },
    attrValueList: {
      handler: function (val) {
        if (val.length > 0) {
          this.formValidate.price = Math.min(...val.map((item) => item.price));
        } else {
          this.formValidate.price = null;
        }
      },
      immediate: false,
      deep: true,
    },
  },
  created() {
    this.tempRoute = Object.assign({}, this.$route);
    if (this.$route.params.id && this.formValidate.specType) {
      this.$watch("formValidate.attr", this.watCh);
    }
  },
  mounted() {
    this.formValidate.sliderImages = [];
    if (this.$route.params.id) {
      this.setTagsViewTitle();
      this.getInfo();
    }
    this.getCategorySelect();
    this.getShippingList();
    this.getGoodsType();
    // this.getUserList()
    this.getUserLevelList();
    // this.getProductSkuList()
  },
  methods: {
    querySearch(queryString, cb) {
      var restaurants = this.productSkuList;
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants;
      // 调用 callback 返回建议列表的数据
      cb(results);
    },
    // 获取会员等级字典
    getUserLevelList() {
      userLevelListApi().then((res) => {
        this.userLevelList = res;
      });
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        );
      };
    },
    handleSelect(item) {
      console.log(item);
    },
    // 获取规格添加历史记录列表
    getProductSkuList(unitName) {
      getProductSkuList({ unit: unitName }).then((res) => {
        this.productSkuList = res.map((item) => {
          return {
            value: item,
          };
        });
      });
    },
    // 获取规格添加历史记录列表
    changeUnit(unitName) {
      this.getProductSkuList(unitName);
    },

    // 校验输入框不能输入0，保留2位小数，库存为正整数
    keyupEvent(key, val, index, num) {
      if (key === "barCode") return;
      var re = /^\D*([0-9]\d*\.?\d{0,2})?.*$/;
      switch (num) {
        case 1:
          if (val == 0) {
            this.oneFormBatch[index][key] = key === "stock" ? 0 : 0.01;
          } else {
            this.oneFormBatch[index][key] =
              key === "stock"
                ? parseInt(val)
                : this.$set(
                    this.oneFormBatch[index],
                    key,
                    val.toString().replace(re, "$1")
                  );
          }
          break;
        case 2:
          if (val == 0) {
            this.OneattrValue[index][key] = key === "stock" ? 0 : 0.01;
          } else {
            this.OneattrValue[index][key] =
              key === "stock"
                ? parseInt(val)
                : this.$set(
                    this.OneattrValue[index],
                    key,
                    val.toString().replace(re, "$1")
                  );
          }
          break;
        default:
          if (val == 0) {
            this.ManyAttrValue[index][key] = key === "stock" ? 0 : 0.01;
          } else {
            this.ManyAttrValue[index][key] =
              key === "stock"
                ? parseInt(val)
                : this.$set(
                    this.ManyAttrValue[index],
                    key,
                    val.toString().replace(re, "$1")
                  );
          }
          break;
      }
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 获取可选用户列表
    getUserList() {
      userListApi({
        page: 1,
        limit: 999,
      }).then((res) => {
        this.userList = res.list;
      });
    },
    handleCloseCoupon(tag) {
      this.isAttr = true;
      this.formValidate.coupons.splice(
        this.formValidate.coupons.indexOf(tag),
        1
      );
      this.formValidate.couponIds.splice(
        this.formValidate.couponIds.indexOf(tag.id),
        1
      );
    },
    addCoupon() {
      const _this = this;
      this.$modalCoupon(
        "wu",
        (this.keyNum += 1),
        this.formValidate.coupons,
        function (row) {
          _this.formValidate.couponIds = [];
          _this.formValidate.coupons = row;
          row.map((item) => {
            _this.formValidate.couponIds.push(item.id);
          });
        },
        ""
      );
    },
    setTagsViewTitle() {
      const title = this.isDisabled ? "商品详情" : "编辑商品";
      const route = Object.assign({}, this.tempRoute, {
        title: `${title}-${this.$route.params.id}`,
      });
      this.$store.dispatch("tagsView/updateVisitedView", route);
    },
    onChangeGroup() {
      this.checkboxGroup.includes("isGood")
        ? (this.formValidate.isGood = true)
        : (this.formValidate.isGood = false);
      this.checkboxGroup.includes("isBenefit")
        ? (this.formValidate.isBenefit = true)
        : (this.formValidate.isBenefit = false);
      this.checkboxGroup.includes("isBest")
        ? (this.formValidate.isBest = true)
        : (this.formValidate.isBest = false);
      this.checkboxGroup.includes("isNew")
        ? (this.formValidate.isNew = true)
        : (this.formValidate.isNew = false);
      this.checkboxGroup.includes("isHot")
        ? (this.formValidate.isHot = true)
        : (this.formValidate.isHot = false);
    },
    watCh(val) {
      const tmp = {};
      const tmpTab = {};
      this.formValidate.attr.forEach((o, i) => {
        // tmp['value' + i] = { title: o.attrName }
        // tmpTab['value' + i] = ''
        tmp[o.attrName] = { title: o.attrName };
        tmpTab[o.attrName] = "";
      });
      this.ManyAttrValue = this.attrFormat(val);
      this.ManyAttrValue.forEach((val, index) => {
        const key = Object.values(val.attrValue).sort().join("/");
        if (this.attrInfo[key]) this.ManyAttrValue[index] = this.attrInfo[key];
      });
      this.attrInfo = [];
      this.ManyAttrValue.forEach((val) => {
        this.attrInfo[Object.values(val.attrValue).sort().join("/")] = val;
      });
      this.manyTabTit = tmp;
      this.manyTabDate = tmpTab;
      this.formThead = Object.assign({}, this.formThead, tmp);
    },
    attrFormat(arr) {
      let data = [];
      const res = [];
      return format(arr);
      function format(arr) {
        if (arr.length > 1) {
          arr.forEach((v, i) => {
            if (i === 0) data = arr[i]["attrValue"];
            const tmp = [];
            if (!data) return;
            data.forEach(function (vv) {
              arr[i + 1] &&
                arr[i + 1]["attrValue"] &&
                arr[i + 1]["attrValue"].forEach((g) => {
                  const rep2 =
                    (i !== 0 ? "" : arr[i]["attrName"] + "_") +
                    vv +
                    "$&" +
                    arr[i + 1]["attrName"] +
                    "_" +
                    g;
                  tmp.push(rep2);
                  if (i === arr.length - 2) {
                    const rep4 = {
                      image: "",
                      price: 0,
                      cost: 0,
                      otPrice: 0,
                      stock: 0,
                      barCode: "",
                      weight: 0,
                      volume: 0,
                      brokerage: 0,
                      brokerage_two: 0,
                    };
                    rep2.split("$&").forEach((h, k) => {
                      const rep3 = h.split("_");
                      if (!rep4["attrValue"]) rep4["attrValue"] = {};
                      rep4["attrValue"][rep3[0]] =
                        rep3.length > 1 ? rep3[1] : "";
                    });
                    for (let attrValueKey in rep4.attrValue) {
                      rep4[attrValueKey] = rep4.attrValue[attrValueKey];
                    }
                    res.push(rep4);
                  }
                });
            });
            data = tmp.length ? tmp : [];
          });
        } else {
          const dataArr = [];
          arr.forEach((v, k) => {
            v["attrValue"].forEach((vv, kk) => {
              dataArr[kk] = v["attrName"] + "_" + vv;
              res[kk] = {
                image: "",
                price: 0,
                cost: 0,
                otPrice: 0,
                stock: 0,
                barCode: "",
                weight: 0,
                volume: 0,
                brokerage: 0,
                brokerage_two: 0,
                attrValue: { [v["attrName"]]: vv },
              };
              // Object.values(res[kk].attrValue).forEach((v, i) => {
              //   res[kk]['value' + i] = v
              // })
              for (let attrValueKey in res[kk].attrValue) {
                res[kk][attrValueKey] = res[kk].attrValue[attrValueKey];
              }
            });
          });
          data.push(dataArr.join("$&"));
        }
        return res;
      }
    },
    // 运费模板
    addTem() {
      this.$refs.addTemplates.dialogVisible = true;
      this.$refs.addTemplates.getCityList();
    },
    // 添加规则；
    addRule() {
      const _this = this;
      this.$modalAttr(this.formDynamics, function () {
        _this.productGetRule();
      });
    },
    // 选择规格
    onChangeSpec(num) {
      this.isAttr = true;
      if (num) this.productGetRule();
    },
    // 选择商品标签
    onChangeSpec(num) {
      this.isAttr = true;
      if (num) this.productGetRule();
    },
    // 选择属性确认
    confirm() {
      this.isAttr = true;
      if (!this.formValidate.selectRule) {
        return this.$message.warning("请选择属性");
      }
      const data = [];
      this.ruleList.forEach((item) => {
        if (item.id === this.formValidate.selectRule) {
          item.ruleValue.forEach((i) => {
            data.push({
              attrName: i.value,
              attrValue: i.detail,
            });
          });
        }
        this.formValidate.attr = data;
      });
    },
    // 商品分类；
    getCategorySelect() {
      categoryApi({ status: -1, type: 1 }).then((res) => {
        this.merCateList = this.filerMerCateList(res);
        let newArr = [];
        res.forEach((value, index) => {
          newArr[index] = value;
          if (value.child)
            newArr[index].child = value.child.filter(
              (item) => item.status === true
            );
        }); //过滤商品分类设置为隐藏的子分类不出现在树形列表里
        this.merCateList = this.filerMerCateList(newArr);
      });
    },
    filerMerCateList(treeData) {
      return treeData.map((item) => {
        if (!item.child) {
          item.disabled = true;
        }
        item.label = item.name;
        return item;
      });
    },
    // 获取商品属性模板；
    productGetRule() {
      templateListApi(this.tableFrom).then((res) => {
        const list = res.list;
        for (var i = 0; i < list.length; i++) {
          list[i].ruleValue = JSON.parse(list[i].ruleValue);
        }
        this.ruleList = list;
      });
    },
    // 运费模板；
    getShippingList() {
      shippingTemplatesList(this.tempData).then((res) => {
        this.shippingList = res.list;
        if (!this.$route.params.id && this.shippingList.length > 0) {
          this.formValidate.tempId = this.shippingList[0].id;
        }
      });
    },
    showInput(item) {
      this.$set(item, "inputVisible", true);
    },
    onChangetype(item) {
      if (item === 1) {
        this.OneattrValue.map((item) => {
          this.$set(item, "brokerage", null);
          this.$set(item, "brokerageTwo", null);
        });
        this.ManyAttrValue.map((item) => {
          this.$set(item, "brokerage", null);
          this.$set(item, "brokerageTwo", null);
        });
      } else {
        this.OneattrValue.map((item) => {
          delete item.brokerage;
          delete item.brokerageTwo;
          this.$set(item, "brokerage", null);
          this.$set(item, "brokerageTwo", null);
        });
        this.ManyAttrValue.map((item) => {
          delete item.brokerage;
          delete item.brokerageTwo;
        });
      }
    },
    // 删除表格中的属性
    delAttrTable(index, name) {
      this.addRuleFormList.splice(index, 1);
      this.attrValueList = this.attrValueList.filter((item) => {
        return item.attrName !== name;
      });
    },

    // 添加规格失去焦点
    handlerBlur() {
      this.attrValueList = this.addRuleFormList.filter((item) => {
        return item.attrName !== "";
      });
    },
    // 添加规格
    addAttrTable(index) {
      this.addRuleFormList.push({
        attrName: "",
        image:
          this.sliderImagesLength > 0 ? this.formValidate.sliderImages[0] : "",
        price: 0,
        otPrice: 0,
        stock: 0,
        cost: 0,
        sort: 0,
        key: this.generateUniqueRandomNumber(),
      });
    },
    generateUniqueRandomNumber() {
      // 生成一个0到1之间的随机数
      let random = Math.random();
      // 获取当前时间戳
      let timestamp = new Date().getTime();
      // 将随机数和时间戳结合，生成一个唯一的随机数
      let uniqueRandomNumber = random * timestamp;
      return uniqueRandomNumber;
    },
    // 批量添加
    batchAdd() {
      // if (!this.oneFormBatch[0].pic || !this.oneFormBatch[0].price || !this.oneFormBatch[0].cost || !this.oneFormBatch[0].ot_price ||
      //     !this.oneFormBatch[0].stock || !this.oneFormBatch[0].bar_code) return this.$Message.warning('请填写完整的批量设置内容！');
      for (const val of this.ManyAttrValue) {
        this.$set(val, "image", this.oneFormBatch[0].image);
        this.$set(val, "price", this.oneFormBatch[0].price);
        this.$set(val, "cost", this.oneFormBatch[0].cost);
        this.$set(val, "otPrice", this.oneFormBatch[0].otPrice);
        this.$set(val, "stock", this.oneFormBatch[0].stock);
        this.$set(val, "barCode", this.oneFormBatch[0].barCode);
        this.$set(val, "weight", this.oneFormBatch[0].weight);
        this.$set(val, "volume", this.oneFormBatch[0].volume);
        this.$set(val, "brokerage", this.oneFormBatch[0].brokerage);
        this.$set(val, "brokerageTwo", this.oneFormBatch[0].brokerageTwo);
      }
    },
    // 添加按钮
    addBtn() {
      this.clearAttr();
      this.isBtn = true;
    },
    // 取消
    offAttrName() {
      this.isBtn = false;
    },
    clearAttr() {
      this.isAttr = true;
      this.formDynamic.attrsName = "";
      this.formDynamic.attrsVal = "";
    },
    // 删除规格
    handleRemoveAttr(index) {
      this.isAttr = true;
      this.formValidate.attr.splice(index, 1);
      this.manyFormValidate.splice(index, 1);
    },
    // 删除属性
    handleClose(item, index) {
      item.splice(index, 1);
    },
    // 添加规则名称
    createAttrName() {
      this.isAttr = true;
      if (this.formDynamic.attrsName && this.formDynamic.attrsVal) {
        const data = {
          attrName: this.formDynamic.attrsName,
          attrValue: [this.formDynamic.attrsVal],
        };
        this.formValidate.attr.push(data);
        var hash = {};
        this.formValidate.attr = this.formValidate.attr.reduce(function (
          item,
          next
        ) {
          /* eslint-disable */
          hash[next.attrName]
            ? ""
            : (hash[next.attrName] = true && item.push(next));
          return item;
        },
        []);
        this.clearAttr();
        this.isBtn = false;
      } else {
        this.$Message.warning("请添加完整的规格！");
      }
    },
    // 添加属性
    createAttr(num, idx) {
      this.isAttr = true;
      if (num) {
        this.formValidate.attr[idx].attrValue.push(num);
        var hash = {};
        this.formValidate.attr[idx].attrValue = this.formValidate.attr[
          idx
        ].attrValue.reduce(function (item, next) {
          /* eslint-disable */
          hash[next] ? "" : (hash[next] = true && item.push(next));
          return item;
        }, []);
        this.formValidate.attr[idx].inputVisible = false;
      } else {
        this.$message.warning("请添加属性");
      }
    },
    //点击展示所有多规格属性
    showAllSku() {
      if (this.isAttr == false) {
        this.isAttr = true;
        if (this.formValidate.specType && this.isAttr)
          this.watCh(this.formValidate.attr); //重要！！！
      } else if (this.isAttr == true) {
        this.isAttr = false;
        this.getInfo();
      }
    },
    // 详情
    getInfo() {
      this.fullscreenLoading = true;
      productDetailApi(this.$route.params.id)
        .then(async (res) => {
          // this.isAttr = true;

          let info = res;
          this.formValidate = {
            image: info.image,
            sliderImage: info.sliderImage,
            sliderImages: JSON.parse(info.sliderImage),
            storeName: info.storeName,
            storeInfo: info.storeInfo,
            price: info.price,
            keyword: info.keyword,
            videoLink: info.videoLink,
            cateIds: info.cateId.split(","), // 商品分类id
            cateId: info.cateId, // 商品分类id传值
            unitName: info.unitName,
            sort: info.sort,
            stock: info.stock,
            storeStatus: info.storeStatus,
            isShow: info.isShow,
            isBenefit: info.isBenefit,
            isNew: info.isNew,
            deliveryType: info.deliveryType ? info.deliveryTyp.split(",") : [0],
            freightType: info.freightType,
            postage: info.postage,
            supportExchange: info.supportExchange,
            supportReturn7Days: info.supportReturn7Days,
            limitPurchaseCount: info.limitPurchaseCount,
            maxPurchaseCount: info.maxPurchaseCount,
            limitSpecificUsers: info.limitSpecificUsers,
            allowedLevels: info.allowedLevels,
            isGood: info.isGood,
            isHot: info.isHot,
            isBest: info.isBest,
            tempId: info.tempId,
            // tag: info.tag ? info.tag.split(",") : [],
            popularity: info.popularity,
            attr: info.attr,
            attrValue: info.attrValue,
            selectRule: info.selectRule,
            isSub: info.isSub,
            // content: this.$selfUtil.replaceImgSrcHttps(info.content),
            content: info.content,
            specType: info.specType,
            id: info.id,
            giveIntegral: info.giveIntegral,
            ficti: info.ficti,
            coupons: info.coupons,
            couponIds: info.couponIds,
            activity: info.activityStr
              ? info.activityStr.split(",")
              : ["默认", "秒杀", "砍价", "拼团"],
          };

          // 获取规格添加历史记录列表
          if (info.unitName) {
            this.getProductSkuList(info.unitName);
          }

          // 判断是否定时上架商品
          if (info.saleTime) {
            this.saleTime = this.parseTime(
              info.saleTime,
              "{y}-{m}-{d} {h}:{i}:{s}"
            );
          }

          this.detailsImages = info.detailImages
            ? info.detailImages.split(",")
            : [];
          // 规格明细
          this.attrValueList = info.attrValue.map(
            ({
              attrName,
              image,
              price,
              otPrice,
              stock,
              quantity,
              cost,
              sort,
            }) => {
              return {
                attrName,
                image,
                quantity,
                price,
                otPrice,
                stock,
                cost,
                sort,
              };
            }
          );
          this.addRuleFormList = info.attrValue.map((item) => {
            return {
              ...item,
              key: this.generateUniqueRandomNumber(),
            };
          });
          let imgs = JSON.parse(info.sliderImage);
          let imgss = [];
          Object.keys(imgs).map((i) => {
            imgss.push(this.$selfUtil.setDomain(imgs[i]));
          });
          this.formValidate.sliderImages = [...imgss];
          if (info.isHot) this.checkboxGroup.push("isHot");
          if (info.isGood) this.checkboxGroup.push("isGood");
          if (info.isBenefit) this.checkboxGroup.push("isBenefit");
          if (info.isBest) this.checkboxGroup.push("isBest");
          if (info.isNew) this.checkboxGroup.push("isNew");
          this.productGetRule();

          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    handleRemove(i) {
      this.formValidate.sliderImages.splice(i, 1);
    },
    handleRemoveDetailsImg(i) {
      this.detailsImages.splice(i, 1);
    },
    // 点击商品图
    modalPicTap(tit, num, i, status) {
      const _this = this;
      if (_this.isDisabled) return;
      this.$modalUpload(
        function (img) {
          if (tit === "1" && !num) {
            _this.formValidate.image = img[0].sattDir;
            _this.OneattrValue[0].image = img[0].sattDir;
          }
          if (tit === "2" && !num) {
            if (img.length > 6)
              return this.$message.warning("最多选择6张图片！");
            if (img.length + _this.formValidate.sliderImages.length > 6)
              return this.$message.warning("最多选择6张图片！");
            img.map((item) => {
              _this.formValidate.sliderImages.push(item.attDir);
            });
          }
          if (tit === "1" && num === "dan") {
            _this.OneattrValue[0].image = img[0].sattDir;
          }
          if (tit === "1" && num === "duo") {
            _this.attrValueList[i].image = img[0].attDir;
          }
          if (tit === "1" && num === "pi") {
            _this.oneFormBatch[0].image = img[0].sattDir;
          }
          // 主图视频
          if (tit === "3") {
            _this.formValidate.videoLink = img[0].sattDir;
          }
          // 详情图片处理
          if (tit === "4" && !num) {
            // if (img.length > 6)
            //   return this.$message.warning("最多选择6张图片！");
            // if (img.length + _this.detailsImages.length > 6)
            //   return this.$message.warning("最多选择6张图片！");
            img.map((item) => {
              if (item.splitImageUrls) {
                // 说明是长图进行了分割了
                _this.detailsImages = [
                  ..._this.detailsImages,
                  ...item.splitImageUrls.split(","),
                ];
              } else {
                _this.detailsImages.push(item.attDir);
              }
            });
          }
        },
        tit,
        "content"
      );
    },
    handleSubmitUp() {
      // this.currentTab --
      if (this.currentTab-- < 0) this.currentTab = 0;
    },
    handleSubmitNest(name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          if (this.currentTab++ > 2) this.currentTab = 0;
        } else {
          if (
            !this.formValidate.store_name ||
            !this.formValidate.cate_id ||
            !this.formValidate.keyword ||
            !this.formValidate.unit_name ||
            !this.formValidate.store_info ||
            !this.formValidate.image ||
            !this.formValidate.slider_image
          ) {
            this.$message.warning("请填写完整商品信息！");
          }
        }
      });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.onChangeGroup();
      // if (this.formValidate.specType && this.formValidate.attr.length < 1)
      //   return this.$message.warning("请填写多规格属性！");
      this.formValidate.cateId = this.formValidate.cateIds.join(",");
      this.formValidate.sliderImage = JSON.stringify(
        this.formValidate.sliderImages
      );

      // 定时开售字段转时间戳
      if (this.saleTime) {
        this.formValidate.saleTime = parseInt(
          new Date(this.saleTime).getTime().toString().slice(0, -3)
        );
      } else {
        this.formValidate.saleTime = 0;
      }

      //  处理商品明细传值
      const attrValue = this.attrValueList.map((item, index) => {
        return {
          ...item,
          attrValue: JSON.stringify({
            [this.formValidate.unitName]: item.attrName,
          }),
        };
      });

      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.params.id
            ? productUpdateApi({
                ...this.formValidate,
                deliveryType:
                  this.formValidate.deliveryType &&
                  this.formValidate.deliveryType.join(","),

                detailImages: this.detailsImages.join(","),
                image: this.formValidate.sliderImages[0],
                thumbnailImage: this.handlerImgUrl(
                  this.formValidate.sliderImages[0]
                ),
                attrValue,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/store/index" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : productCreateApi({
                ...this.formValidate,
                deliveryType:
                  this.formValidate.deliveryType &&
                  this.formValidate.deliveryType.join(","),
                detailImages: this.detailsImages.join(","),
                // tag: this.formValidate.tag.join(','),
                image: this.formValidate.sliderImages[0],
                thumbnailImage: this.handlerImgUrl(
                  this.formValidate.sliderImages[0]
                ),
                attrValue,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/store/index" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          if (
            !this.formValidate.storeName ||
            !this.formValidate.cateId ||
            !this.formValidate.keyword ||
            !this.formValidate.unitName ||
            !this.formValidate.storeInfo ||
            !this.formValidate.image ||
            !this.formValidate.sliderImages
          ) {
            this.$message.warning("请填写完整商品信息！");
          }
        }
      });
    }),
    // 表单验证
    validate(prop, status, error) {
      if (status === false) {
        this.$message.warning(error);
      }
    },
    // 移动
    handleDragStart(e, item) {
      if (!this.isDisabled) this.dragging = item;
    },
    handleDragEnd(e, item, type) {
      if (!this.isDisabled) this.dragging = null;
      if (type == "sliderImages") {
        this.attrValueList.forEach((item, index) => {
          item.image = this.formValidate.sliderImages[0];
        });
      }
    },
    handleDragOver(e) {
      if (!this.isDisabled) e.dataTransfer.dropEffect = "move";
    },
    handleDragEnter(e, item) {
      if (!this.isDisabled) {
        e.dataTransfer.effectAllowed = "move";
        if (item === this.dragging) {
          return;
        }
        const newItems = [...this.formValidate.sliderImages];
        const src = newItems.indexOf(this.dragging);
        const dst = newItems.indexOf(item);
        newItems.splice(dst, 0, ...newItems.splice(src, 1));
        this.formValidate.sliderImages = newItems;
      }
    },
    handleDragEnterDetails(e, item) {
      if (!this.isDisabled) {
        e.dataTransfer.effectAllowed = "move";
        if (item === this.dragging) {
          return;
        }
        const newItems = [...this.detailsImages];
        const src = newItems.indexOf(this.dragging);
        const dst = newItems.indexOf(item);
        newItems.splice(dst, 0, ...newItems.splice(src, 1));
        this.detailsImages = newItems;
      }
    },
    handleDragEnterFont(e, item) {
      if (!this.isDisabled) {
        e.dataTransfer.effectAllowed = "move";
        if (item === this.dragging) {
          return;
        }
        const newItems = [...this.formValidate.activity];
        const src = newItems.indexOf(this.dragging);
        const dst = newItems.indexOf(item);
        newItems.splice(dst, 0, ...newItems.splice(src, 1));
        this.formValidate.activity = newItems;
      }
    },
    getGoodsType() {
      /** 让商品推荐列表的name属性与页面设置tab的name匹配**/
      goodDesignList({ gid: 70 }).then((response) => {
        let list = response.list;
        let arr = [],
          arr1 = [];
        const listArr = [{ name: "是否热卖", value: "isGood" }];
        let typeLists = [
          { name: "", value: "isHot", type: "2" }, //热门榜单
          { name: "", value: "isBenefit", type: "4" }, //促销单品
          { name: "", value: "isBest", type: "1" }, //精品推荐
          { name: "", value: "isNew", type: "3" },
        ]; //首发新品
        list.forEach((item) => {
          let obj = {};
          obj.value = JSON.parse(item.value);
          obj.id = item.id;
          obj.gid = item.gid;
          obj.status = item.status;
          arr.push(obj);
        });
        arr.forEach((item1) => {
          let obj1 = {};
          obj1.name = item1.value.fields[1].value;
          obj1.status = item1.status;
          obj1.type = item1.value.fields[3].value;
          arr1.push(obj1);
        });
        typeLists.forEach((item) => {
          arr1.forEach((item1) => {
            if (item.type == item1.type) {
              listArr.push({
                name: item1.name,
                value: item.value,
                type: item.type,
              });
            }
          });
        });
        this.recommend = listArr;
      });
    },
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .steps {
    margin-bottom: 16px;
  }
  .priamry_border {
    border: 1px solid #1890ff;
    color: #1890ff;
  }
  .color-item {
    height: 30px;
    line-height: 30px;
    padding: 0 10px;
    color: #fff;
    margin-right: 10px;
  }
  .color-list .color-item.blue {
    background-color: #1e9fff;
  }
  .color-list .color-item.yellow {
    background-color: rgb(254, 185, 0);
  }
  .color-list .color-item.green {
    background-color: #009688;
  }
  .color-list .color-item.red {
    background-color: #ed4014;
  }
  .proCoupon {
    ::v-deep.el-form-item__content {
      margin-top: 5px;
    }
  }
  .tabPic {
    width: 40px !important;
    height: 40px !important;
    img {
      width: 100%;
      height: 100%;
    }
  }
  .noLeft {
    ::v-deep.el-form-item__content {
      margin-left: 0 !important;
    }
  }
  .tabNumWidth {
    ::v-deep.el-input-number--medium {
      width: 121px !important;
    }
    ::v-deep.el-input-number__increase {
      width: 20px !important;
      font-size: 12px !important;
    }
    ::v-deep.el-input-number__decrease {
      width: 20px !important;
      font-size: 12px !important;
    }
    ::v-deep.el-input-number--medium .el-input__inner {
      padding-left: 25px !important;
      padding-right: 25px !important;
    }
    ::v-deep thead {
      line-height: normal !important;
    }
    ::v-deep .el-table .cell {
      line-height: normal !important;
    }
  }
  .selWidth {
    width: 100%;
  }
  .selWidthd {
    width: 300px;
  }
  .button-new-tag {
    height: 28px;
    line-height: 26px;
    padding-top: 0;
    padding-bottom: 0;
  }
  .input-new-tag {
    width: 90px;
    margin-left: 10px;
    vertical-align: bottom;
  }
  .pictrue {
    width: 60px;
    height: 60px;
    border: 1px dotted rgba(0, 0, 0, 0.1);
    margin-right: 10px;
    position: relative;
    cursor: pointer;
    /* img {
      width: 100%;
      height: 100%;
    } */
    video {
      width: 100%;
      height: 100%;
    }
  }
  .btndel {
    position: absolute;
    z-index: 1;
    width: 20px !important;
    height: 20px !important;
    left: 46px;
    top: -4px;
  }

  .iview-video-style {
    width: 300px;
    height: 180px;
    border-radius: 10px;
    background-color: #707070;
    margin: 0 120px 20px;
    position: relative;
    overflow: hidden;
  }

  .iview-video-style .iconv {
    color: #fff;
    line-height: 180px;
    width: 50px;
    height: 50px;
    display: inherit;
    font-size: 26px;
    position: absolute;
    top: -74px;
    left: 50%;
    margin-left: -25px;
  }

  .iview-video-style .mark {
    position: absolute;
    width: 100%;
    height: 30px;
    top: 0;
    background-color: rgba(0, 0, 0, 0.5);
    text-align: center;
  }

  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
    .upLoad {
      width: 60px;
      height: 60px;
    }
  }
  .form-info {
    padding-left: 80px;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
    white-space: nowrap;
  }

  .store-details {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
    padding-bottom: 10px;
    .form-info {
      flex: 1;
      display: flex;
      overflow-y: auto;

      .details-left {
        position: sticky;
        top: 0;
        .detail-description {
          margin-top: 10px;
          div {
            line-height: 1.5;
          }
        }
      }
      .details-right {
        flex: 1;
        display: flex;
        justify-content: center;
        .details-row {
          width: 400px;
          .upLoadPicBox {
            margin-top: 2px;
            width: 100%;
            height: 200px;
            border: 1px dashed #efefef;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            &:hover {
              border: 1px dashed #3d65f4;
            }
            .upLoad {
              width: 70px;
              height: 70px;
              border-radius: 0;
              border: none;
              border: 6px solid #fff;
              border-top: 4px solid #fff;
            }
            background-color: rgb(248, 248, 248);
            .update-des {
              margin-top: 10px;
            }
          }
          .preview-imgs {
            width: 100%;
            border: 1px solid #ece9e9;
            min-height: 600px;
            background-color: #f9f9f9;
            .preview-top {
              width: 100%;
              line-height: 40px;
              text-align: center;
              font-size: 14px;
              color: #000000;
              background-color: #ece9e9;
            }
            .preview-main {
              height: 500px;
              .not-upLoad {
                height: 200px;
                border: 1px dashed #87aae6;
                background-color: #ebf8fd;
                color: #88c4dd;
                text-align: center;
                padding-top: 50px;
                .not_preview {
                  font-size: 50px;
                }
                .preview-des {
                  margin-top: 10px;
                  > div {
                    &:first-child {
                      margin-bottom: 6px;
                    }
                  }
                }
              }
              .details-img {
                width: 100%;
                position: relative;
                border: 1px solid #e1e1e1;
                cursor: move;
                img {
                  width: 100%;
                  display: block;
                }
                .deleteBtn {
                  position: absolute;
                  font-size: 18px;
                  top: -10px;
                  right: -10px;
                  cursor: pointer;
                  &:hover {
                    color: rgb(214, 0, 0);
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .specification-table {
    .table-box {
      border: 1px solid #dcdfe6;
      padding: 10px 10px 0;
      border-radius: 4px;
      .pictrue {
        margin-right: 0;
      }
    }
  }

  .area-box {
    background-color: #f7f7f7;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    gap: 12px;
    border-radius: 4px;
  }
  .input-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    .item {
      position: relative;
      .delete-btn {
        display: none;
        position: absolute;
        line-height: 1;
        right: -6px;
        top: -6px;
        background-color: #aeaeae;
        padding: 3px;
        color: white;
        border-radius: 50%;
        cursor: pointer;
        &:hover {
          background-color: #818181;
        }
      }
      &:hover .delete-btn {
        display: block;
      }
    }
  }
  .store-img {
    position: relative;
    border-radius: 4px;
    .master-image-flag {
      position: absolute;
      top: 0;
      left: 0;
      background-color: #61ae64;
      border-radius: 4px 0 4px 0;
      color: #fff;
      font-size: 12px;
      line-height: 1;
      padding: 2px;
    }
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
