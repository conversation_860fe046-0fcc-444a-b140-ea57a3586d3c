<template>
  <MainCard>
    <div class="divBox">
      <div class="header-btn">
        <MyHeaderSearch>
          <template v-slot:left>
            <router-link :to="{ path: '/store/matchList/creatMatch/' }">
              <el-button type="primary">添加组合</el-button></router-link
            >
          </template>
          <template v-slot:right>
            <el-form inline size="small">
              <el-form-item label="产品组合名：">
                <el-input
                  v-model="tableFrom.name"
                  placeholder="产品组合名称检索"
                  class="selWidth"
                  clearable
                >
                  <el-button
                    slot="append"
                    icon="el-icon-search"
                    @click="seachList"
                  />
                </el-input>
              </el-form-item>
            </el-form>
          </template>
        </MyHeaderSearch>
      </div>
      <div class="tabs">
        <el-tabs v-model="tableFrom.statusType" @tab-click="seachList">
          <el-tab-pane
            v-for="(item, index) in headeNum"
            :label="item.name + '(' + item.count + ')'"
            :name="item.statusType.toString()"
            :key="index"
          />
        </el-tabs>
      </div>
      <div class="tips">
        <el-checkbox
          :indeterminate="isIndeterminate"
          v-model="checkAll"
          @change="handleCheckAllChange"
          style="margin-right: 6px"
        ></el-checkbox>
        <span class="mr10">已选 {{ selectStoreId.length }}</span>
        <span v-if="tableFrom.statusType == 3" class="mr10">
          <el-button @click="handlerBatch('上架')" size="small"
            >上架</el-button
          ></span
        >
        <span
          v-if="tableFrom.statusType == 1 || tableFrom.statusType == 2"
          class="mr10"
        >
          <el-button @click="handlerBatch('下架')" size="small"
            >下架</el-button
          ></span
        >
        <span v-if="tableFrom.statusType == 3" class="mr10">
          <el-button @click="handlerBatch('删除')" size="small"
            >删除</el-button
          ></span
        >
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <!-- <el-table-column
          type="selection"
          width="55"
        /> -->
        <el-table-column align="center" type="selection" width="55">
        </el-table-column>
        <el-table-column label="组合名称" min-width="280">
          <template slot-scope="scope">
            <div class="store-match-name">
              {{ scope.row.name }}
            </div>
            <div class="store-list">
              <span @click="onUnfold(scope.row)" class="specification-num">
                <span>商品({{ scope.row.productIds.length }})</span>
                <i
                  :class="
                    scope.row.isFold ? 'el-icon-arrow-down' : 'el-icon-arrow-up'
                  "
                ></i>
              </span>
              <div class="specification-list" v-if="!scope.row.isFold">
                <div
                  v-for="(item, index) in scope.row.productItems"
                  :key="index"
                  class="item"
                >
                  <MyImage
                    :imagePath="handlerImgUrl(item.image)"
                    :previewPath="item.image"
                    :size="40"
                  />
                  <div>
                    <div class="store-name">{{ item.name }}</div>
                    <div>
                      ¥{{ item.price }}，{{ item.quantity }}{{ item.unitName }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="combinationPrice" label="组合价" min-width="150">
          <template slot-scope="scope">
            <div class="flex-box">
              <span>{{ scope.row.combinationPrice }}</span
              ><span class="favorable-price"
                >省{{ getPreferentialPrice(scope.row) }}</span
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="initialSales" label="虚拟销量">
        </el-table-column>
        <el-table-column
          v-if="tableFrom.statusType != 2"
          label="售卖时间"
          min-width="150"
          ><template slot-scope="scope">
            <span v-if="scope.row.storeStatus == 2">
              <span
                >{{ parseTime(scope.row.startTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
                至
                {{
                  parseTime(scope.row.endTime, "{y}-{m}-{d} {h}:{i}:{s}")
                }}</span
              >
            </span>
          </template></el-table-column
        >
        <el-table-column label="状态" min-width="120">
          <template>
            <span v-if="tableFrom.statusType == 1"> 在售中 </span>
            <span v-if="tableFrom.statusType == 2"> 已售空 </span>
            <span v-if="tableFrom.statusType == 3"> 仓库中 </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center">
          <template slot-scope="scope">
            <router-link
              :to="{ path: '/store/matchList/creatMatch/' + scope.row.id }"
            >
              <el-button
                type="text"
                size="small"
                class="mr10"
                v-hasPermi="[
                  'admin:product:rule:update',
                  'admin:product:rule:info',
                ]"
                >编辑</el-button
              >
            </router-link>
            <el-button
              v-if="tableFrom.statusType == 3"
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              v-hasPermi="['admin:product:rule:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>
<script>
import {
  storeCombinationListApi,
  productCombinationStatusCountApi,
  productCombinationBatchDeleteApi,
  productCombinationBatchShellApi,
  productCombinationDeleteApi,
} from "@/api/storeMatch";

const apiMap = {
  上架: productCombinationBatchShellApi,
  下架: productCombinationBatchShellApi,
  删除: productCombinationBatchDeleteApi,
};
export default {
  name: "StoreAttr",
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20,
        statusType: "1",
        name: "",
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      checkAll: false,
      isIndeterminate: false,
      headeNum: [],
      selectStoreId: [],
      listLoading: true,
    };
  },
  mounted() {
    this.goodHeade();
    this.getList();
  },
  methods: {
    seachList() {
      this.tableFrom.page = 1;
      this.getList();
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection();
    },
    handleSelectionChange(val) {
      this.selectStoreId = val.map((item) => item.id);
      if (this.selectStoreId.length == 0) {
        this.isIndeterminate = false;
        this.checkAll = false;
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectStoreId.length == this.tableData.total) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        } else {
          if (this.selectStoreId.length == this.tableFrom.limit) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        }
      }
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 计算优惠价
    getPreferentialPrice(row) {
      const oldTotalPrice = row.productItems.reduce(function (total, item) {
        return total + item.price * item.quantity;
      }, 0);
      return oldTotalPrice - row.combinationPrice;
    },
    // /api/admin/product/combination/status/count
    goodHeade() {
      productCombinationStatusCountApi()
        .then((res) => {
          const countMap = [
            { name: "在售中", key: "onSaleCount", statusType: 1 },
            { name: "已售罄", key: "soldOutCount", statusType: 2 },
            { name: "仓库中", key: "inStorageCount", statusType: 3 },
          ];
          this.headeNum = countMap.map((item) => {
            return { ...item, count: res[item.key] };
          });
        })
        .catch((res) => {
          this.$message.error(res.message);
        });
    },
    // 批量处理
    handlerBatch(typeName) {
      if (this.selectStoreId.length == 0) {
        return this.$message.warning(`请选择需要${typeName}的产品`);
      }
      this.$modalSure(`要${typeName}选中的组合产品吗`).then(() => {
        const apifun = apiMap[typeName];
        let storeStatus = null;
        if (typeName === "下架") {
          storeStatus = 0;
        } else if (typeName === "上架") {
          storeStatus = 1;
        }
        apifun(this.selectStoreId, storeStatus).then(() => {
          this.$message.success("操作成功");
          this.getList();
          this.goodHeade();
        });
      });
    },
    // 列表
    getList() {
      this.listLoading = true;
      storeCombinationListApi(this.tableFrom)
        .then((res) => {
          const list = res.list.map((item) => {
            return { ...item, isFold: true };
          });
          this.tableData.data = list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    onUnfold(row) {
      row.isFold = !row.isFold;
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除该组合产品？")
        .then(() => {
          productCombinationDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.selWidth {
  width: 350px !important;
}
.seachTiele {
  line-height: 35px;
}
.fr {
  float: right;
}

.divBox {
  .search-form {
    background-color: #f7f7f7;
    padding: 20px;
  }
  .tips {
    margin-bottom: 16px;
  }
  .tabs {
    margin-top: -6px;
    margin-bottom: 6px;
  }
  .store-info {
    .store-img {
      display: flex;
      align-items: center;
      gap: 10px;
      & > span {
        color: #333;
      }
    }
  }

  .store-list {
    margin-top: 6px;

    .specification-num {
      cursor: pointer;
      color: #dc3c2e;
      &:hover {
        color: #df5c51;
      }
    }
    .specification-list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      .item {
        .store-name {
          color: #333;
        }
        background-color: #f6f6f6;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        overflow: hidden;
        padding: 6px 10px 6px;
      }
    }
  }
  .flex-box {
    display: flex;
    gap: 4px;
    align-items: center;
    line-height: 1em;
    .favorable-price {
      background-color: #dc3c2e;
      color: white;
      padding: 2px;
      border-radius: 2px;
    }
  }
}
</style>
