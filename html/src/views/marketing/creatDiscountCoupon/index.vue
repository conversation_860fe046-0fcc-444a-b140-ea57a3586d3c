<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="store-info base-card">
            <div class="info-title">基本信息</div>
            <div class="form-info">
              <el-form-item
                style="width: 60%"
                label="优惠券名称："
                prop="name"
              >
                <el-input v-model="formValidate.name" />
              </el-form-item>

              <el-form-item
                required
                :label="couponTypeVal == 3 ? '权益内容：' : '优惠内容：'"
              >
                <el-radio-group v-model="formValidate.couponType">
                  <!-- @change="onChangeSpec(formValidate.specType)" -->
                  <div class="flex-colomn">
                    <el-radio :label="1">
                      <span class="inline-flex">
                        <span>订单满</span
                        ><span>
                          <el-form-item prop="minPrice"
                            ><el-input
                              :disabled="formValidate.couponType === 2"
                              v-model="formValidate.minPrice"
                            >
                              <template slot="append">元</template>
                            </el-input>
                          </el-form-item></span
                        ><span>，减</span
                        ><span
                          ><el-form-item prop="money1"
                            ><el-input
                              :disabled="formValidate.couponType === 2"
                              v-model="formValidate.money1"
                            >
                              <template slot="append">元</template>
                            </el-input></el-form-item
                          ></span
                        >
                      </span>
                    </el-radio>
                    <el-radio :label="2">
                      <span class="inline-flex">
                        <span>无门槛，减</span
                        ><span
                          ><el-input
                            :disabled="formValidate.couponType === 1"
                            v-model="formValidate.money2"
                          >
                            <template slot="append">元</template>
                          </el-input>
                        </span>
                      </span>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>

              <el-form-item
                required
                v-if="couponTypeVal == 1"
                label="用券时间："
              >
                <el-radio-group v-model="formValidate.useTimeType">
                  <div class="flex-colomn">
                    <el-radio :label="1">
                      <span class="inline-flex">
                        <span></span>
                        <span class="inline-flex">
                          <el-form-item>
                            <el-date-picker
                              :disabled="formValidate.useTimeType !== 1"
                              v-model="formValidate.useStartTime"
                              type="datetime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              placeholder="售卖开始时间"
                            >
                            </el-date-picker>
                          </el-form-item>
                          <span>至</span>
                          <el-form-item>
                            <el-date-picker
                              :disabled="formValidate.useTimeType !== 1"
                              v-model="formValidate.useEndTime"
                              type="datetime"
                              placeholder="售卖结束时间"
                              value-format="yyyy-MM-dd HH:mm:ss"
                            >
                            </el-date-picker>
                          </el-form-item>
                        </span>
                      </span>
                    </el-radio>
                    <el-radio :label="2">
                      <span class="inline-flex">
                        <span>领券后立即生效，有效期</span
                        ><span
                          ><el-input
                            :disabled="formValidate.useTimeType !== 2"
                            v-model="formValidate.day1"
                          >
                            <template slot="append">天</template>
                          </el-input>
                        </span>
                      </span>
                    </el-radio>
                    <el-radio :label="3">
                      <span class="inline-flex">
                        <span>领券后</span
                        ><span
                          ><el-input
                            :disabled="formValidate.useTimeType !== 3"
                            v-model="formValidate.afterDays"
                          >
                            <template slot="append">天</template>
                          </el-input>
                        </span>
                        <span>生效，有效期</span
                        ><span
                          ><el-input
                            :disabled="formValidate.useTimeType !== 3"
                            v-model="formValidate.day2"
                          >
                            <template slot="append">天</template>
                          </el-input>
                        </span>
                      </span>
                    </el-radio>
                  </div>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                required
                v-if="couponTypeVal == 3"
                label="发券时间："
              >
                <el-radio-group v-model="formValidate.sendTimeType">
                  <el-radio :label="0"> 无限制 </el-radio>
                  <el-radio :label="1">
                    <span class="inline-flex">
                      <span>生日</span
                      ><span
                        ><el-select
                          :disabled="formValidate.sendTimeType !== 1"
                          v-model="formValidate.birthSendType"
                          placeholder="请选择"
                        >
                          <el-option
                            label="生日当天"
                            :value="1"
                          >
                          </el-option>
                          <el-option
                            label="生日当周"
                            :value="2"
                          >
                          </el-option>
                          <el-option
                            label="生日当月"
                            :value="3"
                          >
                          </el-option>
                        </el-select>
                      </span>
                    </span>
                  </el-radio>
                  <el-radio :label="2">
                    <span class="inline-flex">
                      <span>每月</span
                      ><span
                        ><el-input
                          :disabled="formValidate.sendTimeType !== 2"
                          v-model="formValidate.monthSendDay"
                        >
                          <template slot="append">日</template>
                        </el-input>
                      </span>
                    </span>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                prop="day"
                v-if="couponTypeVal != 1"
                label="用券时间："
              >
                <span class="inline-flex">
                  <span v-if="couponTypeVal == 2">领券后立即生效，有效期</span>
                  <span v-if="couponTypeVal == 3">系统自动发放，有效期</span>
                  <el-input v-model="formValidate.day">
                    <template slot="append">天</template>
                  </el-input>
                </span>
              </el-form-item>
              <el-form-item
                v-if="couponTypeVal == 1"
                label="发放总量："
              >
                <span class="inline-flex"
                  ><el-input
                    placeholder="最多10000000"
                    v-model="formValidate.total"
                  >
                    <template slot="append">张</template>
                  </el-input>
                </span>
                <div class="detail-description">
                  修改优惠券总量时只能增加不能减少，请谨慎设置
                </div>
              </el-form-item>
              <el-form-item
                prop="useType"
                label="适用商品："
              >
                <el-radio-group v-model="formValidate.useType">
                  <el-radio :label="1"> 全部商品可用 </el-radio>
                  <el-radio :label="2"> 指定商品可用 </el-radio>
                  <el-radio :label="3"> 指定商品不可用 </el-radio>
                </el-radio-group>
                <el-form-item
                  v-if="formValidate.useType !== 1"
                  prop="productItems"
                  class="specification-table"
                >
                  <div class="table-box">
                    <el-table
                      :data="formValidate.productItems"
                      class="tabNumWidth"
                      size="mini"
                    >
                      <el-table-column label="产品信息">
                        <template slot-scope="scope">
                          <div class="store-img">
                            <MyImage
                              :imagePath="handlerImgUrl(scope.row.image)"
                              :previewPath="scope.row.image"
                              :size="40"
                            />
                            <span>{{ scope.row.name }}</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="销售单价"
                        prop="price"
                      >
                      </el-table-column>
                      <el-table-column
                        key="3"
                        align="center"
                        label="操作"
                        min-width="80"
                      >
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            class="submission"
                            @click="delAttrTable(scope.$index)"
                            >删除</el-button
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                    <div>
                      <el-button
                        @click="changeGood"
                        type="text"
                        ><i class="el-icon-plus"></i> 选择产品</el-button
                      >
                    </div>
                  </div>
                </el-form-item>
              </el-form-item>

              <!-- <el-form-item
                style="width: 60%"
                label="排序："
              >
                <el-input
                  v-model="formValidate.sort"
                  placeholder="请输入"
                />
              </el-form-item> -->
              <el-form-item label="是否开启：">
                <el-switch
                  v-model="formValidate.status"
                  :active-value="true"
                  :inactive-value="false"
                />
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->

          <!-- 领取和使用规则 start -->
          <div class="store-specification base-card">
            <div class="info-title">领取和使用规则</div>
            <div class="form-info">
              <el-form-item
                v-if="couponTypeVal == 2"
                label="领取次数限制："
              >
                <span class="inline-flex">
                  <span>每人最多可领</span>
                  <el-input v-model="formValidate.receiveLimitCount">
                    <template slot="append">次</template>
                  </el-input>
                </span>
              </el-form-item>
              <el-form-item
                v-if="couponTypeVal == 3"
                label="发放会员限制："
              >
                <el-select
                  v-model="formValidate.memberLevels"
                  multiple
                  placeholder="选择会员等级"
                >
                  <el-option
                    v-for="item in userLevelList"
                    :key="item.grade"
                    :label="item.name"
                    :value="item.grade.toString()"
                  >
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item
                prop="receiveLimitType"
                v-if="couponTypeVal == 1"
                label="领取次数限制："
              >
                <el-radio-group v-model="formValidate.receiveLimitType">
                  <el-radio :label="1"> 每次领取次数不限 </el-radio>
                  <el-radio :label="2">
                    <span class="inline-flex">
                      <span>每人最多可领</span
                      ><span
                        ><el-input
                          :disabled="formValidate.receiveLimitType == 1"
                          v-model="formValidate.receiveLimitCount"
                        >
                          <template slot="append">次</template>
                        </el-input>
                      </span>
                    </span>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item
                v-if="couponTypeVal == 1"
                label="领取客户限制："
              >
                <el-radio-group v-model="formValidate.customerLimitType">
                  <el-radio :label="1"> 不限制，所有人可领 </el-radio>
                  <el-radio :label="2"> 指定客户参与 </el-radio>
                </el-radio-group>
                <div
                  v-if="formValidate.customerLimitType === 2"
                  style="margin: 10px 0"
                >
                  <div>指定会员等级参与</div>
                  <div>
                    <el-select
                      v-model="formValidate.customerLevelIds"
                      multiple
                      placeholder="选择会员等级"
                    >
                      <el-option
                        v-for="item in userLevelList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id.toString()"
                      >
                      </el-option>
                    </el-select>
                  </div>
                </div>
                <div v-if="formValidate.customerLimitType === 2">
                  <div>指定用户标签参与</div>
                  <div>
                    <el-select
                      v-model="formValidate.customerTagIds"
                      multiple
                      placeholder="选择用户标签"
                    >
                      <el-option
                        v-for="item in userTagList"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id.toString()"
                      >
                      </el-option>
                    </el-select>
                    <!-- <el-autocomplete
                      style="width: 26%"
                      popper-class="my-autocomplete"
                      v-model="formValidate.customerTagIds"
                      :fetch-suggestions="querySearch"
                      placeholder="选择用户"
                    >
                     
                    </el-autocomplete> -->
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="过期提醒：">
                <span class="inline-flex">
                  <el-checkbox v-model="formValidate.expireNotice">
                  </el-checkbox>
                  <span>券过期前</span
                  ><span>
                    <el-input
                      :disabled="formValidate.expireNotice === false"
                      v-model="formValidate.expireNoticeDays"
                    >
                    </el-input>
                  </span>
                  <span>天发送消息，提醒客户用券</span>
                </span>
              </el-form-item>
              <el-form-item
                style="width: 60%"
                label="券使用说明："
              >
                <el-input
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  type="textarea"
                  v-model="formValidate.useDescription"
                ></el-input>
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <router-link :to="{ path: '/marketing/discountCoupon' }">
          <el-button>取 消</el-button></router-link
        >
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  couponCreateApi,
  couponUpdateApi,
  couponInfoApi,
  userLevelListApi,
  userTagListApi
} from '@/api/discountCoupon.js'
import { Debounce } from '@/utils/validate'

const defaultObj = {
  name: null,
  type: null,
  isLimited: 1,
  couponType: 1,
  minPrice: null,
  money1: null, // 有门槛面值
  money2: null, // 无门槛面值
  money: null, // 优惠券面值
  useTimeType: 1,
  useType: 1,
  useStartTime: null,
  useEndTime: null,
  day1: null, // 领券后立即生效，有效期 天数
  day2: null, // 领券后多少天后有效期
  day: null, // 有效期
  afterDays: null,
  total: null,
  receiveLimitType: 1,
  receiveLimitCount: null,
  customerLimitType: 1,
  customerLevelIds: [], // 指定会员等级曾参与
  customerTagIds: [], // 指定用户标签参与
  expireNotice: false, // 应该是0跟1
  expireNoticeDays: null,
  useDescription: null,
  sendTimeType: 1,
  birthSendType: 1,
  monthSendDay: null,
  memberLevels: [],
  sort: null,
  status: true,
  productItems: [],
  primaryKey: null
}
export default {
  name: 'ProductProductAdd',
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        day: [{ required: true, message: '请输入', trigger: 'blur' }],
        receiveLimitCount: [
          { required: true, message: '请输入', trigger: 'blur' }
        ],
        useType: [{ required: true, message: '请选择', trigger: 'change' }],
        receiveLimitType: [
          { required: true, message: '请选择', trigger: 'change' }
        ]
      },
      userLevelList: [],
      userTagList: [],
      fullscreenLoading: false,
      couponTypeVal: 1, //1满减券 2新人专享券 3会员专享券
      oprationType: '',
      tempRoute: {}
    }
  },
  computed: {
    oldTotalPrice() {
      return this.formValidate.productItems.reduce(function (total, item) {
        return total + item.price * item.quantity
      }, 0)
    },
    preferentialPrice() {
      return this.oldTotalPrice - this.formValidate.combinationPrice
    }
  },
  watch: {
    'formValidate.couponType': {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.money2 = null
        } else {
          this.formValidate.minPrice = null
          this.formValidate.money1 = null
        }
      }
    },
    'formValidate.useTimeType': {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.day1 = null
          this.formValidate.day2 = null
          this.formValidate.afterDays = null
        } else if (val == 2) {
          this.formValidate.useStartTime = null
          this.formValidate.useEndTime = null
          this.formValidate.afterDays = null
          this.formValidate.day2 = null
        } else {
          this.formValidate.useStartTime = null
          this.formValidate.useEndTime = null
          this.formValidate.day1 = null
        }
      }
    },
    'formValidate.receiveLimitType': {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.receiveLimitCount = null
        }
      }
    },
    'formValidate.customerLimitType': {
      handler: function (val) {
        if (val == 1) {
          this.formValidate.customerLevelIds = []
          this.formValidate.customerTagIds = []
        }
      }
    },
    'formValidate.expireNotice': {
      handler: function (val) {
        if (val === false) {
          this.formValidate.expireNoticeDays = null
        }
      }
    },
    'formValidate.sendTimeType': {
      handler: function (val) {
        if (val === 0) {
          this.formValidate.birthSendType = null
          this.formValidate.monthSendDay = null
        } else if (val === 1) {
          this.formValidate.monthSendDay = null
        } else {
          this.formValidate.birthSendType = null
        }
      }
    }
  },

  mounted() {
    if (this.$route.params.id) {
      // this.setTagsViewTitle();
      this.getInfo()
    }
    if (this.$route.query.oprationType) {
      this.oprationType = this.$route.query.oprationType
    }
    if (this.$route.query.couponType) {
      this.couponTypeVal = this.$route.query.couponType
    }

    this.getUserLevelList()

    this.getUserTagList()
  },
  methods: {
    changeGood() {
      const _this = this
      this.$modalGoodList(
        function (row) {
          if (!row) return
          if (row.length > 3) {
            return this.$message.warning('模板最多可选择三个产品')
          }
          _this.formValidate.productItems = JSON.parse(JSON.stringify(row)).map(
            (item) => ({
              ...item,
              quantity: 1,
              productId: item.id,
              name: item.storeName
            })
          )
          console.log()
        },
        'many',
        _this.formValidate.productItems
      )
    },
    querySearch(queryString, cb) {
      var restaurants = this.userTagList
      var results = queryString
        ? restaurants.filter(this.createFilter(queryString))
        : restaurants
      // 调用 callback 返回建议列表的数据
      cb(results)
    },
    createFilter(queryString) {
      return (restaurant) => {
        return (
          restaurant.value.toLowerCase().indexOf(queryString.toLowerCase()) ===
          0
        )
      }
    },
    // 获取会员等级字典
    getUserLevelList() {
      userLevelListApi().then((res) => {
        this.userLevelList = res
        console.log(res, 3444)
      })
    },
    // 获取用户标签字典
    getUserTagList() {
      userTagListApi().then((res) => {
        this.userTagList = res
      })
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },

    setTagsViewTitle() {
      const title = this.isDisabled ? '商品详情' : '编辑商品'
      const route = Object.assign({}, this.tempRoute, {
        title: `${title}-${this.$route.params.id}`
      })
      this.$store.dispatch('tagsView/updateVisitedView', route)
    },

    // 删除表格中的属性
    delAttrTable(index) {
      this.formValidate.productItems.splice(index, 1)
    },

    // 详情
    getInfo() {
      this.fullscreenLoading = true
      couponInfoApi(this.$route.params.id)
        .then(async (res) => {
          // this.isAttr = true;
          let info = res.coupon
          this.formValidate = {
            name: info.name,
            type: info.type,
            isLimited: info.isLimited,
            couponType: info.couponType,
            minPrice: info.minPrice,
            money1: info.couponType == 1 ? info.money : null, // 有门槛面值
            money2: info.couponType == 2 ? info.money : null, // 无门槛面值
            money: info.money, // 优惠券面值
            useTimeType: info.useTimeType,
            useType: info.useType,
            useStartTime: info.useStartTime,
            useEndTime: info.useEndTime,
            day1: info.useTimeType == 2 ? info.day : null, // 领券后立即生效，有效期 天数
            day2: info.useTimeType == 3 ? info.day : null, // 领券后多少天后有效期
            day: info.day, // 有效期
            afterDays: info.afterDays,
            total: info.total,
            receiveLimitType: info.receiveLimitType,
            receiveLimitCount: info.receiveLimitCount,
            customerLimitType: info.customerLimitType,
            customerTagIds: info.customerTagIds
              ? info.customerTagIds.split(',')
              : [], // 指定用户标签参与
            expireNotice: info.expireNotice, // 应该是0跟1
            expireNoticeDays: info.expireNoticeDays,
            sendTimeType: info.sendTimeType,
            birthSendType: info.birthSendType,
            monthSendDay: info.monthSendDay,
            memberLevels: info.memberLevels ? info.memberLevels.split(',') : [],
            customerLevelIds: info.customerLevelIds
              ? info.customerLevelIds.split(',')
              : [],
            sort: info.sort,
            status: info.status,
            productItems: res.product || [],
            primaryKey: info.primaryKey
          }

          this.fullscreenLoading = false
        })
        .catch((res) => {
          this.fullscreenLoading = false
          this.$message.error(res.message)
        })
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      const day =
        this.formValidate.day ||
        this.formValidate.day1 ||
        this.formValidate.day2
      const money = this.formValidate.money1 || this.formValidate.money2
      const primaryKey = this.formValidate.productItems
        .map((item) => item.productId)
        .join()
      const memberLevels = this.formValidate.memberLevels.join()
      const customerTagIds = this.formValidate.customerTagIds.join()
      const customerLevelIds = this.formValidate.customerLevelIds.join()
      let receiveLimitType = this.formValidate.receiveLimitType
      let useTimeType = this.formValidate.useTimeType
      if (this.couponTypeVal == 2) {
        // 新人券领取次数限制默认传2
        receiveLimitType = 2
      }
      if (this.couponTypeVal == 2 || this.couponTypeVal == 3 ) {
        // 新人券\会员默认传2
        useTimeType = 2
      }
      this.$refs[name].validate((valid) => {
        if (valid) {
          // delete this.formValidate.productItems;
          // delete this.formValidate.money1;
          // delete this.formValidate.money2;
          // delete this.formValidate.day1;
          // delete this.formValidate.day2;
          this.fullscreenLoading = true
          this.oprationType == 'edit'
            ? couponUpdateApi(
                {
                  ...this.formValidate,
                  day,
                  money,
                  primaryKey,
                  memberLevels,
                  customerTagIds,
                  customerLevelIds,
                  receiveLimitType,
                  useTimeType,
                },
                Number(this.$route.params.id)
              )
                .then(async (res) => {
                  this.$message.success('编辑成功')
                  setTimeout(() => {
                    this.$router.push({ path: '/marketing/discountCoupon' })
                  }, 500)
                  this.fullscreenLoading = false
                })
                .catch((res) => {
                  this.fullscreenLoading = false
                })
            : couponCreateApi({
                ...this.formValidate,
                type: this.couponTypeVal,
                day,
                money,
                primaryKey,
                memberLevels,
                customerTagIds,
                customerLevelIds,
                receiveLimitType,
                useTimeType,
              })
                .then(async (res) => {
                  this.$message.success('新增成功')
                  setTimeout(() => {
                    this.$router.push({ path: '/marketing/discountCoupon' })
                  }, 500)
                  this.fullscreenLoading = false
                })
                .catch((res) => {
                  this.fullscreenLoading = false
                })
        } else {
          this.$message.warning('请填写完整信息！')
        }
      })
    })
  }
}
</script>
<style scoped lang="scss">
.tabPic {
  width: 40px !important;
  height: 40px !important;
  img {
    width: 100%;
    height: 100%;
  }
}
.tabNumWidth {
  ::v-deep.el-input-number--medium {
    width: 121px !important;
  }
  ::v-deep.el-input-number__increase {
    width: 20px !important;
    font-size: 12px !important;
  }
  ::v-deep.el-input-number__decrease {
    width: 20px !important;
    font-size: 12px !important;
  }
  ::v-deep.el-input-number--medium .el-input__inner {
    padding-left: 25px !important;
    padding-right: 25px !important;
  }
  ::v-deep thead {
    line-height: normal !important;
  }
  ::v-deep .el-table .cell {
    line-height: normal !important;
  }
}

.form-info {
  padding-left: 80px;
}
.detail-description {
  font-size: 12px;
  color: #666;
}
.inline-flex {
  display: inline-flex;
  gap: 10px;
  align-items: center;
  color: #606266;
  white-space: nowrap;
}

.specification-table {
  .table-box {
    width: 60%;
    border: 1px solid #dcdfe6;
    padding: 10px;
    border-radius: 4px;
  }
}

.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}

.info-title {
  font-size: 14px;
  font-weight: 700;
  margin: 0 0 20px 0;
  &::before {
    content: '';
    display: inline-block;
    width: 5px;
    height: 14px;
    background-color: rgb(3, 158, 3);
    vertical-align: -2px;
    margin-right: 8px;
  }
}

.store-img {
  display: flex;
  align-items: center;
  gap: 10px;
  & > span {
    color: #333;
  }
}
.flex {
  display: flex;
  white-space: nowrap;
  gap: 10px;
  align-items: center;
}

.flex-colomn {
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  gap: 16px;
}
.base-card {
  background-color: #ffffff;
  overflow: hidden;
  border-radius: 4px;
  margin-bottom: 16px;
  padding: 20px 20px 0;
}
</style>
