<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link
                :to="{
                  path: '/marketing/activityList/creatActivity?oprationType=add',
                }"
              >
                <el-button type="primary">新建活动</el-button></router-link
              >
            </template>
          </MyHeaderSearch>
        </div>
        <div class="header-card">
          <div
            v-for="(item, index) in activityList"
            :key="item.key"
            :class="`card-item ${index == 0 ? 'active' : ''}`"
          >
            <div class="value">{{ item.value }}</div>
            <div class="title">{{ item.title }}</div>
          </div>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column width="400" prop="name" label="活动信息">
          <template slot-scope="scope">
            <div class="activity-info">
              <div class="left">
                <MyImage
                  @click.native.stop
                  :imagePath="handlerImgUrl(scope.row.image)"
                  :previewPath="scope.row.image"
                  :width="144"
                />
              </div>
              <div class="right">
                <div class="title">{{ scope.row.name }}</div>
                <div class="view">查看优惠</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          v-if="tableFrom.statusType != 2"
          label="活动时间"
          min-width="150"
          ><template slot-scope="scope">
            <span
              >{{ parseTime(scope.row.startTime, "{y}-{m}-{d} {h}:{i}:{s}") }}
              至
              {{
                parseTime(scope.row.stopTime, "{y}-{m}-{d} {h}:{i}:{s}")
              }}</span
            >
          </template></el-table-column
        >
        <el-table-column prop="hits" label="点击量"> </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <span v-if="scope.row.status"
              ><span class="status-icon"></span
              ><span style="vertical-align: middle">进行中</span></span
            >
            <span v-else>已结束</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="scope">
            <router-link
              :to="{
                path: `/marketing/activityList/creatActivity/${scope.row.id}?oprationType=edit`,
              }"
            >
              <el-button type="text" size="small" class="mr10">编辑</el-button>
            </router-link>
            <span v-if="!!scope.row.status">
              <el-button
                @click="handerStatus(scope.row.status, scope.row.id)"
                type="text"
                size="small"
                class="mr10"
                >关闭活动</el-button
              >
            </span>
            <span>
              <router-link
                :to="{
                  path: `/marketing/activityList/creatActivity/${scope.row.id}?oprationType=copy`,
                }"
              >
                <el-button type="text" size="small" class="mr10"
                  >复制</el-button
                ></router-link
              >
            </span>
            <span>
              <el-button
                type="text"
                size="small"
                @click="handleDelete(scope.row.id, scope.$index)"
                >删除</el-button
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <DataOverview ref="dataOverview" />
  </MainCard>
</template>
<script>
import {
  activityListApi,
  activityDeleteApi,
  activityStatusUpdateApi,
  activityStatisticsInfoApi,
} from "@/api/activity";
import DataOverview from "./dataOverview.vue";

export default {
  name: "StoreAttr",
  components: { DataOverview },
  data() {
    return {
      activityList: [
        { title: "进行中的活动", value: "0", key: "ongoingCount" },
        { title: "未开始的活动", value: "0", key: "notStartedCount" },
        {
          title: "已结束的活动",
          value: "0",
          key: "endedCount",
        },
      ],
      tableFrom: {
        page: 1,
        limit: 20,
        // countType: "ongoingCount",
        name: "",
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      selectStoreId: [],
      listLoading: true,
    };
  },
  mounted() {
    this.activitySelectList();
    this.getList();
  },
  methods: {
    seachList() {
      this.tableFrom.page = 1;

      this.getList();
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    handlerSelect(key) {
      this.tableFrom.countType = key;
      this.getList();
    },
    // 活动状态列表
    activitySelectList() {
      activityStatisticsInfoApi(this.tableFrom)
        .then((res) => {
          if (res) {
            this.activityList.forEach((element) => {
              element.value = res[element.key];
            });
          }
        })
        .catch(() => {});
    },
    // 列表
    getList() {
      this.listLoading = true;
      activityListApi(this.tableFrom)
        .then((res) => {
          const list = res.list;
          this.tableData.data = list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    // 更新状态
    handerStatus(status, id) {
      this.$modalSure("是否" + (status ? "关闭" : "开启") + "该活动？")
        .then(() => {
          activityStatusUpdateApi({ id, status: status ? 0 : 1 })
            .then(() => {
              this.$message.success("操作成功");
              this.getList();
            })
            .catch(() => {});
        })
        .catch(() => {});
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        status: "",
        name: "",
        useType: "",
      };
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除id为" + id + "的活动吗？")
        .then(() => {
          activityDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    .header-btn {
      margin-bottom: 16px;
    }
    .header-card {
      display: flex;
      gap: 14px;
      margin-bottom: 20px;
      .card-item {
        background-color: #f7f7f7;
        padding: 20px 16px;
        flex: 0 0 224px;
        border-radius: 4px;
        &:hover {
          background-color: #f5f5f5;
        }
        .value {
          font-size: 20px;
          line-height: 1;
          font-weight: 700;
          margin-bottom: 8px;
        }

        &.active {
          position: relative;
          &::after {
            position: absolute;
            content: "";
            width: 100%;
            height: 4px;
            bottom: 0;
            left: 0;
            background-color: #3cb059;
          }
        }
      }
    }
  }
  .activity-info {
    display: flex;
    line-height: 1;
    gap: 10px;
    .right {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 2px 0 2px 0;
      .title {
        line-height: 1.2;
        font-weight: 700;
      }
    }
  }
  .search-form {
    border-radius: 4px;
    background-color: #f7f7f7;
    padding: 20px;
    margin-bottom: 20px;
  }
  .store-info {
    .store-img {
      display: flex;
      align-items: center;
      gap: 10px;
      & > span {
        color: #333;
      }
    }
  }
  .status-icon {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0fa142;
    margin-right: 4px;
  }
}
</style>
