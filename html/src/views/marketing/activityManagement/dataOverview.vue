<template>
  <el-dialog
    title="数据概览"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <!-- 左侧优惠券信息和统计数据 -->
      <div class="left-section">
        <div class="coupon-info">
          <div class="coupon-left">
            <div class="coupon-value">
              {{ coupon.value }} <span class="unit">元</span>
            </div>
            <div class="coupon-desc">{{ coupon.desc }}</div>
          </div>
          <div class="coupon-right">
            <div class="coupon-title">爱+满50元立减券</div>
            <div class="coupon-subtitle">满减券</div>
          </div>
        </div>

        <div class="statistics">
          <div class="stat-item" v-for="(item, index) in stats" :key="index">
            <div class="stat-label">
              {{ item.label }}
              <!-- <el-tooltip
                class="item"
                effect="dark"
                :content="item.tooltip"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip> -->
            </div>
            <div class="stat-value">{{ item.value }}</div>
          </div>
        </div>
      </div>

      <!-- 右侧商品信息表格 -->
      <div class="right-section">
        <div class="title">使用优惠券购买的商品</div>
        <el-table
          :data="products"
          :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
          style="width: 100%"
          size="mini"
          highlight-current-row
          class="product-table"
        >
          <el-table-column prop="name" label="商品信息">
            <template #default="scope">
              <div class="product-info">
                <img
                  src="https://via.placeholder.com/50"
                  alt="商品图片"
                  class="product-image"
                />
                <span>{{ scope.row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="price"
            label="商品价格"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="quantity"
            label="付款件数"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="buyers"
            label="付款人数"
            align="center"
            sortable
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      coupon: {
        value: 50,
        desc: "满200元可用",
      },
      stats: [
        { label: "支付总金额", value: "¥341405.2", tooltip: "支付总金额说明" },
        { label: "优惠总金额", value: "¥37910.8", tooltip: "优惠总金额说明" },
        { label: "费效比", value: "11.10%", tooltip: "费效比说明" },
        { label: "用券笔单价", value: "48772.17", tooltip: "用券笔单价说明" },
        { label: "用券客数", value: "3", tooltip: "用券客数说明" },
        { label: "购买商品件数", value: "131", tooltip: "购买商品件数说明" },
      ],
      products: [
        {
          name: "天仙精氨酸牛磺酸粉 - 一盒装",
          price: "¥99",
          quantity: 3,
          buyers: 2,
        },
        {
          name: "天仙精氨酸牛磺酸粉 - 一盒装",
          price: "¥99",
          quantity: 3,
          buyers: 1,
        },
      ],
    };
  },
  methods: {
    handlerOpen() {
      console.log("Dialog opened");
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  height: 600px;
  .left-section {
    flex: 0 0 340px;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .coupon-info {
      display: flex;
      align-items: center;
      border: 1px solid #f56c6c;
      border-radius: 4px;
      margin-bottom: 30px;
      overflow: hidden;
      color: #f56c6c;
      .coupon-left {
        text-align: center;
        border-right: 1px dashed #f56c6c;
        background-color: #fff6f6;
        padding: 16px;
        .coupon-value {
          font-size: 24px;
          font-weight: bold;
          .unit {
            font-size: 12px;
          }
        }
        .coupon-desc {
          margin-top: 5px;
          font-size: 14px;
        }
      }

      .coupon-right {
        padding-left: 20px;
        .coupon-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .coupon-subtitle {
          margin-top: 5px;
          font-size: 14px;
          color: #666;
        }
      }
    }

    .statistics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      row-gap: 50px;
      .stat-item {
        text-align: center;
        &:nth-child(odd) {
          border-right: 1px solid #e5e5e5;
        }
        .stat-value {
          font-size: 18px;
          font-weight: bold;
        }

        .stat-label {
          font-size: 12px;
          color: #333;
          margin-bottom: 6px;

          .el-icon-question {
            margin-left: 5px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .right-section {
    flex: 2;
    overflow: hidden;
    padding: 20px;
    .title {
      margin-bottom: 10px;
    }
    .product-table {
      .product-info {
        display: flex;
        align-items: center;

        .product-image {
          width: 50px;
          height: 50px;
          margin-right: 10px;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
