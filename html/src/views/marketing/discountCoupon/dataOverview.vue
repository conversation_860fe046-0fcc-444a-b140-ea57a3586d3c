<template>
  <el-dialog
    title="数据概览"
    @open="handlerOpen"
    :close-on-click-modal="false"
    :visible.sync="dialogVisible"
    width="80%"
    :before-close="handleClose"
    class="data-overview-dialog"
  >
    <div class="content">
      <!-- 左侧优惠券信息和统计数据 -->
      <div class="left-section">
        <div class="coupon-info">
          <div class="coupon-left">
            <div class="coupon-value">
              {{ couponInfo.money }} <span class="unit">元</span>
            </div>
            <div class="coupon-desc">{{ couponInfo.useCondition }}</div>
          </div>
          <div class="coupon-right">
            <div class="coupon-title">{{ couponInfo.name }}</div>
            <div class="coupon-subtitle">{{ couponInfo.typeName }}</div>
          </div>
        </div>

        <div class="statistics">
          <div
            class="stat-item"
            v-for="(item, index) in statistics"
            :key="index"
          >
            <div class="stat-label">
              {{ item.label }}
              <!-- <el-tooltip
                class="item"
                effect="dark"
                :content="item.tooltip"
                placement="top"
              >
                <i class="el-icon-question"></i>
              </el-tooltip> -->
            </div>
            <div class="stat-value">{{ item.value }}</div>
          </div>
        </div>
      </div>

      <!-- 右侧商品信息表格 -->
      <div class="right-section">
        <div class="title">使用优惠券购买的商品</div>
        <el-table
          :data="products"
          :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
          style="width: 100%"
          size="mini"
          highlight-current-row
          class="product-table"
        >
          <el-table-column label="商品信息">
            <template #default="scope">
              <div class="product-info">
                <img
                  :src="productImage"
                  alt="商品图片"
                  class="product-image"
                />
                <span>{{ scope.row.productName }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="productPrice"
            label="商品价格"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="totalQuantity"
            label="付款件数"
            align="center"
            sortable
          ></el-table-column>
          <el-table-column
            prop="buyerCount"
            label="付款人数"
            align="center"
            sortable
          ></el-table-column>
        </el-table>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import { dataOverviewApi } from "@/api/discountCoupon";
export default {
  props: {
    curRow: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      dialogVisible: false,
      couponInfo: {
        money: null,
        useCondition: "",
        name: "",
        typeName: "",
      },
      statistics: [
        {
          key: "totalPayAmount",
          label: "支付总金额",
          value: "",
          tooltip: "支付总金额说明",
        },
        {
          key: "totalDiscountAmount",
          label: "优惠总金额",
          value: "",
          tooltip: "优惠总金额说明",
        },
        {
          key: "costEffectiveRatio",
          label: "费效比",
          value: "",
          tooltip: "费效比说明",
        },
        {
          key: "avgOrderAmount",
          label: "用券笔单价",
          value: "",
          tooltip: "用券笔单价说明",
        },
        {
          key: "userCount",
          label: "用券客数",
          value: "",
          tooltip: "用券客数说明",
        },
        {
          key: "totalProductCount",
          label: "购买商品件数",
          value: "",
          tooltip: "购买商品件数说明",
        },
      ],
      products: [],
    };
  },
  methods: {
    handlerOpen() {
      this.getDataOverview();
    },
    // 数据概览
    getDataOverview() {
      dataOverviewApi({ couponId: this.curRow.id })
        .then((res) => {
          if (res) {
            this.couponInfo = res.couponInfo;
            this.statistics.forEach((element) => {
              element.value = res.statistics[element.key];
            });
            this.products = res.products || [];
          }
        })
        .catch(() => {});
    },
    handleClose(done) {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__body {
  padding: 0 !important;
}
.content {
  display: flex;
  justify-content: space-between;
  min-height: 600px;
  .left-section {
    flex: 0 0 340px;
    padding: 20px;
    border-right: 1px solid #dcdfe6;
    .coupon-info {
      display: flex;
      align-items: center;
      border: 1px solid #f56c6c;
      border-radius: 4px;
      margin-bottom: 30px;
      overflow: hidden;
      color: #f56c6c;
      .coupon-left {
        text-align: center;
        border-right: 1px dashed #f56c6c;
        background-color: #fff6f6;
        padding: 16px;
        .coupon-value {
          font-size: 24px;
          font-weight: bold;
          .unit {
            font-size: 12px;
          }
        }
        .coupon-desc {
          margin-top: 5px;
          font-size: 14px;
        }
      }

      .coupon-right {
        padding-left: 20px;
        .coupon-title {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }

        .coupon-subtitle {
          margin-top: 5px;
          font-size: 14px;
          color: #666;
        }
      }
    }

    .statistics {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      row-gap: 50px;
      .stat-item {
        text-align: center;
        &:nth-child(odd) {
          border-right: 1px solid #e5e5e5;
        }
        .stat-value {
          font-size: 18px;
          font-weight: bold;
        }

        .stat-label {
          font-size: 12px;
          color: #333;
          margin-bottom: 6px;

          .el-icon-question {
            margin-left: 5px;
            font-size: 12px;
          }
        }
      }
    }
  }

  .right-section {
    flex: 2;
    overflow: hidden;
    padding: 20px;
    .title {
      margin-bottom: 10px;
    }
    .product-table {
      .product-info {
        display: flex;
        align-items: center;

        .product-image {
          width: 50px;
          height: 50px;
          margin-right: 10px;
          border-radius: 4px;
        }
      }
    }
  }
}
</style>
