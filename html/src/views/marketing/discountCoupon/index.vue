<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <div
            v-for="item in addBtnList"
            :key="item.type"
            class="btn-item"
          >
            <div class="title">{{ item.title }}</div>
            <div class="des">{{ item.des }}</div>
            <div class="btn">
              <router-link
                :to="{
                  path: `/marketing/discountCouponList/creatDiscountCoupon?couponType=${item.type}&oprationType=add`
                }"
              >
                <el-button type="primary">立即新建</el-button></router-link
              >
            </div>
          </div>
        </div>
        <div class="search-form">
          <el-form
            inline
            size="small"
            label-width="auto"
            :model="tableFrom"
          >
            <el-form-item label="优惠券名称：">
              <el-input
                v-model="tableFrom.name"
                placeholder="请输入"
                class="selWidth"
                size="small"
                clearable
              >
              </el-input>
            </el-form-item>
            <el-form-item label="优惠券状态：">
              <el-select
                clearable
                v-model="tableFrom.status"
                placeholder="请选择"
              >
                <el-option
                  label="全部"
                  value=""
                >
                </el-option>
                <el-option
                  label="已结束"
                  :value="false"
                >
                </el-option>
                <el-option
                  label="进行中"
                  :value="true"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="类型：">
              <el-select
                clearable
                v-model="tableFrom.type"
                placeholder="请选择"
              >
                <el-option
                  label="全部"
                  value=""
                >
                </el-option>
                <el-option
                  v-for="item in addBtnList"
                  :key="item.type"
                  :label="item.title"
                  :value="item.type"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
          <div class="search-btn">
            <el-button
              @click="seachList"
              size="small"
              type="primary"
              >筛选</el-button
            >
            <el-button
              type="text"
              size="small"
              @click="reset"
              >重置筛选条件</el-button
            >
          </div>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
      >
        <el-table-column
          prop="name"
          label="优惠券名称"
        >
        </el-table-column>
        <el-table-column
          prop="useType"
          label="类型"
        >
          <template slot-scope="scope">
            <span>{{
              addBtnList.find((item) => item.type == scope.row.type).title
            }}</span>
          </template></el-table-column
        >
        <el-table-column label="优惠内容">
          <template slot-scope="scope">
            <span v-if="scope.row.couponType === 1">
              订单满{{ scope.row.minPrice }}元,减{{ scope.row.money }}元
            </span>
            <span v-if="scope.row.couponType === 2">
              无门槛,减{{ scope.row.money }}元
            </span>
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="状态"
        >
          <template slot-scope="scope">
            <span v-if="scope.row.status"
              ><span class="status-icon"></span
              ><span style="vertical-align: middle">进行中</span></span
            >
            <span v-else>已结束</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="usedCount"
          label="已使用"
        >
        </el-table-column>
        <el-table-column
          prop="payAmount"
          label="支付金额"
        >
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="120"
        >
          <template slot-scope="scope">
            <span>
              <el-button
                @click="showDataOverview(scope.row)"
                type="text"
                size="small"
                class="mr10"
                >数据</el-button
              >
            </span>

            <router-link
              :to="{
                path: `/marketing/discountCouponList/creatDiscountCoupon/${scope.row.id}?couponType=${scope.row.type}&oprationType=edit`
              }"
            >
              <el-button
                type="text"
                size="small"
                class="mr10"
                >编辑</el-button
              >
            </router-link>
            <span>
              <el-button
                @click="handerStatus(scope.row.status, scope.row.id)"
                type="text"
                size="small"
                class="mr10"
                >{{ scope.row.status ? '停发' : '发售' }}</el-button
              >
            </span>
            <span>
              <router-link
                :to="{
                  path: `/marketing/discountCouponList/creatDiscountCoupon/${scope.row.id}?couponType=${scope.row.type}&oprationType=copy`
                }"
              >
                <el-button
                  type="text"
                  size="small"
                  class="mr10"
                  >复制</el-button
                ></router-link
              >
            </span>
            <span>
              <el-button
                type="text"
                size="small"
                @click="handleDelete(scope.row.id, scope.$index)"
                >删除</el-button
              >
            </span>
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
    <DataOverview
      :curRow="curRow"
      ref="dataOverview"
    />
  </MainCard>
</template>
<script>
import {
  marketingCouponListApi,
  couponDeleteApi,
  couponStatusUpdateApi
} from '@/api/discountCoupon'
import DataOverview from './dataOverview.vue'

export default {
  name: 'StoreAttr',
  components: { DataOverview },
  data() {
    return {
      addBtnList: [
        { title: '满减券', des: '例：满100减20元', type: '1' },
        { title: '新人专享券', des: '仅限新客可领，提高首单转化', type: '2' },
        {
          title: '会员专享券',
          des: '丰富会员权益，凸显会员身份价值',
          type: '3'
        }
      ],
      tableFrom: {
        page: 1,
        limit: 20,
        status: '',
        name: '',
        type: ''
      },
      tableData: {
        data: [],
        loading: false,
        total: 0
      },
      curRow: {},
      selectStoreId: [],
      listLoading: true
    }
  },
  mounted() {
    this.getList()
  },
  methods: {
    seachList() {
      this.tableFrom.page = 1
      this.getList()
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = 'thumbnailImage'
      let lastDotIndex = url.lastIndexOf('.')
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      )
    },
    // 列表
    getList() {
      this.listLoading = true
      marketingCouponListApi(this.tableFrom)
        .then((res) => {
          const list = res.list
          this.tableData.data = list
          this.tableData.total = res.total
          this.listLoading = false
        })
        .catch(() => {
          this.listLoading = false
        })
    },
    // 显示数据概览视图
    showDataOverview(row) {
      this.curRow = { ...row }
      this.$refs.dataOverview.dialogVisible = true
    },
    // 改变发售状态
    handerStatus(status, id) {
      this.$modalSure('是否' + (status ? '停发' : '发售') + '该优惠券？')
        .then(() => {
          couponStatusUpdateApi({ id, status: status ? 0 : 1 })
            .then(() => {
              this.$message.success('操作成功')
              this.getList()
            })
            .catch(() => {})
        })
        .catch(() => {
          console.log(123)
        })
    },
    pageChange(page) {
      this.tableFrom.page = page
      this.getList()
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val
      this.getList()
    },
    reset() {
      this.tableFrom = {
        page: 1,
        limit: 20,
        status: '',
        name: '',
        type: ''
      }
      this.getList()
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure('删除id为' + id + '的优惠券吗？')
        .then(() => {
          couponDeleteApi(id).then(() => {
            this.$message.success('删除成功')
            this.tableData.data.splice(idx, 1)
          })
        })
        .catch(() => {})
    }
  }
}
</script>

<style scoped lang="scss">
.divBox {
  .search-box {
    .header-btn {
      display: flex;
      gap: 20px;
      margin-bottom: 20px;
      .btn-item {
        background-color: #f7f7f7;
        padding: 20px 20px 10px;
        flex: 0 0 250px;
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 20px;
        border-radius: 4px;
        .title {
          font-size: 16px;
          font-weight: 700;
        }
      }
    }
  }

  .search-form {
    border-radius: 4px;
    background-color: #f7f7f7;
    padding: 20px;
    margin-bottom: 20px;
    .search-btn {
      margin-left: 96px;
    }
  }
  .store-info {
    .store-img {
      display: flex;
      align-items: center;
      gap: 10px;
      & > span {
        color: #333;
      }
    }
  }
  .status-icon {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0fa142;
    margin-right: 4px;
  }
}
</style>
