<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="store-info base-card">
            <div class="info-title">基本信息</div>
            <!-- 商品信息-->
            <div class="form-info">
              <el-form-item style="width: 40%" label="折扣名称：" prop="name">
                <el-input v-model="formValidate.name" placeholder="请输入" />
              </el-form-item>
              <el-form-item label="会员等级限制：" prop="memberLevels">
                <el-select
                  v-model="formValidate.memberLevels"
                  placeholder="选择会员等级"
                >
                  <el-option label="普通会员" value="0"> </el-option>
                  <el-option label="VIP会员" value="1"> </el-option>
                  <el-option label="SVIP会员" value="2"> </el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="折扣值：" prop="discount">
                <span class="inline-flex">
                  <el-input v-model="formValidate.discount">
                    <template slot="append">折</template>
                  </el-input>
                </span>
              </el-form-item>
              <el-form-item prop="useType" label="适用商品：">
                <el-radio-group v-model="formValidate.useType">
                  <el-radio :label="1"> 全部商品可用 </el-radio>
                  <el-radio :label="2"> 指定商品可用 </el-radio>
                  <el-radio :label="3"> 指定商品不可用 </el-radio>
                </el-radio-group>
                <el-form-item
                  v-if="formValidate.useType !== 1"
                  prop="productItems"
                  class="specification-table"
                >
                  <div class="table-box">
                    <el-table
                      :data="formValidate.productItems"
                      class="tabNumWidth"
                      size="mini"
                    >
                      <el-table-column label="产品信息">
                        <template slot-scope="scope">
                          <div class="store-img">
                            <MyImage
                              :imagePath="handlerImgUrl(scope.row.image)"
                              :previewPath="scope.row.image"
                              :size="40"
                            />
                            <span>{{ scope.row.name }}</span>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="销售单价" prop="price">
                      </el-table-column>
                      <el-table-column
                        key="3"
                        align="center"
                        label="操作"
                        min-width="80"
                      >
                        <template slot-scope="scope">
                          <el-button
                            type="text"
                            class="submission"
                            @click="delAttrTable(scope.$index)"
                            >删除</el-button
                          >
                        </template>
                      </el-table-column>
                    </el-table>
                    <div>
                      <el-button @click="changeGood" type="text"
                        ><i class="el-icon-plus"></i> 选择产品</el-button
                      >
                    </div>
                  </div>
                </el-form-item>
              </el-form-item>
               <el-form-item label="是否开启：">
                <el-switch
                  v-model="formValidate.status"
                  :active-value="true"
                  :inactive-value="false"
                />
              </el-form-item>
              <el-form-item style="width: 60%" label="折扣权益说明：">
                <el-input
                  :autosize="{ minRows: 4, maxRows: 8 }"
                  type="textarea"
                  v-model="formValidate.useDescription"
                ></el-input>
              </el-form-item>
              
              <!-- <el-form-item label="优惠叠加：">
                <div class="flex">
                  <el-switch
                    v-model="formValidate.allowDiscount"
                    :active-value="1"
                    :inactive-value="0"
                  />
                  <span class="detail-description">开启后，可使用满减券</span>
                </div>
              </el-form-item> -->
            </div>
          </div>
          <!-- 基本信息 end -->
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  memberDiscountCreateApi,
  memberDiscountUpdateApi,
  memberDiscountInfoApi,
} from "@/api/memberDiscount";
import { Debounce } from "@/utils/validate";

const defaultObj = {
  name: "",
  memberLevels: null,
  discount: "",
  status: true,
  useType: 1,
  description: "",
  productItems: [],
};
export default {
  name: "ProductProductAdd",
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        name: [{ required: true, message: "请输入折扣名称", trigger: "blur" }],
        memberLevels: [
          { required: true, message: "请选择会员等级", trigger: "change" },
        ],
        discount: [
          { required: true, message: "请输入折扣值", trigger: "blur" },
        ],
        useType: [
          { required: true, message: "请选择适用商品", trigger: "change" },
        ],
      },
      fullscreenLoading: false,
      tempRoute: {},
    };
  },

  mounted() {
    if (this.$route.params.id) {
      this.getInfo();
    }
  },
  methods: {
    changeGood() {
      const _this = this;
      this.$modalGoodList(
        function (row) {
          if (!row) return;
          if (row.length > 3) {
            return this.$message.warning("模板最多可选择三个产品");
          }
          _this.formValidate.productItems = JSON.parse(JSON.stringify(row)).map(
            (item) => ({
              ...item,
              quantity: 1,
              productId: item.id,
              name: item.storeName,
            })
          );
          console.log();
        },
        "many",
        _this.formValidate.productItems
      );
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 删除表格中的属性
    delAttrTable(index) {
      this.formValidate.productItems.splice(index, 1);
    },
    // 详情
    getInfo() {
      this.fullscreenLoading = true;
      memberDiscountInfoApi(this.$route.params.id)
        .then(async (res) => {
          let info = res;
          this.formValidate = {
            name: info.name,
            memberLevels: info.memberLevels,
            discount: info.discount,
            status: info.status,
            useType: info.useType,
            description: info.description,
            productItems: info.productItems || [],
          };
          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      const productIds = this.formValidate.productItems
        .map((item) => item.productId)
        .join();
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.params.id
            ? memberDiscountUpdateApi({
                ...this.formValidate,
                productIds,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/marketing/memberDiscount" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : memberDiscountCreateApi({
                ...this.formValidate,
                productIds,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({ path: "/marketing/memberDiscount" });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          this.$message.warning("请填写完整信息！");
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
    .specification-table {
      .table-box {
        width: 60%;
        border: 1px solid #dcdfe6;
        padding: 10px;
        border-radius: 4px;
        .store-img {
          display: flex;
          align-items: center;
          gap: 10px;
          & > span {
            color: #333;
          }
        }
      }
    }
  }
  .form-info {
    padding-left: 80px;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
  }

  .specification-table {
    .table-box {
      border: 1px solid #dcdfe6;
      padding: 10px;
      border-radius: 4px;
    }
  }
  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
