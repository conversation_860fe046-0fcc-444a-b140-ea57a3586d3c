<template>
  <MainCard>
    <div class="divBox">
      <div class="search-box">
        <div class="header-btn">
          <MyHeaderSearch>
            <template v-slot:left>
              <router-link
                :to="{
                  path: '/marketing/memberDiscountList/creatMemberDiscount',
                }"
              >
                <el-button type="primary">新建折扣</el-button></router-link
              >
            </template>
          </MyHeaderSearch>
        </div>
      </div>
      <el-table
        ref="table"
        v-loading="listLoading"
        :data="tableData.data"
        :header-cell-style="{ background: '#f5f5f5', color: '#444' }"
        style="width: 100%"
        highlight-current-row
        @selection-change="handleSelectionChange"
      >
        <el-table-column label="折扣名称" prop="name"> </el-table-column>
        <el-table-column label="会员等级" prop="memberLevels">
          <template slot-scope="scope">
            <span v-if="scope.row.memberLevels === '0'"> 普通会员 </span>
            <span v-if="scope.row.memberLevels === '1'"> VIP会员 </span>
            <span v-if="scope.row.memberLevels === '2'"> SVIP会员 </span>
          </template>
        </el-table-column>
        <el-table-column label="折扣值" prop="discount">
          <template slot-scope="scope">
            <span> {{ scope.row.discount }}折</span>
          </template>
        </el-table-column>
        <el-table-column label="参与笔数" prop="discount"> </el-table-column>
        <el-table-column label="状态" prop="status">
          <template slot-scope="scope">
            <span v-if="!!scope.row.status"
              ><span class="status-icon"></span
              ><span style="vertical-align: middle">进行中</span></span
            >
            <span v-else>已结束</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template slot-scope="scope">
            <router-link
              :to="{
                path:
                  '/marketing/memberDiscountList/creatMemberDiscount/' +
                  scope.row.id,
              }"
            >
              <el-button
                type="text"
                size="small"
                class="mr10"
                v-hasPermi="[
                  'admin:product:rule:update',
                  'admin:product:rule:info',
                ]"
                >编辑</el-button
              >
            </router-link>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row.id, scope.$index)"
              v-hasPermi="['admin:product:rule:delete']"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <div class="block">
        <el-pagination
          :page-sizes="[20, 40, 60, 80]"
          :page-size="tableFrom.limit"
          :current-page="tableFrom.page"
          layout="total, sizes, prev, pager, next, jumper"
          :total="tableData.total"
          @size-change="handleSizeChange"
          @current-change="pageChange"
        />
      </div>
    </div>
  </MainCard>
</template>
<script>
import {
  memberDiscountListApi,
  memberDiscountStatusUpdateApi,
  memberDiscountDeleteApi,
} from "@/api/memberDiscount";

export default {
  name: "StoreAttr",
  data() {
    return {
      tableFrom: {
        page: 1,
        limit: 20,
        status: "1",
        keywords: "",
      },
      tableData: {
        data: [],
        loading: false,
        total: 0,
      },
      checkAll: false,
      isIndeterminate: false,
      selectStoreId: [],
      listLoading: true,
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    seachList() {
      this.getList();
    },
    handleCheckAllChange() {
      this.$refs.table.toggleAllSelection();
    },
    handleSelectionChange(val) {
      this.selectStoreId = val.map((item) => item.id);
      if (this.selectStoreId.length == 0) {
        this.isIndeterminate = false;
        this.checkAll = false;
      } else {
        if (this.tableData.total <= this.tableFrom.limit) {
          if (this.selectStoreId.length == this.tableData.total) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        } else {
          if (this.selectStoreId.length == this.tableFrom.limit) {
            this.isIndeterminate = false;
            this.checkAll = true;
          } else {
            this.isIndeterminate = true;
          }
        }
      }
    },
    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },
    // 批量处理
    handlerBatch(typeName) {
      if (this.selectStoreId.length == 0) {
        return this.$message.warning(`请选择需要${typeName}的产品`);
      }
      this.$modalSure(`确定要${typeName}吗`).then(() => {
        const apifun = apiMap[typeName];
        apifun(this.selectStoreId).then(() => {
          this.$message.success("操作成功");
          this.getList();
        });
      });
    },
    // 批量处理
    handlerBatch(typeName) {
      if (this.selectStoreId.length == 0) {
        return this.$message.warning(`请选择需要${typeName}的产品`);
      }
      this.$modalSure(`确定要${typeName}吗`).then(() => {
        const apifun = apiMap[typeName];
        apifun(this.selectStoreId).then(() => {
          this.$message.success("操作成功");
          this.getList();
        });
      });
    },
    // 列表
    getList() {
      this.listLoading = true;
      memberDiscountListApi(this.tableFrom)
        .then((res) => {
          this.tableData.data = res.list;
          this.tableData.total = res.total;
          this.listLoading = false;
        })
        .catch(() => {
          this.listLoading = false;
        });
    },
    pageChange(page) {
      this.tableFrom.page = page;
      this.getList();
    },
    handleSizeChange(val) {
      this.tableFrom.limit = val;
      this.getList();
    },
    // 删除
    handleDelete(id, idx) {
      this.$modalSure("删除id为" + id + "的折扣吗？")
        .then(() => {
          memberDiscountDeleteApi(id).then(() => {
            this.$message.success("删除成功");
            this.tableData.data.splice(idx, 1);
          });
        })
        .catch(() => {});
    },
  },
};
</script>

<style scoped lang="scss">
.fr {
  float: right;
}

.divBox {
  .search-box {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .tips {
    margin-bottom: 10px;
  }
  .status-icon {
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #0fa142;
    margin-right: 4px;
  }
}
</style>
