<template>
  <MainCard :cardType="2">
    <div class="app-box">
      <el-form
        ref="formValidate"
        v-loading="fullscreenLoading"
        :rules="ruleValidate"
        :model="formValidate"
        label-width="120px"
        @submit.native.prevent
      >
        <div>
          <!-- 基本信息 start -->
          <div class="base-card">
            <div class="info-title">基本信息</div>
            <div class="form-info">
              <el-form-item style="width: 60%" label="活动名称：" prop="name">
                <el-input v-model="formValidate.name" placeholder="请输入" />
              </el-form-item>

              <el-form-item required label="活动时间：">
                <div class="flex">
                  <el-form-item>
                    <el-date-picker
                      v-model="formValidate.startTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="活动开始时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                  <span>至</span>
                  <el-form-item>
                    <el-date-picker
                      v-model="formValidate.stopTime"
                      type="datetime"
                      value-format="yyyy-MM-dd HH:mm:ss"
                      placeholder="活动结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </div>
              </el-form-item>
              <el-form-item label="活动图：">
                <div class="upLoadPicBox">
                  <div
                    @click="modalPicTap('1')"
                    v-if="formValidate.image"
                    class="pictrue"
                  >
                    <!-- <MyImage
                      @click.native.stop
                      :imagePath="formValidate.image"
                      :previewPath="formValidate.image"
                      :size="60"
                    /> -->
                    <img :src="handlerImgUrl(formValidate.image)" alt="" />
                  </div>
                  <div @click="modalPicTap('1')" v-else class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  建议尺寸：750x375 像素，格式支持png、jpg、bmp
                </div>
              </el-form-item>
              <el-form-item label="氛围图标：">
                <div @click="modalPicTap('2')" class="upLoadPicBox">
                  <div v-if="formValidate.atmosphereIcon" class="pictrue">
                    <!-- <MyImage
                      @click.native.stop
                      :imagePath="handlerImgUrl(formValidate.atmosphereIcon)"
                      :previewPath="formValidate.atmosphereIcon"
                      :size="60"
                    /> -->
                    <img
                      :src="handlerImgUrl(formValidate.atmosphereIcon)"
                      alt=""
                    />
                  </div>
                  <div v-else class="upLoad">
                    <i
                      style="font-size: 16px"
                      class="el-icon-plus cameraIconfont"
                    />
                  </div>
                </div>
                <div class="detail-description">
                  建议尺寸：144x96 像素，格式建议使用透明的png、svg、gif
                </div>
              </el-form-item>
            </div>
          </div>
          <!-- 基本信息 end -->

          <!-- 活动内容 start -->
          <div class="store-specification base-card">
            <div class="info-title">活动内容</div>
            <div class="form-info">
              <!-- 活动内容 -->
              <el-form-item label="优惠券：" class="proCoupon">
                <div class="acea-row">
                  <el-tag
                    v-for="(tag, index) in formValidate.coupons"
                    :key="index"
                    class="mr10 mb10"
                    closable
                    :disable-transitions="false"
                    @close="handleCloseCoupon(tag)"
                  >
                    {{ tag.name }}
                  </el-tag>
                  <!-- <span v-if="formValidate.couponIds == null">无</span> -->
                  <el-button type="text" class="mr15" @click="addCoupon"
                    >选择优惠券</el-button
                  >
                </div>
              </el-form-item>
              <el-form-item label="文章内容" prop="content">
                <MyEditer :htmlContent.sync="formValidate.description" />
              </el-form-item>
            </div>
          </div>
        </div>
      </el-form>
    </div>
    <template v-slot:footer>
      <div class="footer-btn">
        <el-button
          type="primary"
          class="submission"
          @click="handleSubmit('formValidate')"
          >保存</el-button
        >
        <el-button class="submission" @click="$router.go(-1)">取消</el-button>
      </div>
    </template>
  </MainCard>
</template>

<script>
import {
  activityCreateApi,
  activityUpdateApi,
  activityInfoApi,
} from "@/api/activity";
import MyEditer from '@/components/myEditer/index'
import { Debounce } from "@/utils/validate";
const defaultObj = {
  name: null,
  startTime: null,
  stopTime: null,
  image: null,
  atmosphereIcon: null,
  couponIds: [],
  coupons: [],
  description: null,
};
export default {
  components: { MyEditer },
  data() {
    return {
      formValidate: Object.assign({}, defaultObj),
      ruleValidate: {
        name: [{ required: true, message: "请输入活动名称", trigger: "blur" }],
      },
      fullscreenLoading: false,
      tempRoute: {},
    };
  },
  computed: {},
  watch: {},

  mounted() {
    if (this.$route.query.oprationType !== "add") {
      // this.setTagsViewTitle();
      this.getInfo();
    }
  },
  methods: {
    // 点击商品图
    modalPicTap(type) {
      const _this = this;
      this.$modalUpload(
        function (img) {
          // 主图视频
          if (type === "1") {
            _this.formValidate.image = img[0].attDir;
          }
          if (type == "2") {
            _this.formValidate.atmosphereIcon = img[0].attDir;
          }
        },
        2,
        "content"
      );
    },
    // 选择优惠券
    addCoupon() {
      const _this = this;
      this.$modalCoupon(
        "wu",
        (this.keyNum += 1),
        this.formValidate.coupons,
        function (row) {
          _this.formValidate.couponIds = [];
          _this.formValidate.coupons = [...row];
          row.map((item) => {
            _this.formValidate.couponIds.push(item.id);
          });
        },
        ""
      );
    },
    handleCloseCoupon(tag) {
      this.formValidate.coupons.splice(
        this.formValidate.coupons.indexOf(tag),
        1
      );
      this.formValidate.couponIds.splice(
        this.formValidate.couponIds.indexOf(tag.id),
        1
      );
    },

    // 根据主图路径返回缩略图路径
    handlerImgUrl(url) {
      let newString = "thumbnailImage";
      let lastDotIndex = url.lastIndexOf(".");
      return (
        url.substring(0, lastDotIndex) + newString + url.substring(lastDotIndex)
      );
    },

    setTagsViewTitle() {
      const route = Object.assign({}, this.tempRoute, {
        title: `${title}-${this.$route.params.id}`,
      });
      this.$store.dispatch("tagsView/updateVisitedView", route);
    },

    // 删除表格中的属性
    delAttrTable(index) {
      this.formValidate.productItems.splice(index, 1);
    },

    // 详情
    getInfo() {
      this.fullscreenLoading = true;
      activityInfoApi(this.$route.params.id)
        .then(async (res) => {
          // this.isAttr = true;
          let info = res;
          this.formValidate = {
            id:
              this.$route.query.oprationType === "edit"
                ? this.$route.params.id
                : null,
            name: info.name,
            startTime: info.startTime,
            stopTime: info.stopTime,
            image: info.image,
            atmosphereIcon: info.atmosphereIcon,
            coupons: info.couponList || [],
            description: info.description,
          };

          this.fullscreenLoading = false;
        })
        .catch((res) => {
          this.fullscreenLoading = false;
          this.$message.error(res.message);
        });
    },
    // 提交
    handleSubmit: Debounce(function (name) {
      this.$refs[name].validate((valid) => {
        if (valid) {
          this.fullscreenLoading = true;
          this.$route.query.oprationType === "edit"
            ? activityUpdateApi({
                ...this.formValidate,
              })
                .then(async (res) => {
                  this.$message.success("编辑成功");
                  setTimeout(() => {
                    this.$router.push({
                      path: "/marketing/activityManagement",
                    });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                })
            : activityCreateApi({
                ...this.formValidate,
              })
                .then(async (res) => {
                  this.$message.success("新增成功");
                  setTimeout(() => {
                    this.$router.push({
                      path: "/marketing/activityManagement",
                    });
                  }, 500);
                  this.fullscreenLoading = false;
                })
                .catch((res) => {
                  this.fullscreenLoading = false;
                });
        } else {
          this.$message.warning("请填写完整信息！");
        }
      });
    }),
  },
};
</script>
<style scoped lang="scss">
.app-box {
  .upLoadPicBox {
    display: inline-flex;
  }
  .base-card {
    background-color: #ffffff;
    overflow: hidden;
    border-radius: 4px;
    margin-bottom: 16px;
    padding: 20px 20px 0;
  }
  .form-info {
    padding-left: 80px;
  }
  .detail-description {
    font-size: 12px;
    color: #666;
  }
  .inline-flex {
    display: inline-flex;
    gap: 10px;
    align-items: center;
  }

  .info-title {
    font-size: 14px;
    font-weight: 700;
    margin: 0 0 20px 0;
    &::before {
      content: "";
      display: inline-block;
      width: 5px;
      height: 14px;
      background-color: rgb(3, 158, 3);
      vertical-align: -2px;
      margin-right: 8px;
    }
  }

  .flex {
    display: flex;
    white-space: nowrap;
    gap: 10px;
    align-items: center;
  }
}
.footer-btn {
  text-align: center;
  /* margin-top: 40px; */
}
</style>
