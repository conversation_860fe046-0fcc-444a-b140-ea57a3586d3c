// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type='file'] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

// refine element ui upload
.upload-container {
  .el-upload {
    //width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block;
  }
}

// fix date-picker ui bug in filter-item
.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

// to fix el-date-picker css style
.el-range-separator {
  box-sizing: content-box;
}

.el-card.is-always-shadow {
  box-shadow: none;
}

.el-button--primary {
  background: linear-gradient(to right, #dc3c2e, #dc5620);
}

.el-button--primary:hover,
.el-button--primary:focus {
  color: #fff;
  background: linear-gradient(to right, #db564a, #dd6f43);
}

.el-tabs__nav-wrap::after {
  content: '';
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background-color: #dfe4ed;
  z-index: 1;
}

.el-table__body tr.current-row>td {
  background-color: #fff;
}

.el-switch__core {
  border-radius: 4px;
  width: 32px;
  height: 16px;
}

.el-switch__core:after {
  border-radius: 3px;
  width: 16px;
  height: 12px;
}

.el-tabs__item.is-active {
  font-size: 15px;
  font-weight: 700;
}

.el-button--text {
  color: #3f6fd6 !important;
  padding: 0!important;
}

.el-button--text:hover,
.el-button--text:active {
  color: #6b90e1 !important;
}

.el-button--text.is-disabled {
  color: #c0c4cc !important;
}

.el-button--small {
  font-size: 14px;
  padding: 8px 15px
}

.el-table {
  border-radius: 5px 5px 0 0;
}