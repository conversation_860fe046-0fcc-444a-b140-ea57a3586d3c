// 推荐的免费省市区API接口
const API_ENDPOINTS = {
  // 高德地图API（需要申请key，免费额度足够）
  amap: 'https://restapi.amap.com/v3/config/district',
  
  // 免费的省市区API（无需key）
  free1: 'https://api.jisuapi.com/area/city',
  free2: 'http://pv.sohu.com/cityjson',
  
  // 本地静态文件方式
  local: '/api/region'
};

// 当前使用的API配置
const currentAPI = 'local'; // 可根据需要切换

/**
 * 动态加载省市区数据
 * @param {string} parentCode - 父级区域代码，为空时加载省份
 * @returns {Promise<Array>} 返回区域数据数组
 */
export const loadRegionData = async (parentCode = '') => {
  try {
    switch (currentAPI) {
      case 'amap':
        return await loadFromAmap(parentCode);
      case 'local':
        return await loadFromLocal(parentCode);
      default:
        return await loadFromLocal(parentCode);
    }
  } catch (error) {
    console.error('加载区域数据失败:', error);
    return [];
  }
};

/**
 * 从本地API加载数据
 */
const loadFromLocal = async (parentCode) => {
  const response = await fetch(`/api/region?parentCode=${parentCode}`);
  const data = await response.json();
  return data.map(item => ({
    value: item.code,
    label: item.name,
    leaf: item.level === 3 // 区县级别为叶子节点
  }));
};

/**
 * 从高德地图API加载数据（需要申请key）
 */
const loadFromAmap = async (parentCode) => {
  const key = 'YOUR_AMAP_KEY'; // 需要申请高德地图API key
  const subdistrict = parentCode ? 1 : 1;
  const keywords = parentCode || '中国';
  
  const response = await fetch(
    `${API_ENDPOINTS.amap}?key=${key}&keywords=${keywords}&subdistrict=${subdistrict}`
  );
  const result = await response.json();
  
  if (result.status === '1' && result.districts[0]) {
    return result.districts[0].districts.map(item => ({
      value: item.adcode,
      label: item.name,
      leaf: item.level === 'district' // 区县级别为叶子节点
    }));
  }
  return [];
};

/**
 * 级联选择器懒加载函数
 * @param {Object} node - 当前节点
 * @param {Function} resolve - 回调函数
 */
export const lazyLoadRegion = async (node, resolve) => {
  const { level, value } = node;
  
  // 根据层级确定是否为叶子节点
  const isLeaf = level >= 2; // 第3级（区县）为叶子节点
  
  if (isLeaf) {
    resolve([]);
    return;
  }
  
  try {
    const regions = await loadRegionData(value);
    resolve(regions);
  } catch (error) {
    console.error('懒加载区域数据失败:', error);
    resolve([]);
  }
};

/**
 * 根据区域代码获取完整路径名称
 * @param {Array} codes - 区域代码数组 [provinceCode, cityCode, districtCode]
 * @returns {Promise<string>} 返回完整路径名称，如"广东省-深圳市-南山区"
 */
export const getRegionPath = async (codes) => {
  if (!codes || codes.length === 0) return '';
  
  try {
    const names = [];
    let parentCode = '';
    
    for (let i = 0; i < codes.length; i++) {
      const regions = await loadRegionData(parentCode);
      const region = regions.find(r => r.value === codes[i]);
      if (region) {
        names.push(region.label);
        parentCode = codes[i];
      }
    }
    
    return names.join('-');
  } catch (error) {
    console.error('获取区域路径失败:', error);
    return '';
  }
};

// 导出空的regionOptions用于向后兼容
export const regionOptions = [];
