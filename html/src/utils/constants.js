// +----------------------------------------------------------------------
// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
// +----------------------------------------------------------------------
// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.
// +----------------------------------------------------------------------
// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
// +----------------------------------------------------------------------
// | Author: CRMEB Team <<EMAIL>>
// +----------------------------------------------------------------------

/*
/ 全局静态变量定义
切勿随意修改数组次序，很多地方已下标方式使用
 */

export const page = {
  limit: [20, 40, 60, 80, 100],
  page: 1,
  layout: 'total, sizes, prev, pager, next, jumper'
}

/**
 * 无限极分类type
 * @type {{product: number, attachment: number, menu: number, article: number, operator: number}}
 */
export const categoryType = [
  // 1 产品分类，2 附件分类，3 文章分类， 4 设置分类， 5 菜单分类， 6 配置分类， 7 秒杀配置
  { name: '产品分类', value: 1, shortName: '产品' },
  { name: '附件分类', value: 2, shortName: '附件' },
  { name: '文章分类', value: 3, shortName: '文章' },
  { name: '设置分类', value: 4, shortName: '设置' },
  { name: '菜单分类', value: 5, shortName: '菜单' },
  { name: '配置分类', value: 6, shortName: '配置' },
  { name: '秒杀配置', value: 7, shortName: '秒杀' }
]

export const roleListStatus = [
  { label: '全部', value: '' },
  { label: '显示', value: 1 },
  { label: '不显示', value: 0 }
]

export const showHiddenStatus = [
  { label: '显示', value: '‘1’' },
  { label: '不显示', value: '‘0’' }
]

export const switchStatus = [
  { label: '开启', value: 1 },
  { label: '关闭', value: 0 }
]

export const deletedOrNormal = [
  { label: '正常', value: 0 },
  { label: '已删除', value: 1 }
]

/**
 * 暂时弃用
 * @type {*[]}
 */
export const configCategory = [
  { label: '系统', value: '0' },
  { label: '应用', value: '1' },
  { label: '支付', value: '2' },
  { label: '其他', value: '3' }
]

/**
 * 表单配置集合集中配置
 * @type {{id: number, dis: string}[]}
 */
export const formConfigIds = [
  { id: 84, dis: '微信公众号表单配置' },
  { id: 86, dis: '秒杀配置' }
]

/**
 * 时间选择器
 */
export const fromList = {
  title: '选择时间',
  custom: true,
  fromTxt: [
    { text: '全部', val: '' },
    { text: '今天', val: 'today' },
    { text: '昨天', val: 'yesterday' },
    { text: '最近7天', val: 'lately7' },
    { text: '最近15天', val: 'lately15' },
    { text: '最近30天', val: 'lately30' },
    { text: '本月', val: 'month' },
    { text: '本年', val: 'year' }
  ]
}

// 统计管理时间选择器
export const timeList = {
  title: '选择时间',
  custom: true,
  fromTxt: [
    { text: '昨天', val: `` },
    { text: '最近7天', val: 'lately7' },
    { text: '最近30天', val: 'lately30' }
  ]
}

const a = [
  {
    priTmplId: 'hWf6iHKsr-vsX8sgb-sntx27QhLzvUlzNmIBYGTt6a0',
    title: '手术进程通知',
    content:
      '患者:{{thing1.DATA}}\n病区:{{thing2.DATA}}\n医护人员:{{thing3.DATA}}\n进程节点:{{thing4.DATA}}\n备注:{{thing5.DATA}}\n',
    example:
      '患者:张三（03床）\n病区:院本部\n医护人员:主刀：李四、王五；麻醉：谢宝庆、李云龙\n进程节点:患者已于18:30到达手术间\n备注:请速来xxx手术间，谢谢\n',
    type: 2,
    keywordEnumValueList: []
  },
  {
    priTmplId: '3VKzliUWrGr0ZV92Ree_2QL_SE_BQ6pzmSSqFqEHbHc',
    title: '手术预约成功通知',
    content:
      '手术名称:{{thing1.DATA}}\n预约日期:{{date2.DATA}}\n姓名:{{name3.DATA}}\n患者ID:{{character_string4.DATA}}\n温馨提示:{{thing5.DATA}}\n',
    example:
      '手术名称:xx癌症手术\n预约日期:2020年 20:20:20\n姓名:张宝宝\n患者ID:32323323xxxsds3223\n温馨提示:您好，你的手术已成功预约，请注意手术时间\n',
    type: 2,
    keywordEnumValueList: []
  },
  {
    priTmplId: 'ZVj8fck7enim4bKId6c-hRUJvOysksTg61h8kZ7R8xk',
    title: '手术排程通知',
    content:
      '手术名称:{{thing1.DATA}}\n姓名:{{thing2.DATA}}\n预约日期:{{time3.DATA}}\n入院科室:{{thing4.DATA}}\n温馨提示:{{thing5.DATA}}\n',
    example:
      '手术名称:白内障乳化摘除术\n姓名:张三\n预约日期:2023年11月18日\n入院科室:眼科\n温馨提示:请按预约日期入院手术\n',
    type: 2,
    keywordEnumValueList: []
  },
  {
    priTmplId: 'oyOcaG0SrfFKyQ0Ne6jMFW-qkogab-XYI2x7mY30SKY',
    title: '明日手术通知',
    content:
      '患者姓名:{{thing1.DATA}}\n手术时间:{{time2.DATA}}\n温馨提醒:{{thing3.DATA}}\n',
    example:
      '患者姓名:张三\n手术时间:2024年7月3日\n温馨提醒:请您做好术前准备，按时入院手术！\n',
    type: 2,
    keywordEnumValueList: []
  },
  {
    priTmplId: '376AmyLcll3APiGrjYm7yq1xhyrLcRx0I9LxcZBVmcA',
    title: '手术状态提醒',
    content:
      '姓名:{{thing1.DATA}}\n患者ID:{{character_string2.DATA}}\n手术状态:{{thing3.DATA}}\n手术名称:{{thing4.DATA}}\n温馨提示:{{thing5.DATA}}\n',
    example:
      '姓名:方某某\n患者ID:36474xxxxx3278327\n手术状态:手术复苏\n手术名称:肺癌xxx手术\n温馨提示:您正在进行手术复苏请耐心等待\n'
  }
]
