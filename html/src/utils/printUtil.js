export function printHiddenContent(contentHTML) {
  // 1. 创建隐藏iframe
  const iframe = document.createElement('iframe');
  iframe.style.cssText = 'position:absolute;width:0;height:0;border:none';
  document.body.appendChild(iframe);

  // 2. 注入打印内容
  const doc = iframe.contentDocument || iframe.contentWindow.document;
  doc.open();
  doc.write(contentHTML);
  doc.close();

  // 3. 等待资源加载完成后打印
  iframe.onload = () => {
    iframe.contentWindow.onafterprint = () => {
      document.body.removeChild(iframe);
    };
    iframe.contentWindow.focus();
    iframe.contentWindow.print();
  };
}
