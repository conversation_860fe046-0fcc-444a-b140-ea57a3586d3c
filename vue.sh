#!/bin/bash
copy_and_restart() {
#  echo $*
#  echo $@
  Host=$1
  Password=$2
  SourceFile=$3
  DistPath=$4
#  echo $SourceFile
  echo $DistPath
#  if [ -f $SourceFile ]; then
    echo '******************拷贝文件******************'
    sshpass -p $Password scp -rp $SourceFile root@$Host:$DistPath
    if [ $? -eq 0 ]; then
      echo '******************拷贝成功******************'
      echo '******************重启系统******************'

      sshpass -p $Password ssh -p 22 root@$Host "source /etc/profile && cd /home/<USER>/html && rm -rf admin && mv dist admin && chown -R nginx:nginx admin &&  nginx -s reload"
    fi
#  fi

}

echo '******************准备更新******************'
cd $SourcePath
 Host='**************'
  Password='Tanjun@1536'
  ServerPath='/home'
 echo '******************开始编译******************'
  cd html
  rm -rf dist
  # nvm use 14
  cnpm i
  ./node_modules/.bin/vue-cli-service build
  if [ $? -eq 0 ]; then
  echo '******************开始更新******************'
  sleep 1
  Host='**************'
  SourceFile="$SourcePath/html/dist/"
#          chmod -R755 $SourceFile
  DistPath="$ServerPath/nginx/html/"
  copy_and_restart $Host $Password $SourceFile $DistPath
  else
    echo '编译失败'
  fi