# 电商商城系统

这是一个基于Java Spring Boot的电商商城后台管理系统，提供商品管理、订单管理、用户管理、分销返佣、会员体系等完整的电商解决方案。

## 🆕 最新更新 (2025-08-01)

### 活动详情图片分割功能增强
系统为活动管理功能增加了智能图片处理能力，基于已上传并处理好的图片信息，自动替换长图片为分割后的图片，提升用户浏览体验：

#### 功能特性
- **智能图片替换**：根据`system_attachment`表中的分割信息，自动替换长图片为分割后的图片
- **双字段设计**：`description_admin`存储原图URL，`description`存储分割后的图片URL
- **前端简化**：前端只需传递`description_admin`，后端自动查询并替换长图片
- **模式区分**：图片模式（逗号分隔URL）和富文本模式分别处理
- **完整清理**：删除活动时自动清理所有分割图片和缩略图

#### 技术实现
- **数据库字段**：`store_activity`表新增`description_admin`字段存储原始内容
- **处理工具类**：`ActivityDescriptionProcessor`专门处理活动详情图片替换
- **数据库驱动**：查询`system_attachment`表的`is_long_image`和`split_image_urls`字段
- **自动化流程**：创建/更新活动时自动处理，删除时自动清理

#### 业务规则
- **图片模式（contentType=1）**：输入逗号分隔的图片URL，查询数据库判断是否为长图片并替换
- **富文本模式（contentType=2）**：直接存储HTML内容，不进行图片处理
- **替换条件**：`system_attachment`表中`is_long_image=1`的图片会被替换
- **全量替换**：每次更新都是全量处理，确保数据一致性

#### 数据库变更
```sql
-- 添加活动详情管理员字段
ALTER TABLE store_activity ADD COLUMN description_admin text COMMENT '活动详情（管理员上传的原图或原富文本）' AFTER description;
```

#### 相关文件
- `model/src/main/java/com/ylpz/model/activity/StoreActivity.java` - 活动实体类增强
- `core/src/main/java/com/ylpz/core/common/request/StoreActivityRequest.java` - 活动请求对象修改
- `core/src/main/java/com/ylpz/core/common/response/StoreActivityResponse.java` - 活动响应对象增强
- `core/src/main/java/com/ylpz/core/common/utils/ActivityDescriptionProcessor.java` - 图片处理工具类
- `core/src/main/java/com/ylpz/core/service/impl/StoreActivityServiceImpl.java` - 活动业务逻辑增强
- `sql/20250801_add_description_admin_to_store_activity.sql` - 活动表数据库变更脚本

### 商品详情图片分割功能增强
系统为商品管理功能也增加了相同的智能图片处理能力，支持商品详情图片的长图片自动替换：

#### 功能特性
- **智能图片替换**：根据`system_attachment`表中的分割信息，自动替换商品详情中的长图片
- **双字段设计**：`detail_images_admin`存储原图URL，`detail_images`存储分割后的图片URL
- **前端简化**：前端只需传递`detailImagesAdmin`字段，后端自动查询并替换长图片
- **完整清理**：删除商品时自动清理所有分割图片和缩略图

#### 技术实现
- **数据库字段**：`store_product`表新增`detail_images_admin`字段存储原始内容
- **处理工具类**：复用`ActivityDescriptionProcessor`处理商品详情图片替换
- **数据库驱动**：查询`system_attachment`表的`is_long_image`和`split_image_urls`字段
- **自动化流程**：创建/更新商品时自动处理，删除时自动清理

#### 数据库变更
```sql
-- 添加商品详情图片管理员字段
ALTER TABLE store_product ADD COLUMN detail_images_admin text COMMENT '商品详情图片（管理员上传的原图或原富文本）' AFTER detail_images;
```

#### 相关文件
- `model/src/main/java/com/ylpz/model/product/StoreProduct.java` - 商品实体类增强
- `core/src/main/java/com/ylpz/core/common/request/StoreProductAddRequest.java` - 商品请求对象修改
- `core/src/main/java/com/ylpz/core/common/response/StoreProductResponse.java` - 商品响应对象增强
- `core/src/main/java/com/ylpz/core/common/response/StoreProductInfoResponse.java` - 商品详情响应对象增强
- `core/src/main/java/com/ylpz/core/service/impl/StoreProductServiceImpl.java` - 商品业务逻辑增强
- `sql/20250801_add_detail_images_admin_to_store_product.sql` - 商品表数据库变更脚本

### 图片上传缩略图功能增强
系统对图片上传功能进行了重大增强，现在支持为分割的长图片生成缩略图，并在删除时完整清理所有相关文件：

#### 功能特性
- **分割图片缩略图生成**：长图片分割时，每个分割图片都会自动生成对应的缩略图
- **统一缩略图规格**：分割图片的缩略图使用与主图相同的尺寸配置（默认336x336）
- **完整删除处理**：删除图片时会同时删除主图、主图缩略图、分割图片和分割图片缩略图
- **格式一致性**：分割图片缩略图文件名格式为 `原文件名_partN_thumbnailImage.扩展名`
- **性能优化**：使用Thumbnailator库高效生成缩略图，支持多种图片格式

#### 技术实现
- **核心工具类**：`ImageSplitUtil` - 新增 `SplitImageResult` 内部类，同时返回分割图片和缩略图路径
- **数据库字段**：`SystemAttachment` 表新增 `split_image_thumbnail_urls` 字段存储分割图片缩略图路径
- **返回对象增强**：`FileResultVo` 新增 `splitImageThumbnailUrls` 字段返回分割图片缩略图URL列表
- **删除逻辑完善**：`SystemAttachmentServiceImpl.deleteWithFiles()` 方法增加分割图片缩略图删除逻辑
- **文件管理工具**：`FileDeleteUtil` 支持批量删除分割图片缩略图文件

#### 业务规则
- **缩略图生成条件**：只有可压缩的图片格式（jpg、png、gif等）才会生成缩略图
- **分割条件**：图片高度大于宽度2倍时才进行分割，每个分割高度等于图片宽度
- **存储路径**：分割图片缩略图与分割图片存储在同一目录下
- **命名规则**：`原文件名_part序号thumbnailImage.扩展名`

#### 数据库变更
```sql
-- 添加分割图片缩略图路径字段
ALTER TABLE system_attachment ADD COLUMN split_image_thumbnail_urls text COMMENT '分割图片缩略图路径列表，以逗号分隔';
```

#### 业务价值
- **用户体验提升**：分割图片也有缩略图，页面预览更加流畅
- **存储管理完善**：删除时完整清理所有相关文件，避免垃圾文件堆积
- **功能一致性**：分割图片与主图享受相同的缩略图功能
- **性能优化**：缩略图加载速度快，减少带宽消耗

#### 相关文件
- `model/src/main/java/com/ylpz/model/system/SystemAttachment.java` - 数据模型增强
- `core/src/main/java/com/ylpz/core/common/vo/FileResultVo.java` - 返回对象增强
- `core/src/main/java/com/ylpz/core/common/utils/ImageSplitUtil.java` - 分割工具类增强
- `core/src/main/java/com/ylpz/core/service/impl/UploadServiceImpl.java` - 上传服务增强
- `core/src/main/java/com/ylpz/core/service/impl/SystemAttachmentServiceImpl.java` - 删除逻辑增强
- `sql/20250801_add_split_image_thumbnail_urls_to_system_attachment.sql` - 数据库变更脚本

## 🆕 最新更新 (2025-08-01)

### 分类删除时自动清理附件功能
系统新增了分类删除时自动清理附件的功能，确保删除附件分类时同步清理相关文件：

#### 功能特性
- **智能类型检测**：删除分类时自动检测分类类型，只对type=2（附件分类）执行附件清理
- **完整文件清理**：自动删除数据库中的附件记录和服务器上对应的物理文件
- **安全性保障**：删除前进行分类存在性检查，附件删除失败时整个操作回滚
- **批量处理**：支持一次性删除分类下的所有附件文件，提高处理效率

#### 业务规则
- **触发条件**：仅当删除的分类类型为2（附件分类）时触发附件清理
- **清理范围**：删除system_attachment表中pid等于分类ID的所有附件记录
- **文件删除**：同时删除服务器上的原图、缩略图和分割图片等所有相关文件
- **异常处理**：如果附件删除失败，抛出异常并阻止分类删除操作

#### 技术实现
- **核心方法**：`CategoryServiceImpl.delete()` - 在原有删除逻辑基础上增加附件清理
- **服务调用**：使用 `SystemAttachmentService.deleteWithFiles()` 方法处理文件删除
- **查询优化**：使用 LambdaQueryWrapper 高效查询分类下的附件记录
- **流式处理**：使用 Java Stream API 提取附件ID列表，提高代码可读性

#### 分类类型说明
- **1**: 产品分类
- **2**: 附件分类（本次功能针对此类型）
- **3**: 文章分类
- **4**: 设置分类
- **5**: 菜单分类
- **6**: 配置分类
- **7**: 秒杀配置

#### 业务价值
- **数据一致性**：确保删除分类时不会留下孤立的附件记录
- **存储优化**：自动清理无用文件，节省服务器存储空间
- **管理便利性**：管理员无需手动清理附件，系统自动处理
- **安全可靠**：完善的异常处理机制，确保操作安全性

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/CategoryServiceImpl.java` - 核心实现

## 🆕 最新更新 (2025-07-29)

### 会员参数设置佣金返现同步功能
系统新增了会员参数设置保存时自动同步佣金返现设置到用户等级表的功能：

#### 功能特性
- **自动同步机制**：当通过 `/admin/system/member/setting/saveForm` 接口更新佣金返现设置时，自动同步更新 `system_user_level` 表的佣金相关字段
- **智能配置解析**：自动解析 `commission_ratio` 配置中的佣金比例设置，根据用户等级匹配对应的佣金配置
- **字段同步更新**：根据配置自动更新 `commission_enabled` 和 `commission_rate` 字段
- **容错处理机制**：同步失败不影响主要的参数保存流程，确保系统稳定性

#### 同步规则
- **普通会员和VIP会员**：配置启用且有佣金比例时，设置 `commission_enabled = 1`，`commission_rate = 配置值`
- **SVIP会员（等级3）特殊逻辑**：除了上述条件外，还需要检查SVIP自购返现开关
  - 如果SVIP自购返现未启用，强制设置 `commission_enabled = 0`，`commission_rate = 0`
  - 如果SVIP自购返现已启用，按正常规则设置佣金
- **配置禁用或无佣金比例**：设置 `commission_enabled = 0`，`commission_rate = 0`
- **配置格式**：`{"1":{"purchase":"10"},"2":{"purchase":8},"3":{"purchase":6}}`（支持字符串和数字混合格式）
- **格式兼容性**：自动处理佣金比例的字符串和数字混合格式，确保历史数据兼容性

#### 技术实现
- **核心方法**：`SystemParamSettingServiceImpl.syncCommissionSettingsToUserLevel()` - 处理佣金设置同步逻辑
- **触发时机**：在 `saveOrUpdateBatchByModule()` 方法中，当模块名称为"佣金返现设置"时自动触发
- **数据解析**：使用 Hutool 的 JSONUtil 解析配置值，智能处理字符串和数字混合格式
- **类型转换**：自动处理 `"10"` (字符串) 和 `8` (数字) 等不同格式的佣金比例值
- **SVIP特殊逻辑**：检查 `svip_auto_refund` 配置，确保SVIP等级的佣金设置符合自购返现开关状态
- **批量更新**：通过 MyBatis-Plus 的 UpdateWrapper 批量更新用户等级表

#### 业务价值
- **数据一致性**：确保参数配置与用户等级表中的佣金设置保持同步
- **管理便利性**：管理员只需在参数设置中修改佣金配置，系统自动同步到等级表
- **维护简化**：避免手动维护两个地方的佣金配置，减少数据不一致的风险
- **扩展性强**：支持未来新增用户等级时的自动配置同步

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/SystemParamSettingServiceImpl.java` - 核心实现
- `sql/20250729_sync_commission_settings_to_user_level.sql` - 功能实现记录和测试脚本
- `sql/20250729_test_mixed_format_commission_sync.sql` - 混合格式处理测试脚本
- `docs/20250729_commission_sync_implementation.md` - 详细技术实现文档
- `docs/20250729_commission_sync_usage_example.md` - 使用示例和说明文档

### 活动表字段增强功能
系统为活动表增加了弹窗图片和内容类型字段，提升活动管理的灵活性和用户体验：

#### 新增字段
1. **未领取弹窗图** (`unclaimed_popup_image`)：用于显示用户未领取活动奖励时的弹窗图片
2. **已领取弹窗图** (`claimed_popup_image`)：用于显示用户已领取活动奖励时的弹窗图片
3. **活动内容类型** (`content_type`)：区分活动详情的展示模式
   - **1**：图片模式 - 活动详情以图片形式展示
   - **2**：富文本模式 - 活动详情以富文本形式展示

#### 功能特性
- **弹窗图片管理**：支持为不同状态的用户显示不同的弹窗图片，提升用户体验
- **内容类型控制**：灵活控制活动详情的展示方式，满足不同的运营需求
- **图片处理优化**：新增字段的图片路径会自动进行前缀清理处理
- **默认值设置**：内容类型默认为图片模式，确保向后兼容性

#### 技术实现
- **数据库变更**：`sql/20250729_add_popup_images_and_content_type_to_activity.sql`
- **模型类更新**：`StoreActivity` - 新增三个字段的属性定义
- **请求类更新**：`StoreActivityRequest` - 支持新字段的输入
- **响应类更新**：`StoreActivityResponse` - 返回新字段的数据
- **服务层更新**：`StoreActivityServiceImpl` - 处理新字段的图片路径和默认值

#### 业务价值
- **用户体验提升**：通过不同状态的弹窗图片，为用户提供更直观的视觉反馈
- **运营灵活性**：支持图片和富文本两种内容展示模式，满足不同活动的展示需求
- **管理便利性**：统一的活动内容类型管理，简化运营人员的操作流程
- **扩展性增强**：为未来更多的活动展示形式预留了扩展空间

#### 相关文件
- `sql/20250729_add_popup_images_and_content_type_to_activity.sql` - 数据库结构变更
- `model/src/main/java/com/ylpz/model/activity/StoreActivity.java` - 活动模型类
- `core/src/main/java/com/ylpz/core/common/request/StoreActivityRequest.java` - 活动请求类
- `core/src/main/java/com/ylpz/core/common/response/StoreActivityResponse.java` - 活动响应类
- `core/src/main/java/com/ylpz/core/service/impl/StoreActivityServiceImpl.java` - 活动服务实现

## 🆕 最新更新 (2025-07-28)

### 活动状态自动更新修复
系统修复了活动编辑后状态不自动更新的问题，现在创建和编辑活动时会根据时间范围自动设置正确的状态：

#### 问题描述
- **原问题**：编辑活动修改时间后，状态仍然显示"已结束"
- **根本原因**：编辑活动时只更新了时间字段，没有根据新的时间范围重新计算活动状态
- **影响范围**：活动创建和编辑后状态显示不准确，需要手动调整状态

#### 修复内容
1. **创建活动时状态计算**：根据设置的时间范围自动设置活动状态
   - 当前时间在活动时间范围内：设置为开启状态（true）
   - 当前时间不在活动时间范围内：设置为关闭状态（false）

2. **编辑活动时状态更新**：修改活动时间后自动重新计算状态
   - 根据新的开始时间和结束时间重新判断活动是否应该开启
   - 自动更新数据库中的 `status` 字段

#### 状态计算逻辑
```java
// 判断当前时间是否在活动时间范围内
Date now = new Date();
boolean shouldBeActive = now.after(startTime) && now.before(stopTime);
activity.setStatus(shouldBeActive);
```

#### 技术实现
- **创建活动**：`StoreActivityServiceImpl.create()` 方法中添加状态计算逻辑
- **编辑活动**：`StoreActivityServiceImpl.updateActivity()` 方法中添加状态重新计算逻辑
- **状态判断**：基于当前时间与活动开始/结束时间的比较来确定状态

#### 业务价值
- **自动化管理**：无需手动调整活动状态，系统自动根据时间设置
- **准确性提升**：活动状态与实际时间范围完全一致
- **用户体验**：编辑活动时间后立即看到正确的状态
- **操作简化**：减少管理员的手动操作步骤

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/StoreActivityServiceImpl.java` - 状态自动计算逻辑

## 🆕 最新更新 (2025-07-27)

### 订单已取消状态查询条件完善
系统完善了订单状态查询功能中"已取消"状态的查询条件，确保已取消订单能够正确查询和显示：

#### 问题修复
- **完善查询条件**：修复了 `getStatusWhereNew` 方法中 "Closed" 状态的查询条件
- **状态码映射**：正确映射已取消状态码（OrderStatusEnum.CLOSED.getCode() = 5）
- **查询逻辑优化**：已取消订单查询条件为 `status = 5 AND is_del = 0`

#### 技术实现
- **核心方法**：`StoreOrderServiceImpl.getStatusWhereNew()` - 订单状态查询条件构建
- **状态枚举**：使用 `OrderStatusEnum.CLOSED` 确保状态码的一致性
- **查询条件**：不限制支付状态，因为已取消订单可能是未支付或已支付状态

#### 业务价值
- **数据准确性**：确保已取消订单能够正确查询和统计
- **管理便利性**：管理员可以正确筛选和查看已取消的订单
- **系统完整性**：补全了订单状态查询功能的完整性

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/StoreOrderServiceImpl.java` - 核心修复文件
- `model/src/main/java/com/ylpz/model/order/OrderStatusEnum.java` - 订单状态枚举定义

### 佣金解冻账单记录功能完善
系统完善了佣金解冻功能，现在佣金解冻时会自动创建UserBill账单记录，确保账单记录的完整性：

#### 功能特性
- **完整账单记录**：佣金解冻时自动创建UserBill记录，确保所有资金变动都有完整的账单记录
- **事务一致性**：佣金解冻和账单记录创建在同一事务中执行，保证数据一致性
- **详细记录信息**：账单记录包含解冻金额、订单号等详细信息
- **异常处理保护**：账单记录创建失败时会回滚整个解冻操作，确保数据完整性

#### 账单记录规范
- **账单类型**：UserBillEnum.BROKERAGE（"brokerage", "佣金返现"）
- **账单分类**：Constants.USER_BILL_CATEGORY_MONEY（"now_money"）
- **收入类型**：pm=1（收入）
- **备注格式**：佣金解冻到账{金额}元，订单号：{订单号}
- **状态标识**：status=1（有效）

#### 技术实现
- **核心方法**：`UserCommissionRecordServiceImpl.brokerageThaw()` - 在佣金解冻时集成账单记录创建
- **事务保证**：使用TransactionTemplate确保佣金解冻和账单记录在同一事务中
- **错误处理**：完善的异常处理机制，确保单条记录失败不影响其他记录处理
- **日志记录**：详细的操作日志，便于问题排查和数据追踪

#### 业务价值
- **数据完整性**：确保所有资金变动都有完整的账单记录，便于用户查询和审计
- **用户体验**：用户可以在账单明细中查看佣金解冻记录，提升透明度
- **审计追踪**：提供完整的资金流水记录，便于财务审计和问题排查
- **系统一致性**：与奖励金发放等其他功能保持一致的账单记录规范

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/UserCommissionRecordServiceImpl.java` - 核心实现
- `sql/20250727_commission_thaw_user_bill.sql` - 功能实现记录和验证脚本

## 🆕 最新更新 (2025-07-24)

### 用户等级更新时自动调整经验值功能
系统新增了用户等级更新时自动调整经验值的功能，确保用户经验值与等级保持一致：

#### 功能特性
- **自动经验值调整**：手动修改用户等级时，系统自动将经验值调整到目标等级的最低门槛
- **智能判断机制**：只有当用户当前经验值低于目标等级门槛时才进行调整
- **完整记录追踪**：所有经验值调整都记录在 `user_experience_record` 表中，便于审计
- **异常处理保护**：经验值调整失败不影响等级更新的正常进行
- **详细操作日志**：提供完整的调整过程日志，包含调整前后的经验值变化

#### 业务规则
- **普通会员(grade=1)**：经验值门槛通常为 0
- **VIP会员(grade=2)**：经验值门槛通常为 500
- **SVIP会员(grade=3)**：经验值门槛通常为 2000
- **门槛值获取**：具体门槛值以 `system_user_level` 表的 `experience` 字段为准
- **调整条件**：仅当用户当前经验值 < 目标等级门槛时进行调整

#### 技术实现
- **核心方法**：`UserServiceImpl.updateUserLevel()` - 在等级更新时集成经验值调整
- **调整方法**：`updateUserExperienceToLevelThreshold()` - 处理经验值调整逻辑
- **记录类型**：使用 `link_type="system"` 和 `title="管理员调整等级"` 标识
- **事务保证**：经验值调整与等级更新在同一事务中执行

#### 业务价值
- **数据一致性**：确保用户经验值与等级始终保持匹配关系
- **用户体验**：避免用户看到等级与经验值不匹配的困惑情况
- **管理便利**：简化管理员操作，无需手动调整经验值
- **审计完整**：提供完整的操作记录，便于问题排查和数据追踪

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/UserServiceImpl.java` - 核心实现
- `sql/20250724_user_level_update_experience_adjustment.sql` - 功能实现记录

### 经验值计算方法修复
系统修复了订单和充值经验值计算方法，现在根据系统参数设置表获取配置，而不是使用硬编码规则：

#### 修复内容
- **订单经验值计算**：`OrderExperienceServiceImpl.calculateOrderExperience()` 方法现在从 `system_param_setting` 表获取 `self_purchase` 配置
- **充值经验值计算**：`UserServiceImpl.calculateRechargeExperienceForAdmin()` 方法现在从 `system_param_setting` 表获取 `member_recharge` 配置
- **配置化管理**：支持通过系统参数配置灵活调整经验值获取规则

#### 配置格式
经验值配置使用JSON格式存储：
```json
{
  "number": 1,    // 每消费/充值的金额单位
  "unit": "元",   // 单位描述
  "ratio": 1      // 获得的经验值比例
}
```

#### 计算公式
- **经验值** = (金额 / number) × ratio
- 示例：配置 `{"number":1,"ratio":1}` 表示每1元获得1经验值
- 示例：配置 `{"number":2,"ratio":1}` 表示每2元获得1经验值

#### 业务价值
- **灵活配置**：运营人员可以通过后台配置调整经验值获取规则
- **统一管理**：避免硬编码，便于维护和调整
- **激励策略**：支持根据业务需要制定不同的激励策略

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/OrderExperienceServiceImpl.java` - 订单经验值计算修复
- `core/src/main/java/com/ylpz/core/service/impl/UserServiceImpl.java` - 充值经验值计算修复
- `sql/20250724_fix_experience_calculation_methods.sql` - 修复记录和验证脚本

### SVIP审批用户信息自动更新功能
系统新增了SVIP审批通过时自动更新用户信息的功能，实现申请资料与用户档案的同步：

#### 功能特性
- **自动信息同步**：SVIP审批通过时，自动将申请表中的姓名、身份证、手机号更新到用户表
- **智能字段映射**：apply_info中的realName、idcard、phone字段自动映射到用户表的real_name、card_id、phone字段
- **安全解析机制**：采用JSON安全解析，解析失败时不影响正常的SVIP升级流程
- **选择性更新**：只有当申请信息中存在且不为空的字段才会进行更新
- **完整日志记录**：提供详细的操作日志，便于追踪和问题排查

#### 业务规则
- **触发时机**：仅在SVIP申请审批通过时触发用户信息更新
- **字段映射关系**：
  - `apply_info.realName` → `user.real_name`（真实姓名）
  - `apply_info.idcard` → `user.card_id`（身份证号）
  - `apply_info.phone` → `user.phone`（手机号）
- **容错处理**：申请信息为空或解析失败时，仍正常完成SVIP升级，仅记录警告日志

#### 技术实现
- **核心文件**：`SvipApplyServiceImpl.auditApply()` - 在审批通过逻辑中增加用户信息更新
- **JSON解析**：使用Hutool的JSONUtil进行安全的JSON解析
- **数据库更新**：通过MyBatis-Plus的LambdaUpdateWrapper进行批量字段更新
- **异常处理**：完善的try-catch机制确保业务流程不被中断

#### 业务价值
- **数据一致性**：确保用户档案信息与SVIP申请资料保持一致
- **管理便利性**：减少手动维护用户信息的工作量
- **合规要求**：满足实名制管理的业务需求
- **用户体验**：用户无需重复填写已提交的个人信息

#### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/SvipApplyServiceImpl.java` - 核心实现
- `sql/20250724_svip_approval_user_info_update.sql` - 功能实现记录

## 🆕 最新更新 (2025-07-23)

### 组合商品定时状态调整功能
系统新增了组合商品定时状态调整功能，实现根据售卖时间自动启用/禁用组合商品：

#### 功能特性
- **定时执行**：每个整点执行一次（每小时的0分钟）
- **智能状态调整**：根据售卖开始时间(startTime)和售卖结束时间(endTime)自动调整状态(status)
- **精确时间控制**：当前时间在售卖时间范围内自动启用，超出范围自动禁用
- **安全筛选**：只处理设置了完整售卖时间的组合商品
- **详细日志**：提供完整的执行日志和统计信息

#### 业务规则
- **启用条件**：当前时间 >= startTime 且 <= endTime → 设置 status = 1（启用）
- **禁用条件**：当前时间 < startTime 或 > endTime → 设置 status = 0（禁用）
- **处理范围**：仅处理同时设置了startTime和endTime的组合商品
- **状态优化**：只有状态需要改变时才执行更新操作

#### 技术实现
- **定时任务**：`ProductCombinationStatusTask` - 每个整点自动执行
- **业务服务**：`ProductCombinationService.autoAdjustCombinationStatus()` - 处理状态调整逻辑
- **执行频率**：使用cron表达式 `0 0 * * * ?` 每小时执行一次
- **日志记录**：详细记录每次调整的组合商品信息和执行统计

#### 执行统计
每次任务执行后会输出详细统计信息：
- 总处理数量：检查的组合商品总数
- 启用数量：从禁用状态改为启用状态的数量
- 禁用数量：从启用状态改为禁用状态的数量
- 无变化数量：状态无需调整的数量

#### 业务价值
- **自动化管理**：无需人工干预，系统自动根据时间调整商品状态
- **精确控制**：确保组合商品严格按照设定的时间范围进行销售
- **运营效率**：减少运营人员的手动操作，降低人为错误
- **用户体验**：确保用户看到的组合商品都是在有效销售期内的

#### 相关文件
- `admin/src/main/java/com/ylpz/admin/task/product/ProductCombinationStatusTask.java` - 定时任务类
- `core/src/main/java/com/ylpz/core/service/ProductCombinationService.java` - 服务接口（新增方法）
- `core/src/main/java/com/ylpz/core/service/impl/ProductCombinationServiceImpl.java` - 服务实现（新增逻辑）

## 🆕 最新更新 (2025-07-22)

### 商品允许购买用户限制功能优化
系统对商品的用户购买限制功能进行了重大优化，将原有的"特定用户ID列表"改为"会员等级列表"：

#### 功能特性
- **会员等级限制**：商品可以限制只允许特定会员等级的用户购买
- **简化管理**：不再需要维护单独的用户关联表，直接在商品表中存储允许的等级
- **灵活配置**：支持多个等级组合，如只允许VIP和SVIP购买
- **向后兼容**：保留原有的限制开关，确保现有功能不受影响

#### 会员等级映射
- **1**: 普通会员
- **2**: VIP会员
- **3**: SVIP会员

#### 数据库变更
- 在 `store_product` 表中新增 `allowed_levels` 字段
- 支持逗号分隔的等级列表，如："1,2,3"

#### 前端界面优化
- 将"只允许特定用户购买"改为"只允许特定会员等级购买"
- 用户选择下拉框改为会员等级选择下拉框
- 提供直观的等级选择界面

#### 技术实现
- **数据库脚本**：`sql/20250722_modify_product_allowed_levels.sql`
- **模型修改**：`StoreProduct`、`StoreProductAddRequest`、`StoreProductResponse`
- **业务逻辑**：`StoreProductServiceImpl` 中的保存、查询、权限验证逻辑
- **前端组件**：`html/src/views/store/creatStore/index.vue`

#### 业务价值
- **管理简化**：基于会员等级的限制更加直观和易于管理
- **性能提升**：减少数据库表关联，提升查询性能
- **扩展性强**：未来新增会员等级时无需修改现有商品配置
- **用户体验**：管理员可以更方便地设置商品的购买权限

### 首页售后数量统计修复
修复了首页数据统计接口中"待处理售后数量"的获取逻辑：
- **问题修复**：解决了 `setPendingAfterSaleNum()` 方法调用时没有传入参数的问题
- **数据一致性**：现在从售后表(`store_order_aftersale`)获取数据，与售后维权页面保持一致
- **查询优化**：使用 `AfterSaleStatusEnum.PENDING_REVIEW` 状态查询待审核的售后申请
- **接口路径**：`/api/admin/statistics/home/<USER>

### 库存批量操作功能
系统新增了库存批量操作功能，支持对多个商品同时进行库存操作，大幅提升库存管理效率：

### 功能特性
- **批量增加库存**：选择多个商品，统一增加指定数量的库存
- **批量减少库存**：选择多个商品，统一减少指定数量的库存
- **统一调整库存**：将选中商品的库存统一调整为指定数值
- **操作安全性**：减少库存时使用乐观锁防止库存变为负数
- **事务保证**：批量操作使用事务确保数据一致性
- **详细反馈**：提供操作成功和失败的详细信息

### 接口信息
- **接口路径**：`/api/admin/store/product/batchOperationStock`
- **请求方法**：POST
- **权限要求**：`admin:product:stock:batch`

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productIds | List<Integer> | 是 | 商品ID列表，最多支持100个商品 |
| operationType | String | 是 | 操作类型：add-增加，sub-减少，set-设置为指定值 |
| quantity | Integer | 是 | 操作数量，必须大于0 |
| remark | String | 否 | 操作备注 |

### 操作类型说明
- **add**：在现有库存基础上增加指定数量
- **sub**：在现有库存基础上减少指定数量（不会低于0）
- **set**：将库存设置为指定数值

### 使用示例
```bash
# 批量增加库存
POST /api/admin/store/product/batchOperationStock
{
  "productIds": [1, 2, 3],
  "operationType": "add",
  "quantity": 50,
  "remark": "补货入库"
}

# 批量减少库存
POST /api/admin/store/product/batchOperationStock
{
  "productIds": [1, 2, 3],
  "operationType": "sub",
  "quantity": 10,
  "remark": "库存调整"
}

# 统一设置库存
POST /api/admin/store/product/batchOperationStock
{
  "productIds": [1, 2, 3],
  "operationType": "set",
  "quantity": 100,
  "remark": "库存标准化"
}
```

### 返回示例
```json
{
  "code": 200,
  "message": "批量库存操作完成",
  "data": {
    "totalCount": 3,
    "successCount": 3,
    "failCount": 0,
    "successIds": [1, 2, 3],
    "failIds": [],
    "operationType": "add",
    "quantity": 50,
    "remark": "补货入库"
  }
}
```

### 技术实现
- **请求类**：`BatchStockOperationRequest` - 批量库存操作请求参数
- **控制器**：`StoreProductController.batchOperationStock()` - 批量库存操作接口
- **服务层**：`StoreProductService.batchOperationStock()` - 批量库存操作业务逻辑
- **响应类**：`BatchStockOperationResponse` - 批量操作结果响应

### 业务价值
- **效率提升**：一次操作可处理多个商品，大幅提升库存管理效率
- **操作便利**：支持三种操作类型，满足不同的库存管理场景
- **数据安全**：事务保证和乐观锁确保操作安全性
- **操作追踪**：详细的操作记录便于库存变动追踪

### 相关文件
- `core/src/main/java/com/ylpz/core/common/request/BatchStockOperationRequest.java` - 批量库存操作请求类
- `core/src/main/java/com/ylpz/core/common/response/BatchStockOperationResponse.java` - 批量库存操作响应类
- `admin/src/main/java/com/ylpz/admin/controller/StoreProductController.java` - 商品控制器（新增批量接口）
- `core/src/main/java/com/ylpz/core/service/StoreProductService.java` - 商品服务接口（新增批量方法）
- `core/src/main/java/com/ylpz/core/service/impl/StoreProductServiceImpl.java` - 商品服务实现（新增批量逻辑）

## 🆕 商品优惠券查询功能 (2025-07-21)

系统新增了根据商品ID获取商品优惠券的接口，提供商品相关的优惠券信息查询功能：

### 功能特性
- **商品信息展示**：显示商品名称、图片等基本信息
- **优惠券查询**：根据商品ID直接查询关联的优惠券
- **优惠券信息**：显示优惠券详情，包括优惠券名称、类型、优惠内容、状态
- **状态智能判断**：自动判断优惠券状态（未开始、进行中、已结束）
- **优惠内容描述**：智能生成优惠券的使用说明和优惠内容

### 接口信息
- **接口路径**：`/api/admin/store/product/coupons/{productId}`
- **请求方法**：GET
- **权限要求**：`admin:product:coupon:list`（可选）

### 返回字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| productId | Integer | 商品ID |
| productName | String | 商品名称 |
| productImage | String | 商品图片 |
| couponList | List | 优惠券列表 |

#### 优惠券信息字段
| 字段名 | 类型 | 说明 |
|--------|------|------|
| activityName | String | 优惠券活动名称（优惠券名称） |
| activityType | String | 活动类型（比如满减券） |
| discountContent | String | 优惠内容（比如：满200元减30元） |
| status | String | 状态（进行中，已结束、未开始） |

### 使用示例
```bash
# 查询商品ID为123的优惠券信息
GET /api/admin/store/product/coupons/123
```

### 返回示例
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "productId": 123,
    "productName": "天仙精氨酸牛磺酸粉",
    "productImage": "http://example.com/product.jpg",
    "couponList": [
      {
        "activityName": "夏季满减券",
        "activityType": "满减券",
        "discountContent": "满200元减30元",
        "status": "进行中"
      }
    ]
  }
}
```

### 技术实现
- **服务层**：`StoreActivityService.getProductCouponsByProductId()` - 处理商品优惠券查询逻辑
- **控制器**：`StoreProductController.getProductCoupons()` - 提供HTTP接口
- **响应类**：`ProductMarketingActivityResponse` - 商品优惠券响应类
- **查询逻辑**：根据`store_coupon`表的`use_type`和`primary_key`字段判断优惠券适用性

### 优惠券适用性规则
- **use_type = 1**：全部商品可用
- **use_type = 2**：指定商品可用（primary_key包含商品ID）
- **use_type = 3**：指定商品不可用（primary_key不包含商品ID）

### 业务价值
- **优惠信息透明**：管理员可以快速查看商品关联的优惠券
- **优惠券管理便利**：便于了解商品的优惠券配置情况
- **决策支持**：为商品定价和促销策略提供数据支持
- **用户体验**：帮助客服人员快速回答用户关于商品优惠的咨询

### 相关文件
- `core/src/main/java/com/ylpz/core/common/response/ProductMarketingActivityResponse.java` - 商品优惠券响应类
- `core/src/main/java/com/ylpz/core/service/StoreActivityService.java` - 服务接口
- `core/src/main/java/com/ylpz/core/service/impl/StoreActivityServiceImpl.java` - 服务实现
- `admin/src/main/java/com/ylpz/admin/controller/StoreProductController.java` - 商品控制器（新增接口）

## 🆕 库存操作Controller接口功能 (2025-07-21)

系统新增了库存操作的Controller接口，为 `operationStock` 方法提供了完整的HTTP API支持：

### 功能特性
- **普通商品库存操作**：支持普通商品的库存增加和减少操作
- **秒杀商品库存操作**：支持秒杀商品的库存增加和减少操作
- **双接口设计**：提供请求参数和请求体两种调用方式，满足不同场景需求
- **完整参数校验**：使用JSR-303注解进行严格的参数验证
- **统一请求类**：创建 `StockOperationRequest` 统一库存操作参数
- **详细错误处理**：提供友好的错误信息和完整的异常处理
- **操作备注支持**：支持为库存操作添加备注信息，便于追踪

### 新增接口
1. **普通商品库存操作**：
   - `POST /api/admin/store/product/operationStock` - 使用请求参数
   - `POST /api/admin/store/product/operationStockByBody` - 使用请求体（推荐）

2. **秒杀商品库存操作**：
   - `POST /api/admin/store/seckill/operationStock` - 使用请求参数
   - `POST /api/admin/store/seckill/operationStockByBody` - 使用请求体（推荐）

### 技术实现
- **核心请求类**：`StockOperationRequest` - 统一的库存操作参数类
- **控制器增强**：在 `StoreProductController` 和 `StoreSeckillController` 中新增库存操作接口
- **参数验证**：使用 `@Validated` 和 JSR-303 注解进行自动参数校验
- **权限控制**：预留权限控制注解，支持细粒度权限管理
- **完整文档**：提供详细的Swagger API文档和使用说明

### 业务价值
- **管理便利性**：管理员可通过API直接操作商品库存，无需进入复杂的管理界面
- **系统集成**：支持与其他系统的库存同步和批量操作
- **操作追踪**：通过备注字段记录操作原因，便于库存变动追踪
- **安全可靠**：严格的参数校验和异常处理确保操作安全性
- **扩展性强**：统一的请求类设计便于后续功能扩展

### 相关文件
- `core/src/main/java/com/ylpz/core/common/request/StockOperationRequest.java` - 库存操作请求类
- `admin/src/main/java/com/ylpz/admin/controller/StoreProductController.java` - 普通商品库存操作接口
- `admin/src/main/java/com/ylpz/admin/controller/StoreSeckillController.java` - 秒杀商品库存操作接口
- `docs/20250721_operation_stock_controller_implementation.md` - 详细实现文档

## 🆕 订单和充值经验值功能 (2025-07-21)

系统新增了订单和充值经验值功能，通过系统配置控制是否增加经验值，并集成到返佣定时任务中处理：

### 功能特性
- **配置化控制**：通过系统参数配置控制订单和充值是否增加经验值
- **定时任务处理**：订单经验值通过返佣定时任务统一处理，确保订单完成后再增加
- **充值即时处理**：充值经验值在充值成功时立即处理
- **经验值计算**：支持灵活配置经验值计算规则（如1元=1经验值）
- **自动升级检查**：经验值增加后自动检查用户是否达到升级条件
- **完整记录**：所有经验值变动都会记录在用户经验记录表中

### 业务规则
通过系统参数配置表（system_param_setting）控制：
- **自购商品经验值**：配置代码`self_purchase`，控制订单消费是否增加经验值
- **会员充值经验值**：配置代码`member_recharge`，控制充值是否增加经验值
- **配置格式**：`{"number":1,"ratio":1}` - 每1元获得1经验值

### 技术实现
- **核心服务**：`OrderExperienceService` - 处理经验值增加逻辑
- **定时任务集成**：在`AutoCommissionServiceImpl`中集成订单经验值处理
- **充值处理**：在`RechargePayServiceImpl`中集成充值经验值处理
- **经验记录**：通过`user_experience_record`表记录所有经验值变动
- **用户升级**：集成现有的`UserLevelService.upLevel()`方法检查升级

### 处理时机
- **订单经验值**：通过返佣定时任务处理，确保订单完成且过了售后期限
- **充值经验值**：
  - 用户充值支付成功时立即处理（`RechargePayServiceImpl`）
  - 后台操作余额充值时立即处理（`UserServiceImpl.updateIntegralMoney`）

### 相关文件
- `core/src/main/java/com/ylpz/core/service/OrderExperienceService.java` - 经验值服务接口
- `core/src/main/java/com/ylpz/core/service/impl/OrderExperienceServiceImpl.java` - 经验值服务实现
- `core/src/main/java/com/ylpz/core/service/impl/AutoCommissionServiceImpl.java` - 返佣定时任务（集成订单经验值）
- `core/src/main/java/com/ylpz/core/service/impl/RechargePayServiceImpl.java` - 充值支付处理（集成充值经验值）
- `core/src/main/java/com/ylpz/core/service/impl/UserServiceImpl.java` - 后台操作余额（集成充值经验值）

### 配置说明
在系统参数配置中设置：
1. **模块名称**：成长值设置
2. **自购商品配置**：
   - 配置代码：`self_purchase`
   - 配置值：`{"number":1,"ratio":1}` - 每1元获得1经验值
   - 状态：启用/禁用
3. **会员充值配置**：
   - 配置代码：`member_recharge`
   - 配置值：`{"number":1,"ratio":1}` - 每1元获得1经验值
   - 状态：启用/禁用

### 业务价值
- **用户激励**：通过消费和充值获得经验值，激励用户活跃度
- **等级成长**：经验值积累促进用户等级提升，享受更多权益
- **灵活配置**：可根据业务需要调整经验值获取规则
- **数据完整性**：完整的经验值变动记录，便于追踪和分析
- **系统稳定性**：通过定时任务处理订单经验值，避免支付回调复杂度

## 🆕 用户余额明细功能增强 (2025-07-21)

系统对用户余额明细功能进行了增强，在分页结果中新增用户信息显示和查询功能：

### 功能特性
- **用户信息显示**：在余额明细分页中显示用户昵称、手机号、会员等级、头像等信息
- **昵称/手机号查询**：新增用户昵称/手机号查询条件，支持模糊查询
- **会员等级名称**：自动显示会员等级的中文名称（普通会员、VIP会员、SVIP会员）
- **向后兼容**：保持原有查询参数和功能不变，仅增强数据展示

### 技术实现
- **新增响应类**：`UserBillWithUserInfoResponse` - 包含账单信息和用户信息
- **扩展查询条件**：`FundsMonitorSearchRequest.userKeywords` - 支持昵称/手机号查询
- **联表查询**：优化数据库查询，一次性获取账单和用户信息
- **等级名称映射**：使用 `MemberLevelConstants` 自动转换等级数值为中文名称

### 接口变更
- **新增查询参数**：`userKeywords` - 用户昵称/手机号查询条件
- **增强返回数据**：分页列表中每个账单记录包含用户昵称、头像、手机号、等级信息
- **查询示例**：
  ```
  GET /api/admin/user/balance/detail?userKeywords=张三&page=1&limit=10
  GET /api/admin/user/balance/detail?userKeywords=138&category=now_money&page=1&limit=10
  ```

### 业务价值
- **提升管理效率**：管理员可直接在账单列表中查看用户信息，无需跳转
- **增强查询能力**：支持按用户昵称或手机号快速定位相关账单
- **改善用户体验**：提供更直观、更完整的账单管理界面
- **数据关联性**：加强账单与用户信息的关联展示

### 相关文件
- `core/src/main/java/com/ylpz/core/common/response/UserBillWithUserInfoResponse.java` - 新增响应类
- `core/src/main/java/com/ylpz/core/service/impl/UserBillServiceImpl.java` - 服务层增强
- `core/src/main/resources/mapper/user/UserBillMapper.xml` - 数据库查询增强
- `admin/src/main/java/com/ylpz/admin/controller/UserBalanceController.java` - 控制器修改
- `docs/20250721_user_balance_detail_enhancement.md` - 详细实现文档

## 🆕 售后退款状态返回修复 (2025-07-20)

系统修复了售后退款方法的状态返回问题，确保退款失败时不会错误记录成功状态：

### 问题描述
- 原有的`refund`方法是`void`类型，不返回退款是否成功的状态
- 导致退款失败时，`processAftersale`方法仍会按成功处理，造成数据不一致
- 退款失败的情况下，售后状态仍会更新为"已完成"，日志记录不准确

### 修复内容
1. **方法返回类型修改**：将退款相关方法的返回类型从`void`改为`Boolean`
   - `refund()` - 主退款方法
   - `processBalanceRefund()` - 余额退款处理
   - `processExternalRefund()` - 外部退款处理
2. **状态判断逻辑**：根据退款结果设置不同的售后状态和消息
3. **事务回滚机制**：退款失败时抛出异常，触发整个售后处理事务回滚
4. **错误处理优化**：提供明确的错误信息和日志记录

### 修复效果
- **数据一致性**：退款成功时状态为"已完成"，失败时状态为"已拒绝"
- **日志准确性**：根据实际退款结果记录相应的日志信息
- **事务安全性**：退款失败时整个售后处理事务回滚，保证数据一致性
- **用户体验**：退款失败时用户能收到明确的错误提示

### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/StoreOrderAftersaleServiceImpl.java` - 核心修复
- `docs/20250720_refund_status_return_fix.md` - 详细修复文档

## 🆕 优惠券发送时间校验功能 (2025-07-20)

系统新增了优惠券发送时的固定日期时间校验功能，确保已过期的固定时间优惠券无法发送：

### 功能特性
- **固定时间校验**：发送优惠券时自动检查固定时间类型优惠券的使用结束时间
- **过期阻止**：如果优惠券使用结束时间已超过当前时间，系统会阻止发送并提示错误
- **智能判断**：仅对固定时间类型（useTimeType=1）的优惠券进行时间校验
- **友好提示**：提供清晰的错误信息"优惠券使用时间已过期，无法发送！"

### 业务规则
- **固定时间类型（useTimeType = 1）**：检查useEndTime是否小于当前时间，如果是则报错
- **领取后天数类型（useTimeType = 2）**：不受影响，正常处理
- **领取后增加天数类型（useTimeType = 3）**：不受影响，正常处理
- **结束时间为空**：不进行校验，允许发送

### 技术实现
- **核心方法**：`StoreCouponUserServiceImpl.processUseTime()` - 增加固定时间过期检查逻辑
- **异常处理**：使用 `CrmebException` 提供友好的错误信息
- **时间比较**：使用 `Date.before()` 方法进行精确的时间比较
- **测试覆盖**：提供完整的单元测试用例验证功能正确性

### 影响范围
- **发送优惠券接口**：`/api/admin/marketing/coupon/user/receive` - 受此校验影响
- **自动发券任务**：会员生日券、每月固定日期券等自动发送功能
- **手动发券操作**：管理员手动给用户发送优惠券的操作

### 业务价值
- **数据准确性**：防止发送已过期的固定时间优惠券，避免用户困惑
- **用户体验**：确保用户收到的优惠券都是可用的，提升用户满意度
- **运营规范**：强制执行优惠券时效性检查，规范运营操作
- **系统稳定性**：减少因过期优惠券导致的客服咨询和用户投诉

### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/StoreCouponUserServiceImpl.java` - 核心实现
- `core/src/test/java/com/ylpz/core/service/impl/StoreCouponUserServiceImplTest.java` - 测试用例
- `admin/src/main/java/com/ylpz/admin/controller/StoreCouponUserController.java` - 发送优惠券接口

## 🆕 售后状态映射修复 (2025-07-20)

系统修复了售后状态映射不一致的问题：

### 问题描述
- `StoreOrderAftersaleMapper.xml` 文件中的售后状态映射与 `AfterSaleStatusEnum` 枚举类定义不一致
- 导致前端显示的售后状态描述与后端枚举定义不匹配

### 修复内容
1. **统一状态映射**：将XML中的CASE WHEN映射与枚举类定义完全对齐
2. **新增状态0映射**：添加了"无"状态的映射
3. **修正错误映射**：
   - 状态5：从"商家验收通过"改为"待换货发货"
   - 状态6：从"待换货发货"改为"售后完成"
   - 状态7：从"换货完成"改为"已取消"
4. **删除多余状态**：移除了状态8-12的映射，因为枚举中没有定义

### 标准化状态定义
现在售后状态完全按照枚举类定义：
- 0: 无
- 1: 待审核
- 2: 已拒绝
- 3: 待买家寄回
- 4: 待商家收货
- 5: 待换货发货
- 6: 售后完成
- 7: 已取消

### 修复效果
- 前端售后状态显示与后端枚举定义完全一致
- 提升了系统数据的准确性和一致性
- 避免了状态显示混乱的问题

### 相关文件
- `core/src/main/resources/mapper/order/StoreOrderAftersaleMapper.xml` - 核心修复文件
- `model/src/main/java/com/ylpz/model/order/AfterSaleStatusEnum.java` - 售后状态枚举定义
- `docs/fixes/20250720_aftersale_status_mapping_fix.md` - 详细修复文档

## 🆕 微页面删除限制功能 (2025-07-20)

系统实现了微页面删除限制功能，确保已发布的微页面不能被删除：

### 功能特性
- **删除限制**：微页面已发布的不能删除，只有未发布才能删除
- **状态检查**：删除前自动检查微页面的发布状态（0-未发布，1-已发布）
- **安全保护**：防止误删除线上正在使用的微页面内容
- **错误提示**：当尝试删除已发布微页面时，提供明确的错误信息

### 业务规则
- **未发布状态（pageStatus = 0）**：可以正常删除
- **已发布状态（pageStatus = 1）**：不能删除，系统会抛出异常阻止操作
- **不存在的微页面**：返回"微页面不存在"错误

### 技术实现
- **后端核心方法**：`SystemMicroPageServiceImpl.delete()` - 增加发布状态检查逻辑
- **异常处理**：使用 `CrmebException` 提供友好的错误信息
- **数据验证**：先查询微页面信息，再进行状态检查和删除操作
- **前端优化**：根据发布状态动态显示删除按钮，已发布状态显示禁用按钮并提供提示

### 使用说明
- **API接口**：`POST /api/admin/micro-page/delete/{id}`
- **操作流程**：如需删除已发布的微页面，请先将状态改为未发布，然后再删除
- **前端建议**：根据微页面状态显示相应的操作按钮，已发布状态可禁用删除按钮

### 业务价值
- **数据安全**：防止误删除已发布的重要页面内容
- **业务连续性**：确保线上正在使用的微页面不会被意外删除
- **操作规范**：强制执行先下线再删除的操作流程
- **用户体验**：避免因误删除导致的页面访问异常

### 相关文件
- `admin/src/main/java/com/ylpz/admin/service/impl/SystemMicroPageServiceImpl.java` - 后端核心实现
- `html/src/views/shop/microPage/index.vue` - 前端页面实现
- `html/src/api/microPage.js` - 前端API接口
- `docs/20250720_micro_page_delete_restriction.md` - 详细实现文档
- `docs/20250720_micro_page_delete_test_cases.md` - 测试用例文档

## 🆕 SQL日志配置修复 (2025-07-20)

系统修复了admin模块SQL日志不输出的问题：

### 问题描述
- admin模块在运行时没有输出SQL日志，影响开发调试和问题排查

### 问题原因
- 在环境配置文件（application-myprod.yml、application-prod.yml、application-prodpath.yml）中，MyBatis-Plus的`log-impl`配置为空
- 环境配置会覆盖主配置文件中的正确配置，导致SQL日志功能被禁用

### 解决方案
1. **修复MyBatis-Plus配置**：在所有环境配置文件中添加正确的SQL日志配置
   ```yaml
   mybatis-plus:
     configuration:
       log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
   ```

2. **优化Logback配置**：在logback-spring.xml中为Mapper包配置DEBUG级别日志
   ```xml
   <logger name="com.ylpz.core.dao" level="DEBUG" additivity="false">
       <appender-ref ref="STDOUT"/>
       <appender-ref ref="DEBUG_FILE"/>
   </logger>
   ```

3. **完善配置内容**：补充缺失的mapper-locations、typeAliasesPackage等配置

### 修复效果
- 重启应用后，所有数据库操作都会在控制台输出详细的SQL语句
- 便于开发调试和性能优化
- 提升问题排查效率

### 相关文件
- `admin/src/main/resources/application-myprod.yml` - 生产环境配置修复
- `admin/src/main/resources/application-prod.yml` - 正式环境配置修复
- `admin/src/main/resources/application-prodpath.yml` - 路径环境配置修复
- `admin/src/main/resources/logback-spring.xml` - 日志配置优化
- `sql/20250720_sql_log_configuration_fix.sql` - 修复记录文档

## 🆕 优惠券使用统计功能完善 (2025-07-20)

系统完善了优惠券管理功能，为 `/admin/marketing/coupon/list` 接口添加了已使用数量和支付金额统计：

### 功能特性
- **已使用数量统计**：显示每个优惠券被使用的次数，帮助运营人员了解优惠券的受欢迎程度
- **支付金额统计**：显示使用该优惠券的订单总支付金额，用于评估优惠券的投资回报率
- **性能优化**：采用批量查询策略，避免N+1查询问题，提升大数据量下的查询性能
- **数据准确性**：只统计已支付且未删除的订单，确保统计数据的准确性

### 技术实现
- **核心方法**：`StoreCouponServiceImpl.getListWithUseInfo()` - 获取带使用统计的优惠券列表
- **统计方法**：`getCouponUsageStats()` - 批量计算优惠券使用统计信息
- **数据关联**：通过 `store_coupon_user.oid` 字段关联到 `store_order` 表获取支付金额
- **内部类**：`CouponUsageStats` - 封装统计数据，提高代码可读性

### 返回字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| `usedCount` | Integer | 已使用数量 - 该优惠券被使用的次数 |
| `payAmount` | BigDecimal | 支付金额 - 使用该优惠券的订单总支付金额 |

### 业务价值
- **运营分析**：帮助运营人员了解优惠券的使用效果和用户接受度
- **ROI计算**：通过支付金额统计评估优惠券活动的投资回报率
- **决策支持**：为优惠券策略调整和未来活动规划提供数据支持
- **用户体验**：为管理员提供更完整、更直观的优惠券管理信息

### 相关文件
- `core/src/main/java/com/ylpz/core/service/impl/StoreCouponServiceImpl.java` - 核心实现

## 🆕 用户优惠券查询功能 (2025-07-19)

系统新增了用户优惠券查询功能，管理员可以通过用户ID快速查询指定用户拥有的所有优惠券：

### 功能特性
- **用户优惠券查询**：通过用户ID查询用户拥有的所有优惠券
- **状态筛选**：支持按优惠券状态筛选（可用、已用、已过期），对应前端三个页签
- **详细信息展示**：显示优惠券名称、面值、类型、状态、有效期等完整信息
- **状态中文描述**：优惠券状态、类型、门槛类型等都有对应的中文描述
- **过期提醒**：自动计算剩余有效天数，标识即将过期的优惠券（7天内）
- **分页支持**：支持分页查询，方便处理大量优惠券数据

### 接口信息
- **接口路径**：`/api/admin/marketing/coupon/user/listByUid`
- **请求方法**：GET
- **权限要求**：`admin:coupon:user:list`
- **数据来源**：`store_coupon_user`表

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| uid | Integer | 是 | 用户ID |
| status | Integer | 否 | 优惠券状态：0-可用(未使用)，1-已用(已使用)，2-已过期(已失效)，不传则查询全部 |
| page | Integer | 否 | 页码，默认1 |
| limit | Integer | 否 | 每页数量，默认10 |

### 返回字段说明
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | Integer | 优惠券记录ID |
| couponId | Integer | 优惠券ID |
| uid | Integer | 用户ID |
| name | String | 优惠券名称 |
| money | BigDecimal | 优惠券面值 |
| couponType | Integer | 门槛类型：1-有门槛，2-无门槛 |
| couponTypeDesc | String | 门槛类型描述 |
| useType | Integer | 使用类型：1-全部商品，2-指定商品可用，3-指定商品不可用 |
| useTypeDesc | String | 使用类型描述 |
| minPrice | BigDecimal | 最低消费金额 |
| useStartTime | Date | 使用开始时间 |
| useEndTime | Date | 使用结束时间 |
| type | Integer | 优惠券类型：1-满减券，2-新人专享券，3-会员专享券 |
| typeDesc | String | 优惠券类型描述 |
| status | Integer | 状态：0-未使用，1-已使用，2-已失效 |
| statusDesc | String | 状态描述 |
| useDescription | String | 使用说明 |
| isExpiringSoon | Boolean | 是否即将过期（7天内） |
| remainingDays | Integer | 剩余有效天数 |
| createTime | Date | 创建时间 |
| updateTime | Date | 更新时间 |

### 使用示例
```bash
# 查询用户ID为123的所有优惠券
GET /api/admin/marketing/coupon/user/listByUid?uid=123&page=1&limit=10

# 查询用户ID为123的可用优惠券（对应"可用"页签）
GET /api/admin/marketing/coupon/user/listByUid?uid=123&status=0&page=1&limit=10

# 查询用户ID为123的已使用优惠券（对应"已用"页签）
GET /api/admin/marketing/coupon/user/listByUid?uid=123&status=1&page=1&limit=10

# 查询用户ID为123的已过期优惠券（对应"已过期"页签）
GET /api/admin/marketing/coupon/user/listByUid?uid=123&status=2&page=1&limit=10
```

### 技术实现
- **服务类**：`StoreCouponUserService.getUserCouponList()`
- **控制器**：`StoreCouponUserController.getListByUid()`
- **响应类**：`UserCouponResponse` - 专门的用户优惠券响应类
- **数据处理**：自动计算过期状态、剩余天数，提供中文状态描述

## 🆕 SVIP会员上级显示优化 (2025-07-19)

系统优化了SVIP会员在会员管理中的上级显示逻辑，提升用户体验和品牌一致性：

### 功能特性
- **统一上级显示**：SVIP会员的上级在所有会员管理界面中统一显示为"养乐铺子"
- **多接口覆盖**：涵盖会员详情、关系查询、变更记录等所有相关接口
- **品牌一致性**：强化"养乐铺子"品牌形象，提升用户认知度
- **用户体验优化**：简化SVIP会员的上级关系显示，避免复杂的层级关系

### 涉及接口
- `/adminapi/api/admin/user/detail` - 会员详情页面的上级显示
- `/api/admin/user/spread/record` - 关系查询列表的上级显示
- `/api/admin/user/spread/record/detail/{uid}` - 关系查询变更记录的上级显示
- 用户列表中的推广人显示

### 显示规则
当用户等级为SVIP（level = 3）时：
- 上级昵称：显示为"养乐铺子"
- 上级手机号：显示为空
- 上级等级：显示为"系统"
- 变更记录：历史记录中的上级信息也统一显示为"养乐铺子"

### 技术实现
- 通过`MemberLevelConstants.isSvip()`方法判断用户是否为SVIP
- 在相关服务类中添加条件判断逻辑
- 保持数据库原有关系不变，仅在显示层面进行优化

## 🆕 自动确认收货定时任务功能 (2025-07-19)

系统新增了自动确认收货定时任务功能，实现发货后超过配置天数的订单自动确认收货：

### 功能特性
- **自动确认收货**：发货后超过配置天数（默认7天）的订单自动从"待收货"状态变更为"已完成"状态
- **佣金冻结期处理**：确认收货时自动设置相关佣金记录的冻结期（默认8天），确保售后期限内佣金不会被误解冻
- **售后佣金失效**：当同意退货退款或仅退款时，自动将相关佣金记录状态修改为已失效，避免不当结算
- **定时任务执行**：每天凌晨3点自动执行，避免与其他定时任务冲突
- **手动触发功能**：管理员可通过后台手动触发自动确认收货任务
- **完整记录**：所有自动确认收货操作都会生成相应的订单状态变更记录
- **配置灵活**：通过系统参数配置表可灵活调整自动确认收货天数和售后期限

### 配置参数
系统通过`system_param_setting`表管理以下配置：

| 配置代码 | 配置名称 | 说明 | 默认值 |
|---------|---------|------|--------|
| `order_receive_time` | 发货后自动确认收货时间 | 发货后多少天自动确认收货 | `{"unit":"天","value":7}` |
| `order_after_sale_time` | 买家申请售后期限 | 确认收货后多少天内可申请售后 | `{"unit":"天","value":"8"}` |

### 技术实现
- **定时任务**：`AutoConfirmReceiptTask` - 每天凌晨3点自动执行
- **业务服务**：`AutoConfirmReceiptService` - 处理自动确认收货业务逻辑
- **管理控制器**：`AutoConfirmReceiptController` - 提供手动执行功能
- **数据库记录**：通过`store_order_status`表记录所有状态变更信息
- **佣金处理**：自动更新`user_commission_record`表中的`frozen_time`和`thaw_time`字段
- **售后处理**：在`StoreOrderAftersaleServiceImpl`中增加佣金失效处理逻辑

### 佣金解冻任务时间调整
- **原执行时间**：每天凌晨0点
- **新执行时间**：每天凌晨4点
- **调整原因**：确保在自动确认收货任务执行后再进行佣金解冻，符合业务逻辑

### 相关文件
- `sql/20250719_add_auto_confirm_receipt_task.sql` - 数据库配置脚本
- `admin/src/main/java/com/ylpz/admin/task/order/AutoConfirmReceiptTask.java` - 自动确认收货定时任务
- `core/src/main/java/com/ylpz/core/service/AutoConfirmReceiptService.java` - 自动确认收货服务接口
- `core/src/main/java/com/ylpz/core/service/impl/AutoConfirmReceiptServiceImpl.java` - 自动确认收货服务实现
- `admin/src/main/java/com/ylpz/admin/controller/AutoConfirmReceiptController.java` - 自动确认收货管理控制器
- `admin/src/main/java/com/ylpz/admin/task/brokerage/BrokerageFrozenTask.java` - 佣金解冻任务（时间调整）

### 业务价值
- **提升用户体验**：避免用户忘记确认收货导致的订单长期处于待收货状态
- **优化资金流转**：及时完成订单状态，加快资金和佣金的流转
- **减少人工干预**：自动化处理减少客服工作量
- **完善订单管理**：确保订单状态的及时更新和流程完整性
- **风险控制**：通过设置佣金冻结期，降低售后纠纷对佣金结算的影响
- **资金安全**：售后退款时自动失效相关佣金，避免重复结算和资金损失

## 🆕 销售数据统计优化 (2025-01-17)

系统已完成销售数据统计逻辑的重大优化，使用 `user_commission_record` 表替代订单表查询，显著提升查询性能和数据一致性。

### 优化背景
原系统在排行榜和销售数据统计时存在以下问题：
- **重复查询**：从订单表重新统计销售数据，而 `user_commission_record` 表已包含完整信息
- **性能问题**：需要 JOIN 订单表和用户表，查询效率低
- **维护复杂**：两套统计逻辑，容易出现数据不一致

### 优化方案
**统一使用 `user_commission_record` 表进行销售数据统计**，该表包含：
- `order_amount`: 订单金额/销售额
- `pay_time`: 订单支付时间
- `user_phone`: 用户手机号
- `user_level`: 用户等级
- `uid`: 用户ID
- `link_id`: 关联订单ID

### 优化内容
1. **排行榜查询优化** (`RankingLeaderboardDetailMapper.xml`)
   - 使用 `user_commission_record` 表统计销售额和订单数量
   - 避免复杂的 JOIN 查询，提升查询性能

2. **销售金额计算优化** (`StoreOrderServiceImpl.java`)
   - `calculateUserSalesAmount()` - 用户自购销售额统计
   - `calculateDownlineSalesAmount()` - 用户下线销售额统计
   - 统一数据源，确保统计结果一致性

3. **销售数据服务优化** (`SalesDataServiceImpl.java`)
   - `getSalesDataDetail()` - 会员销售明细查询优化
   - `calculateSalesData()` - 用户销售数据计算优化
   - 新增 `convertCommissionToSalesDataDetailResponse()` 转换方法
   - 新增 `setOrderProductInfoByOrderNo()` 辅助方法

### 技术优势
- **性能提升**：单表查询替代复杂 JOIN，查询速度显著提升
- **数据一致性**：统一数据源，避免重复统计逻辑
- **维护简化**：减少代码重复，降低维护成本
- **扩展性好**：基于已有索引，支持高效的时间范围和用户筛选

### 相关文件
- `sql/20250117_optimize_sales_data_statistics.sql` - 优化记录和验证SQL
- `sql/20250117_validate_sales_data_optimization.sql` - 验证脚本
- `core/src/main/resources/mapper/user/RankingLeaderboardDetailMapper.xml` - 排行榜查询优化
- `core/src/main/java/com/ylpz/core/service/impl/StoreOrderServiceImpl.java` - 销售额计算优化
- `core/src/main/java/com/ylpz/core/service/impl/SalesDataServiceImpl.java` - 销售数据服务优化
- `admin/src/main/java/com/ylpz/admin/controller/SalesDataController.java` - 销售数据控制器（受益于服务层优化）

## 🆕 售后退款支付方式优化 (2025-01-17)

系统已完成售后退款功能的优化，现在支持根据不同的支付方式执行相应的退款逻辑：

### 功能特性
- **微信支付退款**：继续使用现有的外部退款接口，确保与微信支付系统的正常对接
- **余额支付退款**：直接退回到用户余额账户，无需调用外部接口，提高退款效率
- **智能判断**：系统自动根据订单的支付方式(`pay_type`)选择合适的退款处理逻辑
- **完整记录**：所有退款操作都会生成相应的用户账单记录，确保资金流水的完整性

### 支付方式支持
| 支付方式 | 代码标识 | 退款处理方式 |
|---------|---------|-------------|
| 微信支付 | `weixin` | 外部退款接口 |
| 余额支付 | `yue` | 直接退回用户余额 |
| 支付宝支付 | `alipay` | 外部退款接口 |
| 线下支付 | `offline` | 外部退款接口 |
| 其他支付 | `other` | 外部退款接口 |

### 技术实现
- **核心文件**：`StoreOrderAftersaleServiceImpl.java`
- **新增方法**：
  - `processBalanceRefund()` - 处理余额支付退款
  - `processExternalRefund()` - 处理外部支付退款
  - `updateAftersaleStatus()` - 统一更新售后状态
- **账单记录**：使用 `UserBillEnum.REFUND` 类型记录退款账单
- **数据库记录**：`sql/20250117_update_aftersale_refund_by_paytype.sql`

### 业务价值
- **提升用户体验**：余额支付退款即时到账，无需等待外部接口处理
- **降低系统复杂度**：减少不必要的外部接口调用
- **提高系统稳定性**：避免因外部接口异常影响余额退款
- **完善资金管理**：确保所有退款操作都有完整的审计记录

## 🆕 会员等级体系标准化 (2025-07-13)

系统已完成会员等级体系的标准化改造，统一使用以下等级数值：
- **0**: 无会员等级（默认状态）
- **1**: 普通会员
- **2**: VIP会员
- **3**: SVIP会员

**重要变更**：
- 用户表 `level` 字段现在直接存储等级数值（0,1,2,3），而不是 `system_user_level` 表的ID
- 新增 `MemberLevelConstants` 常量类，提供统一的等级管理和验证
- **消除魔法数字**：所有代码中的等级判断都已替换为使用常量类，不再使用硬编码数字
- 所有会员等级相关的业务逻辑已更新为使用统一的等级数值
- 数据库迁移文件：`sql/20250713_standardize_member_level_system.sql`

## 🆕 佣金返现配置格式标准化 (2025-07-18)

系统已完成佣金返现配置格式的标准化改造，统一使用数字标识符：

**配置格式变更**：
- **旧格式**: `{"1":{"purchase":10},"vip":{"purchase":8},"svip":{"purchase":6}}`
- **新格式**: `{"1":{"purchase":10},"2":{"purchase":8},"3":{"purchase":6}}`

**重要变更**：
- 佣金返现配置中的会员等级标识符已统一为数字格式（1=普通会员, 2=VIP会员, 3=SVIP会员）
- 更新了 `AutoCommissionServiceImpl` 中的配置解析逻辑
- 更新了前端页面中的配置显示逻辑
- 更新了相关文档和示例
- 数据库迁移文件：`sql/20250718_update_commission_ratio_config.sql`

**修改的文件清单**：
- `core/src/main/java/com/ylpz/core/common/constants/MemberLevelConstants.java` - 新增常量类
- `core/src/main/java/com/ylpz/core/service/impl/UserServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/SystemUserLevelServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/UserLevelServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/UserAutoUpgradeServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/RankingTaskServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/SvipManagementServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/SvipApplyServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/SalesDataServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/StoreOrderServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/AutoCommissionServiceImpl.java` - 替换魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/UserBonusRecordServiceImpl.java` - 修正等级映射错误和魔法数字
- `core/src/main/java/com/ylpz/core/service/impl/UserSpreadServiceImpl.java` - 替换默认值中的魔法数字
- `core/src/main/resources/mapper/user/RankingLeaderboardDetailMapper.xml` - 修正等级映射和字段名
- `core/src/main/resources/mapper/order/StoreOrderAftersaleMapper.xml` - 修正JOIN条件
- `html/src/utils/constants.js` - 新增前端会员等级常量
- `html/src/views/member/memberDetails/index.vue` - 替换前端魔法数字
- `sql/create_commission_and_bonus_tables.sql` - 修正注释中的等级映射

**全面检查完成**：
- ✅ 已消除所有Java代码中的魔法数字，全部替换为MemberLevelConstants常量
- ✅ 已修正所有Mapper XML文件中的字段名错误（member_level → level）
- ✅ 已修正所有数据库查询中的JOIN条件（使用grade而不是id）
- ✅ 已统一所有等级映射关系和显示名称
- ✅ 已修正等级映射逻辑错误（如UserBonusRecordServiceImpl中的错误映射）
- ✅ 已修正等级判断逻辑错误（如SvipApplyServiceImpl中的错误判断）
- ✅ 已为前端添加会员等级常量管理，消除前端魔法数字
- ✅ 已添加详细的注释说明，便于后续维护
- ✅ 已生成详细的修复报告文档：`docs/20250713_member_level_magic_number_fixes.md`

## 🆕 推广关系自动脱落功能 (2025-07-13)

系统新增了推广关系自动脱落功能，当用户等级升级时，会根据以下规则自动解除与上级的推广关系：

**脱落规则**：
1. **普通会员上级** → 下级升级为VIP或SVIP → 自动脱落绑定关系
2. **VIP会员上级** → 下级升级为SVIP → 自动脱落绑定关系
3. **SVIP会员上级** → 下级升级为SVIP → 自动脱落绑定关系

**功能特点**：
- 所有脱落变更都会记录在 `user_spread_record` 表中，变更类型为 `2-自动转移`
- 自动在用户等级升级时触发（包括经验值自动升级和管理员手动升级）
- 提供详细的脱落原因记录，便于追踪和审计
- 支持事务回滚，确保数据一致性

**新增文件**：
- `core/src/main/java/com/ylpz/core/service/UserSpreadService.java` - 新增脱落方法接口
- `core/src/main/java/com/ylpz/core/service/impl/UserSpreadServiceImpl.java` - 实现脱落逻辑
- `core/src/test/java/com/ylpz/core/service/UserSpreadAutoDetachmentTest.java` - 功能测试用例
- `sql/20250713_user_spread_auto_detachment.sql` - 数据库优化脚本

**修改的文件**：
- `core/src/main/java/com/ylpz/core/service/impl/UserAutoUpgradeServiceImpl.java` - 集成脱落逻辑
- `core/src/main/java/com/ylpz/core/service/impl/UserServiceImpl.java` - 集成脱落逻辑

## 项目概述

本项目是一个功能完整的电商商城系统，支持B2C电商业务模式，包含后台管理系统、小程序接口、分销体系、会员等级管理、营销活动、**自动返佣系统**等核心功能。系统采用微服务架构设计，具有良好的扩展性和维护性。

### 🆕 最新功能：返现明细功能增强 (2025-07-16)
- **返现金额显示**：在返现明细中显示每个订单对应的返现金额
- **买家信息展示**：显示订单买家的昵称和会员等级
- **数据关联查询**：通过订单号关联查询佣金记录表获取返现金额
- **用户信息关联**：通过用户ID关联查询用户表获取买家名称和等级
- **性能优化**：为相关查询字段添加数据库索引，提升查询效率

## 🆕 最新功能：会员详情功能完善
- **完整会员信息展示**：提供基本信息、资产信息、业绩概览、订单记录等全面数据
- **新增会员详情接口**：`/admin/user/detail` 接口，返回完整的会员详情数据
- **推广用户统计**：显示推广的普通用户和VIP用户数量
- **业绩数据统计**：累计返现、累计充值、累计提现等财务数据
- **订单记录统计**：累计消费金额、订单数量、最近下单时间等
- **前端页面优化**：美化会员详情页面，增加可视化数据展示

### 🆕 最新功能：自动返佣系统
- **SVIP用户自购返佣**：SVIP会员购买商品可获得自动返佣
- **下级用户购买返佣**：SVIP用户的下级购买商品时，上级SVIP用户可获得返佣
- **智能订单筛选**：自动识别符合返佣条件的订单（已完成、售后期限外等）
- **定时任务执行**：每天凌晨1点自动执行返佣计算和发放
- **灵活配置管理**：支持返佣比例、开关状态、售后期限等参数配置

## 🆕 最新功能：用户自动升级VIP系统和奖励金定时任务系统
### 🎯 核心功能模块

#### 1. 用户自动升级VIP系统 ✅
- **升级条件**: 普通用户经验值达到500时自动升级为VIP会员
- **升级逻辑**: 基于system_user_level表配置的experience阈值
- **升级触发**: 定时任务每天凌晨2点检查和处理
- **升级后处理**: 更新用户等级、记录升级日志、触发相关奖励逻辑
- **实现状态**: 已完全实现，包括服务接口、定时任务、管理控制器

#### 2. 奖励金定时任务系统(RankingTask) ✅
基于system_param_setting表配置，实现以下奖励发放逻辑：

**SVIP用户三项奖励机制：**
1. **推广普通会员升级VIP奖励**: 200元现金
2. **推广VIP会员升级SVIP奖励**: 500元现金
3. **销售榜单排名奖励**: 周榜、月榜、季度榜、年度榜前三名奖励

**VIP用户一项奖励机制：**
4. **推广新用户首单奖励**: 首单金额的10%

**特殊处理逻辑：**
- **奖励金发放**: 自动更新用户余额(user.now_money)并创建UserBill记录
- **最低销量门槛**: 只有达到配置门槛的用户才能进入排行榜
- **年度榜单**: 仅统计人员排名到ranking_leaderboard_detail和ranking_leaderboard表，不设置奖励金额，使用公共的榜单显示数量配置
- **榜单数量限制**: 根据配置的reward_count限制奖励人数（年度榜单使用榜单显示数量配置）
- **可提现金额管理**: 奖励金和佣金返现会同时更新用户的可提现金额(user.withdrawable_price)，提现审批后直接减少可提现金额

## 技术栈

### 后端技术
- **框架**: Spring Boot 2.2.6.RELEASE
- **数据库**: MySQL 8.0
- **ORM**: MyBatis-Plus 3.5.7
- **缓存**: Redis 6.0
- **连接池**: Druid 1.1.20
- **文档**: Swagger 2.9.2
- **工具库**: Hutool、Lombok、FastJSON
- **图片处理**: Thumbnailator 0.4.8
- **二维码**: ZXing 3.3.3
- **JWT**: JJWT 0.9.1

### 开发环境
- **JDK**: 1.8+
- **Maven**: 3.6+
- **IDE**: IntelliJ IDEA / Eclipse
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+

## 项目架构

### 模块结构
```
mall/
├── admin/          # 后台管理模块 - 提供管理员操作界面和API
├── core/           # 核心业务模块 - 包含所有业务逻辑和服务层
├── core/common/    # 公共请求和响应类、实体类、配置等
├── entity/         # 实体类模块 - 数据库实体映射
├── model/          # 数据模型模块 - 数据库对象
└── mini/           # 小程序接口模块 - 提供前端和小程序API
```

### 技术架构
- **表现层**: Spring MVC + RESTful API
- **业务层**: Spring Service + 事务管理
- **持久层**: MyBatis-Plus + MySQL
- **缓存层**: Redis + Spring Cache
- **安全层**: JWT Token + 权限控制
- **文件存储**: 本地存储 + 云存储(七牛云/阿里云OSS/腾讯云COS)

## 功能模块

### 商品管理
- 商品列表查询
- 商品新增/编辑/删除
- 商品上架/下架
  - 立即上架：商品立即上架开始售卖
  - 定时开售：设置商品在特定时间自动上架售卖
  - 暂不售卖，放入仓库：商品暂时不售卖，保存在仓库中
- 商品分类管理
- 商品规格管理
  - 支持设置商品规格库存
  - 支持设置商品规格价格
  - 支持设置商品规格图片
  - 支持设置商品规格数量（新增）
- 商品属性管理
- 商品搭配管理
  - 支持创建商品搭配组合
  - 支持为每个搭配中的商品设置数量
  - 支持搭配优惠价设置
  - 支持组合标签功能
  - 支持优惠叠加功能
  - 支持实际销量和库存管理
  - 支持上架状态管理和批量操作
  - 支持在商品列表中查询商品参与的组合活动（新增）

### 商品评价管理功能升级

系统对商品评价功能进行了升级，新增以下特性：

#### 1. 评价标签功能
- 支持为商品评价添加标签，多个标签用逗号分隔
- 标签可用于分类和筛选评价内容
- 支持在评价列表中按标签进行模糊搜索
- 标签字段最大长度为500个字符

#### 2. 数据表结构变更
在`store_product_reply`表中新增了标签字段：
```sql
ALTER TABLE `store_product_reply`
ADD COLUMN `tags` varchar(500) DEFAULT NULL COMMENT '评价标签，多个标签用逗号分隔' AFTER `sku`;

ALTER TABLE `store_product_reply`
ADD INDEX `idx_tags` (`tags`);
```

#### 3. API接口增强
- 评价添加接口支持标签字段
- 评价查询接口支持按标签筛选
- 评价列表返回结果包含标签信息

#### 4. 使用场景
- 商品评价分类：如"质量好"、"物流快"、"服务佳"等
- 评价内容标记：如"好评"、"中评"、"差评"等
- 特殊标记：如"精选评价"、"置顶评价"等
- 便于管理员快速筛选和管理评价内容

### 订单管理功能增强

系统对订单管理功能进行了增强，新增以下特性：

#### 1. 订单查询条件增强
- 新增订单状态查询条件，支持按以下状态筛选：
  - 待付款：订单已创建但未支付
  - 待发货：订单已支付，等待商家发货
  - 已发货：商家已发货，等待买家收货
  - 已完成：订单已完成
  - 已关闭：订单已取消
- 新增付款方式查询条件，支持按支付方式筛选订单
- 新增收货人关键词查询条件（receiverKeyword），支持按收货人姓名或手机号搜索订单
- 新增订单下单时间查询条件，支持按下单时间范围筛选订单
  - 支持设置开始时间（orderTimeStart）
  - 支持设置结束时间（orderTimeEnd）
  - 支持精确到具体时间，格式为：yyyy-MM-dd HH:mm:ss
- 新增支付时间范围查询条件，支持按支付时间区间查询，精确筛选已支付订单
- 新增支付方式查询条件，支持按支付方式筛选订单
- 新增收货人关键词查询条件（receiverKeyword），支持按收货人姓名或手机号搜索订单

#### 2. 查询界面优化
- 在订单查询界面增加订单状态下拉选择框
- 增加付款方式下拉选择框
- 增加收货人关键词输入框，支持模糊搜索姓名或手机号
- 增加订单下单时间选择器，支持选择开始和结束时间

#### 3. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/order/list | GET | 获取订单列表（支持新增的查询条件） |
| /api/admin/store/order/cancel | POST | 取消订单 |

#### 4. 订单取消功能
系统新增了订单取消功能，管理员可以取消未支付或待发货状态的订单：

- **接口路径**: `/api/admin/store/order/cancel`
- **请求方法**: POST
- **接口权限**: `admin:order:cancel`
- **请求参数**:
  - `orderNo`: 订单号（必填）
  - `reason`: 取消原因（可选）
- **业务逻辑**:
  - 仅允许取消未支付或待发货状态的订单
  - 取消后订单状态变更为"已取消"
  - 若订单已支付，系统会自动处理退款
  - 系统记录订单状态变更和取消原因

#### 5. 订单列表返回字段增强
系统对订单列表返回字段进行了增强，新增以下字段：
- 用户ID(uid)：订单所属用户的ID
- 支付时间(payTime)：订单支付的具体时间
- 支付状态(paid)：订单是否已支付
- 买家名称(buyerName)：下单用户的昵称
- 买家会员等级(buyerLevel)：下单用户的会员等级名称
- 收货人手机号(userPhone)：收货人的联系电话
- 收货地址(userAddress)：订单的收货地址信息

这些字段的增加使得订单列表展示更加完整，管理员可以更方便地查看订单相关信息。

### 订单打单发货功能

系统新增订单打单发货功能，提供了对待发货、已发货和已完成订单的专门管理界面：

#### 1. 功能描述
- 提供专门的订单打单发货操作界面
- 可以筛选和查看不同状态的订单（待发货、已发货、已完成）
- 支持打印发货单功能

#### 2. 主要特性
- 待发货订单列表：展示所有等待发货的订单
- 已发货订单列表：展示所有已发货但未完成的订单
- 已完成订单列表：展示所有已完成的订单
- 打印功能：选择订单并打印发货单
- 发货功能：订单录入物流信息并发货

#### 3. 支持的筛选条件
- 订单号：支持按订单号精确查询
- 时间范围：支持按创建时间区间查询
- 支付时间范围：支持按支付时间区间查询，精确筛选已支付订单
- 支付方式：支持按支付方式查询
- 收货人信息：支持按收货人姓名或手机号查询

#### 5. 使用场景
- 电商平台的日常订单处理和发货管理
- 大量订单需要批量处理的场景
- 订单物流状态的跟踪和管理

### 会员余额管理功能升级

系统对会员余额管理功能进行了升级，新增以下特性：

#### 1. 会员余额操作增强
- 新增操作类型选择功能，支持两种操作类型：
  - 会员充值：记录会员自主充值的余额变动
  - 后台调整：记录管理员后台操作的余额变动
- 新增备注功能，支持为每次余额操作添加自定义备注说明
- 备注信息将显示在用户账单记录中，方便追溯余额变动原因

#### 2. 操作界面优化
- 在余额操作界面增加操作类型单选框
- 增加备注输入框，支持输入详细的操作说明
- 操作记录中显示操作类型和备注信息

#### 3. 会员余额列表优化
- 优化会员余额列表接口，只返回必要信息
- 会员余额列表现在包含以下字段：
  - 用户信息（ID、昵称、头像、手机号、会员等级）
  - 现有金额
  - 充值金额
  - 返现&退款
  - 累计消费金额
  - 累计提现金额
- 提升列表加载速度和用户体验

#### 4. 会员余额详情优化
- 优化会员余额详情接口，提供更全面的资金数据
- 会员余额详情现在包含以下字段：
  - 账号余额：当前用户可用余额
  - 充值金额：用户充值的金额
  - 返现&退款金额：用户获得的返现和退款金额
  - 累计充值金额：用户历史充值总额
  - 累计佣金返现：用户历史获得的佣金返现总额
  - 累计实际金额：实际可用金额总计
  - 累计消费金额：用户历史消费总额
  - 累计提现：用户历史提现总额
- 提供更清晰的用户资金流水概览

UserBill的title类型标准化，现在系统使用枚举类型（UserBillEnum）统一管理以下几种类型：
- 消费（UserBillEnum.CONSUME，type="consume"，title="消费"）
- 退款（UserBillEnum.REFUND，type="refund"，title="退款"）
- 会员充值（UserBillEnum.RECHARGE，type="recharge"，title="会员充值"）
- 后台调整（UserBillEnum.ADMIN_ADJUST，type="admin_adjust"，title="后台调整"）
- 佣金返现（UserBillEnum.BROKERAGE，type="brokerage"，title="佣金返现"）
- 奖励金（UserBillEnum.BONUS，type="bonus"，title="奖励金"）
- 提现（UserBillEnum.EXTRACT，type="extract"，title="提现"）

#### 5. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/balance/update | POST | 会员余额操作（支持操作类型和备注） |
| /api/admin/user/balance/list | GET | 获取会员余额列表（优化返回字段） |
| /api/admin/user/balance/info/{id} | GET | 获取指定用户余额详情（优化返回字段） |

#### 6. 请求参数说明
- `uid`: 用户ID
- `integralType`: 积分类型（1=增加，2=减少）
- `integralValue`: 积分值
- `moneyType`: 余额类型（1=增加，2=减少）
- `moneyValue`: 余额值
- `operationType`: 操作类型（1=会员充值，2=后台调整）
- `remark`: 操作备注说明

### 操作界面优化
- 在余额操作界面增加操作类型单选框
- 增加备注输入框，支持输入详细的操作说明
- 操作记录中显示操作类型和备注信息

### 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/balance/update | POST | 会员余额操作（支持操作类型和备注） |

系统中的佣金配置通过会员参数配置(`system_member_param_config`)进行管理，主要包括以下配置项：

## 🆕 用户自动升级VIP系统

### 1. 功能概述

用户自动升级VIP系统是一个智能化的会员等级管理系统，基于用户经验值自动升级用户等级。系统通过定时任务检查所有用户的经验值，当达到升级条件时自动升级用户等级，并触发相关奖励逻辑。

### 2. 核心特性

#### 2.1 自动升级条件
- **升级条件**：用户经验值达到system_user_level表中配置的experience阈值
- **升级逻辑**：当普通会员(grade=1)经验值≥500时，自动升级为VIP会员(grade=2)
- **升级触发**：每天凌晨2点执行定时任务检查所有用户升级条件
- **升级后处理**：更新用户等级、记录升级日志、触发相关奖励逻辑

#### 2.2 技术实现
- **服务类**：`UserAutoUpgradeService` 和 `UserAutoUpgradeServiceImpl`
- **定时任务**：`UserAutoUpgradeTask` - 每天凌晨2点自动执行
- **管理控制器**：`UserAutoUpgradeController` 提供手动执行功能

### 3. 使用说明

#### 3.1 配置检查
1. 检查system_user_level表中的等级配置是否正确
2. 确保auto_upgrade字段设置为true的等级支持自动升级
3. 根据业务需要调整经验值阈值

#### 3.2 运行监控
- 定时任务每天凌晨2点自动执行
- 执行日志记录在系统日志中
- 每次升级操作都有详细的记录和追踪
- 可通过管理控制器手动执行升级检查

## 🆕 奖励金定时任务系统(RankingTask)

### 1. 功能概述

奖励金定时任务系统是一个全面的用户激励系统，基于现有system_param_setting表配置，为SVIP和VIP用户提供多种奖励机制，包括推广奖励、首单奖励和销售榜单奖励。

### 2. 奖励机制详解

#### 2.1 SVIP用户三项奖励机制

**1. 推广普通会员升级VIP奖励**
- **奖励条件**：推广的普通会员升级为VIP
- **奖励金额**：200元现金(config_code: upgrade_to_vip_bonus)
- **发放对象**：推广人(SVIP用户)
- **触发时机**：用户等级从1升级到2时自动触发

**2. 推广VIP会员升级SVIP奖励**
- **奖励条件**：推广的VIP会员通过SvipApplyController审核流程升级为SVIP
- **奖励金额**：500元现金(config_code: upgrade_to_svip_bonus)
- **发放对象**：推广人(SVIP用户)
- **触发时机**：用户等级从2升级到3时自动触发

**3. 销售榜单排名奖励**
- **奖励周期**：周榜、月榜、季度榜
- **销售统计**：包含自购订单和下线购买订单金额
- **奖励配置**：使用system_param_setting表中weekly_ranking_bonus、monthly_ranking_bonus、quarterly_ranking_bonus配置
- **自动发放**：根据ranking_auto_reward配置决定是否自动发放
- **奖励对象**：各榜单前三名(可配置reward_count)
- **注意事项**：仅统计成为SVIP之后的数据，根据时间筛选排除成为SVIP之前的订单数据

#### 2.2 VIP用户一项奖励机制

**4. 推广新用户首单奖励**
- **奖励条件**：推广的新用户完成首次订单购买
- **奖励比例**：首单金额的10%(config_code: first_order_bonus)
- **发放对象**：推广人(VIP用户)
- **触发时机**：新用户完成首次订单支付时自动触发

### 3. 技术实现

#### 3.1 核心组件
- **服务类**：`RankingTaskService` 和 `RankingTaskServiceImpl`
- **定时任务**：`RankingRewardTask`
  - 每周一凌晨3点执行周榜奖励发放
  - 每月1号凌晨4点执行月榜奖励发放
  - 每季度第一天凌晨5点执行季度榜奖励发放
  - 每天凌晨6点执行待处理奖励任务检查
- **管理控制器**：`UserAutoUpgradeController` 提供手动执行功能

#### 3.2 数据统计逻辑
- **销售金额计算**：包含用户自购订单和下线购买订单金额
- **时间筛选**：仅统计用户成为SVIP之后的订单数据
- **排行榜生成**：按销售金额降序排序，生成对应周期的排行榜
- **奖励发放**：根据配置的奖励规则自动发放奖励金

### 4. 配置管理

#### 4.1 系统参数配置
系统通过`system_param_setting`表管理以下配置：

| 配置代码 | 配置名称 | 说明 | 示例配置 |
|---------|---------|------|---------|
| `upgrade_to_vip_bonus` | 推广普通会员升级VIP奖励 | 控制推广VIP升级奖励功能 | `{"enabled": true, "amount": 200}` |
| `upgrade_to_svip_bonus` | 推广VIP会员升级SVIP奖励 | 控制推广SVIP升级奖励功能 | `{"enabled": true, "amount": 500}` |
| `first_order_bonus` | 推广新用户首单奖励 | 控制首单奖励功能 | `{"enabled": true, "ratio": 10}` |
| `weekly_ranking_bonus` | 周榜奖励配置 | 周榜奖励规则配置 | `{"enabled": true, "rewards": [...]}` |
| `monthly_ranking_bonus` | 月榜奖励配置 | 月榜奖励规则配置 | `{"enabled": true, "rewards": [...]}` |
| `quarterly_ranking_bonus` | 季度榜奖励配置 | 季度榜奖励规则配置 | `{"enabled": true, "rewards": [...]}` |

### 5. 安全机制

- **重复处理检查**：通过奖励记录表检查，防止重复发放奖励
- **用户等级验证**：严格验证用户等级，确保奖励发放给正确的用户
- **配置开关控制**：所有奖励功能都有配置开关，可灵活控制启用状态
- **事务保证**：奖励发放操作使用数据库事务确保数据一致性

## 🆕 自动返佣系统

### 1. 功能概述

自动返佣系统是一个智能化的佣金发放系统，专为SVIP用户及其下级用户设计。系统通过定时任务自动识别符合条件的订单，并按照配置的比例自动发放佣金，无需人工干预。

### 2. 核心特性

#### 2.1 智能订单筛选
系统自动筛选符合以下所有条件的订单：
- ✅ **订单确认收货后超过X天**（X天数可配置，默认8天）
- ✅ **用户等级为SVIP或SVIP的下级用户**
- ✅ **订单状态为"已完成"**
- ✅ **未完成返佣**：通过`is_commission_completed`字段快速过滤已处理订单
- ✅ **售后条件验证**：通过关联售后记录表，确保售后处理方式仅限于"换货"或"小额补偿"
- ✅ **多商品订单检查**：如果任意一个商品的售后类型不符合条件，则整个订单不参与返佣

#### 2.2 返佣规则
- **SVIP自购返佣**：SVIP用户购买商品可获得自购返佣（需开启配置）
- **下级购买返佣**：SVIP用户的下级购买商品时，上级SVIP用户可获得推广返佣
- **返佣比例**：
  - 普通会员：3%
  - VIP会员：6%
  - SVIP会员：10%
- **返佣金额** = 订单金额 × 对应的返佣比例

#### 2.3 定时任务执行
- **执行时间**：每天凌晨1点自动执行
- **处理逻辑**：遍历所有符合条件的订单，自动计算并发放返佣
- **即时到账**：由于订单筛选时已确保过了售后期限，返佣金额立即可用，无需冻结

### 3. 系统配置

#### 3.1 配置参数
系统通过`system_param_setting`表管理以下配置：

| 配置代码 | 配置名称 | 说明 | 默认值 |
|---------|---------|------|--------|
| `svip_auto_refund` | SVIP自购返佣 | 控制是否开启SVIP会员自购返佣功能 | `{"enabled": true}` |
| `commission_ratio` | 返佣比例设置 | 不同用户等级的返佣比例配置 | `{"1":{"purchase":10},"2":{"purchase":8},"3":{"purchase":6}}` |
| `order_after_sale_time` | 买家申请售后期限 | 确认收货后多少天内可申请售后 | `{"value": 7, "unit": "天"}` |
| `auto_commission_task` | 自动返佣任务配置 | 定时任务相关配置 | `{"enabled": true, "frozen_days": 7}` |

#### 3.2 配置示例
```json
{
  "svip_auto_refund": {
    "enabled": true,
    "description": "开启后，SVIP会员自购订单将获得佣金返现"
  },
  "commission_ratio": {
    "1": {"purchase": 10},
    "2": {"purchase": 8},
    "3": {"purchase": 6},
    "description": "普通会员10%，VIP会员8%，SVIP会员6%"
  }
}
```

### 4. 技术实现

#### 4.1 核心组件
- **定时任务**：`AutoCommissionTask` - 每天凌晨1点自动执行
- **业务服务**：`AutoCommissionService` - 处理返佣业务逻辑
- **数据记录**：通过`user_commission_record`表记录所有返佣信息

### 5. 使用说明

#### 5.1 配置检查
1. 检查系统参数配置是否正确：
   - `svip_auto_refund`：SVIP自购返佣开关
   - `commission_ratio`：返佣比例配置
   - `order_after_sale_time`：售后期限配置
2. 根据业务需要调整返佣比例

#### 5.2 运行监控
- 定时任务每天凌晨1点自动执行
- 执行日志记录在系统日志中
- 每次返佣操作都有详细的记录和追踪
- 可通过`user_commission_record`表查看返佣记录

### 6. 性能优化

- **索引优化**：为`is_commission_completed`字段添加索引，大幅提升查询性能
- **复合索引**：添加`(paid, is_del, status, is_commission_completed, update_time)`复合索引
- **状态标记**：成功返佣后立即标记订单，避免重复处理
- **分层筛选**：先进行基础条件筛选，再进行售后条件验证，减少数据库查询

### 7. 安全机制

- **重复处理检查**：通过订单表字段和返佣记录双重检查，防止重复返佣
- **售后期限控制**：只处理确认收货后超过售后期限的订单，确保交易稳定
- **售后方式验证**：通过关联售后记录表验证售后处理方式，只允许"换货"或"小额补偿"
- **事务保证**：返佣操作使用数据库事务确保数据一致性

## 奖励金与佣金返现功能

### 1. 功能概述

系统支持两种会员激励模式：
1. **佣金返现**：基于用户等级进行不同比例的返现
2. **奖励金**：针对特定行为（升级、首单、充值、排行榜等）的额外奖励

这些功能通过会员参数配置表（`system_member_param_config`）进行统一管理，用户佣金记录表（`user_brokerage_record`）记录所有佣金与奖励金变动。

### 2. 佣金返现功能

佣金返现是基于用户等级实现的不同比例返现：
- SVIP会员：默认返现比例10%
- VIP会员：默认返现比例6%
- 普通会员：默认返现比例3%

SVIP会员可以启用自动返现功能，订单支付后直接返还佣金给用户自己。

### 3. 奖励金功能

系统支持多种奖励金类型：

| 奖励类型 | 说明 | 默认金额/比例 |
| -------- | ---- | ------------ |
| 推广普通会员升级为VIP | 推广的普通会员升级为VIP后，给推广人一次性奖励 | 200元 |
| 推广会员充值 | 推广的会员充值时，按充值金额的一定比例给推广人奖励 | 10% |
| 推广新用户首单购买 | 推广的新用户首次下单，按订单金额的一定比例给推广人奖励 | 10% |
| 周排行榜奖励 | 根据每周销售业绩排名，给予前几名的用户固定金额奖励 | 第一名500元，第二名300元等 |
| 月排行榜奖励 | 根据每月销售业绩排名，给予前几名的用户固定金额奖励 | 根据配置决定 |
| 季排行榜奖励 | 根据每季度销售业绩排名，给予前几名的用户固定金额奖励 | 根据配置决定 |

### 4. 奖励金管理界面

奖励金管理界面提供以下功能：
1. 显示奖励金合计金额
2. 提供时间范围、奖励类型、人员等筛选条件
3. 显示奖励记录列表，包含奖励人员、金额、类型、时间、奖励信息等
4. 针对排行榜奖励，显示详细的排名和奖励信息

### 5. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/member/bonus/list | GET | 获取奖励金记录列表 |
| /api/admin/member/bonus/total | GET | 获取奖励金合计金额 |
| /api/admin/member/bonus/distribute/rank | POST | 手动发放排行榜奖励金 |

## SVIP会员管理功能

### 1. 功能概述

SVIP会员管理功能是针对系统中高级会员的管理模块，包含SVIP会员的申请、审核、管理等功能。SVIP会员享有更高的佣金返现比例、专属服务和特权。

### 2. SVIP申请管理

系统支持用户申请成为SVIP会员，申请流程如下：
- 用户提交SVIP申请，包含个人资料、成长值等信息
- 管理员审核申请，可以通过或拒绝
- 申请通过后，用户自动升级为SVIP会员

### 3. SVIP会员管理

SVIP会员管理功能包括：
- SVIP会员列表查询，支持多条件筛选
- SVIP会员统计数据查看
- 手动添加SVIP会员
- 取消SVIP会员资格
- 查看SVIP会员详情
- 更新SVIP会员信息
- 调整会员等级
- 批量导入SVIP会员

### 4. 接口说明

#### SVIP申请管理接口

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/svip/apply/list | GET | 获取SVIP申请列表 |
| /api/admin/user/svip/apply/detail/{id} | GET | 获取SVIP申请详情 |
| /api/admin/user/svip/apply/audit | POST | 审核SVIP申请 |
| /api/admin/user/svip/apply/batchAudit | POST | 批量审核SVIP申请 |

#### SVIP会员管理接口

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/svip/list | GET | 获取SVIP会员列表 |
| /api/admin/user/svip/statistics | GET | 获取SVIP会员统计数据 |
| /api/admin/user/svip/add | POST | 添加SVIP会员 |
| /api/admin/user/svip/remove | POST | 取消SVIP会员资格 |
| /api/admin/user/svip/detail/{uid} | GET | 获取SVIP会员详情 |
| /api/admin/user/svip/update | POST | 更新SVIP会员信息 |
| /api/admin/user/svip/search | GET | 根据关键字查找用户 |
| /api/admin/user/svip/template/download | GET | 下载SVIP会员导入模板 |
| /api/admin/user/svip/import | POST | 批量导入SVIP会员 |
| /api/admin/user/svip/adjust | POST | 调整会员等级 |
| /api/admin/user/svip/level/list | GET | 获取会员等级列表 |
| /api/admin/user/svip/apply/template | GET | 获取SVIP申请模板信息 |
| /api/admin/user/svip/apply/records | GET | 获取SVIP申请记录列表 |

### 5. 数据表设计

#### SVIP申请记录表 (user_svip_apply)

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | int | 主键ID |
| uid | int | 用户ID |
| apply_info | text | 申请资料(JSON格式) |
| growth | int | 成长值 |
| apply_time | datetime | 申请时间 |
| status | tinyint | 审核状态：0-待审核，1-已通过，2-已拒绝 |
| auditor_id | int | 审核人ID |
| auditor_name | varchar | 审核人名称 |
| audit_time | datetime | 审核时间 |
| reject_reason | varchar | 拒绝原因 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_del | tinyint | 是否删除 |

### 6. 奖励金发放流程

1. 触发奖励金发放事件（如：用户升级、新用户首单等）
2. 查询对应的奖励金规则配置
1. 检查是否开启分销功能
2. 检查订单是否为营销活动订单（营销产品不参与分佣）
3. 检查用户是否有上级推广人
4. 获取参与分佣的人员（支持两级分销）
5. 计算各级分销员应得佣金
6. 生成佣金记录（初始状态为冻结）

#### 3. 佣金冻结与解冻
- 新生成的佣金记录会根据系统配置进入冻结期
- 定时任务会处理冻结期满的佣金记录，将其状态更新为完成，并增加到用户可用佣金中

#### 4. 佣金提现
- 用户可申请提现自己的可用佣金
- 提现申请需满足最低提现金额要求
- 提现申请需经过后台管理员审核
- 提现成功后会扣减用户可用佣金

#### 5. 佣金记录状态
佣金记录状态流转：
1. 订单创建 -> 生成佣金记录（status=1）
2. 进入冻结期 -> 佣金记录状态更新（status=2）
3. 冻结期结束 -> 佣金解冻完成（status=3）
4. 如订单退款 -> 佣金失效（status=4）
5. 申请提现 -> 提现申请中（status=5）

### 佣金相关接口

#### 分销配置管理接口
```
GET /api/admin/store/retail/spread/manage/get   // 获取分销配置
POST /api/admin/store/retail/spread/manage/set  // 保存分销配置
```

#### 分销员管理接口
```
GET /api/admin/store/retail/list  // 分销员列表
```

#### 佣金记录查询接口
```
GET /api/admin/user/brokerage/record  // 佣金记录查询
```

### 提现管理接口
```
GET /api/admin/finance/extract/list  // 提现申请列表
POST /api/admin/finance/extract/verify/{id}/{status}  // 提现审核
```

### 改造思路建议

1. **多级分销支持**：当前系统仅支持两级分销，可扩展至多级分销，通过动态配置每级分佣比例
2. **佣金分配规则优化**：增加按产品分类设置不同分佣比例的功能
3. **分销员等级体系**：引入分销员等级，不同等级享受不同分佣比例
4. **自动提现功能**：增加符合条件自动提现到微信或支付宝的功能
5. **分销活动支持**：特定活动期间设置特殊分佣比例
6. **分销统计分析**：增强分销数据分析功能，提供更直观的数据可视化
7. **分销推广工具**：提供更丰富的推广素材和链接生成工具
8. **分销裂变功能**：支持以优惠券或积分形式激励用户裂变推广

### 优惠券使用时间类型功能升级

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 用券时间类型设置
- 将原有的"是否固定使用时间"字段改为"用券时间类型"，支持三种模式：
  - 固定时间：指定具体的使用开始和结束时间
  - 领取后天数：优惠券从领取当天开始计算，可在指定天数内使用
  - 领取后增加天数后可用：优惠券领取后需要等待指定天数后才能使用，并可在之后的指定天数内使用

#### 2. 数据表结构变更
在`store_coupon`表中进行了以下变更：
```sql
-- 修改is_fixed_time字段为use_time_type
ALTER TABLE `store_coupon` CHANGE COLUMN `is_fixed_time` `use_time_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用券时间类型，1-固定时间，2-领取后天数，3-领取后增加天数';

-- 添加after_days字段
ALTER TABLE `store_coupon` ADD COLUMN `after_days` int(11) DEFAULT NULL COMMENT '领取后需等待天数后才可使用' AFTER `day`;
```

#### 3. 字段说明
- `use_time_type`: 用券时间类型
  - 1: 固定时间 - 使用固定的开始时间和结束时间
  - 2: 领取后天数 - 从领取日开始计算，可使用N天
  - 3: 领取后增加天数后可用 - 领取后需等待M天才能使用，然后可使用N天
- `day`: 可使用天数，对应类型2和类型3
- `after_days`: 领取后需等待天数，对应类型3

### 商品搭配功能升级

系统对商品搭配功能进行了全面升级，新增以下特性：

#### 1. 组合标签功能
- 支持为商品搭配添加多个标签（逗号分隔）
- 标签可用于前端搜索和筛选
- 标签可以表示搭配的适用场景、风格、活动等属性

#### 2. 优惠叠加功能
- 支持设置商品搭配是否允许使用满减券
- 开启后，消费者在购买搭配商品时可同时使用满减券
- 满减券将按照搭配的优惠价计算折扣

#### 3. 实际销量与库存管理
- 系统自动记录商品搭配的实际销量
- 支持设置商品搭配的库存数量（stock字段）
- 下单时自动扣减库存，防止超卖

#### 4. 商品上架状态管理
- 支持三种上架状态：
  - 暂不售卖放入仓库（storeStatus=0）：商品保存但不显示在前台
  - 立即上架（storeStatus=1）：商品立即在前台展示并可购买
  - 定时上架（storeStatus=2）：设置特定时间点自动上架售卖
- 定时上架功能可用于新品预告、活动预热等场景

#### 5. 商品搭配状态分类
- 在售中：上架状态(storeStatus=1) 且 有库存(stock>0) 且 未删除(isDel=0)
- 已售罄：上架状态(storeStatus=1) 且 无库存(stock<=0) 且 未删除(isDel=0)
- 仓库中：未上架(storeStatus!=1) 或 已删除(isDel=1)

#### 6. 批量操作功能
- 批量上架/下架商品搭配
- 批量删除商品搭配
- 批量设置库存

### 商品搭配API接口

#### 1. 商品搭配创建
```
POST /api/admin/product/combination
```
参数示例：
```json
{
  "name": "冬季暖心套装",
  "description": "冬季必备暖心组合",
  "comboTags": "冬季,保暖,限时特惠",
  "combinationPrice": 199.00,
  "initialSales": 50,
  "stock": 200,
  "allowDiscount": 1,
  "storeStatus": 1,
  "productIds": [101, 202, 303]
}
```

#### 2. 更新商品上架状态
```
PUT /api/admin/product/combination/{id}/storeStatus/{storeStatus}
```

#### 3. 批量更新商品上架状态
```
PUT /api/admin/product/combination/batch/storeStatus/{storeStatus}
```

#### 4. 更新库存数量
```
PUT /api/admin/product/combination/{id}/stock/{stock}
```

#### 5. 批量更新库存数量
```
PUT /api/admin/product/combination/batch/stock/{stock}
```

#### 6. 删除商品搭配
```
DELETE /api/admin/product/combination/{id}
```

#### 7. 批量删除商品搭配
```
DELETE /api/admin/product/combination/batch
```

#### 8. 获取商品搭配状态统计
```
GET /api/admin/product/combination/status/count
```

#### 9. 分页查询商品搭配列表（支持状态筛选）
```
GET /api/admin/product/combination/page
```
参数说明：
- `page`: 页码，默认值1
- `size`: 每页数量，默认值10
- `name`: 搭配名称，可选
- `statusType`: 状态类型，可选，默认值0
  - 0: 全部
  - 1: 在售中（上架且有库存且未删除）
  - 2: 已售罄（上架但无库存且未删除）
  - 3: 仓库中（未上架或已删除）

#### 10. 根据商品ID查询组合列表
```
GET /api/admin/product/combination/queryByProductId
```
参数说明：
- `productId`: 商品ID，必填

该接口用于查询指定商品ID相关的所有组合活动，返回结果包含组合名称、价格、库存、标签、状态等信息，常用于商品列表中的组合查询功能。

### 商品搭配数据表结构

```sql
-- 商品搭配表结构
CREATE TABLE `store_product_combination` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搭配ID',
    `name` varchar(100) NOT NULL COMMENT '搭配组合名称',
    `description` varchar(500) DEFAULT NULL COMMENT '搭配描述',
    `combo_tags` varchar(255) DEFAULT NULL COMMENT '组合标签（逗号分隔）',
    `combination_price` decimal(10, 2) NOT NULL COMMENT '组合优惠价',
    `initial_sales` int(11) NOT NULL DEFAULT '0' COMMENT '初始已售数量（虚拟销量基础值）',
    `actual_sales` int(11) NOT NULL DEFAULT '0' COMMENT '实际销量',
    `stock` int(11) NOT NULL DEFAULT '0' COMMENT '库存数量',
    `allow_discount` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否允许使用满减券：0-不允许，1-允许',
    `start_time` datetime DEFAULT NULL COMMENT '售卖开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '售卖结束时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '是否启用：0-禁用，1-启用',
    `store_status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '商品上架状态：0-暂不售卖放入仓库，1-立即上架，2-定时上架',
    `sale_time` int(11) NOT NULL DEFAULT '0' COMMENT '定时开售时间（时间戳，0表示实时开售）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_name` (`name`),
    KEY `idx_status` (`status`),
    KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品搭配表';

-- 商品搭配明细表
CREATE TABLE `store_product_combination_detail` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
    `combination_id` bigint(20) NOT NULL COMMENT '搭配组合ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `quantity` int(11) NOT NULL DEFAULT 1 COMMENT '商品数量',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_combination_id` (`combination_id`),
    KEY `idx_product_id` (`product_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品搭配明细表';
```

### 运费模板数据表结构

```sql
-- 运费模板表
CREATE TABLE `shipping_templates` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `name` varchar(255) NOT NULL COMMENT '模板名称',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式',
  `appoint` tinyint(1) NOT NULL DEFAULT '0' COMMENT '指定包邮',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：0-禁用，1-启用',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板';

-- 运费模板指定区域费用
CREATE TABLE `shipping_templates_region` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `temp_id` int(11) NOT NULL DEFAULT '0' COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市ID',
  `title` text COMMENT '描述',
  `first` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '首件',
  `first_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '首件运费',
  `renewal` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '续件',
  `renewal_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '续件运费',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式 1按件数 2按重量 3按体积',
  `uniqid` varchar(32) NOT NULL DEFAULT '' COMMENT '分组唯一值',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否无效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板指定区域费用';

-- 运费模板包邮
CREATE TABLE `shipping_templates_free` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `temp_id` int(11) NOT NULL DEFAULT '0' COMMENT '模板ID',
  `city_id` int(11) NOT NULL DEFAULT '0' COMMENT '城市ID',
  `title` text COMMENT '描述',
  `number` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '包邮件数',
  `price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '包邮金额',
  `type` tinyint(1) NOT NULL DEFAULT '1' COMMENT '计费方式',
  `uniqid` varchar(32) NOT NULL DEFAULT '' COMMENT '分组唯一值',
  `status` tinyint(1) DEFAULT '0' COMMENT '是否无效',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='运费模板包邮';
```

### 运费模板API接口

1. 获取运费模板列表: `GET /api/admin/express/shipping/templates/list`
2. 获取启用状态的运费模板列表: `GET /api/admin/express/shipping/templates/enabled/list`
3. 新增运费模板: `POST /api/admin/express/shipping/templates/save`
4. 修改运费模板: `POST /api/admin/express/shipping/templates/update`
5. 运费模板详情: `GET /api/admin/express/shipping/templates/info`
6. 删除运费模板: `GET /api/admin/express/shipping/templates/delete`
7. 修改运费模板启用状态: `POST /api/admin/express/shipping/templates/set_status`

### 运费模板启用状态功能

系统支持对运费模板设置启用状态：

1. 启用状态说明：
   - 启用（status=1）：商品可以使用该运费模板
   - 禁用（status=0）：商品不能使用该运费模板

2. 业务应用：
   - 只有启用状态的运费模板才能在商品管理中选择
   - 系统提供单独的接口获取所有启用状态的运费模板列表
   - 可以通过接口快速修改运费模板的启用状态

3. 接口使用示例：
   - 修改运费模板启用状态：
     ```
     POST /api/admin/express/shipping/templates/set_status?id=1&status=0
     ```
   - 获取所有启用状态的运费模板：
     ```
     GET /api/admin/express/shipping/templates/enabled/list
     ```

### 订单管理
- 订单列表查询
- 订单详情
- 订单状态修改
- 订单发货

### 用户管理
- 用户列表查询
- 用户详情
- 用户权限管理

### 会员等级管理
- 会员等级列表查询
- 会员等级详情查看
- 会员等级新增/编辑/删除
- 会员等级启用/禁用

#### 会员等级API接口

系统支持多级会员体系，可为不同会员等级配置特权，包括折扣、优惠券、生日特权等。

#### 1. 获取会员等级详情
```
GET /api/admin/system/user/level/info/{id}
```

参数说明：
- `id`: 会员等级ID

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "userLevel": {
      "id": 1,
      "name": "普通会员",
      "experience": 0,
      "isShow": true,
      "grade": 1,
      "discount": 100,
      "discountEnabled": true,
      "icon": "http://example.com/icon.png",
      "experienceSource": "自购消费,会员充值",
      "autoUpgrade": true,
      "registrationInfo": false,
      "couponId": 101,
      "couponEnabled": true,
      "birthCouponEnabled": true,
      "birthCouponId": 102,
      "commissionEnabled": false,
      "commissionRate": 0,
      "customerServiceEnabled": false,
      "customerServiceWechat": "",
      "isDel": false,
      "updateTime": "2023-10-01 12:00:00",
      "createTime": "2023-10-01 12:00:00"
    },
    "memberCoupon": {
      "id": 101,
      "name": "会员专享券",
      "type": 3,
      "useMinPrice": 100.00,
      "couponPrice": 20.00,
      "couponType": 1,
      "useTimeType": 1,
      "useStartTime": "2023-10-01 00:00:00",
      "useEndTime": "2023-12-31 23:59:59"
      // 其他优惠券字段...
    },
    "birthCoupon": {
      "id": 102,
      "name": "生日礼遇券",
      "type": 3,
      "useMinPrice": 50.00,
      "couponPrice": 15.00,
      "couponType": 1,
      "useTimeType": 2,
      "day": 30
      // 其他优惠券字段...
    }
  }
}
```

说明：
- `userLevel`: 原始会员等级对象
- `memberCoupon`: 会员券信息，仅当该等级设置了会员券且已启用时返回
- `birthCoupon`: 生日券信息，仅当该等级设置了生日券且已启用时返回

#### 2. 获取会员等级列表
```
GET /api/admin/system/user/level/list
```

#### 3. 新增会员等级
```
POST /api/admin/system/user/level/save
```

#### 4. 删除会员等级
```
POST /api/admin/system/user/level/delete/{id}
```

#### 5. 更新会员等级
```
POST /api/admin/system/user/level/update/{id}
```

#### 6. 启用/禁用会员等级
```
POST /api/admin/system/user/level/use
```

### 会员等级功能说明

系统支持多级会员等级设置，具有以下特性：

1. **会员等级基本信息**
   - 等级名称：会员等级的显示名称
   - 会员等级：数值，决定等级高低
   - 达到经验：升级到该等级所需经验值
   - 会员图标：等级显示图标

2. **会员折扣功能**
   - 享受折扣：会员购物时享受的折扣比例
   - 是否启用会员折扣：控制该等级是否享受折扣

3. **成长值来源设置**
   - 自购消费：购物消费可获得成长值
   - 推广金额：推广他人消费可获得成长值
   - 会员充值：充值可获得成长值
   - 是否达到成长值自动升级：控制是否自动升级

4. **会员专属权益**
   - 是否启用优惠券赠送：控制是否赠送优惠券
   - 是否启用生日券：控制是否赠送生日券
   - 是否启用佣金返现：控制是否开启分销返佣
   - 是否启用专属客服：控制是否提供专属客服服务

该功能主要用于提升用户忠诚度，鼓励用户消费和推广，同时为不同等级会员提供差异化服务。

### 售后管理
- 售后列表查询
- 售后详情查看
- 售后状态修改（同意退款、同意退货、同意换货、同意补寄、拒绝）

#### 售后流程信息增强

系统对售后流程信息进行了增强，新增以下特性：

1. **最终售后类型字段**
   - 在 `AftersaleFlowResponse` 中新增 `finalAftersaleType` 字段
   - 记录售后完成后的最终处理方式（1:仅退款,2:换货,3:小额补偿）
   - 从售后记录表的 `processing_method` 字段获取

2. **退款金额字段**
   - 在 `AftersaleFlowResponse` 中新增 `refundAmount` 字段
   - 记录实际的退款金额或小额补偿金额
   - 从售后记录表的 `refund_price` 字段获取

3. **数据来源**
   - 这两个字段的数据来源于 `store_order_aftersale_record` 表
   - 在售后处理完成时，系统会自动从售后记录中提取相关信息
   - 用于前端显示售后完成的暂时消息，内容从售后记录里面查询

4. **物流信息优化**
   - 退货快递单号：从 `store_order_aftersale.return_delivery_id` 字段获取
   - 换货发货快递单号：从 `store_order_aftersale.delivery_id` 字段获取
   - 不再从 `processing_explain` 文本字段解析快递单号
   - 使用专门的数据库字段，提高数据准确性和查询效率

5. **方法逻辑优化**
   - 快递单号设置提前到方法开始部分，不依赖特定状态
   - 最终售后类型和退款金额设置不再限制在完成状态
   - 状态判断逻辑与数据库定义完全一致（0-7状态码）
   - 减少重复代码，提升代码可维护性

6. **使用场景**
   - 前端在后台售后完成时显示暂时消息
   - 显示最终的处理方式和退款金额
   - 提供更完整的售后流程信息
   - 准确显示退货和换货的快递单号

### 用户管理

#### 用户列表查询功能升级

系统对用户查询功能进行了升级，新增以下特性：

1. **成为会员时间范围查询**
   - 支持按成为会员时间范围进行筛选
   - 开始时间和结束时间可分开设置
   - 系统使用用户创建时间作为成为会员时间

2. **查询参数说明**
   - `memberTimeStart`: 成为会员开始时间，格式：yyyy-MM-dd HH:mm:ss
   - `memberTimeEnd`: 成为会员结束时间，格式：yyyy-MM-dd HH:mm:ss

该功能主要用于精准筛选特定时间段注册的用户，方便运营人员进行用户分析和营销活动策划。

## 接口说明

### 商品接口
1. 获取商品列表: `GET /api/admin/store/product/list`
2. 新增商品: `POST /api/admin/store/product/save`
3. 修改商品: `POST /api/admin/store/product/update`
4. 商品详情: `GET /api/admin/store/product/info/{id}`
5. 删除商品: `GET /api/admin/store/product/delete/{id}`
6. 上架商品: `GET /api/admin/store/product/putOnShell/{id}`
7. 下架商品: `GET /api/admin/store/product/offShell/{id}`

### 商品接口返回字段说明
商品接口返回的数据中，除了基本信息外，还包含以下特殊字段：

1. `image`: 商品主图原图
2. `thumbnailImage`: 商品缩略图，尺寸为336*336像素，用于列表和缩略图展示
3. `sliderImage`: 商品轮播图
4. `detailImages`: 商品详情图

## 商品图示信息配置
商品在新增和修改时支持以下图示信息配置：

1. 售后服务图示：
   - 支持买家申请换货
   - 商品详情页将展示支持换货的说明
   - 7天无理由退货

2. 限购图示：
   - 限制每人可购买数量
     - 设置每人最大购买数量
   - 只允许特定用户购买
     - 可以指定允许购买的用户ID列表
     - 系统会自动验证购买者是否在允许列表中

## 自动缩略图功能

系统支持在上传图片的同时自动生成缩略图，便于在网站不同场景下加载合适尺寸的图片，提高页面访问性能。

### 功能介绍
- 上传图片时自动生成缩略图
- 缩略图命名规则为"原图片名称 + thumbnailImage"
- 缩略图与原图存储在相同路径下
- 支持本地存储和云存储（七牛云、阿里云OSS、腾讯云COS）

### 使用方法
上传图片时，系统会自动生成缩略图，并在返回的数据中包含缩略图URL：
```json
{
  "fileName": "example.jpg",
  "url": "/image/public/product/2023/12/01/example.jpg",
  "thumbnailUrl": "/image/public/product/2023/12/01/examplethumbnailImage.jpg"
}
```

### 配置说明
- 缩略图尺寸默认为336*336像素
- 可以通过系统配置修改缩略图尺寸
- 配置项键名：`upload_image_thumbnail_size`
- 配置值格式：`宽x高`，如：`336x336`
- 配置路径：后台 > 系统设置 > 缩略图设置

### 技术实现
- 使用Thumbnailator库处理图片生成缩略图
- 缩略图信息会保存到系统附件表中
- 自动处理各种存储方式下的缩略图上传

## 特定用户购买限制功能使用说明

管理员可以限制某些商品只能由特定用户购买。这对于限量版商品、会员特供商品或预售商品特别有用。

### 配置步骤：
1. 在商品编辑页面，勾选"只允许特定用户购买"选项
2. 添加允许购买该商品的用户ID列表
3. 保存商品信息

### 工作原理：
- 系统会在用户下单时自动验证用户ID是否在允许列表中
- 验证会在多个环节进行：加入购物车、预下单和创建订单时
- 对于没有购买权限的用户，系统会显示明确的错误信息
- 管理员可以随时更新允许购买的用户列表

### 数据表：
- `store_product_allowed_user` - 存储商品和允许购买用户的关联关系

## 售后管理模块
系统支持以下售后类型和状态管理：

1. 售后筛选条件：
   - 临期待处理
   - 未发货退款待处理
   - 已发货退款待处理
   - 退货待处理
   - 换货/补寄待处理
   - 退货待商家收货
   - 换货待商家收货
   - 仲裁待处理

2. 售后列表展示信息：
   - 订单编号
   - 售后号
   - 申请下单时间
   - 产品照片
   - 产品名称
   - 净重
   - 收货人
   - 商品ID
   - 商品SKU
   - 售后属性
   - 退款金额
   - 订单金额
   - 件数
   - 售后状态
   - 原因分类
   - 物流状态
   - 查看详情

3. 售后操作：
   - 支持批量或单个操作
   - 同意退款
   - 同意退货
   - 同意换货
   - 同意补寄
   - 拒绝申请

4. 技术实现：
   - 利用现有订单表结构（store_order, store_order_info, store_order_status）
   - 通过订单状态和退款状态组合表示不同售后状态
   - 操作记录保存在订单状态表中

## 商品批量操作功能

除了现有的批量上架、下架、加入回收站功能外，系统还支持以下批量操作：

### 批量设置商品属性

管理员可以批量设置多个商品的属性，包括：

1. 批量设置虚拟销量
   - 可以为多个商品统一设置相同的虚拟销量
   - 虚拟销量将影响商品的展示排名和热门度

2. 批量设置快递方式
   - 可以为多个商品统一设置邮费类型（统一邮费或运费模板）
   - 设置为统一邮费时，需要指定邮费金额
   - 设置为运费模板时，需要指定运费模板ID

### API接口

批量设置商品属性: `POST /api/admin/store/product/batchSetting`

请求参数说明：
```json
{
  "ids": [1, 2, 3],              // 商品ID列表，必填
  "setFicti": true,              // 是否设置虚拟销量，可选
  "ficti": 100,                  // 虚拟销量，当setFicti为true时必填
  "setFreightType": true,        // 是否设置快递方式，可选
  "freightType": 0,              // 运费类型：0-统一邮费，1-运费模板，当setFreightType为true时必填
  "postage": 10.00,              // 邮费，当freightType为0时必填
  "tempId": 1                    // 运费模板ID，当freightType为1时必填
}
```

返回结果：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": null
}
```

## 城市地区分类功能

系统支持按照一二区、三区、港澳台对城市进行分类管理，方便在业务中对不同地区进行差异化处理。

### 区域分类定义

系统将城市划分为三种区域类型：

1. **一二区** - 包括：北京市、天津市、河北省、山西省、内蒙古自治区、辽宁省等大部分东部和中部省份
2. **三区** - 包括：西藏自治区、宁夏回族自治区、新疆维吾尔自治区
3. **港澳台** - 包括：香港特别行政区、澳门特别行政区、台湾省

### 使用方式

1. 在城市管理中可设置和查看城市的区域分类
2. 可以通过API按区域分类获取城市列表：
   ```
   GET /api/admin/system/city/list/region?regionType=1
   ```
3. 可以通过API按区域分组获取城市列表：
   ```
   GET /api/admin/system/city/list/grouped
   ```
   返回格式为：
   ```json
   {
     "oneAndTwoArea": [...一二区城市列表...],
     "threeArea": [...三区城市列表...],
     "hkMacaoTaiwan": [...港澳台地区列表...],
     "all": [...所有城市列表...]
   }
   ```
4. 详细使用方法请参考 [城市地区分类功能说明文档](docs/city_region_type.md)

## 系统功能

### 活动氛围图标功能

系统对活动模块进行了升级，新增了氛围图标功能，主要特性如下：

#### 1. 功能说明
- 支持为每个活动设置专属的氛围图标
- 氛围图标可用于前端页面装饰，提升活动视觉效果和用户体验
- 与主图不同，氛围图标通常更为轻量和精致，可作为活动的视觉点缀元素

#### 2. 数据结构变更
在`store_activity`表中新增了以下字段：
```sql
ALTER TABLE `store_activity` ADD COLUMN `atmosphere_icon` varchar(255) DEFAULT NULL COMMENT '氛围图标' AFTER `image`;
```

#### 3. 使用场景
- 节日主题活动：使用节日元素作为氛围图标
- 促销活动：使用折扣标识、限时标识等作为氛围图标
- 新品首发：使用新品标识作为氛围图标
- 品牌联名：使用品牌logo作为氛围图标

#### 4. 技术实现
- 前端支持氛围图标的上传和预览
- 后端接口支持氛围图标的保存和获取
- 支持在活动列表和详情页面展示氛围图标

### 🆕 活动统计功能

系统新增了活动统计功能，为管理员提供活动状态的实时统计信息：

#### 1. 功能说明
- 提供活动状态的实时统计数据
- 支持查看进行中、未开始、已结束的活动数量
- 帮助管理员快速了解活动整体状况

#### 2. 统计维度
- **进行中的活动**：当前时间在活动开始时间和结束时间之间，且状态为开启的活动数量
- **未开始的活动**：开始时间大于当前时间，且状态为开启的活动数量
- **已结束的活动**：结束时间小于等于当前时间的活动数量（包含所有状态）

#### 3. 统计条件
- 仅统计未删除的活动
- 基于活动的开始时间和结束时间进行状态判断
- 进行中和未开始的活动需要状态为开启，已结束的活动包含所有状态
- 实时计算，确保数据准确性

#### 4. 活动列表筛选功能
活动列表支持按状态筛选，筛选条件与统计逻辑保持一致：
- `ongoing`：进行中的活动
- `not_started`：未开始的活动
- `ended`：已结束的活动

**API参数示例**：
```
GET /api/admin/marketing/activity/list?status=ongoing
GET /api/admin/marketing/activity/list?status=not_started
GET /api/admin/marketing/activity/list?status=ended
```

#### 5. API接口
```
GET /api/admin/marketing/activity/statistics
```

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "ongoingCount": 6,
    "notStartedCount": 4,
    "endedCount": 12
  }
}
```

#### 6. 使用场景
- 活动管理首页展示活动概况
- 运营人员快速了解活动状态分布
- 为活动策划提供数据支持

### 会员券发券时间功能

会员券（类型为3）增加了发券时间功能，支持以下发券方式：

1. **生日发券**：根据会员的生日信息，自动发放优惠券
   - 生日当天：在会员生日当天发放优惠券
   - 生日当周：在会员生日所在周发放优惠券
   - 生日当月：在会员生日所在月发放优惠券

2. **每月固定日期发券**：在每月指定日期自动向所有会员发放优惠券
   - 可设置每月的1-31日中的任一天

### 🆕 生日格式特殊处理功能

系统对用户生日字段进行了特殊处理，支持多种生日格式：

#### 1. 支持的生日格式
- **中文格式**：`xx月xx日`（如：12月25日、1月1日）
- **标准日期格式**：`yyyy-MM-dd`、`yyyy/MM/dd`等标准格式

#### 2. 智能解析机制
- 自动识别生日格式类型
- 对于中文格式，提取月份和日期数字进行解析
- 对于标准格式，使用hutool的日期解析功能
- 解析失败时记录详细错误日志，便于问题排查

#### 3. 数据验证
- 验证月份范围（1-12）和日期范围（1-31）
- 解析失败的用户不会收到生日优惠券
- 系统会记录解析失败的详细信息到日志中

### 使用方法

1. 创建会员券时，可以选择发券时间类型：
   - 无限制：不限制发券时间，手动发放
   - 生日发券：选择生日当天、生日当周或生日当月
   - 每月指定日期：选择每月的某一天（1-31日）

2. 系统会根据设置的发券时间自动执行发券任务：
   - 每天凌晨1点：处理生日当天和每月指定日期的发券
   - 每周一凌晨2点：处理生日当周的发券
   - 每月1号凌晨3点：处理生日当月的发券

3. 会员需要在个人资料中设置生日信息才能收到生日券
   - 支持"xx月xx日"格式（推荐）
   - 也支持标准日期格式

### 数据库更新

系统对store_coupon表进行了以下字段扩展：
- send_time_type：发券时间类型（0-无限制 1-生日 2-每月指定日期）
- birth_send_type：生日发券类型（1-生日当天 2-生日当周 3-生日当月）
- month_send_day：每月发券日期

执行以下SQL更新数据库：
```sql
ALTER TABLE `store_coupon` ADD COLUMN `send_time_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '发券时间类型 0-无限制 1-生日 2-每月指定日期' AFTER `after_days`;
ALTER TABLE `store_coupon` ADD COLUMN `birth_send_type` tinyint(1) DEFAULT NULL COMMENT '生日发券类型 1-生日当天 2-生日当周 3-生日当月' AFTER `send_time_type`;
ALTER TABLE `store_coupon` ADD COLUMN `month_send_day` int(11) DEFAULT NULL COMMENT '每月发券日期' AFTER `birth_send_type`;
```

### 长图片自动分割功能

系统提供了长图片自动分割功能，可以将超过设定高度的长图片自动分割为多张图片，便于在移动端展示。

#### 1. 功能特性
- 自动检测长图片：当图片高度超过宽度的2倍时，自动识别为长图片并进行分割
- 智能分割处理：分割高度等于图片自身的宽度，保持良好的展示效果
- 原图和分割后的图片都会保存，可以根据需要选择使用
- 存储优化：分割图片信息存储在原图记录中，避免数据库冗余

#### 2. 数据表结构变更
在`system_attachment`表中新增了以下字段：
```sql
ALTER TABLE system_attachment ADD COLUMN is_long_image tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否为长图片 0否 1是';
ALTER TABLE system_attachment ADD COLUMN split_image_urls text COMMENT '分割图片路径列表，以逗号分隔';
```

#### 3. 长图片判断逻辑
- 长图片判断标准：图片高度 > 图片宽度 × 2
- 分割高度计算：每段分割的高度 = 图片宽度
- 例如：宽度为800px的图片，分割高度为800px；宽度为1000px的图片，分割高度为1000px
- 合理处理最后一部分：如果最后一部分高度小于宽度的一半，则合并到前一部分，不单独分割
- 优化文件命名：分割图片使用主文件名称（不是原始上传文件名，而是系统生成的文件名）加上"_part序号"的方式命名

#### 4. 使用方式
长图片上传后，系统会自动检测并处理：
- 对于普通图片：正常上传并生成缩略图
- 对于长图片：自动分割并同时保存原图和分割后的图片
- 前端展示时可以根据`is_long_image`字段判断是否为长图片，选择合适的展示方式
- 删除长图片时会同时删除所有分割的图片文件，避免残留文件占用存储空间

### 优惠券功能升级

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 会员等级限制
- 支持为优惠券设置会员等级限制条件
- 可指定优惠券仅对特定会员等级可用（普通会员、VIP会员、SVIP会员）
- 支持多选，满足任一会员等级条件即可领取

#### 2. 过期提醒功能
- 支持设置优惠券过期前自动提醒
- 可配置提醒时间（比如过期前1-30天）
- 系统自动检测即将过期的优惠券并发送提醒通知

#### 3. 使用说明功能
- 支持为每张优惠券添加详细的使用说明
- 使用说明支持富文本内容，最多2000字符
- 使用说明将在用户查看优惠券详情时展示

#### 4. 会员等级筛选功能
- 支持在优惠券分页查询时按会员等级ID进行筛选
- 可精确查找特定会员等级可用的优惠券
- 筛选逻辑智能匹配会员等级ID是否存在于优惠券的会员等级限制中

#### 5. 优惠券查询API
```
GET /api/admin/marketing/coupon/list
```

参数说明：
- `name`: 优惠券名称，可选，支持模糊查询
- `type`: 优惠券类型，可选，1-满减券，2-新人专享券，3-会员专享券
- `status`: 状态，可选，true-启用，false-禁用
- `memberLevelId`: 会员等级ID，可选，用于筛选指定会员等级可用的优惠券
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

#### 6. 数据表结构变更
在`store_coupon`表中进行了以下变更：
```sql
-- 添加会员等级限制、过期提醒、使用说明字段
ALTER TABLE `store_coupon` ADD COLUMN `member_levels` varchar(255) DEFAULT NULL COMMENT '可发放会员等级限制，多个用逗号分隔' AFTER `month_send_day`;
ALTER TABLE `store_coupon` ADD COLUMN `expire_notice` tinyint(1) NOT NULL DEFAULT 0 COMMENT '过期提醒 0-不提醒 1-提醒' AFTER `member_levels`;
ALTER TABLE `store_coupon` ADD COLUMN `expire_notice_days` int(11) DEFAULT NULL COMMENT '过期前提醒天数' AFTER `expire_notice`;
ALTER TABLE `store_coupon` ADD COLUMN `use_description` text DEFAULT NULL COMMENT '券使用说明' AFTER `expire_notice_days`;
```

### 优惠券领取限制功能升级

系统对优惠券领取功能进行了升级，新增以下特性：

#### 1. 领取次数限制
- 支持两种领取次数限制模式：
  - 每人仅限一次：用户只能领取一次该优惠券
  - 每人多次可领：用户可多次领取该优惠券，可设置具体领取次数上限

#### 2. 客户领取限制
- 支持三种客户领取限制模式：
  - 不限制，所有人可领：所有用户都可以领取该优惠券
  - 指定会员等级：只有特定会员等级的用户可以领取该优惠券
  - 指定用户标签：只有拥有特定标签的用户可以领取该优惠券

#### 3. 数据表结构变更
在`store_coupon`表中新增了以下字段：
```sql
ALTER TABLE `store_coupon`
    ADD COLUMN `receive_limit_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '领取次数限制：1-每人仅限一次，2-每人多次可领' AFTER `last_total`,
    ADD COLUMN `receive_limit_count` int(11) DEFAULT 1 COMMENT '每人可领取次数，receive_limit_type=2时生效' AFTER `receive_limit_type`,
    ADD COLUMN `customer_limit_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '领取客户限制：1-不限制，所有人可领，2-指定会员等级，3-指定用户标签' AFTER `receive_limit_count`,
    ADD COLUMN `customer_level_ids` varchar(255) DEFAULT NULL COMMENT '指定可领取的会员等级ID，多个以逗号分隔，customer_limit_type=2时生效' AFTER `customer_limit_type`,
    ADD COLUMN `customer_tag_ids` varchar(255) DEFAULT NULL COMMENT '指定可领取的用户标签ID，多个以逗号分隔，customer_limit_type=3时生效' AFTER `customer_level_ids`;
```

#### 4. 新增API接口
- 获取会员等级列表接口，用于选择指定会员等级
```
GET /api/admin/marketing/coupon/user/level/list
```

- 获取用户标签列表接口，用于选择指定用户标签
```
GET /api/admin/marketing/coupon/user/tag/list
```

#### 5. 字段说明
- `receive_limit_type`: 领取次数限制类型
  - 1: 每人仅限一次
  - 2: 每人多次可领
- `receive_limit_count`: 每人可领取次数，当receive_limit_type=2时生效
- `customer_limit_type`: 领取客户限制类型
  - 1: 不限制，所有人可领
  - 2: 指定会员等级
  - 3: 指定用户标签
- `customer_level_ids`: '指定可领取的会员等级ID，多个以逗号分隔，当customer_limit_type=2时生效
- `customer_tag_ids`: '指定可领取的用户标签ID，多个以逗号分隔，当customer_limit_type=3时生效
```

### 商品分类功能升级

系统对商品分类功能进行了升级，新增以下特性：

#### 1. 分类扩展字段2
- 支持为商品分类添加第二个扩展字段(extra2)
- 可用于存储更多分类相关的扩展信息
- 与原有extra字段配合使用，提供更丰富的分类管理功能

#### 2. 数据表结构变更
在`category`表中新增了extra2字段：
```sql
ALTER TABLE `category` ADD COLUMN `extra2` text COMMENT '扩展字段2' AFTER `extra`;
```

#### 3. 相关API字段说明
分类相关接口新增了以下字段：
- `extra2`: 分类扩展字段2，可存储更多分类相关信息

### 优惠券门槛类型功能

系统对优惠券功能进行了升级，新增以下特性：

#### 1. 优惠券门槛类型设置
- 新增优惠券门槛类型字段，支持两种类型：
  - 有门槛(满减券)：需要满足最低消费金额才能使用的优惠券
  - 无门槛：无需满足最低消费金额即可使用的优惠券

#### 2. 门槛类型与优惠券面值、最低消费的关系
- 有门槛优惠券：必须设置最低消费金额(minPrice > 0)，用户消费满足最低金额后才能使用
- 无门槛优惠券：最低消费金额自动设为0，用户无需满足消费金额即可使用

#### 3. 数据表结构变更
在`store_coupon`和`store_coupon_user`表中新增了coupon_type字段：
```sql
ALTER TABLE `store_coupon`
    ADD COLUMN `coupon_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠券门槛类型 1 有门槛(满减券), 2 无门槛' AFTER `money`;

ALTER TABLE `store_coupon_user`
    ADD COLUMN `coupon_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '优惠券门槛类型 1 有门槛(满减券), 2 无门槛' AFTER `money`;
```

#### 4. 字段说明
- `coupon_type`: 优惠券门槛类型
  - 1: 有门槛(满减券) - 需要满足最低消费金额才能使用的优惠券
  - 2: 无门槛 - 无需满足最低消费金额即可使用的优惠券
- `money`: 优惠券面值
- `min_price`: 最低消费金额，有门槛时必须大于0，无门槛时固定为0

### 会员折扣功能

系统新增会员折扣功能，支持为不同会员等级设置不同的折扣优惠。

#### 1. 会员折扣功能说明
- 支持为不同会员等级设置专属折扣
- 支持全场通用、指定商品可用、指定商品不可用三种模式
- 支持设置折扣值（如8折、9折等）
- 支持批量操作（启用/禁用、复制、删除）
- 支持查看会员折扣的使用统计数据

#### 2. 数据表设计
会员折扣表 `store_member_discount` 结构如下：
```sql
CREATE TABLE `store_member_discount` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '会员折扣ID',
    `name` varchar(64) NOT NULL COMMENT '折扣名称',
    `member_levels` varchar(255) NOT NULL COMMENT '会员等级限制，多个用逗号分隔',
    `discount` decimal(10, 2) NOT NULL COMMENT '折扣值',
    `use_type` tinyint(1) NOT NULL COMMENT '适用范围 1 全场通用, 2 指定商品可用, 3 指定商品不可用',
    `product_ids` varchar(1000) DEFAULT NULL COMMENT '指定商品ID，多个用逗号分隔',
    `description` varchar(255) DEFAULT NULL COMMENT '描述说明',
    `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态（0：关闭，1：开启）',
    `is_del` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除 状态（0：否，1：是）',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员折扣表';
```

#### 3. 会员折扣API接口

##### 3.1 获取会员折扣列表
```
GET /api/admin/marketing/member/discount/list
参数说明：
- `keywords`: 折扣名称关键词，可选
- `status`: 状态，可选
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

##### 3.2 新增会员折扣
```
POST /api/admin/marketing/member/discount/save
```
参数示例：
```json
{
  "name": "VIP会员8折",
  "memberLevels": "2,3",
  "discount": 8.00,
  "useType": 1,
  "productIds": "",
  "description": "VIP会员专享折扣",
  "sort": 0
}
```

##### 3.3 修改会员折扣
```
POST /api/admin/marketing/member/discount/update
```
参数示例（与新增类似，但需要包含id）：
```json
{
  "id": 1,
  "name": "VIP会员8折",
  "memberLevels": "2,3",
  "discount": 8.00,
  "useType": 1,
  "productIds": "",
  "description": "VIP会员专享折扣（已更新）",
  "sort": 0
}
```

##### 3.4 修改会员折扣状态
```
POST /api/admin/marketing/member/discount/update/status
```
参数说明：
- `id`: 折扣ID，必填
- `status`: 状态值，必填，true-启用，false-禁用

##### 3.5 获取会员折扣详情
```
GET /api/admin/marketing/member/discount/info
```
参数说明：
- `id`: 折扣ID，必填

##### 3.6 删除会员折扣
```
POST /api/admin/marketing/member/discount/delete
```
参数说明：
- `id`: 折扣ID，必填

##### 3.7 复制会员折扣
```
POST /api/admin/marketing/member/discount/copy
```
参数说明：
- `id`: 折扣ID，必填

##### 3.8 获取可用会员等级列表
```
GET /api/admin/marketing/member/discount/level/list
```

##### 3.9 获取数据统计
```
GET /api/admin/marketing/member/discount/statistics
```

#### 4. 使用说明
会员折扣功能用于为不同等级的会员提供不同的折扣优惠：
1. 创建会员折扣：设置折扣名称、会员等级、折扣值及适用范围
2. 启用会员折扣：设置会员折扣状态为启用
3. 会员购物时系统自动应用对应等级的折扣
4. 支持查看统计数据，了解各折扣的使用情况

#### 5. 注意事项
- 可为同一会员等级设置多个折扣，系统会自动选择最优惠的折扣使用
- 折扣值以小数表示，如8折输入8.00
- 指定商品可用/不可用时，需填写商品ID，多个用逗号分隔

#### 6. 会员佣金返现
- 新增配置"是否启用佣金返现"选项
- 开启后，用户可享受指定比例的分销提成
- 支持为不同会员等级设置不同的佣金返现比例
- 系统会根据用户等级自动设置相应的分销提成比例

#### 7. 专属客服服务
- 新增配置"是否启用专属客服"选项
- 开启后，用户可查看专属客服微信号进行咨询
- 支持为不同会员等级设置不同的客服微信号
- 客服信息将在会员中心展示

#### 8. 会员折扣启用控制
- 新增配置"是否启用会员折扣"选项
- 开启后，用户可享受会员等级设定的商品折扣优惠
- 关闭后，即使设置了折扣值，也不会实际应用折扣
- 支持为不同会员等级灵活控制折扣功能的启用状态
- 可与其他优惠方式组合使用，实现灵活的营销策略

#### 数据库表结构变更
在`system_user_level`表中新增了以下字段：
```sql
ALTER TABLE `system_user_level`
ADD COLUMN `experience_source` varchar(255) NOT NULL DEFAULT '' COMMENT '成长值来源（多选：自购消费、推广金额、会员充值，用逗号分隔）' AFTER `icon`,
ADD COLUMN `auto_upgrade` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否达到成长值自动升级 1=是,0=否' AFTER `experience_source`,
ADD COLUMN `registration_info` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要注册信息(获取手机号) 1=是,0=否' AFTER `auto_upgrade`,
ADD COLUMN `coupon_id` int(11) NULL DEFAULT NULL COMMENT '补充资料赠送的优惠券ID' AFTER `registration_info`,
ADD COLUMN `coupon_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否开启优惠券赠送 1=是,0=否' AFTER `coupon_id`,
ADD COLUMN `birth_coupon_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用生日券 1=是,0=否' AFTER `coupon_enabled`,
ADD COLUMN `birth_coupon_id` int(11) NULL DEFAULT NULL COMMENT '生日券ID' AFTER `birth_coupon_enabled`,
ADD COLUMN `commission_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用佣金返现 1=是,0=否' AFTER `birth_coupon_id`,
ADD COLUMN `commission_rate` int(11) NULL DEFAULT NULL COMMENT '分销提成比例' AFTER `commission_enabled`,
ADD COLUMN `customer_service_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用专属客服 1=是,0=否' AFTER `commission_rate`,
ADD COLUMN `customer_service_wechat` varchar(50) NULL DEFAULT NULL COMMENT '客服微信号' AFTER `customer_service_enabled`,
ADD COLUMN `discount_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用会员折扣 1=是,0=否' AFTER `discount`;
```

在`user`表中新增了以下字段：
```sql
ALTER TABLE `user`
ADD COLUMN `commission_rate` int(11) NULL DEFAULT 0 COMMENT '分销提成比例' AFTER `brokerage_price`;
```

### 会员参数设置功能

系统对会员参数设置功能进行了全面升级，新增以下特性：

#### 1. 成长值设置
- 支持设置不同来源的成长值获取规则
- 支持自购商品、会员充值、推广金额等多种来源类型
- 可配置每消费数量和获取比例，精确控制成长值获取

#### 2. 提现设置
- 支持设置提现等级限制（可多选SVIP会员、VIP会员等），一条记录控制多种会员类型
- 可配置提现金额限制（单次最小金额、单次最大金额）
- 可设置提现次数限制（每月最大次数）
- 支持设置提现手续费比例

#### 3. 奖励金设置
- 支持设置不同奖励金来源的奖励规则
- 包括推广普通会员升级为VIP、推广会员充值等场景
- 支持设置周排行榜、月排行榜和季排行榜奖励
- 可灵活配置榜单排名显示数量（TOP10、TOP20、TOP30等）
- 可灵活配置奖励金额或比例

#### 4. 佣金返现设置
- 支持为不同会员类型（SVIP会员、VIP会员、普通会员）设置不同的佣金返现比例
- 可设置佣金返现比例（百分比）
- 用户可根据会员等级获得相应的佣金返现
- 支持SVIP会员自购返现功能：开启后，SVIP会员自购买后将获得佣金返现

#### 数据表结构变更
在系统中新增了`system_member_param_config`表：
```sql
CREATE TABLE IF NOT EXISTS `system_member_param_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '配置id',
  `config_type` int(11) NOT NULL COMMENT '配置类型：1-成长值设置，2-提现设置，3-佣金返现设置，4-奖励金设置',
  `source_type` varchar(50) DEFAULT NULL COMMENT '来源类型：自购商品、推广金额、会员充值等',
  `number` int(11) DEFAULT '0' COMMENT '每消费数量',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位：元/件',
  `ratio` int(11) DEFAULT '0' COMMENT '获取比例',
  `withdraw_type` varchar(50) DEFAULT NULL COMMENT '提现类型：SVIP会员，VIP会员，普通会员',
  `withdraw_value` decimal(10,2) DEFAULT NULL COMMENT '提现规则',
  `min_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最小金额',
  `max_amount` decimal(10,2) DEFAULT NULL COMMENT '提现金额限制：单次最大金额',
  `max_times` int(11) DEFAULT NULL COMMENT '提现次数限制：每月最大次数',
  `fee_rate` decimal(10,2) DEFAULT NULL COMMENT '提现手续费：百分比',
  `auto_refund` tinyint(1) DEFAULT '0' COMMENT '是否SVIP自购返现：1-启用，0-禁用',
  `withdraw_levels` varchar(100) DEFAULT NULL COMMENT '提现等级限制：多个用逗号分隔，如"SVIP会员,VIP会员"',
  `rank_display_count` int(11) DEFAULT '20' COMMENT '榜单排名显示数量：如20表示TOP20',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用：1-启用，0-禁用',
  `sort` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='会员参数设置表';
```

#### 会员参数设置相关API接口

##### 1. 分页查询会员参数设置
```
GET /api/admin/system/member/param/list
```
参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

##### 2. 根据配置类型获取会员参数设置列表
```
GET /api/admin/system/member/param/list/{configType}
```
参数说明：
- `configType`: 配置类型（1-成长值设置，2-提现设置，3-奖励金设置，4-佣金返现设置），必填

##### 3. 获取所有会员参数设置列表
```
GET /api/admin/system/member/param/all
```

##### 4. 新增会员参数设置
```
POST /api/admin/system/member/param/save
```
请求体示例（成长值设置）：
```json
{
  "configType": 1,
  "sourceType": "自购商品",
  "number": 1,
  "unit": "件",
  "ratio": 1,
  "status": true,
  "sort": 0,
  "remark": "购买单件商品获得1点成长值"
}
```

请求体示例（提现设置）：
```json
{
  "configType": 2,
  "withdrawLevels": "SVIP会员,VIP会员",
  "minAmount": 100.00,
  "maxAmount": 5000.00,
  "maxTimes": 3,
  "feeRate": 0.00,
  "status": true,
  "sort": 1,
  "remark": "SVIP和VIP会员提现设置"
}
```

请求体示例（奖励金设置-排行榜）：
```json
{
  "configType": 3,
  "sourceType": "周排行榜奖励",
  "number": 500,
  "unit": "元",
  "ratio": 0,
  "rankDisplayCount": 20,
  "status": true,
  "sort": 2,
  "remark": "周排行榜第一名奖励500元，显示TOP20"
}
```

请求体示例（佣金返现设置）：
```json
{
  "configType": 4,
  "withdrawType": "SVIP会员",
  "ratio": 10,
  "autoRefund": true,
  "status": true,
  "sort": 0,
  "remark": "SVIP会员佣金返现比例10%，开启SVIP自购返现"
}
```

##### 5. 更新会员参数设置
```
POST /api/admin/system/member/param/update
```
请求体示例（与新增接口类似，但需要包含id字段）

##### 6. 删除会员参数设置
```
POST /api/admin/system/member/param/delete/{id}
```
参数说明：
- `id`: 配置ID，必填

##### 7. 更新会员参数设置状态
```
POST /api/admin/system/member/param/status
```
参数说明：
- `id`: 配置ID，必填
- `status`: 状态值（true-启用，false-禁用），必填

### 用户等级注册信息扩展功能

系统对用户等级的注册信息功能进行了扩展，新增以下特性：

#### 1. 注册信息收集扩展
- 原有功能仅支持设置是否必须收集手机号
- 新增支持设置是否必须收集头像和昵称（作为一个选项）以及生日信息
- 可以根据不同会员等级的需求，灵活配置需要收集的用户信息

#### 2. 数据表结构变更
在`system_user_level`表中进行了以下变更：
```sql
ALTER TABLE `system_user_level`
CHANGE COLUMN `registration_info` `phone_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要手机号 1=是,0=否',
ADD COLUMN `avatar_nickname_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要头像和昵称 1=是,0=否' AFTER `phone_required`,
ADD COLUMN `birthday_required` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否需要生日 1=是,0=否' AFTER `avatar_nickname_required`;
```

#### 3. 字段说明
- `phone_required`: 是否需要手机号 1=是,0=否
- `avatar_nickname_required`: 是否需要头像和昵称 1=是,0=否
- `birthday_required`: 是否需要生日 1=是,0=否

### 会员余额管理功能

系统提供了完整的会员余额管理功能，包括余额查询、明细查看、余额操作等。

#### 1. 会员余额概览
- 支持查看会员余额总计
- 支持查看VIP充值金额总计
- 支持查看累计返现和差额总计
- 支持查看累计消费金额总计
- 支持查看累计提现金额总计

#### 2. 会员余额列表
- 支持查看所有会员的余额信息
- 支持按关键词搜索会员
- 支持按时间范围筛选
- 支持查看明细和增减款操作

#### 3. 会员余额明细
- 支持查看指定会员的余额变动明细
- 支持按交易类型筛选（充值、消费、返现等）
- 支持按收支类型筛选（收入、支出）
- 支持按时间范围筛选

#### 4. 余额操作功能
- 支持对会员余额进行增加或减少操作
- 所有操作都会记录详细的操作日志
- 支持输入操作备注

### 会员余额API接口

#### 1. 获取会员余额统计
```
GET /api/admin/user/balance/statistics
```
返回示例：
```json
{
  "code": 200,
  "data": {
    "totalBalance": 6000.00,
    "totalRecharge": 6000.00,
    "totalRefund": 6000.00,
    "totalConsume": 6000.00,
    "totalWithdrawal": 6000.00
  },
  "message": "操作成功"
}
```

#### 2. 获取会员余额列表
```
GET /api/admin/user/balance/list
```
参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10
- `keywords`: 搜索关键词（会员昵称、手机号、真实姓名）
- `dateLimit`: 日期范围，例如"today"、"yesterday"、"last7days"、"last30days"等

#### 3. 获取指定用户余额信息
```
GET /api/admin/user/balance/info/{id}
```
参数说明：
- `id`: 用户ID

返回示例：
```json
{
  "code": 200,
  "data": {
    "nowMoney": 5569.00,
    "recharge": 2000.00,
    "orderStatusSum": 2000.00,
    "refund": 2000.00,
    "withdrawal": 2000.00,
    "totalIncome": 2000.00
  },
  "message": "操作成功"
}
```

#### 4. 会员余额操作
```
POST /api/admin/user/balance/update
```
请求参数：
```json
{
  "uid": 123,
  "moneyValue": 100.00,
  "moneyType": 1,
  "integralValue": 0,
  "integralType": 1
}
```
参数说明：
- `uid`: 用户ID
- `moneyValue`: 操作金额
- `moneyType`: 操作类型，1-增加，2-减少
- `integralValue`: 积分值（可选）
- `integralType`: 积分操作类型（可选），1-增加，2-减少

#### 5. 获取会员余额明细
```
GET /api/admin/user/balance/detail
```
参数说明：
- `uid`: 用户ID（可选）
- `pm`: 收支类型，0-支出，1-收入（可选）
- `category`: 明细类别（可选），例如"now_money"表示资金
- `dateLimit`: 日期范围，例如"today"、"yesterday"、"last7days"、"last30days"等（可选）
- `startTime`: 开始时间，格式为"yyyy-MM-dd HH:mm:ss"（可选，优先级高于dateLimit）
- `endTime`: 结束时间，格式为"yyyy-MM-dd HH:mm:ss"（可选，优先级高于dateLimit）
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

### 提现功能接口

#### 1. 提现申请列表
```
GET /api/admin/finance/apply/list
```

参数说明：
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值20
- `keywords`: 搜索关键字（微信号/姓名/支付宝账号/银行卡号/失败原因）
- `extractType`: 提现方式（bank=银行卡，alipay=支付宝，weixin=微信）
- `status`: 审核状态（-1=未通过，0=审核中，1=已提现）
- `dateLimit`: 时间范围（today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/）
- `extractNumber`: 提现编号

返回数据字段说明：
- `id`: 提现ID
- `uid`: 用户ID
- `realName`: 姓名
- `extractPrice`: 提现金额
- `fee`: 提现手续费
- `actualAmount`: 实际到账金额
- `extractType`: 提现方式（bank=银行卡，alipay=支付宝，weixin=微信）
- `bankCode`: 银行卡号
- `bankName`: 银行名称
- `alipayCode`: 支付宝账号
- `wechat`: 微信号
- `qrcodeUrl`: 收款码
- `status`: 状态（-1=未通过，0=审核中，1=已提现）
- `paymentNo`: 付款流水号
- `createTime`: 申请时间
- `handleTime`: 处理时间
- `failTime`: 驳回时间
- `failMsg`: 驳回原因

#### 2. 提现申请审核
```
POST /api/admin/finance/apply/apply
```

参数说明：
- `id`: 提现申请ID，必填
- `status`: 审核状态，必填（-1=未通过，0=审核中，1=已提现）
- `backMessage`: 驳回原因，当status=-1时必填

#### 3. 批量审核提现申请
```
POST /api/admin/finance/apply/batch
```

参数说明：
- `ids`: 提现申请ID列表，逗号分隔，必填
- `status`: 审核状态，必填（-1=未通过，0=审核中，1=已提现）
- `backMessage`: 驳回原因，当status=-1时必填

#### 4. 提现申请修改
```
POST /api/admin/finance/apply/update
```

参数说明：
- `id`: 提现申请ID，必填
- `realName`: 姓名，必填
- `extractType`: 提现方式，必填（bank=银行卡，alipay=支付宝，weixin=微信）
- `bankCode`: 银行卡号，当extractType=bank时必填
- `bankName`: 银行名称，当extractType=bank时必填
- `alipayCode`: 支付宝账号，当extractType=alipay时必填
- `wechat`: 微信号，当extractType=weixin时必填
- `extractPrice`: 提现金额，必填
- `mark`: 备注，可选

#### 5. 更新付款流水号
```
POST /api/admin/finance/apply/payment
```

参数说明：
- `id`: 提现申请ID，必填
- `paymentNo`: 付款流水号，必填

#### 6. 提现统计
```
POST /api/admin/finance/apply/balance
```

参数说明：
- `dateLimit`: 时间范围，可选（today,yesterday,lately7,lately15,lately30,month,year,/yyyy-MM-dd hh:mm:ss,yyyy-MM-dd hh:mm:ss/）

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "withdrawn": 1000.00,      // 已提现金额
    "unDrawn": 500.00,         // 未提现金额
    "commissionTotal": 1500.00, // 佣金总金额
    "toBeWithdrawn": 200.00     // 待提现金额（审核中）
  }
}
```

#### 7. 提现记录详情
```
GET /api/admin/finance/apply/info
```

参数说明：
- `id`: 提现记录ID，必填

### 提现功能说明

系统支持多种提现方式，包括银行卡、支付宝和微信，具有以下特性：

1. **提现申请流程**
   - 用户发起提现申请，填写提现信息
   - 系统冻结用户对应金额
   - 管理员审核提现申请
   - 审核通过后，系统记录提现成功，计算手续费，确定实际到账金额
   - 审核不通过，系统返还冻结金额给用户

2. **提现方式**
   - 银行卡：需填写姓名、银行卡号、银行名称
   - 支付宝：需填写姓名、支付宝账号、收款码（可选）
   - 微信：需填写姓名、微信号、收款码（可选）

3. **提现限制与费用**
   - 最低提现金额可在系统配置中设置
   - 提现手续费率可在系统配置中设置
   - 实际到账金额 = 提现金额 - 手续费
   - 用户提现金额不能超过可提现余额

4. **提现状态**
   - 审核中（status=0）：提现申请已提交，等待管理员审核
   - 已提现（status=1）：管理员审核通过，提现成功
   - 未通过（status=-1）：管理员审核不通过，提现失败

5. **批量审核功能**
   - 支持批量审核提现申请
   - 可同时审核多个提现申请
   - 批量驳回时需填写统一的驳回原因

6. **付款流水号管理**
   - 支持为已审核通过的提现记录添加付款流水号
   - 付款流水号可用于对账和跟踪支付流程
   - 通过API接口可更新付款流水号

该功能主要用于管理用户的提现申请，提高财务管理效率，保障用户资金安全。

## 提现功能

提现功能允许用户将账户中的余额或佣金提取到指定的银行账户、支付宝账户或微信账户。

### 提现流程

1. 用户提交提现申请，选择提现方式并输入提现金额和账户信息
2. 管理员审核提现申请
3. 审核通过后，系统自动计算手续费并更新状态
4. 管理员可以填写付款流水号用于记录实际打款信息
5. 用户收到提现金额

### 提现设置

提现功能支持以下设置：

1. **提现金额限制**：设置每笔提现的最小金额和最大金额限制，超出限制范围的提现申请将被拒绝
2. **会员等级限制**：可以限制指定会员等级才能进行提现操作，如SVIP会员、VIP会员等
3. **提现手续费**：可以设置提现的手续费率，例如10%表示提现金额的10%将被收取为手续费
4. **每月提现次数限制**：可以限制用户每月最多可以提现的次数

### API接口

#### 1. 提现列表查询
- 接口地址：`/api/admin/finance/apply/list`
- 请求方式：`GET`
- 参数：
  - `keywords`: 搜索关键词，可搜索微信号/姓名/支付宝账号/银行卡号/失败原因
  - `queryType`: 查询类型，1=提现审核（查询待审核、审核通过、已拒绝），2=提现记录（查询提现成功、打款失败）
  - `extractType`: 提现方式，bank=银行卡，alipay=支付宝，weixin=微信
  - `dateLimit`: 时间范围
  - `page`: 页码
  - `limit`: 每页条数

#### 2. 提现审核
- 接口地址：`/api/admin/finance/apply/apply`
- 请求方式：`POST`
- 参数：
  - `id`: 提现申请ID
  - `status`: 审核状态，-1=未通过，1=通过
  - `backMessage`: 驳回原因（当status=-1时必填）

#### 3. 批量审核提现申请
- 接口地址：`/api/admin/finance/apply/batch`
- 请求方式：`POST`
- 参数：
  - `ids`: 提现申请ID列表，逗号分隔
  - `status`: 审核状态，-1=未通过，1=通过
  - `backMessage`: 驳回原因（当status=-1时必填）

#### 4. 更新付款流水号
- 接口地址：`/api/admin/finance/apply/payment`
- 请求方式：`POST`
- 参数：
  - `id`: 提现申请ID
  - `paymentNo`: 付款流水号

#### 5. 提现统计
- 接口地址：`/api/admin/finance/apply/balance`
- 请求方式：`POST`
- 参数：
  - `dateLimit`: 时间限制，格式可以是today,yesterday,lately7,lately15,lately30,month,year或具体日期范围

### 提现记录字段说明

| 字段名 | 说明 | 类型 |
|-------|-----|------|
| id | 提现记录ID | int |
| uid | 用户ID | int |
| real_name | 提现用户姓名 | string |
| extract_type | 提现方式(alipay=支付宝,weixin=微信) | string |
| bank_code | 银行卡号 | string |
| bank_address | 开户行地址 | string |
| alipay_code | 支付宝账号 | string |
| extract_price | 提现金额 | decimal |
| fee | 提现手续费 | decimal |
| mark | 备注 | string |
| balance | 用户余额 | decimal |
| fail_msg | 拒绝原因 | string |
| payment_no | 付款流水号 | string |
| status | 状态(-1=已拒绝,0=待审核,1=审核通过,2=提现成功,3=打款失败) | int |
| wechat | 微信号 | string |
| create_time | 申请时间 | datetime |
| update_time | 更新时间 | datetime |
| fail_time | 拒绝时间 | datetime |
| approve_time | 审核通过时间 | datetime |
| complete_time | 提现完成时间 | datetime |
| bank_name | 银行名称 | string |
| qrcode_url | 收款二维码地址 | string |
| approve_admin_account | 审核管理员账号 | string |
| approve_admin_name | 审核管理员姓名 | string |

### 提现流程

1. 用户在小程序或APP中申请提现
2. 管理员在后台审核提现申请
   - 审核通过：系统记录审核通过时间、审核人信息，状态变更为"审核通过"
   - 审核拒绝：系统记录拒绝时间、拒绝原因、审核人信息，状态变更为"已拒绝"，并退还提现金额到用户账户
3. 系统调用小程序提现接口进行打款
4. 提现接口调用成功后：
   - 更新提现状态为"提现成功"
   - 记录提现完成时间
   - 扣减用户可提现金额
   - **自动创建用户账单记录**
5. 提现接口调用失败时：
   - **保持"审核通过"状态不变**
   - 记录错误日志
   - 管理员可以重新尝试或手动处理
6. 如需要，管理员可手动将状态更新为"打款失败"

### 审核人信息记录

系统在提现审核时会自动记录以下审核人信息：
- **审核管理员账号** (`approve_admin_account`)：执行审核操作的管理员账号
- **审核管理员姓名** (`approve_admin_name`)：执行审核操作的管理员真实姓名

这些信息在审核通过或拒绝时自动记录，用于审计和追溯审核操作。

### 用户账单记录

当提现接口调用成功后，系统会自动创建用户账单记录：

**账单字段说明：**
- **用户ID** (`uid`)：提现用户的ID
- **关联ID** (`link_id`)：关联的提现记录ID
- **收支类型** (`pm`)：0=支出（提现是支出）
- **账单标题** (`title`)：提现
- **明细种类** (`category`)：now_money（现金账单）
- **明细类型** (`type`)：extract（提现类型）
- **金额** (`number`)：提现金额
- **余额** (`balance`)：用户当前余额
- **备注** (`mark`)：提现成功，金额XX元
- **状态** (`status`)：1=有效

这样确保了每笔成功的提现都有完整的账单记录，方便用户查询和系统审计。

### 提现状态流转

- **待审核(0)** → **审核通过(1)** → **提现成功(2)**：正常提现流程
- **待审核(0)** → **已拒绝(-1)**：提现被拒绝
- **审核通过(1)** → **打款失败(3)**：提现审核通过但打款失败

### 提现时间节点

- **申请时间(create_time)**：用户提交提现申请的时间
- **审核通过时间(approve_time)**：管理员审核通过提现申请的时间
- **提现完成时间(complete_time)**：提现成功打款到账的时间
- **拒绝时间(fail_time)**：提现申请被拒绝的时间

## 销售数据功能

### 功能概述

销售数据功能提供了完整的销售数据统计和分析能力，支持按会员等级、时间范围、手机号等多维度查询销售数据，帮助管理员了解各会员的销售表现。

#### 1. 功能特性

- **多维度筛选**：支持按手机号、时间范围、会员等级进行筛选
- **销售统计**：统计销售金额、订单数量、待售数量、已售数量、自购金额等关键指标
- **明细查看**：支持查看每个会员的详细销售明细
- **数据导出**：支持将销售数据导出为Excel文件
- **实时更新**：数据实时更新，确保统计准确性

#### 2. 数据字段说明

| 字段名 | 说明 | 计算方式 |
| ------ | ---- | -------- |
| SVIP会员 | 会员昵称和手机号 | 从用户表获取 |
| 销售金额 | 该会员的总销售金额 | 统计所有已支付订单的实付金额 |
| 订单数量 | 该会员的总订单数量 | 统计所有订单数量 |
| 待结算返现 | 待结算的佣金返现金额 | 统计创建状态和冻结期状态的佣金记录 |
| 已结算返现 | 已结算的佣金返现金额 | 统计完成状态的佣金记录 |
| 自购金额 | 该会员自己购买的金额 | 统计会员自己下单的金额 |

#### 3. 销售数据API接口

##### 3.1 获取销售数据列表
```
GET /api/admin/sales/data/list
```
参数说明：
- `keyword`: 关键字，可选，同时支持手机号和昵称模糊查询
- `mobile`: 手机号，可选，支持模糊查询
- `startTime`: 开始时间，格式：yyyy-MM-dd HH:mm:ss
- `endTime`: 结束时间，格式：yyyy-MM-dd HH:mm:ss
- `memberLevel`: 会员等级，可选（1-VIP会员，2-SVIP会员）
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "uid": 123,
        "nickname": "张三",
        "phone": "13800138000",
        "memberLevel": "SVIP会员",
        "salesAmount": 14829.34,
        "orderCount": 43,
        "pendingBrokerageAmount": 150.00,
        "settledBrokerageAmount": 1852.50,
        "selfPurchaseAmount": 4523.00
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

##### 3.2 获取销售数据统计
```
GET /api/admin/sales/data/statistics
```
参数说明：
- `keyword`: 关键字，可选，同时支持手机号和昵称模糊查询
- `mobile`: 手机号，可选
- `startTime`: 开始时间，可选
- `endTime`: 结束时间，可选
- `memberLevel`: 会员等级，可选

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalSalesAmount": 1482934.00,
    "totalOrderCount": 4300,
    "totalPendingBrokerageAmount": 15000.00,
    "totalSettledBrokerageAmount": 185200.50,
    "totalSelfPurchaseAmount": 452300.00,
    "totalMemberCount": 150
  }
}
```

##### 3.3 获取会员销售明细
```
GET /api/admin/sales/data/detail/{uid}
```
参数说明：
- `uid`: 用户ID，必填
- `startTime`: 开始时间，可选
- `endTime`: 结束时间，可选
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "orderId": "************",
        "orderTime": "2023-12-01 10:30:00",
        "orderAmount": 299.00,
        "payAmount": 279.00,
        "orderStatus": "已完成",
        "productName": "商品名称",
        "quantity": 2
      }
    ],
    "total": 50,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

#### 4. 使用说明

1. **访问销售数据管理**
   - 登录管理后台
   - 进入"会员管理" -> "销售数据"

2. **筛选销售数据**
   - 输入手机号进行精确或模糊查询
   - 选择时间范围查看特定时期的销售数据
   - 选择会员等级筛选特定等级的会员数据

3. **查看销售明细**
   - 点击"查看明细"按钮查看该会员的详细订单信息
   - 支持按时间范围筛选明细数据

4. **数据导出**
   - 点击"导出"按钮将当前筛选条件下的数据导出为Excel文件
   - 导出文件包含所有显示的字段信息

#### 5. 技术实现

- **后端技术**：Spring Boot + MyBatis-Plus
- **数据库**：基于现有的订单表和用户表进行统计查询
- **缓存优化**：对频繁查询的统计数据进行Redis缓存
- **性能优化**：使用索引优化查询性能，支持分页查询
- **导出功能**：使用Apache POI实现Excel导出
- **权限控制**：基于Spring Security实现细粒度权限控制

#### 6. 部署说明

##### 6.1 数据库初始化
```bash
# 1. 创建销售数据索引
mysql -u root -p your_database < sql/create_sales_data_view.sql

# 2. 插入测试数据（可选）
mysql -u root -p your_database < sql/insert_test_sales_data.sql
```

##### 6.2 权限配置
销售数据功能使用现有的权限系统，管理员可以通过后台管理界面为相应角色分配权限：
1. 登录管理后台
2. 进入"系统管理" -> "角色管理"
3. 为相应角色分配销售数据相关权限：
   - `admin:sales:data:list` - 查看销售数据列表
   - `admin:sales:data:statistics` - 查看销售数据统计
   - `admin:sales:data:detail` - 查看销售明细
   - `admin:sales:data:export` - 导出销售数据

##### 6.3 功能测试
```bash
# 执行API测试脚本
chmod +x scripts/test_sales_data_api.sh
./scripts/test_sales_data_api.sh http://localhost:8080 your_admin_token
```

#### 7. 文件结构

```
### 返现明细功能增强详情

#### 1. 新增字段说明
在`SalesDataDetailResponse`类中新增以下字段：
- `commissionAmount`: 返现金额，显示该订单对应的佣金返现总额
- `buyerName`: 买家名称，显示下单用户的昵称
- `buyerLevel`: 买家等级，显示下单用户的会员等级名称

#### 2. 数据获取逻辑
- **返现金额获取**：通过订单号关联查询`user_commission_record`表，汇总该订单的所有返现记录
- **买家信息获取**：通过订单的用户ID关联查询`user`表，获取用户昵称和等级信息
- **等级名称转换**：使用`MemberLevelConstants`工具类将等级数值转换为对应的等级名称

#### 3. 性能优化措施
- 为`user_commission_record.link_id`字段添加索引，优化返现记录查询
- 为`user_commission_record.type`字段添加索引，优化类型筛选
- 为`user.level`字段添加索引，优化等级查询
- 为`store_order.uid`字段添加索引，优化用户订单查询

#### 4. 接口返回示例
```json
{
  "orderId": 12345,
  "orderNo": "************",
  "orderTime": "2025-07-16 10:30:00",
  "orderAmount": 299.00,
  "payAmount": 299.00,
  "orderStatus": "已完成",
  "productName": "商品名称",
  "quantity": 2,
  "productImage": "商品图片URL",
  "payType": "微信支付",
  "isSelfPurchase": 1,
  "commissionAmount": 29.90,
  "buyerName": "张三",
  "buyerLevel": "VIP会员"
}
```

销售数据功能相关文件：
├── core/src/main/java/com/ylpz/core/
│   ├── common/request/SalesDataRequest.java          # 销售数据查询请求对象
│   ├── common/response/SalesDataResponse.java        # 销售数据响应对象
│   ├── common/response/SalesDataStatisticsResponse.java # 销售数据统计响应对象
│   ├── common/response/SalesDataDetailResponse.java  # 销售数据明细响应对象（已增强）
│   ├── service/SalesDataService.java                 # 销售数据服务接口
│   └── service/impl/SalesDataServiceImpl.java        # 销售数据服务实现类（已增强）
├── admin/src/main/java/com/ylpz/admin/
│   └── controller/SalesDataController.java           # 销售数据控制器
├── admin/src/test/java/com/ylpz/admin/
│   └── controller/SalesDataControllerTest.java       # 销售数据控制器测试类
├── sql/
│   ├── create_sales_data_view.sql                    # 创建销售数据索引
│   └── 20250716_add_commission_buyer_info_to_sales_detail.sql # 返现明细功能增强优化脚本
│   └── insert_test_sales_data.sql                    # 插入测试数据
├── docs/
│   └── 销售数据功能使用说明.md                        # 功能使用说明文档
└── scripts/
    └── test_sales_data_api.sh                        # API测试脚本
```

## 排行榜单功能

### 功能概述

排行榜单功能是基于销售数据的排名统计和奖励发放系统，支持周榜、月榜、季度榜、年度榜等多种排行周期，为优秀销售人员提供奖励激励机制。该功能与会员参数配置中的奖励金设置紧密结合，根据配置的奖励规则自动计算和发放奖励。

#### 1. 功能特性

- **多周期排行**：支持周榜、月榜、季度榜、年度榜等不同时间维度的排行统计
- **自动排名**：根据销售金额自动计算排名，支持实时更新
- **奖励发放**：支持手动和自动发放排行榜奖励金
- **奖励状态管理**：跟踪奖励发放状态（待发放、已发放）
- **入榜人数统计**：统计每期排行榜的参与人数
- **历史记录**：保存历史排行榜数据，支持查看往期排行
- **同期对比**：支持查看累计销售额和已计发奖励金额统计

#### 2. 排行榜数据字段说明

| 字段名 | 说明 | 计算方式 |
| ------ | ---- | -------- |
| 排名 | 在当期排行榜中的名次 | 按销售金额降序排列 |
| 时间 | 排行榜统计周期 | 如"2025年1月27日-2月5日" |
| 销售额 | 该周期内的总销售金额 | 统计周期内所有已支付订单金额 |
| 入榜人数 | 参与排行的总人数 | 统计周期内有销售记录的用户数 |
| 奖励状态 | 奖励发放状态 | 待发放、已发放等 |
| 奖励发放 | 发放方式和时间 | 自动发放、手动发放及具体时间 |

#### 3. 排行榜API接口

##### 3.1 获取排行榜列表
```
GET /api/admin/ranking/list
```
参数说明：
- `rankType`: 排行类型，必填（week-周榜，month-月榜，quarter-季度榜，year-年度榜）
- `year`: 年份，可选，默认当前年份
- `page`: 页码，默认值1
- `limit`: 每页数量，默认值10

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "list": [
      {
        "id": 1,
        "rankType": "week",
        "rankPeriod": "2025年1月27日-2月5日",
        "startDate": "2025-01-27",
        "endDate": "2025-02-05",
        "totalSalesAmount": 48596.39,
        "participantCount": 36,
        "rewardStatus": "待发放",
        "rewardAmount": 500.00,
        "isAutoReward": true,
        "rewardTime": null,
        "createTime": "2025-01-27 00:00:00"
      }
    ],
    "total": 100,
    "pageNum": 1,
    "pageSize": 10
  }
}
```

##### 3.2 获取排行榜统计数据
```
GET /api/admin/ranking/statistics
```
参数说明：
- `rankType`: 排行类型，可选
- `year`: 年份，可选

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "totalSalesAmount": 1354465.23,
    "totalRewardAmount": 504000.00,
    "currentPeriodSales": 48596.39,
    "pendingRewardCount": 5,
    "completedRewardCount": 15
  }
}
```

##### 3.3 获取排行榜详细排名
```
GET /api/admin/ranking/detail/{rankingId}
```
参数说明：
- `rankingId`: 排行榜ID，必填

返回数据示例：
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "rankingInfo": {
      "id": 1,
      "rankType": "week",
      "rankPeriod": "2025年1月27日-2月5日",
      "totalSalesAmount": 48596.39,
      "participantCount": 36
    },
    "rankList": [
      {
        "rank": 1,
        "uid": 123,
        "nickname": "张三",
        "phone": "13800138000",
        "salesAmount": 8596.39,
        "rewardAmount": 500.00,
        "rewardStatus": "已发放"
      }
    ]
  }
}
```

##### 3.4 手动发放排行榜奖励
```
POST /api/admin/ranking/reward/distribute
```
参数说明：
- `rankingId`: 排行榜ID，必填
- `rewardConfig`: 奖励配置，可选，不传则使用系统默认配置

##### 3.5 批量发放排行榜奖励
```
POST /api/admin/ranking/reward/batch
```
参数说明：
- `rankingIds`: 排行榜ID列表，必填
- `rewardConfig`: 奖励配置，可选

#### 4. 排行榜数据表结构

```sql
-- 排行榜主表
CREATE TABLE `ranking_leaderboard` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '排行榜ID',
  `rank_type` varchar(20) NOT NULL COMMENT '排行类型：week-周榜，month-月榜，quarter-季度榜，year-年度榜',
  `rank_period` varchar(100) NOT NULL COMMENT '排行周期描述，如"2025年1月27日-2月5日"',
  `start_date` date NOT NULL COMMENT '统计开始日期',
  `end_date` date NOT NULL COMMENT '统计结束日期',
  `total_sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总销售金额',
  `participant_count` int(11) NOT NULL DEFAULT '0' COMMENT '参与人数',
  `reward_status` varchar(20) NOT NULL DEFAULT '待发放' COMMENT '奖励状态：待发放、已发放、已取消',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '奖励总金额',
  `is_auto_reward` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否自动发放奖励：1-是，0-否',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_rank_type` (`rank_type`),
  KEY `idx_start_date` (`start_date`),
  KEY `idx_reward_status` (`reward_status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜主表';

-- 排行榜明细表
CREATE TABLE `ranking_leaderboard_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `ranking_id` int(11) NOT NULL COMMENT '排行榜ID',
  `uid` int(11) NOT NULL COMMENT '用户ID',
  `rank` int(11) NOT NULL COMMENT '排名',
  `sales_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '销售金额',
  `order_count` int(11) NOT NULL DEFAULT '0' COMMENT '订单数量',
  `reward_amount` decimal(10,2) DEFAULT '0.00' COMMENT '奖励金额',
  `reward_status` varchar(20) DEFAULT '待发放' COMMENT '奖励状态：待发放、已发放、无奖励',
  `reward_time` datetime DEFAULT NULL COMMENT '奖励发放时间',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_ranking_id` (`ranking_id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_rank` (`rank`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排行榜明细表';
```

#### 5. 奖励规则配置

排行榜功能完全基于`system_member_param_config`表的配置，支持灵活的配置管理：

1. **配置表结构**：
   - 配置类型：`config_type = 4`（奖励金设置）
   - 来源类型：`source_type`用于区分不同的配置项
   - 数值字段：`number`存储具体的配置值
   - 显示数量：`rank_display_count`存储榜单显示数量

2. **入榜门槛值配置**：
   ```sql
   -- 通过source_type区分不同排行榜类型的门槛值
   source_type = 'week排行榜门槛'    -> number = 10000   (周榜门槛10000元)
   source_type = 'month排行榜门槛'   -> number = 30000   (月榜门槛30000元)
   source_type = 'quarter排行榜门槛' -> number = 100000  (季度榜门槛100000元)
   source_type = 'year排行榜门槛'    -> number = 500000  (年榜门槛500000元)
   ```

3. **显示设置配置**：
   ```sql
   -- 榜单显示数量通过rank_display_count字段配置
   rank_display_count = 20  (显示TOP20)
   ```

4. **奖励金额配置**：
   ```sql
   -- 周榜奖励配置
   source_type = '周排行榜奖励', sort = 3, number = 500   (第1名500元)
   source_type = '周排行榜奖励', sort = 4, number = 300   (第2名300元)
   source_type = '周排行榜奖励', sort = 5, number = 200   (第3名200元)

   -- 月榜奖励配置
   source_type = '月排行榜奖励', sort = 6, number = 1000  (第1名1000元)
   source_type = '月排行榜奖励', sort = 7, number = 600   (第2名600元)
   source_type = '月排行榜奖励', sort = 8, number = 400   (第3名400元)

   -- 季度榜奖励配置
   source_type = '季度排行榜奖励', sort = 9, number = 2000   (第1名2000元)
   source_type = '季度排行榜奖励', sort = 10, number = 1200  (第2名1200元)
   source_type = '季度排行榜奖励', sort = 11, number = 800   (第3名800元)

   -- 年榜奖励配置
   source_type = '年排行榜奖励', sort = 12, number = 5000  (第1名5000元)
   source_type = '年排行榜奖励', sort = 13, number = 3000  (第2名3000元)
   source_type = '年排行榜奖励', sort = 14, number = 2000  (第3名2000元)
   ```

5. **配置特点**：
   - 所有配置都存储在`system_member_param_config`表中
   - 支持通过管理后台动态修改，无需重启系统
   - 通过`sort`字段控制奖励排名顺序
   - 低于门槛值的用户不会显示在排行榜中
   - 超出显示数量的排名不会被记录
   - 超出奖励人数范围的排名不会获得奖励
   - 奖励人数由对应排行榜类型的配置记录数量决定

6. **配置管理**：
   - 可以通过会员参数配置管理界面修改所有配置
   - 添加新的奖励名次只需插入新的配置记录
   - 修改门槛值和奖励金额只需更新对应的`number`字段
   - 修改显示数量只需更新`rank_display_count`字段

#### 6. 定时任务

系统提供自动生成排行榜的定时任务：

- **执行时间**：每天凌晨1点
- **生成规则**：
  - 每周一生成上周周榜
  - 每月1日生成上月月榜
  - 每季度第一天生成上季度榜
  - 每年1月1日生成上年年榜

#### 7. 使用说明

排行榜单功能主要用于：
1. 激励销售人员提升业绩表现
2. 定期统计和发布销售排行榜
3. 自动或手动发放排行榜奖励金
4. 查看历史排行榜数据和趋势分析
5. 为销售团队提供竞争激励机制

该功能与会员参数配置中的奖励金设置紧密结合，根据配置的奖励规则自动计算和发放奖励。

## 会员上下级关系及推广记录管理系统

### 1. 系统概述

本系统实现了完整的会员上下级关系管理和推广记录查询功能，主要包含以下核心功能：

1. 会员上下级关系管理
2. 推广记录查询与历史追踪
3. 会员等级变更记录
4. SVIP自动返利功能

### 2. 数据结构

#### 主要表结构

1. **user表** - 用户基本信息表
   - `uid`: 用户ID
   - `spread_uid`: 推广员ID (上级分销员)
   - `spread_time`: 成为推广员时间
   - `is_promoter`: 是否为推广员(分销员)
   - `level`: 会员等级

2. **user_brokerage_record表** - 用户佣金记录表
   - `id`: 记录ID
   - `uid`: 用户ID
   - `link_id`: 关联ID (订单号、提现ID等)
   - `link_type`: 关联类型
   - `type`: 类型 (1-增加, 2-扣减)
   - `price`: 佣金金额
   - `balance`: 操作后余额
   - `status`: 状态 (1-订单创建, 2-冻结期, 3-完成, 4-失效, 5-提现申请)
   - `thaw_time`: 解冻时间
   - `brokerage_level`: 分销等级 (1-一级, 2-二级)
   - `change_message`: 变更信息

3. **system_user_level表** - 会员等级表
   - `id`: 等级ID
   - `name`: 等级名称（如SVIP会员、VIP会员、普通会员）
   - `discount`: 折扣比例
   - `discount_enabled`: 是否启用折扣
   - `experience`: 所需成长值
   - `icon`: 等级图标
   - `auto_upgrade`: 是否自动升级

4. **system_member_param_config表** - 会员参数配置表
   - 成长值设置
   - 提现设置
   - 佣金返现设置
   - 奖励金设置

### 3. 会员上下级关系

会员上下级关系是基于推广关系建立的：
- 用户通过另一用户的推广链接注册或下单时，建立上下级关系
- 下级用户的`spread_uid`字段存储上级用户的ID
- 关系绑定方式可通过系统配置进行设置（新用户绑定/所有用户可绑定）
- 一旦绑定关系建立，后续该用户的所有符合条件的订单都将为上级带来佣金收益

### 4. 变更记录管理

系统记录每一次等级变更和佣金返利的历史记录：
- 等级变更：记录用户从一个等级升级到另一个等级的完整历史
- 佣金记录：详细记录每一笔佣金的来源、金额、状态等信息
- 变更信息：针对每一次变更，记录详细的变更原因和相关信息

### 5. 推广记录查询功能

如图所示，系统提供了完整的推广记录查询功能：
1. 会员基本信息展示
2. 变更记录时间线显示
3. 当前所属上级展示
4. 历史变更记录详情查看
5. 变更类型及原因记录

### 6. SVIP自动返利功能

SVIP会员享有特殊的自动返利特权：
- 当SVIP会员购买商品时，系统可配置为自动返还一定比例的佣金
- 返还比例通过`system_member_param_config`表中的配置决定
- 返还记录会在用户佣金记录中显示，并标记为"自购返利"

### 7. 功能实现要点

1. **记录变更历史**
   - 每次用户的上级关系发生变更时，生成详细的变更记录
   - 记录变更前后的值、变更原因、变更时间等

2. **记录查看功能**
   - 提供按时间范围、用户、变更类型等多维度的筛选功能
   - 展示变更详细信息，包括变更前后的值对比

3. **自动返利流程**
   - 判断用户是否为SVIP会员
   - 检查是否启用SVIP自动返利功能
   - 根据配置的返利比例计算返利金额
   - 直接增加用户可用佣金余额
   - 生成佣金记录，标记为自购返利

### 8. API接口设计

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/spread/record | GET | 获取用户推广记录列表 |
| /api/admin/user/spread/record/detail/:id | GET | 获取指定记录的详细信息 |
| /api/admin/user/spread/change-history/:uid | GET | 获取指定用户的上级变更历史 |
| /api/admin/user/spread/update | POST | 手动更新用户的上级关系 |
| /api/admin/member/param/config | GET | 获取会员参数配置信息 |
| /api/admin/member/param/config | POST | 更新会员参数配置信息 |

### 9. 前端展示

系统前端展示包括：
1. 会员列表页面，展示所有会员及其等级、上级信息
2. 会员详情页面，展示会员的详细信息及推广记录
3. 推广记录列表页面，可查看所有推广记录
4. 推广记录详情页面，展示推广记录的详细信息
5. 变更记录查看页面，可查看会员上级变更的历史记录

### 10. 变更记录详情展示

如图所示，变更记录详情弹窗展示以下信息：
- 用户基本信息（头像、ID、电话）
- 上级信息（上级ID、电话）
- 变更历史时间线
- 每次变更的详细信息（变更时间、变更原因、变更前后的值）
- 操作按钮（可编辑重置上级关系）

## 会员上下级关系与推广记录管理

### 功能概述

会员上下级关系与推广记录管理模块是系统中重要的分销体系功能之一，主要实现以下功能：

1. **会员上下级关系查询**：管理员可以根据用户ID、手机号、上级信息等条件查询会员上下级关系。
2. **上下级关系变更记录**：系统记录所有上下级关系的变更历史，包括初始绑定、手动调整和自动转移三种类型。
3. **上下级关系手动调整**：管理员可以手动调整用户的上级关系，系统会记录变更历史。
4. **变更历史时间线展示**：以时间线形式展示用户上下级关系的完整变更历史。
5. **SVIP自动解除上级关系**：用户升级为SVIP后，系统自动解除其上级关系，防止佣金分配冲突。
6. **SVIP自动返利**：SVIP会员可享受自购返利福利，无需上级分佣。

### 数据结构

该模块利用以下数据表：

1. **用户表(user)**：扩展了以下字段
   - `spread_change_time`: 上级关系变更时间
   - `spread_change_type`: 推广关系变更类型(0-初始绑定，1-手动调整，2-自动转移)
   - `spread_status`: 上下级关系状态(0-无效，1-有效)
   - `spread_message`: 上级关系变更原因

2. **推广关系变更记录表(user_spread_record)**：记录所有关系变更历史
   - `uid`: 用户ID
   - `spread_uid`: 变更后上级用户ID
   - `old_spread_uid`: 变更前上级用户ID
   - `change_type`: 变更类型(0-初始绑定，1-手动调整，2-自动转移)
   - `change_time`: 变更时间
   - `status`: 状态(0-无效，1-有效)
   - `change_message`: 变更原因

### 业务流程

1. **初始绑定流程**：用户通过推广链接注册或首次下单时，系统建立上下级关系并记录。

2. **手动调整流程**：管理员在后台操作调整用户上级，系统记录原上级信息和变更原因。

3. **自动转移流程**：用户升级为SVIP会员时，系统自动解除原上级关系并更新状态。

4. **SVIP自动返利流程**：SVIP会员下单后，根据配置的比例自动计算返利金额并添加到用户佣金账户。

### 界面功能

1. **会员上下级关系列表页面**：支持多条件筛选，展示用户信息、上级信息、关系状态等。

2. **变更记录详情弹窗**：展示用户详情、当前上级信息，以时间线形式展示历史变更记录。

3. **手动调整上级弹窗**：支持设置新上级、变更原因和关系状态。

### 业务约束

1. 上级ID不能是自己
2. 上级必须存在且为有效用户
3. SVIP会员不能有上级(自动解除)
4. 上下级关系不能形成循环

## 排行榜奖励配置

### 排行榜入榜门槛值配置

系统支持设置周排行榜、月排行榜、季度排行榜的入榜门槛值，只有销售额超过门槛值的会员才能进入排行榜。

配置项包括：
- 周排行榜入榜门槛值：设置周排行榜的入榜销售额门槛值
- 月排行榜入榜门槛值：设置月排行榜的入榜销售额门槛值
- 季度排行榜入榜门槛值：设置季度排行榜的入榜销售额门槛值

### 排行榜显示和奖励配置

系统支持设置排行榜显示的人数和奖励的人数：
- 排行榜显示数量：设置排行榜显示的人数，默认为前20名
- 排行榜奖励人数：设置排行榜奖励的人数，默认为前3名

### 排行榜自动发放奖励配置

系统支持设置各类型排行榜是否自动发放奖励：
- 周排行榜自动发放：设置周排行榜是否自动发放奖励
- 月排行榜自动发放：设置月排行榜是否自动发放奖励
- 季度排行榜自动发放：设置季度排行榜是否自动发放奖励

### 排行榜奖励金额配置

系统支持为每个排行榜类型（周榜、月榜、季度榜）的不同名次设置固定奖励金额：
- 周排行榜第一名奖励：设置周排行榜第一名的奖励金额
- 周排行榜第二名奖励：设置周排行榜第二名的奖励金额
- 周排行榜第三名奖励：设置周排行榜第三名的奖励金额
- 月排行榜第一名奖励：设置月排行榜第一名的奖励金额
- 月排行榜第二名奖励：设置月排行榜第二名的奖励金额
- 月排行榜第三名奖励：设置月排行榜第三名的奖励金额
- 季度排行榜第一名奖励：设置季度排行榜第一名的奖励金额
- 季度排行榜第二名奖励：设置季度排行榜第二名的奖励金额
- 季度排行榜第三名奖励：设置季度排行榜第三名的奖励金额

### 配置路径

可以在管理后台的"设置 > 会员参数设置 > 奖励金设置"中配置以上参数。

## 提现管理功能

### 1. 功能概述

系统提供完整的用户提现管理功能，包括提现申请审核和提现记录查询两个主要功能模块。这些功能通过统一的接口实现，但根据不同的查询类型参数区分业务场景。

### 2. 提现审核功能

提现审核功能主要用于管理员审核用户的提现申请，具有以下特点：
- 只显示状态为"待审核"(status=0)的提现申请记录
- 提供审核通过、拒绝功能
- 支持批量审核操作
- 显示用户基本信息、提现金额、提现方式等关键信息
- 提供驳回原因填写功能

### 3. 提现记录功能

提现记录功能主要用于查询和管理已处理的提现申请，具有以下特点：
- 显示所有非"待审核"状态的提现记录
- 包括已通过、已拒绝、提现成功、打款失败等状态的记录
- 提供付款流水号管理功能
- 支持按时间范围、提现方式、状态等条件筛选
- 显示提现手续费、实际到账金额等详细信息

### 4. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/finance/apply/list | GET | 提现列表查询接口(通过queryType区分提现审核和提现记录) |
| /api/admin/finance/apply/update | POST | 修改提现申请信息 |
| /api/admin/finance/apply/apply | POST | 提现申请审核(通过/拒绝) |
| /api/admin/finance/apply/batch | POST | 批量审核提现申请 |
| /api/admin/finance/apply/payment | POST | 更新付款流水号 |
| /api/admin/finance/apply/info | GET | 获取提现记录详情 |
| /api/admin/finance/apply/balance | POST | 获取提现统计数据 |

### 5. 查询参数说明

提现列表查询接口(/api/admin/finance/apply/list)使用以下参数区分不同的业务场景：

- `queryType`: 查询类型
  - 1: 提现审核页面查询，只查询待审核的记录
  - 2: 提现记录页面查询，查询已审核的记录

其他常用查询参数：
- `keywords`: 搜索关键字(支持搜索用户名、提现账号等)
- `extractType`: 提现方式(bank=银行卡, alipay=支付宝, weixin=微信)
- `dateLimit`: 时间范围
- `startTime`/`endTime`: 精确的开始和结束时间
- `extractNumber`: 提现编号

## SVIP查询功能

### 功能概述
SVIP查询功能允许管理员快速查找和筛选系统中的SVIP会员，支持多种条件筛选，包括用户名/手机号和开通SVIP时间范围等。

### 查询条件
- **用户名/手机号**：支持模糊匹配用户名或手机号
- **开通SVIP时间**：支持选择时间范围进行筛选
- **其他条件**：支持更多高级筛选条件

### 显示内容
SVIP会员列表显示以下信息：
- 会员基本信息（ID、昵称、头像、手机号）
- 会员等级（SVIP）
- 销售数据（销售金额、订单数量）
- 佣金数据（待结算返现、已结算返现）
- 自购金额
- 升级SVIP时间
- 创建时间

### 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/svip/list | GET | 获取SVIP会员列表 |

### 请求参数
- `mobile`: 手机号，支持模糊查询
- `startTime`: 开始时间，格式：yyyy-MM-dd HH:mm:ss
- `endTime`: 结束时间，格式：yyyy-MM-dd HH:mm:ss
- `memberLevel`: 会员等级，固定值2（SVIP会员）
- `nickname`: 用户昵称，支持模糊查询
- `uid`: 用户ID
- `page`: 页码，默认1
- `limit`: 每页记录数，默认20

## 微页面管理功能

### 1. 功能概述

微页面管理功能是一个灵活的页面内容管理系统，允许管理员创建和管理自定义页面，如隐私政策、用户协议、会员权益介绍等。系统支持两种内容编辑模式：图片模式和富文本模式。

### 2. 主要功能

- **微页面列表管理**：查看所有微页面，支持按名称和状态筛选
- **微页面创建与编辑**：创建新的微页面或编辑现有微页面
- **内容编辑**：支持图片模式和富文本模式两种内容编辑方式
- **页面状态管理**：控制页面是否发布，未发布的页面对用户不可见
- **页面删除**：删除不需要的微页面

### 3. 内容编辑模式

系统支持两种内容编辑模式：

1. **图片模式**：适用于纯图片展示的页面，可上传多张图片组成页面内容
2. **富文本模式**：适用于需要文字、图片混排的页面，提供所见即所得的编辑器

### 4. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/micro-page/list | GET | 分页获取微页面列表 |
| /api/admin/micro-page/info/{id} | GET | 获取微页面详情 |
| /api/admin/micro-page/save | POST | 保存微页面 |
| /api/admin/micro-page/update | POST | 更新微页面 |
| /api/admin/micro-page/delete/{id} | POST | 删除微页面 |
| /api/admin/micro-page/status/{id} | POST | 更新微页面状态 |

### 5. 数据库表结构

系统使用一个表来存储微页面相关数据：

**system_micro_page**：存储微页面信息和内容
- id: 主键ID
- page_name: 页面名称
- page_status: 页面状态（0-未发布，1-已发布）
- content_type: 内容类型（1-图片模式，2-富文本模式）
- content: 页面内容，富文本模式存储HTML内容
- image_urls: 图片URL列表，JSON格式
- last_update_user: 最后更新人
- create_time: 创建时间
- update_time: 更新时间
- is_deleted: 是否删除

## 首页统计接口增强

系统对首页统计接口进行了增强，新增以下数据项：

### 1. 新增数据项
- **待发货订单数量**：显示当前需要发货的订单数量
- **待处理售后数量**：显示当前需要处理的售后申请数量
- **待回复评价数量**：显示当前需要商家回复的评价数量
- **今日退款金额**：当日退款的总金额
- **昨日退款金额**：昨日退款的总金额
- **今日佣金返现**：当日产生的佣金返现总额
- **访问-支付转化率**：今日用户访问量与下单数的转化百分比

### 2. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /admin/statistics/home/<USER>

### 3. 返回字段说明
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| sales | Object | 今日销售额 |
| yesterdaySales | Object | 昨日销售额 |
| pageviews | Object | 今日访问量 |
| yesterdayPageviews | Object | 昨日访问量 |
| orderNum | Object | 今日订单量 |
| yesterdayOrderNum | Object | 昨日订单量 |
| newUserNum | Object | 今日新增用户 |
| yesterdayNewUserNum | Object | 昨日新增用户 |
| pendingShipNum | Object | 待发货订单数量 |
| pendingAfterSaleNum | Object | 待处理售后数量 |
| pendingReplyNum | Object | 待回复评价数量 |
| todayRefundAmount | Object | 今日退款金额 |
| yesterdayRefundAmount | Object | 昨日退款金额 |
| todayBrokerageAmount | Object | 今日佣金返现 |
| visitPayRate | Object | 访问-支付转化率 |

### 4. 使用场景
- 管理员通过此接口可以在首页直观地了解店铺的各项关键指标
- 特别是待处理的业务（发货、售后、评价）数量，便于及时处理
- 退款和佣金数据有助于了解资金流动情况
- 转化率数据有助于评估营销效果

## 店铺信息管理功能

### 1. 功能概述

店铺信息管理功能是针对系统中店铺基本信息的管理模块，包含店铺名称、店铺分类、小程序预览图logo、小程序登录logo等信息的配置和管理。

### 2. 店铺信息管理

店铺信息管理功能包括：
- 获取店铺信息
- 更新店铺信息

### 3. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/info/get | GET | 获取店铺信息 |
| /api/admin/store/info/update | POST | 更新店铺信息 |

### 4. 数据表设计

#### 店铺信息表 (store_info)

| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | int | 主键ID |
| store_name | varchar(100) | 店铺名称 |
| store_category | varchar(100) | 店铺分类 |
| mini_app_logo | varchar(255) | 小程序预览图logo |
| mini_app_login_logo | varchar(255) | 小程序登录logo |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### 5. 请求参数说明

#### 更新店铺信息请求参数
- `storeName`: 店铺名称，不能为空，最大长度100个字符
- `storeCategory`: 店铺分类，不能为空，最大长度100个字符

## 小程序首页管理设置

### 功能概述

小程序首页管理设置功能允许管理员自定义小程序首页的展示内容，包括Banner轮播图、热销推荐产品和人气爆款产品。

### 主要功能

#### 1. Banner轮播图设置
- 支持添加、编辑、删除、排序Banner轮播图
- 支持设置图片地址、跳转类型（小程序内跳转/H5页面跳转）和跳转地址
- 支持最多上传5张轮播图
- 支持调整轮播图顺序

#### 2. 热销推荐设置
- 支持添加、编辑、删除、排序热销推荐产品
- 支持设置产品ID、热销关键词、排序
- 支持设置首页显示数量（默认6个）
- 支持调整产品顺序

#### 3. 人气爆款设置
- 支持添加、编辑、删除、排序人气爆款产品
- 支持设置产品ID、爆款标签、排序
- 支持设置首页显示数量（默认6个）
- 支持调整产品顺序

### 数据表结构

#### Banner轮播图表 (system_mini_banner)
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | bigint(20) | 主键ID |
| image_url | varchar(255) | 图片地址 |
| jump_type | tinyint(1) | 跳转类型：1-商品，2-活动，3-微页面 |
| jump_url | varchar(255) | 跳转地址 |
| sort | int(11) | 排序 |
| status | tinyint(1) | 状态：0-禁用，1-启用 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint(1) | 是否删除：0-未删除，1-已删除 |

#### 热销推荐表 (system_mini_hot_product)
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | bigint(20) | 主键ID |
| product_id | bigint(20) | 产品ID |
| hot_keyword | varchar(50) | 热销关键词 |
| sort | int(11) | 排序 |
| status | tinyint(1) | 状态：0-禁用，1-启用 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint(1) | 是否删除：0-未删除，1-已删除 |

#### 人气爆款表 (system_mini_popular_product)
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | bigint(20) | 主键ID |
| product_id | bigint(20) | 产品ID |
| popular_tag | varchar(30) | 爆款标签 |
| sort | int(11) | 排序 |
| status | tinyint(1) | 状态：0-禁用，1-启用 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_deleted | tinyint(1) | 是否删除：0-未删除，1-已删除 |

#### 配置表 (system_mini_config)
| 字段名 | 类型 | 描述 |
| ------ | ---- | ---- |
| id | bigint(20) | 主键ID |
| config_key | varchar(50) | 配置键 |
| config_value | varchar(255) | 配置值 |
| config_desc | varchar(255) | 配置描述 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 订单设置功能

系统提供了完善的订单设置功能，管理员可以通过后台界面配置订单相关的参数，主要包括以下三部分：

### 1. 交易设置

- **付款减库存预占时间**：拍下后，内未付款，库存自动解锁，方便其他消费者下单，单位为分钟。
- **待付款订单自动取消时间**：拍下未付款订单，自动取消订单，单位为分钟。
- **发货后自动确认收货时间**：发货后多少天自动确认收货，单位为天。
- **买家申请售后期限**：买家在多少天内可以申请售后，超过期限后不支持申请售后，单位为天。

### 2. 打单设置

- **支持打单功能**：可以开启或关闭订单打印功能。
- **物流查询ApiKey**：设置物流查询接口的API密钥，用于查询物流信息。

### 3. 地址管理

- **售后收货地址**：设置商家用于接收客户退回商品的收货地址，包括收件人姓名、联系方式、详细地址等信息。
- **发货地址**：设置商家用于发货的发货地址，包括发件人姓名、联系方式、详细地址等信息。

### 4. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/order/setting/group | GET | 获取所有订单设置（按模块分组） |
| /api/admin/store/order/setting/list | GET | 获取所有订单设置列表 |
| /api/admin/store/order/setting/list/{moduleName} | GET | 根据模块名称获取订单设置列表 |
| /api/admin/store/order/setting/batch/save/{moduleName} | POST | 按模块批量保存订单设置 |
| /api/admin/store/order/setting/saveForm | POST | 保存整个订单设置表单 |

### 5. 请求参数说明

#### 获取订单设置接口

不需要请求参数，直接GET请求即可。

返回数据格式：
```json
{
  "tradeConfigList": [
    {
      "id": 1,
      "moduleName": "交易设置",
      "configCode": "order_cancel_time",
      "configName": "付款减库存预占时间",
      "description": "拍下后，内未付款，库存自动解锁，方便其他消费者下单",
      "configValue": "{\"value\":10,\"unit\":\"分钟\"}",
      "status": true,
      "sort": 0
    },
    ...
  ],
  "printConfigList": [
    {
      "id": 5,
      "moduleName": "打单设置",
      "configCode": "print_order_enabled",
      "configName": "支持打单功能",
      "description": "可以开启或关闭订单打印功能。",
      "configValue": "{\"enabled\":1}",
      "status": true,
      "sort": 0
    },
    ...
  ],
  "addressConfigList": [
    {
      "id": 7,
      "moduleName": "地址管理",
      "configCode": "after_sale_address",
      "configName": "售后收货地址",
      "description": "用于商家接收客户退回的商品",
      "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
      "status": true,
      "sort": 0
    },
    {
      "id": 8,
      "moduleName": "地址管理",
      "configCode": "shipping_address",
      "configName": "发货地址",
      "description": "用于商家发货的地址信息",
      "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
      "status": true,
      "sort": 1
    }
  ]
}
```

#### 更新订单设置接口

支持三种格式的请求参数：

**格式一：按模块分组的请求参数（推荐）**

```json
{
  "tradeConfigList": [
    {
      "moduleName": "交易设置",
      "configCode": "order_cancel_time",
      "configName": "付款减库存预占时间",
      "description": "拍下后，内未付款，库存自动解锁，方便其他消费者下单",
      "configValue": "{\"value\":15,\"unit\":\"分钟\"}",
      "status": true,
      "sort": 0
    },
    ...
  ],
  "printConfigList": [...],
  "addressConfigList": [
    {
      "moduleName": "地址管理",
      "configCode": "after_sale_address",
      "configName": "售后收货地址",
      "description": "用于商家接收客户退回的商品",
      "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
      "status": true,
      "sort": 0
    },
    {
      "moduleName": "地址管理",
      "configCode": "shipping_address",
      "configName": "发货地址",
      "description": "用于商家发货的地址信息",
      "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
      "status": true,
      "sort": 1
    }
  ]
}
```

**格式二：按模块名称批量保存**

```json
[
  {
    "moduleName": "地址管理",
    "configCode": "after_sale_address",
    "configName": "售后收货地址",
    "description": "用于商家接收客户退回的商品",
    "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
    "status": true,
    "sort": 0
  },
  {
    "moduleName": "地址管理",
    "configCode": "shipping_address",
    "configName": "发货地址",
    "description": "用于商家发货的地址信息",
    "configValue": "{\"name\":\"冯彦梅\",\"tel\":\"\",\"mobile\":\"18298944671\",\"company\":\"\",\"postCode\":\"737100\",\"country\":\"中国\",\"province\":\"甘肃省\",\"city\":\"金昌市\",\"area\":\"金川区\",\"address\":\"金谷里丽景苑6号楼下21号商铺\"}",
    "status": true,
    "sort": 1
  }
]
```

### 6. 地址管理字段说明

地址管理包含以下字段：

| 字段名 | 类型 | 说明 | 限制 |
| ------ | ---- | ---- | ---- |
| name | String | 发/收件人姓名 | 不超过64字节 |
| tel | String | 发/收件人座机号码 | 不超过32字节，若不填写则必须填写mobile |
| mobile | String | 发/收件人手机号码 | 不超过32字节，若不填写则必须填写tel |
| company | String | 发/收件人公司名称 | 不超过64字节 |
| postCode | String | 发/收件人邮编 | 不超过10字节 |
| country | String | 发/收件人国家 | 不超过64字节 |
| province | String | 发/收件人省份 | 不超过64字节 |
| city | String | 发/收件人市/地区 | 不超过64字节 |
| area | String | 发/收件人区/县 | 不超过64字节 |
| address | String | 发/收件人详细地址 | 不超过512字节 |

## 佣金返现和奖励金功能优化建议

### 当前实现分析

目前系统中的佣金返现和奖励金功能使用同一张表 `user_brokerage_record` 进行存储，通过 `link_type` 字段区分不同类型的记录：
- 佣金返现记录：`link_type = "commission"`
- 奖励金记录：`link_type` 为 `bonus_upgrade`、`bonus_recharge`、`bonus_first_order`、`bonus_rank` 等

这种设计存在以下问题：
1. 数据混合存储，使得查询和统计变得复杂
2. 前端展示时需要额外的过滤逻辑
3. 功能扩展时容易相互影响
4. 数据量增长后可能影响查询性能

### 优化建议

#### 1. 数据库层面拆分

建议将佣金返现和奖励金拆分为两个独立的表：

```sql
-- 佣金返现记录表
CREATE TABLE `user_commission_record` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `uid` int(10) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `link_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '关联订单id',
  `type` int(1) NOT NULL DEFAULT '1' COMMENT '类型：1-增加，2-扣减',
  `title` varchar(64) NOT NULL DEFAULT '' COMMENT '标题',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '剩余',
  `mark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-订单创建，2-冻结期，3-完成，4-失效（订单退款），5-提现申请',
  `frozen_time` int(3) NOT NULL DEFAULT '0' COMMENT '冻结期时间（天）',
  `thaw_time` bigint(14) NOT NULL DEFAULT '0' COMMENT '解冻时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户佣金返现记录表';

-- 奖励金记录表
CREATE TABLE `user_bonus_record` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT '记录id',
  `uid` int(10) NOT NULL DEFAULT '0' COMMENT '用户uid',
  `link_id` varchar(32) NOT NULL DEFAULT '0' COMMENT '关联id',
  `bonus_type` varchar(32) NOT NULL COMMENT '奖励类型：upgrade-升级奖励，recharge-充值奖励，first_order-首单奖励，rank-排行榜奖励',
  `source_type` varchar(64) NOT NULL DEFAULT '' COMMENT '来源类型：推广普通会员升级为VIP，周排行榜奖励等',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '金额',
  `balance` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '剩余',
  `mark` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-已发放，2-已失效',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '添加时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_bonus_type` (`bonus_type`),
  KEY `idx_source_type` (`source_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户奖励金记录表';
```

#### 2. 服务层面拆分

创建独立的服务类来处理佣金返现和奖励金：

- `UserCommissionService`: 专门处理佣金返现相关业务
- `UserBonusService`: 专门处理奖励金相关业务

#### 3. 前端展示拆分

前端页面按照功能拆分为两个独立的页面：

- 佣金返现页面：展示佣金返现记录、统计数据、佣金规则等
- 奖励金页面：展示奖励金记录、统计数据、奖励规则等

#### 4. 数据迁移方案

1. 创建新表结构
2. 编写数据迁移脚本，将原表中的数据按类型迁移到新表
3. 更新相关服务代码，使用新表进行数据操作
4. 保留原表一段时间，待系统稳定后可考虑删除

### 实施收益

1. 提高代码可维护性和可扩展性
2. 提升查询性能和统计效率
3. 前端展示更加清晰直观
4. 数据结构更符合业务逻辑

## 订单详情接口兼容性优化

系统对订单详情接口进行了兼容性优化，解决了以下问题：

### 1. 功能概述

在原有系统中，`api/admin/store/order/info` 接口的 `StoreOrderInfoVo` 中的 `info` 字段直接从数据库的 `info` 字段解析 JSON 数据。当 `info` 字段为空或者不是有效的 JSON 格式时，会导致数据不完整或者接口异常。

### 2. 优化方案

现在系统对这种情况进行了兼容处理：
- 当 `info` 字段包含有效的 JSON 数据时，仍然按原有逻辑解析
- 当 `info` 字段为空或不是有效的 JSON 格式时，系统会：
  1. 首先使用订单详情表中已有的基础信息（商品ID、名称、图片、价格等）
  2. 根据商品ID查询商品表补充缺失的信息（积分、是否单独分佣、运费模板等）
  3. 如果有规格ID，则查询商品规格表补充规格相关信息（重量、体积等）

### 3. 修改的方法

以下方法进行了兼容性优化：
- `getOrderListByOrderId` - 根据订单ID获取订单详情列表
- `getMapInId` - 根据订单ID集合获取订单详情映射
- `getVoListByOrderId` - 获取订单详情VO列表

### 4. 优化效果

- 提高了系统稳定性，避免因数据格式问题导致的接口异常
- 确保即使在历史数据不完整的情况下，也能展示完整的订单信息
- 无需修改前端代码，保持了接口的向后兼容性

### 5. 接口信息

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/order/info | GET | 获取订单详情信息（已优化） |

## 订单优惠券关联功能

系统新增了订单优惠券关联功能，用于记录订单使用的优惠券信息，并提供查询接口。

### 1. 功能概述

订单优惠券关联功能主要实现以下功能：
- 记录订单使用的优惠券信息
- 支持通过订单号查询订单使用的优惠券详情

### 2. 数据库表设计

新增了`store_coupon_order`表，用于存储订单与优惠券的关联关系：

```sql
CREATE TABLE `store_coupon_order` (
  `id` int(11) unsigned NOT NULL AUTO_INCREMENT COMMENT '订单优惠券ID',
  `order_id` varchar(32) NOT NULL COMMENT '订单号',
  `coupon_user_id` int(11) unsigned NOT NULL COMMENT '用户优惠券记录ID',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_id` (`order_id`) USING BTREE,
  KEY `idx_coupon_user_id` (`coupon_user_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='订单优惠券关联表';
```

### 3. 接口说明

| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/coupon/order/list/{orderId} | GET | 根据订单号获取订单使用的优惠券详情 |

### 4. 返回数据说明

接口返回的优惠券详情包含以下字段：
- `id`: 订单优惠券关联ID
- `orderId`: 订单号
- `couponUserId`: 用户优惠券记录ID
- `couponName`: 优惠券名称
- `couponMoney`: 优惠券金额
- `couponType`: 优惠券类型（1-满减券，2-无门槛）
- `minPrice`: 最低消费金额
- `createTime`: 创建时间

## 🏦 用户可提现金额功能

### 功能说明

用户表新增了`withdrawable_price`字段，用于记录用户可提现的金额。**只有佣金返现和奖励金可以提现**，其他类型的余额（如充值、消费退款等）不能提现。

### 数据库变更

在用户表中添加了`withdrawable_price`字段：

```sql
ALTER TABLE `user`
ADD COLUMN IF NOT EXISTS `withdrawable_price` decimal(10,2) DEFAULT 0.00 COMMENT '可提现金额' AFTER `brokerage_price`;
```

### 🔄 业务逻辑

#### 1. 可提现金额增加场景
- **佣金返现解冻**：当佣金记录达到解冻时间时，系统会将佣金记录状态更新为已完成，同时增加用户的可提现金额
- **奖励金发放**：发放奖励金时（升级奖励、首单奖励、排行榜奖励等），同时增加用户的可提现金额
- **提现申请拒绝**：提现申请被拒绝时，将提现金额返还到用户的可提现金额中

#### 2. 可提现金额减少场景
- **提现申请审核通过**：提现申请审核通过后，直接从用户的可提现金额中扣减提现金额

#### 3. 提现流程
1. **提现申请**：用户申请提现时，系统检查用户的可提现金额是否足够
2. **提现审核**：
   - **审核通过**：直接扣减用户的可提现金额，无需额外操作
   - **审核拒绝**：将提现金额返还到用户的可提现金额中

### 💰 资金类型说明
- **可提现资金**：佣金返现、奖励金
- **不可提现资金**：会员充值、消费退款、后台调整等其他类型余额

## 优惠券功能

### 优惠券基本功能
- 支持多种类型优惠券：满减券、新人专享券、会员专享券
- 支持设置优惠券使用门槛：有门槛(满减券)、无门槛
- 支持限量发放：可设置发放总数和剩余数量
- 支持领取限制：可设置每人领取次数限制
- 支持客户限制：可指定会员等级或用户标签可领取
- 支持使用范围限制：全部商品可用、指定商品可用、指定商品不可用
- 支持设置领取时间范围

### 优惠券使用时间设置
系统支持三种优惠券使用时间类型：
1. **固定时间**：设置固定的使用开始和结束时间，所有用户的优惠券在同一时间段内有效
2. **领取后天数**：设置优惠券在领取后的有效天数，从领取当天开始计算
3. **领取后增加天数**：设置领取后需等待的天数和有效天数，例如领取后3天开始生效，有效期为7天

### 优惠券自动填充使用时间功能
当用户领取优惠券或系统发放优惠券时，系统会根据优惠券的`use_time_type`(使用时间类型)自动计算并填充优惠券记录的`use_start_time`和`use_end_time`字段：

- 当`use_time_type=1`(固定时间)时：直接使用优惠券模板中设置的固定时间范围
- 当`use_time_type=2`(领取后天数)时：起始时间为当前时间，结束时间为当前时间加上设定的天数
- 当`use_time_type=3`(领取后增加天数)时：起始时间为当前时间加上等待天数，结束时间为起始时间加上有效天数

此功能确保了在所有创建用户优惠券记录的场景中（包括用户领取、后台发放、购买赠送等），优惠券的使用时间都能正确计算并记录，方便其他系统使用。

## 优惠券结构优化

为了简化优惠券与订单的关联关系，系统进行了以下优化：

### 1. 数据结构变更

- 移除了`store_coupon_order`优惠券订单关联表
- 在`store_coupon_user`表中增加了`oid`字段，直接记录订单ID
- 添加了`oid`字段的索引，提高查询性能

### 2. 功能优化

- 简化了优惠券使用流程，减少了数据库表之间的关联查询
- 优化了订单创建时优惠券的使用逻辑
- 保留了原有的优惠券查询功能，确保前端接口兼容性

### 3. 数据迁移

系统会自动将原有的`store_coupon_order`表中的数据迁移到`store_coupon_user`表中，确保数据完整性。迁移脚本位于`sql/update_coupon_user_add_oid.sql`。

### 4. 注意事项

在系统稳定运行一段时间后，可以执行`sql/drop_store_coupon_order.sql`脚本删除不再使用的`store_coupon_order`表。

## 订单状态和售后功能升级

系统对订单状态和售后功能进行了升级，主要包括以下变更：

### 1. 订单状态修改
- 订单状态由字符串类型修改为数字类型，具体对应关系如下：
  - 0：待付款（原Unpaid）
  - 1：待发货（原Unshipped）
  - 2：待收货（原Shipped）
  - 3：待评价（原Unreviewed）
  - 4：已完成（原Completed）
  - 5：已取消（原Closed）
  - 6：售后（原AfterSales）
  - -1：已删除（原Deleted）

### 2. 新增售后类型枚举
- 0：无
- 1：退货退款
- 2：换货
- 3：未发货退款
- 4：仅退款

### 3. 新增处理方式枚举
- 0：无
- 1：仅退款
- 2：换货
- 3：小额补偿

### 5. 订单表结构变更
- 修改`status`字段类型为INT
- 新增`after_sale_type`字段，表示售后类型
- 新增`processing_method`字段，表示处理方式
- 新增`after_sale_status`字段，表示售后状态

### 6. 注意事项
- `StoreOrderSearchRequest.java`请求类中的`status`字段保持字符串类型不变，但在后端处理时会映射到对应的数字状态
- 所有涉及订单状态的前端展示需要更新，以适配新的状态码

### 7. 数据库升级脚本
- 执行`sql/update_order_status_and_aftersale.sql`脚本完成数据库结构和数据的更新

### 订单售后管理功能

系统新增订单售后申请记录功能，提供了对售后申请的全流程管理：

#### 1. 功能描述
- 提供完整的订单售后申请记录管理
- 支持多种售后类型：退货退款、换货、未发货退款、仅退款
- 支持售后申请的全流程跟踪和管理
- 支持多种处理方式：仅退款、换货、小额补偿

#### 2. 主要特性
- 售后申请记录：记录所有售后申请信息，包括申请原因、图片、说明等
- 售后状态跟踪：完整记录售后处理的各个状态变化
- 退款/补偿管理：支持退款或补偿金额的记录和管理
- 换货管理：支持换货流程和换货单号的记录
- 处理记录：记录处理方式、处理描述和处理人

#### 3. 售后状态流程
- 待审核：用户提交售后申请，等待商家审核
- 已拒绝：商家拒绝售后申请，并提供拒绝理由
- 待买家寄回：商家同意售后，等待买家寄回商品
- 待商家收货：买家已寄回商品，等待商家确认收货
- 商家验收通过：商家收到退回商品并验收通过
- 待换货发货：换货流程中，等待商家发送换货商品
- 退款处理中：退款流程中，等待退款到账
- 已完成：售后流程已完成
- 已取消：售后申请已取消

#### 4. 数据表结构

##### 4.1 售后申请表(store_order_aftersale)
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | int | 售后申请ID |
| order_id | int | 订单ID |
| order_info_id | varchar| 订单购物详情ID |
| aftersale_type | tinyint | 售后方式（1:退货退款,2:换货,3:未发货退款,4:仅退款） |
| aftersale_reason | varchar | 售后原因 |
| aftersale_images | varchar | 售后图片 |
| aftersale_explain | varchar | 售后用户说明 |
| refund_price | decimal | 退款/补偿金额 |
| frontend_aftersale_reason | varchar | 前台售后原因 |
| aftersale_status | tinyint | 售后状态 |
| create_time | timestamp | 创建时间 |
| update_time | timestamp | 更新时间 |

##### 4.2 售后记录表(store_order_aftersale_record)
| 字段名 | 类型 | 说明 |
| ------ | ---- | ---- |
| id | int | 记录ID |
| aftersale_id | int | 售后申请ID |
| change_type | varchar | 变更类型 |
| change_message | varchar | 变更说明 |
| processing_method | tinyint | 处理方式（1:仅退款,2:换货,3:小额补偿） |
| processing_explain | varchar | 处理描述 |
| exchange_delivery_id | varchar | 换货单号 |
| refund_price | decimal | 退款/补偿金额 |
| reject_reason | varchar | 拒绝售后的理由 |
| aftersale_status | tinyint | 售后状态 |
| admin_id | int | 处理人ID |
| create_time | timestamp | 创建时间 |

#### 5. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/order/aftersale/list | GET | 获取售后申请列表 |
| /api/admin/order/aftersale/detail/{id} | GET | 获取售后申请详情 |
| /api/admin/order/aftersale/process | POST | 处理售后申请 |
| /api/admin/order/aftersale/reject | POST | 拒绝售后申请 |
| /api/admin/order/aftersale/complete | POST | 完成售后申请 |
| /api/mini/order/aftersale/apply | POST | 用户申请售后 |
| /api/mini/order/aftersale/cancel | POST | 用户取消售后申请 |
| /api/mini/order/aftersale/list | GET | 用户获取售后申请列表 |
| /api/mini/order/aftersale/detail/{id} | GET | 用户获取售后申请详情 |

### 订单流程状态功能

系统新增订单流程状态功能，提供了对订单从下单到完成的完整流程状态展示：

#### 1. 功能描述
- 提供订单详情中的流程状态信息展示
- 包含四个主要流程节点：下单、付款、发货、交易完成
- 每个流程节点包含状态、时间和相关信息

#### 2. 主要特性
- 下单流程：展示下单时间和下单用户
- 付款流程：展示付款时间和支付方式
- 发货流程：展示发货时间和快递名称
- 交易完成流程：展示交易完成时间

#### 3. 数据结构
订单流程状态信息包含在`StoreOrderFlowResponse`对象中，主要字段如下：
- `orderStatus`：下单状态（1-已完成）
- `orderTime`：下单时间
- `orderUser`：下单用户
- `payStatus`：付款状态（1-已完成）
- `payTime`：付款时间
- `payType`：支付方式
- `deliveryStatus`：发货状态（1-已完成）
- `deliveryTime`：发货时间
- `expressName`：快递名称
- `completeStatus`：交易完成状态（1-已完成）
- `completeTime`：交易完成时间

#### 4. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/order/info | GET | 获取订单详情（包含流程状态信息） |

#### 5. 请求参数说明
- `orderNo`: 订单编号

#### 6. 使用场景
- 管理员查看订单详细信息时，可以清晰了解订单的处理流程和状态
- 客服人员解答用户关于订单状态的咨询
- 物流跟踪和订单处理过程的监控

## 订单详情成本价功能

### 1. 功能概述

系统新增了订单详情成本价功能，在订单详情中显示商品的成本价，方便管理员了解订单的利润情况。

### 2. 主要特性
- 在订单详情中显示每个商品的成本价
- 成本价字段与商品表的成本价字段保持一致
- 成本价字段在订单创建时从商品表中获取并保存

### 3. 数据表更新
在`store_order_info`表中添加了成本价字段：
```sql
ALTER TABLE `store_order_info`
    ADD COLUMN `cost` decimal(8,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '成本价' AFTER `vip_price`;
```

### 4. 接口说明
订单详情接口`/api/admin/store/order/info`返回值中的商品信息现在包含成本价字段。

### 5. 使用场景
- 管理员可以查看每个订单的成本价，计算订单利润
- 便于进行商品成本核算和利润分析
- 辅助商品定价策略的制定

## 订单详情接口售后字段增强

### 1. 功能概述

系统对订单详情接口进行了增强，新增了售后相关字段，使管理员可以在查看订单详情时了解订单的售后状态和相关信息。

### 2. 主要特性
- 在订单详情接口中增加售后相关字段
- 通过订单号从售后表中获取售后信息
- 支持查看售后类型、售后状态、售后原因等信息

### 3. 新增字段说明
| 字段名 | 类型 | 说明                                                                                               |
| ------ | ---- |--------------------------------------------------------------------------------------------------|
| aftersaleId | Integer | 售后申请ID                                                                                           |
| aftersaleType | Integer | 售后类型（1:退货退款,2:换货,3:未发货退款,4:仅退款）                                                                  |
| aftersaleReason | String | 售后原因                                                                                             |
| aftersaleImages | String | 售后图片                                                                                             |
| aftersaleExplain | String | 售后用户说明                                                                                           |
| aftersaleStatus | Integer | 售后状态（0:无,1:待审核,2:已拒绝,3:待买家寄回,4:待商家收货,5:待换货发货,6:售后完成,7:已取消） |
| frontendAftersaleReason | String | 前台售后原因                                                                                           |
| aftersaleRefundPrice | BigDecimal | 售后退款金额                                                                                           |
| aftersaleCreateTime | Date | 售后创建时间                                                                                           |

### 4. 接口说明
订单详情接口`/api/admin/store/order/info`返回值中现在包含售后相关字段，如果订单没有售后记录，则相关字段为默认值。

### 5. 使用场景
- 管理员可以在订单详情页面查看订单是否有售后申请
- 了解售后类型、原因和状态，便于处理售后问题
- 提高售后处理效率，减少管理员在不同页面之间切换的操作

### 订单售后管理功能

系统提供完整的订单售后管理功能，主要包括以下特性：

#### 1. 售后类型支持
系统支持多种售后类型：
- 退货退款：买家申请退货并退款
- 换货：买家申请更换同款商品
- 未发货退款：订单尚未发货时申请退款
- 仅退款：商品已收到但申请退款

#### 2. 售后状态流转
系统实现了完整的售后状态流转：
- 待审核：买家提交申请，等待商家审核
- 已拒绝：商家拒绝了售后申请
- 待买家寄回：商家同意了售后申请，等待买家寄回商品
- 待商家收货：买家已寄出，等待商家收货
- 待换货发货：换货场景下，等待商家发送新商品
- 售后完成：售后流程已完成
- 已取消：售后申请已取消

#### 3. 售后功能模块

##### 3.1 售后列表管理
- 提供售后订单列表查询功能
- 支持多种查询条件：申请时间、售后类型、订单号、收货人信息等
- 支持售后状态筛选
- 显示售后订单的详细信息

##### 3.2 售后详情查看
- 展示售后申请的详细信息
- 显示相关商品信息
- 提供售后流程追踪
- 显示售后记录日志

##### 3.3 售后拒绝功能
- 商家可以拒绝不符合条件的售后申请
- 支持填写拒绝理由
- 系统记录拒绝操作并通知买家
- 自动生成售后记录日志

#### 4. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/store/after-sale/list | GET | 获取售后订单列表 |
| /api/admin/store/after-sale/aftersale/list | GET | 获取专门的售后列表(使用售后请求和响应对象) |
| /api/admin/store/after-sale/detail | GET | 获取售后订单详情 |
| /api/admin/store/after-sale/reject | POST | 拒绝售后申请 |

#### 5. 拒绝售后接口说明
**请求路径**: `/api/admin/store/after-sale/reject`
**请求方法**: POST
**接口权限**: `admin:order:after:sale:reject`
**请求参数**:
- `aftersaleId`: 售后ID（必填）
- `rejectReason`: 拒绝理由（必填）

**业务逻辑**:
- 系统检查售后状态，仅允许对"待审核"状态的售后申请执行拒绝操作
- 拒绝后售后状态变更为"已拒绝"
- 系统记录拒绝理由
- 系统在售后记录表中生成一条拒绝记录，记录拒绝时间、操作人和拒绝理由

### 售后处理功能

系统新增售后处理功能，支持商家处理用户提交的售后申请。该功能提供了三种处理方式：仅退款、换货和小额补偿。

#### 1. 功能概述

售后处理功能允许商家根据用户的售后申请情况，选择合适的处理方式进行处理，具体支持：

- **仅退款**：直接同意退款，无需用户寄回商品
- **换货**：同意为用户更换同款商品，需填写换货单号
- **小额补偿**：不退不换，给予用户一定金额的补偿

#### 2. 处理流程

1. 商家在售后详情页面查看用户的售后申请
2. 选择合适的处理方式（仅退款/换货/小额补偿）
3. 根据处理方式填写相应信息：
   - 仅退款：填写退款金额和处理说明
   - 换货：填写换货单号和处理说明
   - 小额补偿：填写补偿金额和处理说明
4. 提交处理结果
5. 系统自动更新售后状态，并在售后记录中添加一条处理记录

#### 3. 接口说明

##### 3.1 售后处理接口

**请求路径**: `/api/admin/store/order/aftersale/process`
**请求方法**: POST
**接口权限**: `admin:order:after:sale:process`
**请求参数**:
- `aftersaleId`: 售后ID（必填）
- `processingMethod`: 处理方式（必填，1:仅退款,2:换货,3:小额补偿）
- `processingExplain`: 处理说明（选填）
- `exchangeDeliveryId`: 换货单号（处理方式为换货时必填）
- `refundPrice`: 退款/补偿金额（处理方式为仅退款或小额补偿时必填）

**响应数据**:
```json
{
  "code": 200,
  "message": "操作成功",
  "data": true
}
```

**业务逻辑**:
- 系统会根据处理方式的不同，验证相应的必填字段
- 仅允许对"待审核"状态的售后申请执行处理操作
- 根据处理方式不同，自动更新售后状态为相应的状态
- 在售后记录表中生成一条处理记录，记录处理时间、处理方式、处理说明等信息

#### 4. 状态流转

不同处理方式导致的售后状态流转：
- 仅退款：从"待审核(1)"变更为"售后完成(6)"
- 换货：从"待审核(1)"变更为"待换货发货(5)"
- 小额补偿：从"待审核(1)"变更为"售后完成(6)"

#### 5. 前端交互

前端在售后处理页面提供三种处理方式的选择，并根据用户选择的处理方式动态显示不同的表单字段：
- 仅退款：显示退款金额输入框和处理说明输入框
- 换货：显示换货单号输入框和处理说明输入框
- 小额补偿：显示补偿金额输入框和处理说明输入框

#### 6. 注意事项

- 处理方式为"仅退款"或"小额补偿"时，退款/补偿金额不能为空且必须大于0
- 处理方式为"换货"时，换货单号不能为空
- 同一售后申请只能处理一次，处理后状态将变更，无法再次处理
- 系统会自动记录处理操作的相关信息，包括处理人、处理时间等

## 售后管理功能增强

系统对售后管理功能进行了增强，新增以下特性：

### 1. 售后表字段增强
- 新增退货快递单号字段（return_delivery_id）
  - 用于记录买家在退货/换货过程中使用的快递单号
  - 便于商家跟踪退货物流状态
  - 支持在售后详情中查看和录入
- 新增发货快递单号字段（delivery_id）
  - 用于记录商家在换货过程中发出新商品的快递单号
  - 便于买家跟踪换货物流状态
  - 支持在售后详情中查看和录入

### 2. 售后流程优化
- 完善售后物流信息记录
- 提升售后处理效率和透明度
- 便于商家和买家双方跟踪售后进度

### 3. 数据库变更
- 在store_order_aftersale表中增加了两个字段
  - return_delivery_id: varchar(64)，用于存储退货快递单号
  - delivery_id: varchar(64)，用于存储发货快递单号

### 4. 使用场景
- 买家申请退货并填写退货快递单号
- 商家确认收到退货并处理退款
- 买家申请换货并填写退货快递单号
- 商家确认收到退货并发出新商品，填写发货快递单号
- 买家确认收到换货商品，完成售后流程

### SVIP管理功能增强

系统对SVIP会员管理功能进行了增强，新增以下特性：

#### 1. SVIP信息存储
- 新增SVIP信息表(svip_info)，用于存储SVIP会员的详细信息
- 支持存储手机号和5个自定义扩展字段
- 与用户表(user)关联，通过uid字段进行关联

#### 2. SVIP添加功能增强
- 新增SVIP会员时，支持同时录入手机号和扩展字段信息
- 自动关联用户信息，设置会员等级和SVIP升级时间

#### 3. SVIP批量导入功能
- 支持通过Excel批量导入SVIP会员信息
- Excel模板第一列为手机号(必填)，其余为扩展字段(可选)
- 导入时自动匹配已有用户，并将其升级为SVIP会员
- 支持对已存在的SVIP信息进行更新

#### 4. 接口说明
| 接口路径 | 方法 | 说明 |
| -------- | ---- | ---- |
| /api/admin/user/detail | GET | 获取会员详情信息(完整版) |
| /api/admin/user/info | GET | 获取用户基本信息 |
| /api/admin/user/svip/add | POST | 添加SVIP会员(支持扩展字段) |
| /api/admin/user/svip/import | POST | 批量导入SVIP会员 |
| /api/admin/user/svip/template/download | GET | 下载SVIP导入模板 |

#### 5. 会员详情接口返回数据说明
**GET /api/admin/user/detail?id={用户ID}**

返回数据包含以下信息：
- **基本信息**：用户昵称、头像、手机号、性别、生日、会员等级、上级、地区、注册时间、升级时间等
- **资产信息**：账号余额、可用优惠券、成长值、推广用户数、可提现金额等
- **业绩概览**：累计返现、累计销售、累计充值、累计提现等
- **订单记录**：累计消费金额、累计订单数、最近下单时间等

## 🆕 最新功能：优惠券数据概览系统

### 功能概述
为商城系统新增了优惠券数据概览查询功能，当用户在优惠券列表中点击某张优惠券时，可以展示该优惠券的详细统计数据和使用情况。

### 核心功能

#### 1. 优惠券数据概览接口
- **接口路径**: `/admin/marketing/coupon/data/overview`
- **请求方式**: GET
- **参数**: couponId (优惠券ID)
- **权限**: 管理员权限

#### 2. 数据统计维度

**优惠券基本信息**:
- 优惠券名称、面值、使用条件
- 优惠券类型（满减券、新人专享券、会员专享券）
- 门槛类型（有门槛/无门槛）

**核心统计数据**:
- **支付总金额**: 使用该优惠券的订单支付总金额
- **优惠总金额**: 该优惠券为用户节省的总金额
- **费效比**: 优惠总金额占支付总金额的比例
- **用券笔单价**: 使用该优惠券的订单平均金额
- **用券客数**: 使用该优惠券的用户数量（去重统计）
- **购买商品件数**: 使用该优惠券购买的商品总件数

**商品购买详情**:
- 使用该优惠券购买的商品列表
- 商品信息：名称、图片、价格、规格
- 购买统计：付款件数、付款人数

#### 3. 技术实现

**数据查询逻辑**:
1. 通过优惠券用户表(StoreCouponUser)查询已使用的优惠券记录
2. 关联订单表(StoreOrder)获取订单支付信息和用户信息
3. 关联订单详情表(StoreOrderInfo)获取商品购买详情
4. 进行数据聚合和统计计算

**核心类文件**:
- `StoreCouponDataOverviewResponse`: 数据概览响应类
- `StoreCouponService.getDataOverview()`: 服务层查询方法
- `StoreCouponController.getDataOverview()`: 控制器接口

**数据准确性保障**:
- 用户数量统计支持去重，避免重复计算
- 商品购买人数按实际用户去重统计
- 费效比计算精确到小数点后两位
- 异常情况处理，确保接口稳定性

### 使用说明

1. **查看优惠券数据概览**
   - 在优惠券管理列表页面
   - 点击任意优惠券的"数据"按钮
   - 系统将弹出数据概览对话框
   - 展示该优惠券的完整统计信息

2. **数据更新机制**
   - 数据实时查询，反映最新的使用情况
   - 统计范围包含所有已使用该优惠券的订单
   - 支持大数据量查询，性能优化

3. **业务价值**
   - 帮助运营人员分析优惠券效果
   - 评估优惠券的投入产出比
   - 了解用户使用偏好和购买行为

## 🔍 提现审核人信息字段说明

### 新增字段

| 字段名 | 说明 | 类型 | 备注 |
|-------|-----|------|------|
| approve_admin_account | 审核管理员账号 | varchar(32) | 记录执行审核操作的管理员登录账号 |
| approve_admin_name | 审核管理员姓名 | varchar(50) | 记录执行审核操作的管理员真实姓名 |

### 使用说明

- 这些字段在管理员执行提现审核操作时自动填充
- 支持单个审核和批量审核操作的审核人记录
- 用于审计追溯和操作责任确认
- 提高系统操作的透明度和可追溯性
   - 为优惠券策略调整提供数据支持
