#!/bin/bash
copy_and_restart() {
#  echo $*
#  echo $@
  Host=$1
  Password=$2
  SourceFile=$3
  DistPath=$4
#  if [ -f $SourceFile ]; then
    echo '******************拷贝文件******************'
    sshpass -p $Password scp -rp $SourceFile root@$Host:$DistPath
    if [ $? -eq 0 ]; then
      echo '******************拷贝成功******************'
      echo '******************重启系统******************'
      sshpass -p $Password ssh -p 22 root@$Host "source /etc/profile && cd $DistPath && $Shell"
    fi
#  fi

}

echo '******************准备更新******************'
cd $SourcePath

#read -p "是否编译最新版程序?" INPUT

#INPUT=$(echo $INPUT | tr 'A-Z' 'a-z')

#if [[ $INPUT = "y" ]] || [[ $INPUT = "" ]]; then
  echo '******************编译开始******************'
  /home/<USER>/apps/maven/bin/mvn clean install -DskipTests #>/dev/null 2>&1
#fi

if [ $? -eq 0 ]; then
  echo '******************编译成功******************'
  Shell='./restart.sh'
  Password='Tanjun@1536'
  ServerPath='/home'

#  临时修改只更新mini
   echo '******************开始更新******************'
      sleep 1
      Host='**************'
      SourceFile="$SourcePath/mini/target/mini-1.0-SNAPSHOT.jar"
      DistPath="$ServerPath/mini/"
      copy_and_restart $Host $Password $SourceFile $DistPath
  return
  #  临时修改只更新mini
  declare -a options
  options[1]='1、更新微信小程序接口(**************)'
  options[2]='2、更新后端管理接口(**************)'
  options[3]='3、更新后端管理页面(**************)'

  echo '操作说明:'
  for i in $(seq 0 $((${#options[*]}))); do
    echo ${options[$i]}
  done
  read -p "请选择你要执行的操作:" INPUT
  case $INPUT in
  1 | １)
    echo ${options[$INPUT]}
    echo '******************开始更新******************'
    sleep 1
    Host='**************'
    SourceFile="$SourcePath/mini/target/mini-1.0-SNAPSHOT.jar"
    DistPath="$ServerPath/mini/"
    copy_and_restart $Host $Password $SourceFile $DistPath

    ;;
   2 | 2)
      echo ${options[$INPUT]}
      echo '******************开始更新******************'
      sleep 1
      Host='**************'
      SourceFile="$SourcePath/admin/target/admin-1.0-SNAPSHOT.jar"
      DistPath="$ServerPath/admin/"
      copy_and_restart $Host $Password $SourceFile $DistPath

      ;;
    3 | 3)
          echo ${options[$INPUT]}
          echo '******************开始更新******************'
          sleep 1
          Host='**************'
          SourceFile="$SourcePath/html/dist/"
#          chmod -R755 $SourceFile
          DistPath="$ServerPath/nginx/html/"
          copy_and_restart $Host $Password $SourceFile $DistPath

          ;;

  esac
else
  echo '******************编译失败******************'
fi
