-- 为store_activity表添加images_admin字段
-- 创建日期：2025-08-01
-- 功能：支持活动详情图的原图存储，系统自动处理分割图片

-- 添加活动轮播图管理员字段
ALTER TABLE store_activity ADD COLUMN images_admin varchar(2000) COMMENT '活动详情图（管理员上传的原图）' AFTER images;

-- 更新字段注释，确保字段含义清晰
ALTER TABLE store_activity MODIFY COLUMN images varchar(2000) COMMENT '活动图详情（分割）';
ALTER TABLE store_activity MODIFY COLUMN images_admin varchar(2000) COMMENT '活动详情图（管理员上传的原图）';
