package com.ylpz.model.activity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 活动管理表
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("store_activity")
@ApiModel(value = "StoreActivity对象", description = "活动管理表")
public class StoreActivity implements Serializable {

    protected static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动ID")
    @TableId(value = "id", type = IdType.AUTO)
    protected Integer id;

    @ApiModelProperty(value = "活动名称")
    protected String name;

    @ApiModelProperty(value = "活动主图")
    protected String image;
    
    @ApiModelProperty(value = "氛围图标")
    protected String atmosphereIcon;

    @ApiModelProperty(value = "未领取弹窗图")
    protected String unclaimedPopupImage;

    @ApiModelProperty(value = "已领取弹窗图")
    protected String claimedPopupImage;

    @ApiModelProperty(value = "活动内容类型（1：图片模式，2：富文本模式）")
    protected Integer contentType;

    @ApiModelProperty(value = "活动图详情（分割）")
    protected String images;

    @ApiModelProperty(value = "活动轮播图（管理员上传的原图）")
    protected String imagesAdmin;

    @ApiModelProperty(value = "开始时间")
    protected Date startTime;

    @ApiModelProperty(value = "结束时间")
    protected Date stopTime;

    @ApiModelProperty(value = "状态（0：关闭，1：开启）")
    protected Boolean status;

    @ApiModelProperty(value = "排序")
    protected Integer sort;

    @ApiModelProperty(value = "活动简介")
    protected String info;

    @ApiModelProperty(value = "活动详情")
    protected String description;

    @ApiModelProperty(value = "关联文章ID")
    protected Integer articleId;

    @ApiModelProperty(value = "是否删除")
    protected Boolean isDel;

    @ApiModelProperty(value = "创建时间")
    protected Date createTime;

    @ApiModelProperty(value = "更新时间")
    protected Date updateTime;
}