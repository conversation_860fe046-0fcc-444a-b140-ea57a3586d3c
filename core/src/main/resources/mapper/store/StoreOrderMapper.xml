<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.StoreOrderDao">
    <select id="getOrderStatisticsPriceDetail" parameterType="com.ylpz.core.common.request.StoreDateRangeSqlPram"
            resultType="com.ylpz.core.common.response.StoreOrderStatisticsChartItemResponse">
        select sum(o.pay_price) as num,date_format(o.create_time, '%Y-%m-%d') as time
        from store_order o
        where o.is_del >= 0 and o.paid >= 1
        and o.create_time >= #{ startTime }
        and o.create_time &lt; #{ endTime }
        group by date_format(o.create_time, '%Y-%m-%d')
        order by o.create_time desc;
    </select>
    <select id="getOrderStatisticsOrderCountDetail" parameterType="com.ylpz.core.common.request.StoreDateRangeSqlPram"
            resultType="com.ylpz.core.common.response.StoreOrderStatisticsChartItemResponse">
        select count(id) as num, date_format(o.create_time, '%Y-%m-%d') as time
        from store_order o
        where o.is_del >= 0 and o.paid >= 1
        and o.create_time >= #{ startTime }
        and o.create_time &lt; #{ endTime }
        group by date_format(o.create_time, '%Y-%m-%d')
        order by o.create_time asc;
    </select>

    <select id="sumUserOrderAmount" resultType="java.math.BigDecimal">
        select sum(pay_price) 
        from store_order 
        where uid = #{uid} and is_del = 0 and paid = 1
    </select>
</mapper>
