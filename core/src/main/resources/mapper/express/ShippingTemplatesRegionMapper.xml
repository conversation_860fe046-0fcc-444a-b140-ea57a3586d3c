<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.ShippingTemplatesRegionDao">

    <select id="getListGroup" resultType="com.ylpz.core.common.request.ShippingTemplatesRegionRequest" parameterType="integer">
        SELECT group_concat(`city_id`) AS city_id, ANY_VALUE(title) as title, ANY_VALUE(`first`) as `first`, ANY_VALUE(first_price) as first_price, ANY_VALUE(`renewal`) as `renewal`, ANY_VALUE(renewal_price) as renewal_price, uniqid, MIN(id) as id FROM shipping_templates_region where temp_id = #{tempId, jdbcType=INTEGER} GROUP BY `uniqid` ORDER BY id ASC
        <!-- SELECT group_concat(`city_id`) AS city_id, title, `first`, first_price, `renewal`, renewal_price, uniqid FROM shipping_templates_region where temp_id = #{tempId, jdbcType=INTEGER} GROUP BY `uniqid` ORDER BY id ASC-->
    </select>
</mapper>
