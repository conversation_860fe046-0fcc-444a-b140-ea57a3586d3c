<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.StoreOrderAftersaleRecordDao">

    <!-- 获取售后记录列表 -->
    <select id="getRecordsByAftersaleId" resultType="com.ylpz.model.order.StoreOrderAftersaleRecord">
        SELECT
            id,
            aftersale_id AS aftersaleId,
            change_type AS changeType,
            change_message AS changeMessage,
            processing_method AS processingMethod,
            processing_explain AS processingExplain,
            exchange_delivery_id AS exchangeDeliveryId,
            refund_price AS refundPrice,
            reject_reason AS rejectReason,
            aftersale_status AS aftersaleStatus,
            admin_id AS adminId,
            create_time AS createTime
        FROM
            store_order_aftersale_record
        WHERE
            aftersale_id = #{aftersaleId}
        ORDER BY
            create_time ASC
    </select>
</mapper> 