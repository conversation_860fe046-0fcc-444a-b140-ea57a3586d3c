<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.StoreOrderAftersaleDao">

    <!-- 获取售后订单列表 -->
    <select id="getAftersaleOrderList" resultType="com.ylpz.core.common.response.StoreOrderDetailResponse">
        SELECT
            o.id,
            o.order_id AS orderId,
            o.uid,
            o.real_name AS realName,
            o.user_phone AS userPhone,
            o.user_address AS userAddress,
            o.pay_price AS payPrice,
            o.total_num AS totalNum,
            o.total_price AS totalPrice,
            o.pay_type AS payType,
            o.paid,
            o.pay_time AS payTime,
            o.status,
            o.refund_reason_wap AS refundReasonWap,
            o.refund_reason_wap_img AS refundReasonWapImg,
            o.refund_reason_wap_explain AS refundReasonWapExplain,
            o.refund_reason AS refundReason,
            o.refund_price AS refundPrice,
            o.delivery_name AS deliveryName,
            o.delivery_type AS deliveryType,
            o.delivery_id AS deliveryId,
            o.mark,
            o.create_time AS createTime,
            u.nickname AS buyerName,
            ul.grade AS buyerLevel,
            a.id AS aftersaleId,
            a.aftersale_type AS aftersaleType,
            a.aftersale_reason AS aftersaleReason,
            a.aftersale_images AS aftersaleImages,
            a.aftersale_explain AS aftersaleExplain,
            a.aftersale_status AS aftersaleStatus,
            a.frontend_aftersale_reason AS frontendAftersaleReason,
            a.refund_price AS aftersaleRefundPrice
        FROM
            store_order_aftersale a
        JOIN
            store_order o ON a.order_id = o.id
        LEFT JOIN
            user u ON o.uid = u.id
        LEFT JOIN
            system_user_level ul ON u.level = ul.grade
        <where>
            <if test="request.aftersaleStatus != null and request.aftersaleStatus != ''">
                a.aftersale_status = #{request.aftersaleStatus}
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                AND o.id LIKE CONCAT('%', #{request.orderNo}, '%')
            </if>
            <if test="request.receiverKeyword != null and request.receiverKeyword != ''">
                AND (o.real_name LIKE CONCAT('%', #{request.receiverKeyword}, '%')
                OR o.user_phone LIKE CONCAT('%', #{request.receiverKeyword}, '%'))
            </if>
            <if test="request.dateLimit != null and request.dateLimit != ''">
                <if test="request.dateLimit == 'today'">
                    AND TO_DAYS(a.create_time) = TO_DAYS(NOW())
                </if>
                <if test="request.dateLimit == 'yesterday'">
                    AND TO_DAYS(NOW()) - TO_DAYS(a.create_time) = 1
                </if>
                <if test="request.dateLimit == 'lately7'">
                    AND DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt;= date(a.create_time)
                </if>
                <if test="request.dateLimit == 'lately30'">
                    AND DATE_SUB(CURDATE(), INTERVAL 30 DAY) &lt;= date(a.create_time)
                </if>
            </if>
            <if test="request.orderTimeStart != null and request.orderTimeStart != ''">
                AND a.create_time >= #{request.orderTimeStart}
            </if>
            <if test="request.orderTimeEnd != null and request.orderTimeEnd != ''">
                AND a.create_time &lt;= #{request.orderTimeEnd}
            </if>
            <if test="request.aftersaleType != null">
                AND a.aftersale_type = #{request.aftersaleType}
            </if>
            <if test="request.refundNo != null">
                AND a.id = #{request.refundNo}
            </if>
        </where>
        ORDER BY a.create_time DESC
    </select>

    <!-- 获取售后订单详情 -->
    <select id="getAftersaleOrderDetail" resultType="com.ylpz.core.common.response.StoreOrderInfoResponse">
        SELECT
            o.id,
            o.order_id AS orderId,
            o.uid,
            o.real_name AS realName,
            o.user_phone AS userPhone,
            o.user_address AS userAddress,
            o.pay_price AS payPrice,
            o.total_num AS totalNum,
            o.total_price AS totalPrice,
            o.pay_type AS payType,
            o.paid,
            o.pay_time AS payTime,
            o.status,
            o.refund_reason_wap AS refundReasonWap,
            o.refund_reason_wap_img AS refundReasonWapImg,
            o.refund_reason_wap_explain AS refundReasonWapExplain,
            o.refund_reason AS refundReason,
            o.refund_price AS refundPrice,
            o.delivery_name AS deliveryName,
            o.delivery_type AS deliveryType,
            o.delivery_id AS deliveryId,
            o.mark,
            o.create_time AS createTime,
            u.nickname AS nikeName,
            ul.grade AS buyerLevel,
            a.id AS aftersaleId,
            a.aftersale_type AS aftersaleType,
            a.aftersale_reason AS aftersaleReason,
            a.aftersale_images AS aftersaleImages,
            a.aftersale_explain AS aftersaleExplain,
            a.aftersale_status AS aftersaleStatus,
            a.frontend_aftersale_reason AS frontendAftersaleReason,
            a.refund_price AS aftersaleRefundPrice,
            a.create_time AS aftersaleCreateTime
        FROM
            store_order_aftersale a
        JOIN
            store_order o ON a.order_id = o.id
        LEFT JOIN
            user u ON o.uid = u.id
        LEFT JOIN
            system_user_level ul ON u.level = ul.grade
        WHERE
            o.id = #{orderNo}
        LIMIT 1
    </select>
    
    <!-- 获取售后记录列表 -->
    <select id="getRecordsByAftersaleId" resultType="com.ylpz.model.order.StoreOrderAftersaleRecord">
        SELECT
            id,
            aftersale_id AS aftersaleId,
            change_type AS changeType,
            change_message AS changeMessage,
            processing_method AS processingMethod,
            processing_explain AS processingExplain,
            exchange_delivery_id AS exchangeDeliveryId,
            refund_price AS refundPrice,
            reject_reason AS rejectReason,
            aftersale_status AS aftersaleStatus,
            create_time AS createTime,
            update_time AS updateTime
        FROM
            store_order_aftersale_record
        WHERE
            aftersale_id = #{aftersaleId}
        ORDER BY
            id ASC
    </select>
    
    <!-- 获取售后订单列表（使用专门的售后请求和响应对象） -->
    <select id="getAftersaleList" resultType="com.ylpz.core.common.response.StoreOrderAftersaleResponse">
        SELECT
            a.id AS aftersaleId,
            CONCAT('RF', a.id) AS refundNo,
            o.order_id AS orderNo,
            o.id AS orderId,
            a.aftersale_type AS aftersaleType,
            CASE 
                WHEN a.aftersale_type = 1 THEN '退货退款'
                WHEN a.aftersale_type = 2 THEN '换货'
                WHEN a.aftersale_type = 3 THEN '未发货退款'
                WHEN a.aftersale_type = 4 THEN '仅退款'
                ELSE '未知'
            END AS aftersaleTypeDesc,
            a.create_time AS applyTime,
            o.pay_price AS orderAmount,
            a.refund_price AS refundAmount,
            a.aftersale_status AS aftersaleStatus,
            CASE
                WHEN a.aftersale_status = 0 THEN '无'
                WHEN a.aftersale_status = 1 THEN '待审核'
                WHEN a.aftersale_status = 2 THEN '已拒绝'
                WHEN a.aftersale_status = 3 THEN '待买家寄回'
                WHEN a.aftersale_status = 4 THEN '待商家收货'
                WHEN a.aftersale_status = 5 THEN '待换货发货'
                WHEN a.aftersale_status = 6 THEN '售后完成'
                WHEN a.aftersale_status = 7 THEN '已取消'
                ELSE '未知'
            END AS aftersaleStatusDesc,
            o.uid AS uid,
            u.nickname AS nickname,
            o.real_name AS receiverName,
            o.user_phone AS receiverPhone
        FROM
            store_order_aftersale a
        JOIN
            store_order o ON a.order_id = o.id
        LEFT JOIN
            user u ON o.uid = u.id
        LEFT JOIN
            store_order_info i ON i.order_id = o.id
        <where>
            <if test="request.startTime != null and request.startTime != '' and request.endTime != null and request.endTime != ''">
                a.create_time BETWEEN #{request.startTime} AND #{request.endTime}
            </if>
            <if test="request.aftersaleType != null">
                AND a.aftersale_type = #{request.aftersaleType}
            </if>
            <if test="request.aftersaleStatus != null">
                AND a.aftersale_status = #{request.aftersaleStatus}
            </if>
            <if test="request.statusList != null and request.statusList.size() > 0">
                AND a.aftersale_status IN
                <foreach collection="request.statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="request.orderNo != null and request.orderNo != ''">
                AND o.order_id = #{request.orderNo}
            </if>
            <if test="request.receiverKeyword != null and request.receiverKeyword != ''">
                AND (o.real_name LIKE CONCAT('%', #{request.receiverKeyword}, '%')
                OR o.user_phone LIKE CONCAT('%', #{request.receiverKeyword}, '%'))
            </if>
            <if test="request.productName != null and request.productName != ''">
                AND i.product_name LIKE CONCAT('%', #{request.productName}, '%')
            </if>
            <if test="request.expressNo != null and request.expressNo != ''">
                AND o.delivery_id = #{request.expressNo}
            </if>
            <if test="request.refundNo != null and request.refundNo != ''">
                AND a.id = #{request.refundNo}
            </if>
        </where>
        GROUP BY a.id
        ORDER BY a.create_time DESC
    </select>
    
    <!-- 获取售后订单详情（使用专门的售后响应对象） -->
    <select id="getAftersaleDetail" resultType="com.ylpz.core.common.response.StoreOrderAftersaleDetailResponse">
        SELECT
            a.id AS aftersaleId,
            CONCAT('RF', a.id) AS refundNo,
            o.order_id AS orderNo,
            o.id AS orderId,
            a.aftersale_type AS aftersaleType,
            CASE 
                WHEN a.aftersale_type = 1 THEN '退货退款'
                WHEN a.aftersale_type = 2 THEN '换货'
                WHEN a.aftersale_type = 3 THEN '未发货退款'
                WHEN a.aftersale_type = 4 THEN '仅退款'
                ELSE '未知'
            END AS aftersaleTypeDesc,
            a.create_time AS applyTime,
            o.pay_price AS orderAmount,
            o.pay_postage AS shippingFee,
            a.refund_price AS refundAmount,
            a.aftersale_status AS aftersaleStatus,
            CASE
                WHEN a.aftersale_status = 0 THEN '无'
                WHEN a.aftersale_status = 1 THEN '待审核'
                WHEN a.aftersale_status = 2 THEN '已拒绝'
                WHEN a.aftersale_status = 3 THEN '待买家寄回'
                WHEN a.aftersale_status = 4 THEN '待商家收货'
                WHEN a.aftersale_status = 5 THEN '待换货发货'
                WHEN a.aftersale_status = 6 THEN '售后完成'
                WHEN a.aftersale_status = 7 THEN '已取消'
                ELSE '未知'
            END AS aftersaleStatusDesc,
            o.uid AS uid,
            u.nickname AS nickname,
            ul.grade AS buyerLevel,
            o.real_name AS receiverName,
            o.user_phone AS receiverPhone,
            o.user_address AS receiverAddress,
            o.delivery_name AS expressName,
            o.delivery_id AS expressNo,
            o.pay_type AS payType,
            CASE 
                WHEN o.pay_type = 'weixin' THEN '微信支付'
                WHEN o.pay_type = 'yue' THEN '余额支付'
                WHEN o.pay_type = 'offline' THEN '线下支付'
                WHEN o.pay_type = 'alipay' THEN '支付宝支付'
                ELSE o.pay_type
            END AS payTypeDesc,
            a.aftersale_reason AS aftersaleReason,
            a.aftersale_explain AS aftersaleExplain,
            a.aftersale_images AS aftersaleImages
        FROM
            store_order_aftersale a
        JOIN
            store_order o ON a.order_id = o.id
        LEFT JOIN
            user u ON o.uid = u.id
        LEFT JOIN
            system_user_level ul ON u.level = ul.grade
        WHERE
            a.id = #{aftersaleId}
        LIMIT 1
    </select>
</mapper> 