<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.UserBillDao">

    <select id="getListAdminAndIntegeal" resultType="com.ylpz.core.common.response.UserBillResponse" parameterType="map">
        SELECT ub.id,ub.link_id AS linkId,ub.pm,ub.title,ub.category,ub.type,ub.number,ub.balance,ub.mark,ub.status,ub.create_time AS createTime,ub.update_time AS updateTime,u.nickname,ub.uid FROM user_bill ub
        LEFT JOIN user u ON ub.uid = u.id
        where 1 = 1
        <if test="keywords != '' and keywords != null ">
            and ( ub.id like #{keywords} or ub.uid like #{keywords} or ub.link_id like #{keywords, jdbcType=VARCHAR} or ub.title like #{keywords, jdbcType=VARCHAR} or u.nickname like #{keywords, jdbcType=VARCHAR})
        </if>
        <if test="type != '' and type != null">
            and ub.type = #{type, jdbcType=VARCHAR}
        </if>
        <if test="category != '' and category != null">
            and ub.category = #{category, jdbcType=VARCHAR}
        </if>
        <if test="userIdList != null">
            and ub.uid in 
            <foreach collection="userIdList" item="userIdList" index="index" open="(" separator="," close=")">
                #{userIdList}
            </foreach>
        </if>
        <if test="startTime != '' and endTime != '' and startTime != null and endTime != null">
            and (ub.create_time between #{startTime} and #{endTime})
        </if>

        ORDER BY ub.id DESC,ub.create_time DESC
    </select>

    <select id="fundMonitoring" resultType="com.ylpz.core.common.response.UserBillResponse" parameterType="map">
        SELECT ub.id,ub.link_id AS linkId,ub.pm,ub.title,ub.category,ub.type,ub.number,ub.balance,ub.mark,ub.status,ub.create_time AS createTime,ub.update_time AS updateTime,u.nickname,ub.uid FROM user_bill ub
        LEFT JOIN user u ON ub.uid = u.id
        where ub.category = 'now_money'
        <if test="keywords != '' and keywords != null ">
            and ( ub.uid like #{keywords} or u.nickname like #{keywords, jdbcType=VARCHAR})
        </if>
        <if test="title != '' and title != null">
            and ub.title = #{title, jdbcType=VARCHAR}
        </if>
        <if test="startTime != '' and endTime != '' and startTime != null and endTime != null">
            and (ub.create_time between #{startTime} and #{endTime})
        </if>

        ORDER BY ub.id DESC,ub.create_time DESC
    </select>

    <!-- 查询带用户信息的账单列表 -->
    <select id="selectUserBillWithUserInfo" resultType="com.ylpz.core.common.response.UserBillWithUserInfoResponse">
        SELECT
            ub.id,
            ub.uid,
            ub.link_id AS linkId,
            ub.pm,
            ub.title,
            ub.category,
            ub.type,
            ub.number,
            ub.balance,
            ub.mark,
            ub.status,
            ub.update_time AS updateTime,
            ub.create_time AS createTime,
            u.nickname,
            u.avatar,
            u.phone,
            u.level
        FROM user_bill ub
        LEFT JOIN user u ON ub.uid = u.id
        <where>
            ${ew.sqlSegment}
        </where>
    </select>

    <!-- 根据参数查询带用户信息的账单列表 -->
    <select id="selectUserBillWithUserInfoByParams" resultType="com.ylpz.core.common.response.UserBillWithUserInfoResponse" parameterType="map">
        SELECT
            ub.id,
            ub.uid,
            ub.link_id AS linkId,
            ub.pm,
            ub.title,
            ub.category,
            ub.type,
            ub.number,
            ub.balance,
            ub.mark,
            ub.status,
            ub.update_time AS updateTime,
            ub.create_time AS createTime,
            u.nickname,
            u.avatar,
            u.phone,
            u.level
        FROM user_bill ub
        LEFT JOIN user u ON ub.uid = u.id
        WHERE ub.status > 0
        <if test="uid != null">
            AND ub.uid = #{uid}
        </if>
        <if test="keywords != null and keywords != ''">
            AND (ub.id LIKE #{keywords} OR ub.uid LIKE #{keywords} OR ub.link_id LIKE #{keywords} OR ub.title LIKE #{keywords})
        </if>
        <if test="userKeywords != null and userKeywords != ''">
            AND (u.nickname LIKE #{userKeywords} OR u.phone LIKE #{userKeywords})
        </if>
        <if test="category != null and category != ''">
            AND ub.category = #{category}
        </if>
        <if test="pm != null">
            AND ub.pm = #{pm}
        </if>
        <if test="type != null and type != ''">
            AND ub.type = #{type}
        </if>
        <if test="title != null and title != ''">
            AND ub.title = #{title}
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND ub.create_time BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="orderBy != null and orderBy != ''">
            ORDER BY ${orderBy}
        </if>
        <if test="orderBy == null or orderBy == ''">
            ORDER BY ub.create_time DESC
        </if>
    </select>

</mapper>
