<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.UserCommissionRecordDao">
    <!-- 查询佣金返现记录列表 -->
    <select id="getCommissionList" resultType="com.ylpz.model.user.UserCommissionRecord">
        SELECT 
            r.*,
            u.nickname AS userName
        FROM 
            user_commission_record r
        LEFT JOIN 
            user u ON r.uid = u.id
        <where>
            <if test="status != null">
                AND r.status = #{status}
            </if>
            <if test="startTime != null and endTime != null">
                AND r.pay_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="userPhone != null and userPhone != ''">
                AND r.user_phone = #{userPhone}
            </if>
            <if test="linkId != null and linkId != ''">
                AND r.link_id = #{linkId}
            </if>
        </where>
        ORDER BY r.create_time DESC
    </select>
    
    <!-- 统计已结算佣金返现总额 -->
    <select id="sumSettledCommission" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(price), 0)
        FROM user_commission_record
        WHERE status = 2
          AND type = 1
    </select>
    
    <!-- 统计已结算订单的销售额合计，不包含退款订单 -->
    <select id="sumSettledSalesAmount" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(order_amount), 0) 
        FROM user_commission_record
        WHERE status = 2 AND type = 1
    </select>

</mapper> 