<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.RankingLeaderboardDetailMapper">

    <!-- 根据排行榜ID查询排名详情 -->
    <select id="selectRankDetailByRankingId" resultType="com.ylpz.model.ranking.RankItem">
        SELECT rld.rank,
               rld.uid,
               u.nickname,
               u.phone,
               u.avatar,
               u.level                                           AS memberLevel,
               rld.sales_amount                                  AS salesAmount,
               rld.order_count                                   AS orderCount,
               rld.reward_amount                                 AS rewardAmount,
               rld.reward_status                                 AS rewardStatus,
               DATE_FORMAT(rld.reward_time, '%Y-%m-%d %H:%i:%s') AS rewardTime
        FROM ranking_leaderboard_detail rld
                 LEFT JOIN user u ON rld.uid = u.uid
        WHERE rld.ranking_id = #{rankingId}
        ORDER BY rld.rank ASC
    </select>

    <!-- 根据时间范围统计销售数据并生成排名 - 优化版本：使用user_commission_record表 -->
    <select id="selectSalesRankingByDateRange" resultType="com.ylpz.model.ranking.RankItem">
        SELECT
        ROW_NUMBER() OVER (ORDER BY sales_amount DESC) AS rank,
        uid,
        nickname,
        phone,
        avatar,
        memberLevel,
        sales_amount AS salesAmount,
        order_count AS orderCount,
        0 AS rewardAmount,
        '待发放' AS rewardStatus,
        NULL AS rewardTime
        FROM (
        SELECT
        u.uid,
        u.nickname,
        u.phone,
        u.avatar,
        u.level AS memberLevel,
        COALESCE(SUM(ucr.order_amount), 0) AS sales_amount,
        COUNT(DISTINCT ucr.link_id) AS order_count
        FROM user u
        LEFT JOIN user_commission_record ucr ON u.uid = ucr.uid
        AND ucr.type = 1 -- 增加类型
        AND ucr.status IN (1, 2) -- 待结算和已结算状态
        AND ucr.pay_time &gt;= #{startDate}
        AND ucr.pay_time &lt;= #{endDate}
        WHERE u.level IN (2, 3) -- VIP会员和SVIP会员
        GROUP BY u.uid, u.nickname, u.phone, u.avatar, u.level
        HAVING sales_amount > 0
        ) t
        ORDER BY sales_amount DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 批量插入排行榜明细 -->
    <insert id="batchInsert">
        INSERT INTO ranking_leaderboard_detail (
        ranking_id, uid, rank, sales_amount, order_count,
        reward_amount, reward_status, create_time, update_time
        ) VALUES
        <foreach collection="detailList" item="item" separator=",">
            (
            #{item.rankingId}, #{item.uid}, #{item.rank}, #{item.salesAmount}, #{item.orderCount},
            #{item.rewardAmount}, #{item.rewardStatus}, NOW(), NOW()
            )
        </foreach>
    </insert>

</mapper>
