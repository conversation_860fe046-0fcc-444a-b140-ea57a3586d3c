<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylpz.core.dao.UserBonusRecordDao">

    <!-- 查询奖励金记录列表 -->
    <select id="getBonusList" resultType="com.ylpz.model.user.UserBonusRecord">
        SELECT 
            r.*,
            u.nickname AS userName
        FROM 
            user_bonus_record r
        LEFT JOIN 
            user u ON r.uid = u.id
        <where>
            <if test="bonusType != null and bonusType != ''">
                AND r.bonus_type = #{bonusType}
            </if>
            <if test="startTime != null and endTime != null">
                AND r.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="userPhone != null and userPhone != ''">
                AND r.user_phone = #{userPhone}
            </if>
            <if test="linkId != null and linkId != ''">
                AND r.link_id = #{linkId}
            </if>
        </where>
        ORDER BY r.create_time DESC
    </select>
    
    <!-- 统计奖励金合计 -->
    <select id="sumBonusTotal" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(price), 0) 
        FROM user_bonus_record
        WHERE status = 1
    </select>
    
    <!-- 按类型统计奖励金 -->
    <select id="sumBonusByType" resultType="java.util.Map">
        SELECT 
            bonus_type as type,
            IFNULL(SUM(price), 0) as amount
        FROM 
            user_bonus_record
        WHERE 
            status = 1
        GROUP BY 
            bonus_type
    </select>

</mapper> 