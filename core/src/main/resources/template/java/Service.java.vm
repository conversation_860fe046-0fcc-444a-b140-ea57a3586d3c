package ${package}.${moduleName}.service;

import com.baomidou.mybatisplus.extension.service.IService;
import ${mainPath}.common.utils.PageUtils;
import ${package}.${moduleName}.entity.${className}Entity;

import java.util.Map;

/**
 * ${comments} 业务接口
 */
public interface ${className}Service extends IService<${className}Entity> {

            /**
            * ${className} 列表查询
        * @param request 默认是是体类 根据自己需求修改或者创建自己的request
        * @param pageParamRequest 分页参数对象
        * @return
        */
    List<${className}> getList(${className}Entity request, PageParamRequest pageParamRequest)
}

