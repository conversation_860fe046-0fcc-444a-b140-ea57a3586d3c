package ${package}.${moduleName}.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

	#if(${hasBigDecimal})
	import java.math.BigDecimal;
	#end
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * ${comments} Entity 实体类
 */
@Data
@TableName("${tableName}")
public class ${className}Entity implements Serializable {
	private static final long serialVersionUID = 1L;

	#foreach ($column in $columns)
		/**
			 * $column.comments
			 */
		#if($column.columnName == $pk.columnName)
		@TableId
		#end
	private $column.attrType $column.attrname;
	#end

}
