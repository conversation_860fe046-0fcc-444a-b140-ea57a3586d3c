package com.ylpz.core.delete;

import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.service.StoreOrderService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.order.StoreOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 订单工具类
 */
@Service
public class OrderUtils {

    @Autowired
    private UserService userService;

    @Autowired
    private StoreOrderService storeOrderService;


    /**
     * 根据订单号查询订单信息
     *
     * @param id 订单id
     * @return 计算后的价格集合
     */
    public StoreOrder getInfoById(Integer id) {
        StoreOrder storeOrder = storeOrderService.getById(id);
        Integer userId = userService.getUserIdException();

        if (null == storeOrder || !userId.equals(storeOrder.getUid())) {
            //订单号错误
            throw new CrmebException("没有找到相关订单信息!");
        }

        return storeOrder;
    }
}
