package com.ylpz.core.service;

import java.util.Map;

/**
 * 自动确认收货服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
public interface AutoConfirmReceiptService {

    /**
     * 执行自动确认收货任务
     * 查找发货后超过配置天数的订单，自动确认收货
     * 
     * @return 执行结果统计信息
     */
    Map<String, Object> executeAutoConfirmReceiptTask();

    /**
     * 手动执行自动确认收货任务（用于测试和管理员手动触发）
     * 
     * @return 执行结果统计信息
     */
    Map<String, Object> manualExecuteTask();
}
