package com.ylpz.core.service;

import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.system.SystemParamSetting;

/**
 * 系统参数设置Service接口
 */
public interface SystemParamSettingService extends IService<SystemParamSetting> {
    /**
     * 获取所有系统参数设置列表
     * 
     * @return 系统参数设置列表
     */
    List<SystemParamSetting> getList();

    /**
     * 根据模块名称获取系统参数设置列表
     * 
     * @param moduleName 模块名称
     * @return 系统参数设置列表
     */
    List<SystemParamSetting> getListByModuleName(String moduleName);
    
    /**
     * 获取按模块类型分组的系统参数设置
     * 
     * @param moduleType 模块类型：member-会员相关模块，order-订单相关模块
     * @return 按模块分组的系统参数设置Map
     */
    Map<String, List<SystemParamSetting>> getGroupedConfigByModuleType(String moduleType);

    /**
     * 按模块批量保存或更新系统参数设置
     * 
     * @param moduleName 模块名称
     * @param configList 配置列表
     * @return 是否成功
     */
    Boolean saveOrUpdateBatchByModule(String moduleName, List<SystemParamSetting> configList);
} 