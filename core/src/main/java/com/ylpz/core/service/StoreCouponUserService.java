package com.ylpz.core.service;

import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreCouponUserRequest;
import com.ylpz.core.common.request.StoreCouponUserSearchRequest;
import com.ylpz.core.common.request.UserCouponReceiveRequest;
import com.ylpz.core.common.response.StoreCouponUserOrder;
import com.ylpz.core.common.response.StoreCouponUserResponse;
import com.ylpz.core.common.response.UserCouponResponse;
import com.ylpz.core.common.vo.MyRecord;
import com.ylpz.core.common.vo.StoreCouponOrderVo;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.coupon.StoreCouponUser;

/**
 * StoreCouponUserService 接口
 */
public interface StoreCouponUserService extends IService<StoreCouponUser> {

    /**
     * 优惠券发放记录
     * @param request 查询参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<StoreCouponUserResponse> getList(StoreCouponUserSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * PC领取优惠券
     * @param storeCouponUserRequest 优惠券参数
     * @return Boolean
     */
    Boolean receive(StoreCouponUserRequest storeCouponUserRequest);

    /**
     * 优惠券过期定时任务
     */
    void overdueTask();

    /**
     * 根据uid获取列表
     * @param uid uid
     * @param pageParamRequest 分页参数
     * @return List<StoreCouponUser>
     */
    List<StoreCouponUser> findListByUid(Integer uid, PageParamRequest pageParamRequest);

    /**
     * 根据uid和状态获取列表
     * @param uid uid
     * @param status 优惠券状态：0-可用(未使用)，1-已用(已使用)，2-已过期(已失效)，null-查询全部
     * @param pageParamRequest 分页参数
     * @return List<StoreCouponUser>
     */
    List<StoreCouponUser> findListByUidAndStatus(Integer uid, Integer status, PageParamRequest pageParamRequest);

    /**
     * 获取可用优惠券数量
     * @param uid 用户uid
     */
    Integer getUseCount(Integer uid);

    /**
     * 发送优惠券过期提醒
     * @param coupon 优惠券
     */
    void sendExpireNotice(StoreCoupon coupon);
    
    /**
     * 给用户发送优惠券
     * @param uid 用户ID
     * @param couponId 优惠券ID
     * @param remark 备注信息
     * @return Boolean 是否发送成功
     */
    Boolean send(Integer uid, Integer couponId, String remark);
    
    /**
     * 根据订单ID获取订单使用的优惠券
     * @param orderId 订单ID
     * @return 优惠券列表
     */
    List<StoreCouponUser> getByOrderId(Integer orderId);
    
    /**
     * 根据订单ID获取订单使用的优惠券详细信息
     * @param orderId 订单ID
     * @return 订单使用的优惠券详细信息
     */
    List<StoreCouponUser> getOrderCouponDetail(Integer orderId);

    /**
     * 根据用户ID获取用户优惠券详细信息列表
     * @param uid 用户ID
     * @param status 优惠券状态：0-可用(未使用)，1-已用(已使用)，2-已过期(已失效)，null-查询全部
     * @param pageParamRequest 分页参数
     * @return 用户优惠券详细信息列表
     */
    List<UserCouponResponse> getUserCouponList(Integer uid, Integer status, PageParamRequest pageParamRequest);
}
