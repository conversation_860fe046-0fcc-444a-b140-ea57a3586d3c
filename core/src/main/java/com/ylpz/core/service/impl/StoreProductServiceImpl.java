package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ylpz.core.common.response.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.*;
import com.ylpz.core.common.utils.ActivityDescriptionProcessor;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.core.common.vo.MyRecord;
import com.ylpz.core.dao.StoreProductDao;
import com.ylpz.core.dao.StoreSeckillDao;
import com.ylpz.core.delete.ProductUtils;
import com.ylpz.core.service.*;
import com.ylpz.model.category.Category;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.model.product.StoreProductAttr;
import com.ylpz.model.product.StoreProductAttrResult;
import com.ylpz.model.product.StoreProductAttrValue;
import com.ylpz.model.product.StoreProductCoupon;
import com.ylpz.model.product.StoreProductDescription;
import com.ylpz.model.seckill.StoreSeckill;
import com.ylpz.model.user.User;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;

/**
 * 商品
 */
@Service
public class StoreProductServiceImpl extends ServiceImpl<StoreProductDao, StoreProduct> implements StoreProductService {

    @Resource
    private StoreProductDao dao;

    @Resource
    private StoreSeckillDao storeSeckillDao;

    @Autowired
    private StoreProductAttrService storeProductAttrService;

    @Autowired
    private StoreProductAttrValueService storeProductAttrValueService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private StoreProductDescriptionService storeProductDescriptionService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private StoreProductRelationService storeProductRelationService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private StoreProductAttrResultService storeProductAttrResultService;

    @Autowired
    private StoreProductCouponService storeProductCouponService;

    @Autowired
    private StoreCouponService storeCouponService;

    @Autowired
    private ProductUtils productUtils;

    @Autowired
    private StoreSeckillService storeSeckillService;

    @Autowired
    private StoreCartService storeCartService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserService userService;

    @Autowired
    private ActivityDescriptionProcessor activityDescriptionProcessor;

    private static final Logger logger = LoggerFactory.getLogger(StoreProductServiceImpl.class);

    /**
     * 获取产品列表Admin
     *
     * @param request 筛选参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    @Override
    public PageInfo<StoreProductResponse> getAdminList(StoreProductSearchRequest request,
        PageParamRequest pageParamRequest) {
        // 带 StoreProduct 类的多条件查询
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 类型搜索
        switch (request.getType()) {
            case 1:
                // 出售中（已上架）
                lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                lambdaQueryWrapper.ne(StoreProduct::getStock, 0);
                break;
            case 2:
                // 仓库中（未上架）
                lambdaQueryWrapper.eq(StoreProduct::getIsShow, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                break;
            case 3:
                // 已售罄
                lambdaQueryWrapper.le(StoreProduct::getStock, 0);
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
                break;
            case 4:
                // 警戒库存
                Integer stock = Integer.parseInt(systemConfigService.getValueByKey("store_stock"));
                lambdaQueryWrapper.le(StoreProduct::getStock, stock);
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                break;
            case 5:
                // 回收站
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, true);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                break;
            default:
                break;
        }

        lambdaQueryWrapper.apply(StringUtils.isNotBlank(request.getCateId()),
            "FIND_IN_SET ('" + request.getCateId() + "', cate_id)");

        lambdaQueryWrapper.orderByDesc(StoreProduct::getSort).orderByDesc(StoreProduct::getId);

        //keywords查询条件  表示商品名称和商品id
        lambdaQueryWrapper.and(StringUtils.isNotBlank(request.getKeywords()),
                wrapper -> wrapper.like(StoreProduct::getId, request.getKeywords())
                    .or()
                    .like(StoreProduct::getStoreName, request.getKeywords())
                );

        // 添加标签查询条件
        if (StrUtil.isNotBlank(request.getTag())) {
            lambdaQueryWrapper.apply("FIND_IN_SET('" + request.getTag() + "', tag)");
        }

        Page<StoreProduct> storeProductPage =
            PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<StoreProduct> storeProducts = dao.selectList(lambdaQueryWrapper);
        List<StoreProductResponse> storeProductResponses = new ArrayList<>();

        // 批量获取所有商品的分类ID
        List<Integer> cateIds = storeProducts.stream().map(StoreProduct::getCateId).filter(Objects::nonNull)
            .flatMap(cateId -> Arrays.stream(cateId.split(","))).map(Integer::valueOf).distinct().collect(Collectors.toList());

        // 批量获取分类信息
        Map<Integer, List<Category>> categoryMap;
        if (!cateIds.isEmpty()) {
            List<Category> categories = categoryService.getByIds(cateIds);
            categoryMap = categories.stream().collect(Collectors.groupingBy(Category::getId));
        } else {
            categoryMap = new HashMap<>();
        }

        for (StoreProduct product : storeProducts) {
            StoreProductResponse storeProductResponse = new StoreProductResponse();
            try {
                BeanUtils.copyProperties(product, storeProductResponse);
                storeProductResponse.setProductId(product.getId());
            } catch (Exception e) {
                // 处理属性拷贝异常
                e.printStackTrace();
                continue;
            }

            List<StoreProductAttrValueResponse> storeProductAttrValueResponse = new ArrayList<>();

            StoreProductAttrValue storeProductAttrValuePram = new StoreProductAttrValue();
            storeProductAttrValuePram.setProductId(product.getId()).setType(Constants.PRODUCT_TYPE_NORMAL);
            List<StoreProductAttrValue> storeProductAttrValues = storeProductAttrValueService.getListByProductIdAndType(product.getId(), Constants.PRODUCT_TYPE_NORMAL);
            storeProductAttrValues.stream().map(e -> {
                StoreProductAttrValueResponse response = new StoreProductAttrValueResponse();
                BeanUtils.copyProperties(e, response);
                storeProductAttrValueResponse.add(response);
                return e;
            }).collect(Collectors.toList());
            storeProductResponse.setAttrValue(storeProductAttrValueResponse);

            // 处理分类中文
            String cateId = product.getCateId();
            if (cateId != null) {
                List<String> cateIdList = Arrays.asList(cateId.split(","));
                List<Category> cg = cateIdList.stream().map(id -> categoryMap.get(Integer.parseInt(id)))
                    .filter(Objects::nonNull).flatMap(List::stream).distinct().collect(Collectors.toList());
                if (CollUtil.isEmpty(cg)) {
                    storeProductResponse.setCateValues("");
                } else {
                    storeProductResponse
                        .setCateValues(cg.stream().map(Category::getName).collect(Collectors.joining(",")));
                }
            } else {
                storeProductResponse.setCateValues("");
            }

            // 生成商品缩略图
            setThumbnailImage(storeProductResponse);
            storeProductResponses.add(storeProductResponse);
        }
        // 多条sql查询处理分页正确
        return CommonPage.copyPageInfo(storeProductPage, storeProductResponses);
    }

    /**
     * 根据商品id集合获取
     *
     * @param productIds id集合
     * @return
     */
    @Override
    public List<StoreProduct> getListInIds(List<Integer> productIds) {
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StoreProduct::getId, productIds);
        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 新增产品
     *
     * @param request 新增产品request对象
     * @return 新增结果
     */
    @Override
    public Boolean save(StoreProductAddRequest request) {
        // 多规格需要校验规格参数
        // if (!request.getSpecType()) {
        // if (request.getAttrValue().size() > 1) {
        // throw new CrmebException("单规格商品属性值不能大于1");
        // }
        // }

        StoreProduct storeProduct = new StoreProduct();
        BeanUtils.copyProperties(request, storeProduct);
        storeProduct.setId(null);
        storeProduct.setAddTime(DateUtil.getNowTime());
        if (1 == request.getStoreStatus()) {
            storeProduct.setIsShow(true);
        } else {
            storeProduct.setIsShow(false);
        }


        // 设置Acticity活动
        storeProduct.setActivity(getProductActivityStr(request.getActivity()));

        // 主图
        if (StrUtil.isBlank(storeProduct.getImage())) {
            JSONArray array = JSONUtil.parseArray(request.getSliderImage());
            storeProduct.setImage(String.valueOf(array.get(0)));
        }
        storeProduct.setImage(systemAttachmentService.clearPrefix(storeProduct.getImage()));
        //缩略图
        storeProduct.setThumbnailImage(systemAttachmentService.clearPrefix(storeProduct.getThumbnailImage()));
        // 轮播图
        storeProduct.setSliderImage(systemAttachmentService.clearPrefix(storeProduct.getSliderImage()));
        // 主图视频
        storeProduct.setVideoLink(systemAttachmentService.clearPrefix(storeProduct.getVideoLink()));
        // 展示图
        if (StrUtil.isNotEmpty(storeProduct.getFlatPattern())) {
            storeProduct.setFlatPattern(systemAttachmentService.clearPrefix(storeProduct.getFlatPattern()));
        }
        // 处理商品详情图片：前端传入detailImagesAdmin，后端自动处理生成detailImages
        if (request.getDetailImagesAdmin() != null) {
            // 清理URL前缀
            String cleanDetailImagesAdmin = systemAttachmentService.clearPrefix(request.getDetailImagesAdmin());
            storeProduct.setDetailImagesAdmin(cleanDetailImagesAdmin);

            // 根据图片信息处理detailImages（图片模式：contentType=1）
            String processedDetailImages = activityDescriptionProcessor.processDescription(
                cleanDetailImagesAdmin, 1);
            storeProduct.setDetailImages(processedDetailImages);
        }

        List<StoreProductAttrValueAddRequest> attrValueAddRequestList = request.getAttrValue();
        // 计算价格
        StoreProductAttrValueAddRequest minAttrValue =
            attrValueAddRequestList.stream().min(Comparator.comparing(StoreProductAttrValueAddRequest::getPrice)).get();
        storeProduct.setPrice(minAttrValue.getPrice());
        storeProduct.setOtPrice(minAttrValue.getOtPrice());
        storeProduct.setCost(minAttrValue.getCost());
        storeProduct
            .setStock(attrValueAddRequestList.stream().mapToInt(StoreProductAttrValueAddRequest::getStock).sum());

        // 默认值设置
        if (ObjectUtil.isNull(request.getSort())) {
            storeProduct.setSort(0);
        }
        if (ObjectUtil.isNull(request.getIsHot())) {
            storeProduct.setIsHot(false);
        }
        if (ObjectUtil.isNull(request.getIsBenefit())) {
            storeProduct.setIsBenefit(false);
        }
        if (ObjectUtil.isNull(request.getIsBest())) {
            storeProduct.setIsBest(false);
        }
        if (ObjectUtil.isNull(request.getIsNew())) {
            storeProduct.setIsNew(false);
        }
        if (ObjectUtil.isNull(request.getIsGood())) {
            storeProduct.setIsGood(false);
        }
        if (ObjectUtil.isNull(request.getGiveIntegral())) {
            storeProduct.setGiveIntegral(0);
        }
        if (ObjectUtil.isNull(request.getFicti())) {
            storeProduct.setFicti(0);
        }

        // 设置售后服务和限购相关字段的默认值
        if (ObjectUtil.isNull(request.getSupportExchange())) {
            storeProduct.setSupportExchange(false);
        }
        if (ObjectUtil.isNull(request.getSupportReturn7days())) {
            storeProduct.setSupportReturn7days(false);
        }
        if (ObjectUtil.isNull(request.getLimitPurchaseCount())) {
            storeProduct.setLimitPurchaseCount(false);
        }
        if (ObjectUtil.isNull(request.getMaxPurchaseCount())) {
            storeProduct.setMaxPurchaseCount(0);
        }
        if (ObjectUtil.isNull(request.getLimitSpecificUsers())) {
            storeProduct.setLimitSpecificUsers(false);
        }

        List<StoreProductAttrAddRequest> addRequestList = request.getAttr();
        List<StoreProductAttr> attrList = addRequestList.stream().map(e -> {
            StoreProductAttr attr = new StoreProductAttr();
            BeanUtils.copyProperties(e, attr);
            attr.setType(Constants.PRODUCT_TYPE_NORMAL);
            return attr;
        }).collect(Collectors.toList());

        List<StoreProductAttrValue> attrValueList = attrValueAddRequestList.stream().map(e -> {
            StoreProductAttrValue attrValue = new StoreProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setId(null);
            attrValue.setSuk(getSku(e.getAttrValue()));
            attrValue.setQuota(0);
            attrValue.setQuotaShow(0);
            attrValue.setType(Constants.PRODUCT_TYPE_NORMAL);
            attrValue.setImage(systemAttachmentService.clearPrefix(e.getImage()));
            return attrValue;
        }).collect(Collectors.toList());

        // 处理富文本
        StoreProductDescription spd = new StoreProductDescription();
        spd.setDescription(
            request.getContent().length() > 0 ? systemAttachmentService.clearPrefix(request.getContent()) : "");
        spd.setType(Constants.PRODUCT_TYPE_NORMAL);

        Boolean execute = transactionTemplate.execute(e -> {
            save(storeProduct);

            attrList.forEach(attr -> attr.setProductId(storeProduct.getId()));
            attrValueList.forEach(value -> value.setProductId(storeProduct.getId()));
            if(CollUtil.isNotEmpty(attrList)){
                storeProductAttrService.saveBatch(attrList);
            }

            storeProductAttrValueService.saveBatch(attrValueList);

            spd.setProductId(storeProduct.getId());
            storeProductDescriptionService.deleteByProductId(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
            storeProductDescriptionService.save(spd);

            if (CollUtil.isNotEmpty(request.getCouponIds())) {
                List<StoreProductCoupon> couponList = new ArrayList<>();
                for (Integer couponId : request.getCouponIds()) {
                    StoreProductCoupon spc =
                        new StoreProductCoupon(storeProduct.getId(), couponId, DateUtil.getNowTime());
                    couponList.add(spc);
                }
                storeProductCouponService.saveBatch(couponList);
            }

            // 保存允许购买的会员等级
            if (storeProduct.getLimitSpecificUsers() != null && storeProduct.getLimitSpecificUsers()
                && CollUtil.isNotEmpty(request.getAllowedLevels())) {
                // 将等级列表转换为逗号分隔的字符串
                String allowedLevelsStr = request.getAllowedLevels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
                storeProduct.setAllowedLevels(allowedLevelsStr);
                updateById(storeProduct);
            } else {
                // 如果不限制特定用户或没有指定等级，清空允许等级字段
                storeProduct.setAllowedLevels(null);
                updateById(storeProduct);
            }

            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 商品sku
     *
     * @param attrValue json字符串
     * @return sku
     */
    private String getSku(String attrValue) {
        LinkedHashMap<String, String> linkedHashMap =
            JSONObject.parseObject(attrValue, LinkedHashMap.class, Feature.OrderedField);
        Iterator<Map.Entry<String, String>> iterator = linkedHashMap.entrySet().iterator();
        List<String> strings = CollUtil.newArrayList();
        while (iterator.hasNext()) {
            Map.Entry<String, String> next = iterator.next();
            strings.add(next.getValue());
        }
        // List<String> strings = jsonObject.values().stream().map(o -> (String) o).collect(Collectors.toList());
        return String.join(",", strings);
    }

    /**
     * 商品活动字符串
     *
     * @param activityList 活动数组
     * @return 商品活动字符串
     */
    private String getProductActivityStr(List<String> activityList) {
        if (CollUtil.isEmpty(activityList)) {
            return "0, 1, 2, 3";
        }
        List<Integer> activities = new ArrayList<>();
        activityList.forEach(e -> {
            switch (e) {
                case Constants.PRODUCT_TYPE_NORMAL_STR:
                    activities.add(Constants.PRODUCT_TYPE_NORMAL);
                    break;
                case Constants.PRODUCT_TYPE_SECKILL_STR:
                    activities.add(Constants.PRODUCT_TYPE_SECKILL);
                    break;
                case Constants.PRODUCT_TYPE_BARGAIN_STR:
                    activities.add(Constants.PRODUCT_TYPE_BARGAIN);
                    break;
                case Constants.PRODUCT_TYPE_PINGTUAN_STR:
                    activities.add(Constants.PRODUCT_TYPE_PINGTUAN);
                    break;
            }
        });
        return activities.stream().map(Object::toString).collect(Collectors.joining(","));
    }

    /**
     * 更新商品信息
     *
     * @param storeProductRequest 商品参数
     * @return 更新结果
     */
    @Override
    public Boolean update(StoreProductAddRequest storeProductRequest) {
        if (ObjectUtil.isNull(storeProductRequest.getId())) {
            throw new CrmebException("商品ID不能为空");
        }

        // if (!storeProductRequest.getSpecType()) {
        // if (storeProductRequest.getAttrValue().size() > 1) {
        // throw new CrmebException("单规格商品属性值不能大于1");
        // }
        // }

        StoreProduct tempProduct = getById(storeProductRequest.getId());
        if (ObjectUtil.isNull(tempProduct)) {
            throw new CrmebException("商品不存在");
        }
        if (tempProduct.getIsRecycle() || tempProduct.getIsDel()) {
            throw new CrmebException("商品已删除");
        }
        //if (tempProduct.getIsShow()) {
        //    throw new CrmebException("请先下架商品，再进行修改");
        //}
        // 如果商品是活动商品主商品不允许修改
        // if (storeSeckillService.isExistByProductId(storeProductRequest.getId())) {
        // throw new CrmebException("商品作为秒杀商品的主商品，需要修改请先删除对应秒杀商品");
        // }
        // if (storeBargainService.isExistByProductId(storeProductRequest.getId())) {
        // throw new CrmebException("商品作为砍价商品的主商品，需要修改请先删除对应砍价商品");
        // }
        // if (storeCombinationService.isExistByProductId(storeProductRequest.getId())) {
        // throw new CrmebException("商品作为拼团商品的主商品，需要修改请先删除对应拼团商品");
        // }

        StoreProduct storeProduct = new StoreProduct();
        BeanUtils.copyProperties(storeProductRequest, storeProduct);

        // 设置Activity活动
        storeProduct.setActivity(getProductActivityStr(storeProductRequest.getActivity()));

        // 主图
        if (StrUtil.isBlank(storeProduct.getImage())) {
            JSONArray array = JSONUtil.parseArray(storeProductRequest.getSliderImage());
            storeProduct.setImage(String.valueOf(array.get(0)));
        }
        storeProduct.setImage(systemAttachmentService.clearPrefix(storeProduct.getImage()));
        //缩略图
        storeProduct.setThumbnailImage(systemAttachmentService.clearPrefix(storeProduct.getThumbnailImage()));
        // 轮播图
        storeProduct.setSliderImage(systemAttachmentService.clearPrefix(storeProduct.getSliderImage()));
        // 主图视频
        storeProduct.setVideoLink(systemAttachmentService.clearPrefix(storeProduct.getVideoLink()));
        // 处理商品详情图片：前端传入detailImagesAdmin，后端自动处理生成detailImages
        if (storeProductRequest.getDetailImagesAdmin() != null) {
             // 清理URL前缀
            String cleanDetailImagesAdmin = systemAttachmentService.clearPrefix(storeProductRequest.getDetailImagesAdmin());
            storeProduct.setDetailImagesAdmin(cleanDetailImagesAdmin);

            // 根据图片信息处理detailImages（图片模式：contentType=1）
            String processedDetailImages = activityDescriptionProcessor.processDescription(
                cleanDetailImagesAdmin, 1);
            storeProduct.setDetailImages(processedDetailImages);
        }

        List<StoreProductAttrValueAddRequest> attrValueAddRequestList = storeProductRequest.getAttrValue();
        // 计算价格
        StoreProductAttrValueAddRequest minAttrValue =
            attrValueAddRequestList.stream().min(Comparator.comparing(StoreProductAttrValueAddRequest::getPrice)).get();
        storeProduct.setPrice(minAttrValue.getPrice());
        storeProduct.setOtPrice(minAttrValue.getOtPrice());
        storeProduct.setCost(minAttrValue.getCost());

        if (1 == storeProductRequest.getStoreStatus()) {
            storeProduct.setIsShow(true);
        } else {
            storeProduct.setIsShow(false);
        }

        // attr部分
        List<StoreProductAttrAddRequest> addRequestList = storeProductRequest.getAttr();
        List<StoreProductAttr> attrAddList = CollUtil.newArrayList();
        List<StoreProductAttr> attrUpdateList = CollUtil.newArrayList();
        addRequestList.forEach(e -> {
            StoreProductAttr attr = new StoreProductAttr();
            BeanUtils.copyProperties(e, attr);
            if (ObjectUtil.isNull(attr.getId())) {
                attr.setProductId(storeProduct.getId());
                attr.setType(Constants.PRODUCT_TYPE_NORMAL);
                attrAddList.add(attr);
            } else {
                attr.setIsDel(false);
                attrUpdateList.add(attr);
            }
        });

        // attrValue部分
        List<StoreProductAttrValue> attrValueAddList = CollUtil.newArrayList();
        List<StoreProductAttrValue> attrValueUpdateList = CollUtil.newArrayList();
        attrValueAddRequestList.forEach(e -> {
            StoreProductAttrValue attrValue = new StoreProductAttrValue();
            BeanUtils.copyProperties(e, attrValue);
            attrValue.setSuk(getSku(e.getAttrValue()));
            attrValue.setImage(systemAttachmentService.clearPrefix(e.getImage()));
            if (ObjectUtil.isNull(attrValue.getId()) || attrValue.getId().equals(0)) {
                attrValue.setId(null);
                attrValue.setProductId(storeProduct.getId());
                attrValue.setQuota(0);
                attrValue.setQuotaShow(0);
                attrValue.setType(Constants.PRODUCT_TYPE_NORMAL);
                attrValueAddList.add(attrValue);
            } else {
                attrValue.setIsDel(false);
                attrValueUpdateList.add(attrValue);
            }
        });

        // 处理富文本
        //StoreProductDescription spd = new StoreProductDescription();
        //spd.setDescription(storeProductRequest.getContent().length() > 0
        //    ? systemAttachmentService.clearPrefix(storeProductRequest.getContent()) : "");
        //spd.setType(Constants.PRODUCT_TYPE_NORMAL);
        //spd.setProductId(storeProduct.getId());

        Boolean execute = transactionTemplate.execute(e -> {
            dao.updateById(storeProduct);

            // 先删除原用attr+value
            storeProductAttrService.deleteByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
            storeProductAttrValueService.deleteByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);

            if (CollUtil.isNotEmpty(attrAddList)) {
                storeProductAttrService.saveBatch(attrAddList);
            }
            if (CollUtil.isNotEmpty(attrUpdateList)) {
                storeProductAttrService.saveOrUpdateBatch(attrUpdateList);
            }

            if (CollUtil.isNotEmpty(attrValueAddList)) {
                storeProductAttrValueService.saveBatch(attrValueAddList);
            }
            if (CollUtil.isNotEmpty(attrValueUpdateList)) {
                storeProductAttrValueService.saveOrUpdateBatch(attrValueUpdateList);
            }

            storeProductDescriptionService.deleteByProductId(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
            //storeProductDescriptionService.save(spd);

            if (CollUtil.isNotEmpty(storeProductRequest.getCouponIds())) {
                storeProductCouponService.deleteByProductId(storeProduct.getId());
                List<StoreProductCoupon> couponList = new ArrayList<>();
                for (Integer couponId : storeProductRequest.getCouponIds()) {
                    StoreProductCoupon spc =
                        new StoreProductCoupon(storeProduct.getId(), couponId, DateUtil.getNowTime());
                    couponList.add(spc);
                }
                storeProductCouponService.saveBatch(couponList);
            } else {
                storeProductCouponService.deleteByProductId(storeProduct.getId());
            }

            // 保存允许购买的会员等级
            if (storeProduct.getLimitSpecificUsers() != null && storeProduct.getLimitSpecificUsers()
                && CollUtil.isNotEmpty(storeProductRequest.getAllowedLevels())) {
                // 将等级列表转换为逗号分隔的字符串
                String allowedLevelsStr = storeProductRequest.getAllowedLevels().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
                storeProduct.setAllowedLevels(allowedLevelsStr);
                updateById(storeProduct);
            } else {
                // 如果不限制特定用户或没有指定等级，清空允许等级字段
                storeProduct.setAllowedLevels(null);
                updateById(storeProduct);
            }

            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 商品详情
     *
     * @param id 商品id
     * @return 详情数据
     */
    @Override
    public StoreProductResponse getByProductId(Integer id) {
        StoreProduct storeProduct = dao.selectById(id);
        if (null == storeProduct)
            throw new CrmebException("未找到对应商品信息");
        StoreProductResponse storeProductResponse = new StoreProductResponse();
        BeanUtils.copyProperties(storeProduct, storeProductResponse);
        StoreProductAttr spaPram = new StoreProductAttr();
        spaPram.setProductId(storeProduct.getId()).setType(Constants.PRODUCT_TYPE_NORMAL);
        storeProductResponse.setAttr(storeProductAttrService.getByEntity(spaPram));

        // 设置商品所参与的活动
        storeProductResponse.setActivityH5(productUtils.getProductCurrentActivity(storeProduct));
        StoreProductAttrValue spavPram = new StoreProductAttrValue();
        spavPram.setProductId(id).setType(Constants.PRODUCT_TYPE_NORMAL);
        List<StoreProductAttrValue> storeProductAttrValues = storeProductAttrValueService.getByEntity(spavPram);
        // 根据attrValue生成前端所需的数据
        List<HashMap<String, Object>> attrValues = new ArrayList<>();

        if (storeProduct.getSpecType()) {
            // 后端多属性用于编辑
            StoreProductAttrResult sparPram = new StoreProductAttrResult();
            sparPram.setProductId(storeProduct.getId()).setType(Constants.PRODUCT_TYPE_NORMAL);
            List<StoreProductAttrResult> attrResults = storeProductAttrResultService.getByEntity(sparPram);
            if (null == attrResults || attrResults.size() == 0) {
                throw new CrmebException("未找到对应属性值");
            }
            StoreProductAttrResult attrResult = attrResults.get(0);
            // PC 端生成skuAttrInfo
            List<StoreProductAttrValueRequest> storeProductAttrValueRequests =
                com.alibaba.fastjson.JSONObject.parseArray(attrResult.getResult(), StoreProductAttrValueRequest.class);
            if (null != storeProductAttrValueRequests) {
                for (int i = 0; i < storeProductAttrValueRequests.size(); i++) {
                    // StoreProductAttrValueRequest storeProductAttrValueRequest = storeProductAttrValueRequests.get(i);
                    HashMap<String, Object> attrValue = new HashMap<>();
                    String currentSku = storeProductAttrValues.get(i).getSuk();
                    List<StoreProductAttrValue> hasCurrentSku = storeProductAttrValues.stream()
                        .filter(e -> e.getSuk().equals(currentSku)).collect(Collectors.toList());
                    StoreProductAttrValue currentAttrValue = hasCurrentSku.get(0);
                    attrValue.put("id", hasCurrentSku.size() > 0 ? hasCurrentSku.get(0).getId() : 0);
                    attrValue.put("image", currentAttrValue.getImage());
                    attrValue.put("cost", currentAttrValue.getCost());
                    attrValue.put("price", currentAttrValue.getPrice());
                    attrValue.put("otPrice", currentAttrValue.getOtPrice());
                    attrValue.put("stock", currentAttrValue.getStock());
                    attrValue.put("barCode", currentAttrValue.getBarCode());
                    attrValue.put("weight", currentAttrValue.getWeight());
                    attrValue.put("volume", currentAttrValue.getVolume());
                    attrValue.put("suk", currentSku);
                    attrValue.put("attrValue",
                        JSON.parseObject(storeProductAttrValues.get(i).getAttrValue(), Feature.OrderedField));
                    attrValue.put("brokerage", currentAttrValue.getBrokerage());
                    attrValue.put("brokerageTwo", currentAttrValue.getBrokerageTwo());
                    String[] skus = currentSku.split(",");
                    for (int k = 0; k < skus.length; k++) {
                        attrValue.put("value" + k, skus[k]);
                    }
                    attrValues.add(attrValue);
                }
            }
        }

        // H5 端用于生成skuList
        List<StoreProductAttrValueResponse> sPAVResponses = new ArrayList<>();

        for (StoreProductAttrValue storeProductAttrValue : storeProductAttrValues) {
            StoreProductAttrValueResponse atr = new StoreProductAttrValueResponse();
            BeanUtils.copyProperties(storeProductAttrValue, atr);
            sPAVResponses.add(atr);
        }
        storeProductResponse.setAttrValues(attrValues);
        storeProductResponse.setAttrValue(sPAVResponses);
        // if (null != storeProductAttrResult) {
        StoreProductDescription sd =
            storeProductDescriptionService.getOne(new LambdaQueryWrapper<StoreProductDescription>()
                .eq(StoreProductDescription::getProductId, storeProduct.getId())
                .eq(StoreProductDescription::getType, Constants.PRODUCT_TYPE_NORMAL));
        if (null != sd) {
            storeProductResponse.setContent(null == sd.getDescription() ? "" : sd.getDescription());
        }
        // }
        // 获取已关联的优惠券
        List<StoreProductCoupon> storeProductCoupons =
            storeProductCouponService.getListByProductId(storeProduct.getId());
        if (null != storeProductCoupons && storeProductCoupons.size() > 0) {
            List<Integer> ids =
                storeProductCoupons.stream().map(StoreProductCoupon::getIssueCouponId).collect(Collectors.toList());
            List<StoreCoupon> shipCoupons = storeCouponService.getByIds(ids);
            storeProductResponse.setCoupons(shipCoupons);
            storeProductResponse.setCouponIds(ids);
        }

        // 获取允许购买的会员等级
        if (storeProduct.getLimitSpecificUsers() && StringUtils.isNotBlank(storeProduct.getAllowedLevels())) {
            // 将逗号分隔的字符串转换为整数列表
            List<Integer> allowedLevels = Arrays.stream(storeProduct.getAllowedLevels().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
            storeProductResponse.setAllowedLevels(allowedLevels);
        }

        // 生成商品缩略图
        setThumbnailImage(storeProductResponse);

        return storeProductResponse;
    }

    /**
     * 商品详情（管理端）
     *
     * @param id 商品id
     * @return StoreProductInfoResponse
     */
    @Override
    public StoreProductInfoResponse getInfo(Integer id) {
        StoreProduct storeProduct = dao.selectById(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new CrmebException("未找到对应商品信息");
        }

        StoreProductInfoResponse storeProductResponse = new StoreProductInfoResponse();
        BeanUtils.copyProperties(storeProduct, storeProductResponse);

        // 设置商品所参与的活动
        List<String> activityList = getProductActivityList(storeProduct.getActivity());
        storeProductResponse.setActivity(activityList);

        List<StoreProductAttr> attrList =
            storeProductAttrService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        storeProductResponse.setAttr(attrList);

        List<StoreProductAttrValue> attrValueList =
            storeProductAttrValueService.getListByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        List<AttrValueResponse> valueResponseList = attrValueList.stream().map(e -> {
            AttrValueResponse valueResponse = new AttrValueResponse();
            BeanUtils.copyProperties(e, valueResponse);
            return valueResponse;
        }).collect(Collectors.toList());
        storeProductResponse.setAttrValue(valueResponseList);

        StoreProductDescription sd =
            storeProductDescriptionService.getByProductIdAndType(storeProduct.getId(), Constants.PRODUCT_TYPE_NORMAL);
        if (ObjectUtil.isNotNull(sd)) {
            storeProductResponse.setContent(ObjectUtil.isNull(sd.getDescription()) ? "" : sd.getDescription());
        }

        // 获取已关联的优惠券
        List<StoreProductCoupon> storeProductCoupons =
            storeProductCouponService.getListByProductId(storeProduct.getId());
        if (CollUtil.isNotEmpty(storeProductCoupons)) {
            List<Integer> ids =
                storeProductCoupons.stream().map(StoreProductCoupon::getIssueCouponId).collect(Collectors.toList());
            storeProductResponse.setCouponIds(ids);
        }

        // 获取允许购买的会员等级
        if (storeProduct.getLimitSpecificUsers() && StringUtils.isNotBlank(storeProduct.getAllowedLevels())) {
            // 将逗号分隔的字符串转换为整数列表
            List<Integer> allowedLevels = Arrays.stream(storeProduct.getAllowedLevels().split(","))
                .map(String::trim)
                .filter(StringUtils::isNotBlank)
                .map(Integer::valueOf)
                .collect(Collectors.toList());
            storeProductResponse.setAllowedLevels(allowedLevels);
        }

        // 生成商品缩略图
        if (StrUtil.isNotBlank(storeProductResponse.getImage())) {
                storeProductResponse.setThumbnailImage(storeProductResponse.getImage());
        }

        return storeProductResponse;
    }

    /**
     * 商品活动字符列表
     *
     * @param activityStr 商品活动字符串
     * @return 商品活动字符列表
     */
    private List<String> getProductActivityList(String activityStr) {
        List<String> activityList = CollUtil.newArrayList();
        if ("0, 1, 2, 3".equals(activityStr)) {
            activityList.add(Constants.PRODUCT_TYPE_NORMAL_STR);
            activityList.add(Constants.PRODUCT_TYPE_SECKILL_STR);
            activityList.add(Constants.PRODUCT_TYPE_BARGAIN_STR);
            activityList.add(Constants.PRODUCT_TYPE_PINGTUAN_STR);
            return activityList;
        }
        String[] split = activityStr.split(",");
        for (String s : split) {
            Integer integer = Integer.valueOf(s);
            if (integer.equals(Constants.PRODUCT_TYPE_NORMAL)) {
                activityList.add(Constants.PRODUCT_TYPE_NORMAL_STR);
            }
            if (integer.equals(Constants.PRODUCT_TYPE_SECKILL)) {
                activityList.add(Constants.PRODUCT_TYPE_SECKILL_STR);
            }
            if (integer.equals(Constants.PRODUCT_TYPE_BARGAIN)) {
                activityList.add(Constants.PRODUCT_TYPE_BARGAIN_STR);
            }
            if (integer.equals(Constants.PRODUCT_TYPE_PINGTUAN)) {
                activityList.add(Constants.PRODUCT_TYPE_PINGTUAN_STR);
            }
        }
        return activityList;
    }

    /**
     * 根据商品tabs获取对应类型的产品数量
     *
     * @return List
     */
    @Override
    public List<StoreProductTabsHeader> getTabsHeader() {
        List<StoreProductTabsHeader> headers = new ArrayList<>();
        StoreProductTabsHeader header1 = new StoreProductTabsHeader(0, "在售中", 1);
        StoreProductTabsHeader header2 = new StoreProductTabsHeader(0, "仓库中", 2);
        StoreProductTabsHeader header3 = new StoreProductTabsHeader(0, "已售罄", 3);
        // StoreProductTabsHeader header4 = new StoreProductTabsHeader(0, "警戒库存", 4);
        // StoreProductTabsHeader header5 = new StoreProductTabsHeader(0, "商品回收站", 5);
        headers.add(header1);
        headers.add(header3);
        headers.add(header2);
        // headers.add(header4);
        // headers.add(header5);
        for (StoreProductTabsHeader h : headers) {
            LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            switch (h.getType()) {
                case 1:
                    // 出售中（已上架）
                    lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    lambdaQueryWrapper.ne(StoreProduct::getStock, 0);
                    break;
                case 2:
                    // 仓库中（未上架）
                    lambdaQueryWrapper.eq(StoreProduct::getIsShow, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    break;
                case 3:
                    // 已售罄
                    lambdaQueryWrapper.le(StoreProduct::getStock, 0);
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
                    break;
                case 4:
                    // 警戒库存
                    Integer stock = Integer.parseInt(systemConfigService.getValueByKey("store_stock"));
                    lambdaQueryWrapper.le(StoreProduct::getStock, stock);
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    break;
                case 5:
                    // 回收站
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, true);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    break;
                default:
                    break;
            }
            List<StoreProduct> storeProducts = dao.selectList(lambdaQueryWrapper);
            h.setCount(storeProducts.size());
        }

        return headers;
    }

    /**
     * 根据商品库存tabs获取对应类型的产品数量
     *
     * @return List
     */
    @Override
    public List<StoreProductTabsHeader> getStockTabsHeader() {
        List<StoreProductTabsHeader> headers = new ArrayList<>();
        StoreProductTabsHeader header1 = new StoreProductTabsHeader(0, "商品库存管理", 1);
        StoreProductTabsHeader header2 = new StoreProductTabsHeader(0, "活动库存管理", 2);
        StoreProductTabsHeader header3 = new StoreProductTabsHeader(0, "售卖预警管理", 3);
        headers.add(header1);
        headers.add(header2);
        headers.add(header3);

        for (StoreProductTabsHeader h : headers) {
            LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            switch (h.getType()) {
                case 1:
                    // 商品库存管理
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    List<StoreProduct> storeProducts = dao.selectList(lambdaQueryWrapper);
                    h.setCount(storeProducts.size());
                    break;
                case 3:
                    // 售卖预警管理
                    Integer stock = Integer.parseInt(systemConfigService.getValueByKey("store_stock"));
                    lambdaQueryWrapper.le(StoreProduct::getStock, stock);
                    lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                    lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                    List<StoreProduct> saleWarningProducts = dao.selectList(lambdaQueryWrapper);
                    h.setCount(saleWarningProducts.size());
                    break;
                case 2:
                    // 活动库存管理
                    LambdaQueryWrapper<StoreSeckill> seckillMangerLambdaQueryWrapper = Wrappers.lambdaQuery();
                    List<StoreSeckill> storeSeckillMangers =
                        storeSeckillDao.selectList(seckillMangerLambdaQueryWrapper);
                    h.setCount(storeSeckillMangers.size());
                    break;
                default:
                    break;
            }
        }
        return headers;
    }

    /**
     * 后台任务批量操作库存
     */
    @Override
    public void consumeProductStock() {
        String redisKey = Constants.PRODUCT_STOCK_UPDATE;
        Long size = redisUtil.getListSize(redisKey);
        logger.info("StoreProductServiceImpl.doProductStock | size:" + size);
        if (size < 1) {
            return;
        }
        for (int i = 0; i < size; i++) {
            // 如果10秒钟拿不到一个数据，那么退出循环
            Object data = redisUtil.getRightPop(redisKey, 10L);
            if (null == data) {
                continue;
            }
            try {
                StoreProductStockRequest storeProductStockRequest = com.alibaba.fastjson.JSONObject.toJavaObject(
                    com.alibaba.fastjson.JSONObject.parseObject(data.toString()), StoreProductStockRequest.class);
                boolean result = doProductStock(storeProductStockRequest);
                if (!result) {
                    redisUtil.lPush(redisKey, data);
                }
            } catch (Exception e) {
                redisUtil.lPush(redisKey, data);
            }
        }
    }

    /**
     * 根据商品id取出二级分类
     *
     * @param productIdStr String 商品分类
     * @return List<Integer>
     */
    @Override
    public List<Integer> getSecondaryCategoryByProductId(String productIdStr) {
        List<Integer> idList = new ArrayList<>();

        if (StringUtils.isBlank(productIdStr)) {
            return idList;
        }
        List<Integer> productIdList = CrmebUtil.stringToArray(productIdStr);
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StoreProduct::getId, productIdList);
        List<StoreProduct> productList = dao.selectList(lambdaQueryWrapper);
        if (productIdList.size() < 1) {
            return idList;
        }

        // 把所有的分类id写入集合
        for (StoreProduct storeProduct : productList) {
            List<Integer> categoryIdList = CrmebUtil.stringToArray(storeProduct.getCateId());
            idList.addAll(categoryIdList);
        }

        // 去重
        List<Integer> cateIdList = idList.stream().distinct().collect(Collectors.toList());
        if (cateIdList.size() < 1) {
            return idList;
        }

        // 取出所有的二级分类
        List<Category> categoryList = categoryService.getByIds(cateIdList);
        if (categoryList.size() < 1) {
            return idList;
        }

        for (Category category : categoryList) {
            List<Integer> parentIdList = CrmebUtil.stringToArrayByRegex(category.getPath(), "/");
            if (parentIdList.size() > 2) {
                Integer secondaryCategoryId = parentIdList.get(2);
                if (secondaryCategoryId > 0) {
                    idList.add(secondaryCategoryId);
                }
            }
        }
        return idList;
    }

    /**
     * @param productId 商品id
     * @param type 类型：recycle——回收站 delete——彻底删除
     * @return Boolean
     */
    @Override
    public Boolean deleteProduct(Integer productId, String type) {
        StoreProduct product = getById(productId);
        if (ObjectUtil.isNull(product)) {
            throw new CrmebException("商品不存在");
        }
        if (StrUtil.isNotBlank(type) && "recycle".equals(type) && product.getIsDel()) {
            throw new CrmebException("商品已存在回收站");
        }

        LambdaUpdateWrapper<StoreProduct> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if (StrUtil.isNotBlank(type) && "delete".equals(type)) {
            // 判断商品活动状态(秒杀、砍价、拼团)
            //isExistActivity(productId);

//            // 删除商品详情中的分割图片
//            if (product.getDetailImages() != null) {
//                activityDescriptionProcessor.cleanupDescriptionImages(product.getDetailImages());
//            }

            lambdaUpdateWrapper.eq(StoreProduct::getId, productId);
            lambdaUpdateWrapper.set(StoreProduct::getIsDel, true);
            return update(lambdaUpdateWrapper);
        }
        lambdaUpdateWrapper.eq(StoreProduct::getId, productId);
        lambdaUpdateWrapper.set(StoreProduct::getIsRecycle, true);
        return update(lambdaUpdateWrapper);
    }

    /**
     * 判断商品活动状态(秒杀、砍价、拼团)
     *
     * @param productId
     */
    private void isExistActivity(Integer productId) {
        Boolean existActivity = false;
        // 秒杀活动判断
        existActivity = storeSeckillService.isExistActivity(productId);
        if (existActivity) {
            throw new CrmebException("有商品关联的秒杀商品活动开启中，不能删除");
        }
    }

    /**
     * 恢复已删除的商品
     *
     * @param productId 商品id
     * @return 恢复结果
     */
    @Override
    public Boolean reStoreProduct(Integer productId) {
        LambdaUpdateWrapper<StoreProduct> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(StoreProduct::getId, productId);
        lambdaUpdateWrapper.set(StoreProduct::getIsRecycle, false);
        return update(lambdaUpdateWrapper);
    }

    /////////////////////////////////////////// 自定义方法

    /**
     * 扣减库存任务操作
     *
     * @param storeProductStockRequest 扣减库存参数
     * @return 执行结果
     */
    @Override
    public boolean doProductStock(StoreProductStockRequest storeProductStockRequest) {
        // 获取商品本身信息
        StoreProduct existProduct = getById(storeProductStockRequest.getProductId());
        List<StoreProductAttrValue> existAttr =
            storeProductAttrValueService.getListByProductIdAndAttrId(storeProductStockRequest.getProductId(),
                storeProductStockRequest.getAttrId().toString(), storeProductStockRequest.getType());
        if (null == existProduct || null == existAttr) { // 未找到商品
            logger.info("库存修改任务未获取到商品信息" + JSON.toJSONString(storeProductStockRequest));
            return true;
        }

        // 回滚商品库存/销量 并更新
        boolean isPlus = "add".equals(storeProductStockRequest.getOperationType());
        int productStock = isPlus ? existProduct.getStock() + storeProductStockRequest.getNum()
            : existProduct.getStock() - storeProductStockRequest.getNum();
        existProduct.setStock(productStock);
        existProduct.setSales(existProduct.getSales() - storeProductStockRequest.getNum());
        updateById(existProduct);

        // 回滚sku库存
        for (StoreProductAttrValue attrValue : existAttr) {
            int productAttrStock = isPlus ? attrValue.getStock() + storeProductStockRequest.getNum()
                : attrValue.getStock() - storeProductStockRequest.getNum();
            attrValue.setStock(productAttrStock);
            attrValue.setSales(attrValue.getSales() - storeProductStockRequest.getNum());
            storeProductAttrValueService.updateById(attrValue);
        }
        return true;
    }

    /**
     * 添加/扣减库存
     *
     * @param id 商品id
     * @param num 数量
     * @param type 类型：add—添加，sub—扣减
     */
    @Override
    public Boolean operationStock(Integer id, Integer num, String type) {
        UpdateWrapper<StoreProduct> updateWrapper = new UpdateWrapper<>();
        if ("add".equals(type)) {
            updateWrapper.setSql(StrUtil.format("stock = stock + {}", num));
            //updateWrapper.setSql(StrUtil.format("sales = sales - {}", num));
        }
        if ("sub".equals(type)) {
            updateWrapper.setSql(StrUtil.format("stock = stock - {}", num));
            //updateWrapper.setSql(StrUtil.format("sales = sales + {}", num));
            // 扣减时加乐观锁保证库存不为负
            updateWrapper.last(StrUtil.format(" and (stock - {} >= 0)", num));
        }
        updateWrapper.eq("id", id);
        boolean update = update(updateWrapper);
        if (!update) {
            throw new CrmebException("更新普通商品库存失败,商品id = " + id);
        }
        return update;
    }

    /**
     * 批量库存操作
     *
     * @param productIds 商品ID列表
     * @param operationType 操作类型：add—增加，sub—减少，set—设置为指定值
     * @param quantity 操作数量
     * @param remark 操作备注
     * @return 批量操作结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchStockOperationResponse batchOperationStock(
            List<Integer> productIds, String operationType, Integer quantity, String remark) {

        BatchStockOperationResponse response = new BatchStockOperationResponse();

        List<Integer> successIds = new ArrayList<>();
        List<Integer> failIds = new ArrayList<>();
        StringBuilder failReasons = new StringBuilder();

        // 设置基本信息
        response.setTotalCount(productIds.size());
        response.setOperationType(operationType);
        response.setQuantity(quantity);
        response.setRemark(remark);

        // 批量处理每个商品
        for (Integer productId : productIds) {
            try {
                boolean success = false;

                // 根据操作类型执行不同的库存操作
                if ("add".equals(operationType)) {
                    success = operationStock(productId, quantity, "add");
                } else if ("sub".equals(operationType)) {
                    success = operationStock(productId, quantity, "sub");
                } else if ("set".equals(operationType)) {
                    // 设置为指定值：先查询当前库存，然后计算差值进行调整
                    StoreProduct product = getById(productId);
                    if (product != null) {
                        int currentStock = product.getStock();
                        int diff = quantity - currentStock;
                        if (diff > 0) {
                            success = operationStock(productId, diff, "add");
                        } else if (diff < 0) {
                            success = operationStock(productId, Math.abs(diff), "sub");
                        } else {
                            success = true; // 库存已经是目标值，无需操作
                        }
                    }
                }

                if (success) {
                    successIds.add(productId);
                } else {
                    failIds.add(productId);
                    if (failReasons.length() > 0) {
                        failReasons.append("; ");
                    }
                    failReasons.append("商品ID ").append(productId).append(" 操作失败");
                }

            } catch (Exception e) {
                failIds.add(productId);
                if (failReasons.length() > 0) {
                    failReasons.append("; ");
                }
                failReasons.append("商品ID ").append(productId).append(" 操作异常: ").append(e.getMessage());
                logger.error("批量库存操作失败，商品ID: {}, 错误: {}", productId, e.getMessage(), e);
            }
        }

        // 设置结果统计
        response.setSuccessCount(successIds.size());
        response.setFailCount(failIds.size());
        response.setSuccessIds(successIds);
        response.setFailIds(failIds);
        response.setFailReason(failReasons.toString());

        return response;
    }

    /**
     * 下架
     *
     * @param id 商品id
     */
    @Override
    public Boolean offShelf(Integer id) {
        StoreProduct storeProduct = getById(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new CrmebException("商品不存在");
        }
        if (!storeProduct.getIsShow()) {
            return true;
        }

        storeProduct.setIsShow(false);
        storeProduct.setStoreStatus(0); // 设置为仓库状态
        Boolean execute = transactionTemplate.execute(e -> {
            dao.updateById(storeProduct);
            storeCartService.productStatusNotEnable(id);
            // 商品下架时，清除用户收藏
            storeProductRelationService.deleteByProId(storeProduct.getId());
            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 上架
     *
     * @param id 商品id
     * @return Boolean
     */
    @Override
    public Boolean putOnShelf(Integer id) {
        StoreProduct storeProduct = getById(id);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new CrmebException("商品不存在");
        }
        if (storeProduct.getIsShow()) {
            return true;
        }

        // 获取商品skuid
        StoreProductAttrValue tempSku = new StoreProductAttrValue();
        tempSku.setProductId(id);
        tempSku.setType(Constants.PRODUCT_TYPE_NORMAL);
        List<StoreProductAttrValue> skuList = storeProductAttrValueService.getByEntity(tempSku);
        List<Integer> skuIdList = skuList.stream().map(StoreProductAttrValue::getId).collect(Collectors.toList());

        storeProduct.setIsShow(true);
        storeProduct.setStoreStatus(1); // 设置为立即上架状态
        
        Boolean execute = transactionTemplate.execute(e -> {
            dao.updateById(storeProduct);
            //storeCartService.productStatusNoEnable(skuIdList);
            return Boolean.TRUE;
        });
        return execute;
    }

    /**
     * 首页商品列表
     *
     * @param type 类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】
     * @param pageParamRequest 分页参数
     * @return CommonPage
     */
    @Override
    public List<StoreProduct> getIndexProduct(Integer type, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.select(StoreProduct::getId, StoreProduct::getImage, StoreProduct::getStoreName,
            StoreProduct::getPrice, StoreProduct::getOtPrice, StoreProduct::getActivity);
        switch (type) {
            case Constants.INDEX_RECOMMEND_BANNER: // 精品推荐
                lambdaQueryWrapper.eq(StoreProduct::getIsBest, true);
                break;
            case Constants.INDEX_HOT_BANNER: // 热门榜单
                lambdaQueryWrapper.eq(StoreProduct::getIsHot, true);
                break;
            case Constants.INDEX_NEW_BANNER: // 首发新品
                lambdaQueryWrapper.eq(StoreProduct::getIsNew, true);
                break;
            case Constants.INDEX_BENEFIT_BANNER: // 促销单品
                lambdaQueryWrapper.eq(StoreProduct::getIsBenefit, true);
                break;
            case Constants.INDEX_GOOD_BANNER: // 优选推荐
                lambdaQueryWrapper.eq(StoreProduct::getIsGood, true);
                break;
        }

        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
        lambdaQueryWrapper.gt(StoreProduct::getStock, 0);
        lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);

        lambdaQueryWrapper.orderByDesc(StoreProduct::getSort);
        lambdaQueryWrapper.orderByDesc(StoreProduct::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取商品移动端列表
     *
     * @param request 筛选参数
     * @param pageRequest 分页参数
     * @return List
     */
    @Override
    public List<StoreProduct> findH5List(ProductRequest request, PageParamRequest pageRequest) {

        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        // id、名称、图片、价格、销量、活动
        lqw.select(StoreProduct::getId, StoreProduct::getStoreName, StoreProduct::getImage, StoreProduct::getPrice,
            StoreProduct::getActivity, StoreProduct::getSales, StoreProduct::getFicti, StoreProduct::getUnitName,
            StoreProduct::getFlatPattern, StoreProduct::getStock);

        lqw.eq(StoreProduct::getIsRecycle, false);
        lqw.eq(StoreProduct::getIsDel, false);
        lqw.eq(StoreProduct::getMerId, false);
        lqw.gt(StoreProduct::getStock, 0);
        lqw.eq(StoreProduct::getIsShow, true);

        if (ObjectUtil.isNotNull(request.getCid()) && request.getCid() > 0) {
            // 查找当前类下的所有子类
            List<Category> childVoListByPid = categoryService.getChildVoListByPid(request.getCid());
            List<Integer> categoryIdList = childVoListByPid.stream().map(Category::getId).collect(Collectors.toList());
            categoryIdList.add(request.getCid());
            lqw.apply(CrmebUtil.getFindInSetSql("cate_id", (ArrayList<Integer>)categoryIdList));
        }

        if (StrUtil.isNotBlank(request.getKeyword())) {
            if (CrmebUtil.isString2Num(request.getKeyword())) {
                Integer productId = Integer.valueOf(request.getKeyword());
                lqw.like(StoreProduct::getId, productId);
            } else {
                lqw.like(StoreProduct::getStoreName, request.getKeyword());
            }
        }

        // 排序部分
        if (StrUtil.isNotBlank(request.getSalesOrder())) {
            if (request.getSalesOrder().equals(Constants.SORT_DESC)) {
                lqw.last(" order by (sales + ficti) desc, sort desc, id desc");
            } else {
                lqw.last(" order by (sales + ficti) asc, sort asc, id asc");
            }
        } else {
            if (StrUtil.isNotBlank(request.getPriceOrder())) {
                if (request.getPriceOrder().equals(Constants.SORT_DESC)) {
                    lqw.orderByDesc(StoreProduct::getPrice);
                } else {
                    lqw.orderByAsc(StoreProduct::getPrice);
                }
            }

            lqw.orderByDesc(StoreProduct::getSort);
            lqw.orderByDesc(StoreProduct::getId);
        }
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());
        return dao.selectList(lqw);
    }

    /**
     * 获取移动端商品详情
     *
     * @param id 商品id
     * @return StoreProduct
     */
    @Override
    public StoreProduct getH5Detail(Integer id) {
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreProduct::getId, StoreProduct::getImage, StoreProduct::getStoreName,
            StoreProduct::getSliderImage, StoreProduct::getOtPrice, StoreProduct::getStock, StoreProduct::getSales,
            StoreProduct::getPrice, StoreProduct::getActivity, StoreProduct::getFicti, StoreProduct::getIsSub,
            StoreProduct::getStoreInfo, StoreProduct::getBrowse, StoreProduct::getUnitName);
        lqw.eq(StoreProduct::getId, id);
        lqw.eq(StoreProduct::getIsRecycle, false);
        lqw.eq(StoreProduct::getIsDel, false);
        lqw.eq(StoreProduct::getIsShow, true);
        StoreProduct storeProduct = dao.selectOne(lqw);
        if (ObjectUtil.isNull(storeProduct)) {
            throw new CrmebException(StrUtil.format("未找到编号为{}的商品", id));
        }

        StoreProductDescription sd =
            storeProductDescriptionService.getOne(new LambdaQueryWrapper<StoreProductDescription>()
                .eq(StoreProductDescription::getProductId, storeProduct.getId())
                .eq(StoreProductDescription::getType, Constants.PRODUCT_TYPE_NORMAL));
        if (ObjectUtil.isNotNull(sd)) {
            storeProduct.setContent(StrUtil.isBlank(sd.getDescription()) ? "" : sd.getDescription());
        }
        return storeProduct;
    }

    /**
     * 获取购物车商品信息
     *
     * @param productId 商品编号
     * @return StoreProduct
     */
    @Override
    public StoreProduct getCartByProId(Integer productId) {
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreProduct::getId, StoreProduct::getImage, StoreProduct::getStoreName);
        lqw.eq(StoreProduct::getId, productId);
        return dao.selectOne(lqw);
    }

    /**
     * 根据日期获取新增商品数量
     *
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    @Override
    public Integer getNewProductByDate(String date) {
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreProduct::getId);
        lqw.eq(StoreProduct::getIsDel, 0);
        lqw.apply("date_format(add_time, '%Y-%m-%d') = {0}", date);
        return Math.toIntExact(dao.selectCount(lqw));
    }

    /**
     * 获取所有未删除的商品
     *
     * @return List<StoreProduct>
     */
    @Override
    public List<StoreProduct> findAllProductByNotDelte() {
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreProduct::getId);
        lqw.eq(StoreProduct::getIsDel, 0);
        return dao.selectList(lqw);
    }

    /**
     * 模糊搜索商品名称
     *
     * @param productName 商品名称
     * @return List
     */
    @Override
    public List<StoreProduct> likeProductName(String productName) {
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreProduct::getId);
        lqw.like(StoreProduct::getStoreName, productName);
        lqw.eq(StoreProduct::getIsDel, 0);
        return dao.selectList(lqw);
    }

    /**
     * 警戒库存数量
     *
     * @return Integer
     */
    @Override
    public Integer getVigilanceInventoryNum() {
        Integer stock = Integer.parseInt(systemConfigService.getValueByKey("store_stock"));
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.le(StoreProduct::getStock, stock);
        lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        return Math.toIntExact(dao.selectCount(lambdaQueryWrapper));
    }

    /**
     * 销售中（上架）商品数量
     *
     * @return Integer
     */
    @Override
    public Integer getOnSaleNum() {
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
        lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        return Math.toIntExact(dao.selectCount(lambdaQueryWrapper));
    }

    /**
     * 未销售（仓库）商品数量
     *
     * @return Integer
     */
    @Override
    public Integer getNotSaleNum() {
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(StoreProduct::getIsShow, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        return Math.toIntExact(dao.selectCount(lambdaQueryWrapper));
    }

    /**
     * 获取商品排行榜 1. 3个商品以内不返回数据 2. TOP10
     *
     * @return List
     */
    @Override
    public List<StoreProduct> getLeaderboard() {
        QueryWrapper<StoreProduct> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_show", true);
        queryWrapper.eq("is_recycle", false);
        queryWrapper.eq("is_del", false);
        queryWrapper.last("limit 10");
        Integer count = Math.toIntExact(dao.selectCount(queryWrapper));
        if (count < 4) {
            return CollUtil.newArrayList();
        }
        queryWrapper.select("id", "store_name", "image", "price", "ot_price", "(sales + ficti) as sales");
        queryWrapper.orderByDesc("sales");
        return dao.selectList(queryWrapper);
    }

    /**
     * 自动上架商品 检查商品的预售时间，如果时间到达则自动上架
     */
    @Override
    public void autoOnShelf() {
        // 获取当前时间戳
        int currentTime = DateUtil.getNowTime();

        // 查询所有未上架且预售时间小于等于当前时间的商品
        LambdaQueryWrapper<StoreProduct> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreProduct::getIsShow, false) // 未上架
            .eq(StoreProduct::getIsDel, false) // 未删除
            .eq(StoreProduct::getIsRecycle, false) // 未在回收站
            .eq(StoreProduct::getStoreStatus, 2) // 定时上架状态
            .le(StoreProduct::getSaleTime, currentTime) // 预售时间小于等于当前时间
            .gt(StoreProduct::getSaleTime, 0); // 预售时间大于0（表示设置了预售时间）

        List<StoreProduct> productList = dao.selectList(lqw);
        if (CollUtil.isEmpty(productList)) {
            return;
        }

        // 批量更新商品状态为上架
        for (StoreProduct product : productList) {
            try {
                product.setIsShow(true);
                product.setStoreStatus(1); // 更新为立即上架状态
                dao.updateById(product);
                logger.info("商品自动上架成功，商品ID：{}, 商品名称：{}", product.getId(), product.getStoreName());
            } catch (Exception e) {
                logger.error("商品自动上架失败，商品ID：" + product.getId() + " | msg : " + e.getMessage());
            }
        }
    }

    @Override
    public PageInfo<StoreProductResponse> getAdminStockList(StoreProductStockSearchRequest request,
        PageParamRequest pageParamRequest) {
        // 带 StoreProduct 类的多条件查询
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        switch (request.getType()) {
            case 1:
                // 商品库存管理
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                break;
            case 3:
                // 售卖预警管理
                Integer stock = Integer.parseInt(systemConfigService.getValueByKey("store_stock"));
                lambdaQueryWrapper.le(StoreProduct::getStock, stock);
                lambdaQueryWrapper.eq(StoreProduct::getIsRecycle, false);
                lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
                break;
            default:
                break;
        }
        // 关键字搜索
        if (StrUtil.isNotBlank(request.getKeywords())) {
            lambdaQueryWrapper.and(i -> i.or().eq(StoreProduct::getId, request.getKeywords()).or()
                .like(StoreProduct::getStoreName, request.getKeywords()).or()
                .like(StoreProduct::getKeyword, request.getKeywords()));
        }
        lambdaQueryWrapper.apply(StringUtils.isNotBlank(request.getCateId()),
            "FIND_IN_SET ('" + request.getCateId() + "', cate_id)");
        lambdaQueryWrapper.orderByDesc(StoreProduct::getSort).orderByDesc(StoreProduct::getId);

        Page<StoreProduct> storeProductPage =
            PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<StoreProduct> storeProducts = dao.selectList(lambdaQueryWrapper);
        List<StoreProductResponse> storeProductResponses = new ArrayList<>();
        for (StoreProduct product : storeProducts) {
            StoreProductResponse storeProductResponse = new StoreProductResponse();
            BeanUtils.copyProperties(product, storeProductResponse);
            StoreProductAttr storeProductAttrPram = new StoreProductAttr();
            storeProductAttrPram.setProductId(product.getId()).setType(Constants.PRODUCT_TYPE_NORMAL);
            List<StoreProductAttr> attrs = storeProductAttrService.getByEntity(storeProductAttrPram);

            if (attrs.size() > 0) {
                storeProductResponse.setAttr(attrs);
            }
            List<StoreProductAttrValueResponse> storeProductAttrValueResponse = new ArrayList<>();

            StoreProductAttrValue storeProductAttrValuePram = new StoreProductAttrValue();
            storeProductAttrValuePram.setProductId(product.getId()).setType(Constants.PRODUCT_TYPE_NORMAL);
            List<StoreProductAttrValue> storeProductAttrValues =
                storeProductAttrValueService.getByEntity(storeProductAttrValuePram);
            storeProductAttrValues.stream().map(e -> {
                StoreProductAttrValueResponse response = new StoreProductAttrValueResponse();
                BeanUtils.copyProperties(e, response);
                storeProductAttrValueResponse.add(response);
                return e;
            }).collect(Collectors.toList());
            storeProductResponse.setAttrValue(storeProductAttrValueResponse);
            // 处理富文本
            StoreProductDescription sd =
                storeProductDescriptionService.getOne(new LambdaQueryWrapper<StoreProductDescription>()
                    .eq(StoreProductDescription::getProductId, product.getId())
                    .eq(StoreProductDescription::getType, Constants.PRODUCT_TYPE_NORMAL));
            if (null != sd) {
                storeProductResponse.setContent(null == sd.getDescription() ? "" : sd.getDescription());
            }
            // 处理分类中文
            List<Category> cg = categoryService.getByIds(CrmebUtil.stringToArray(product.getCateId()));
            if (CollUtil.isEmpty(cg)) {
                storeProductResponse.setCateValues("");
            } else {
                storeProductResponse.setCateValues(cg.stream().map(Category::getName).collect(Collectors.joining(",")));
            }

            storeProductResponse
                .setCollectCount(storeProductRelationService.getList(product.getId(), "collect").size());
            // 生成商品缩略图
            setThumbnailImage(storeProductResponse);
            storeProductResponses.add(storeProductResponse);
        }
        // 多条sql查询处理分页正确
        return CommonPage.copyPageInfo(storeProductPage, storeProductResponses);
    }

    private void setTagName(StoreProductResponse response) {
        if (StrUtil.isNotBlank(response.getTag())) {
            String[] tags = response.getTag().split(",");
            StringBuilder tagNames = new StringBuilder();
            for (String tag : tags) {
                switch (tag) {
                    case "1":
                        tagNames.append("人气爆款,");
                        break;
                    case "2":
                        tagNames.append("热销推荐,");
                        break;
                    default:
                        break;
                }
            }
            if (tagNames.length() > 0) {
                response.setTagName(tagNames.substring(0, tagNames.length() - 1));
            } else {
                response.setTagName("");
            }
        }
    }

    /**
     * 验证用户是否有权限购买商品
     * 
     * @param productId 商品ID
     * @param uid 用户ID
     * @return 是否有权限购买
     */
    @Override
    public boolean validateUserPurchasePermission(Integer productId, Integer uid) {
        if (productId == null || uid == null) {
            return false;
        }

        StoreProduct product = getById(productId);
        if (product == null || product.getIsDel() || !product.getIsShow()) {
            return false;
        }

        // 如果商品限制特定用户购买，需要验证用户会员等级是否在允许列表中
        if (product.getLimitSpecificUsers() != null && product.getLimitSpecificUsers()) {
            if (StringUtils.isBlank(product.getAllowedLevels())) {
                // 如果没有设置允许的等级，则不允许购买
                return false;
            }

            // 获取用户信息
            User user = userService.getById(uid);
            if (user == null) {
                return false;
            }

            // 检查用户等级是否在允许的等级列表中
            List<String> allowedLevelList = Arrays.asList(product.getAllowedLevels().split(","));
            String userLevel = String.valueOf(user.getLevel());
            return allowedLevelList.contains(userLevel);
        }

        return true;
    }

    /**
     * 设置商品缩略图（336*336尺寸）
     * @param storeProductResponse 商品响应对象
     */
    private void setThumbnailImage(StoreProductResponse storeProductResponse) {
        if (storeProductResponse == null || StrUtil.isBlank(storeProductResponse.getImage())) {
            return;
        }
        storeProductResponse.setThumbnailImage(storeProductResponse.getThumbnailImage());
    }


    /**
     * 批量上架商品
     *
     * @param ids 商品id集合
     * @return Boolean
     */
    @Override
    public Boolean batchPutOnShelf(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 查询商品列表
        List<StoreProduct> productList = dao.selectBatchIds(ids);
        if (CollUtil.isEmpty(productList)) {
            throw new CrmebException("未找到商品信息");
        }

        // 批量处理
        Boolean execute = transactionTemplate.execute(e -> {
            for (StoreProduct product : productList) {
                if (!product.getIsShow()) { // 只处理未上架的商品
                    // 获取商品skuid
                    StoreProductAttrValue tempSku = new StoreProductAttrValue();
                    tempSku.setProductId(product.getId());
                    tempSku.setType(Constants.PRODUCT_TYPE_NORMAL);
                    List<StoreProductAttrValue> skuList = storeProductAttrValueService.getByEntity(tempSku);
                    List<Integer> skuIdList = skuList.stream().map(StoreProductAttrValue::getId).collect(Collectors.toList());

                    product.setIsShow(true);
                    product.setStoreStatus(1); // 设置为立即上架状态
                    dao.updateById(product);
                    //storeCartService.productStatusNoEnable(skuIdList);
                }
            }
            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 批量下架商品
     *
     * @param ids 商品id集合
     * @return Boolean
     */
    @Override
    public Boolean batchOffShelf(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 查询商品列表
        List<StoreProduct> productList = dao.selectBatchIds(ids);
        if (CollUtil.isEmpty(productList)) {
            throw new CrmebException("未找到商品信息");
        }

        // 批量处理
        Boolean execute = transactionTemplate.execute(e -> {
            for (StoreProduct product : productList) {
                if (product.getIsShow()) { // 只处理已上架的商品
                    product.setIsShow(false);
                    product.setStoreStatus(0); // 设置为仓库状态
                    dao.updateById(product);
                    storeCartService.productStatusNotEnable(product.getId());
                    // 商品下架时，清除用户收藏
                    storeProductRelationService.deleteByProId(product.getId());
                }
            }
            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 批量将商品加入回收站
     *
     * @param ids 商品id集合
     * @return Boolean
     */
    @Override
    public Boolean batchRecycle(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return false;
        }

        // 查询商品列表
        List<StoreProduct> productList = dao.selectBatchIds(ids);
        if (CollUtil.isEmpty(productList)) {
            throw new CrmebException("未找到商品信息");
        }

        // 批量处理
        Boolean execute = transactionTemplate.execute(e -> {
            for (StoreProduct product : productList) {
                if (product.getIsRecycle()) {
                    continue; // 跳过已经在回收站的商品
                }

                // 判断商品活动状态
                // try {
                // isExistActivity(product.getId());
                // } catch (CrmebException ex) {
                // logger.error("商品ID：" + product.getId() + " 加入回收站失败，原因：" + ex.getMessage());
                // continue; // 如果有活动，跳过此商品
                // }

                // 更新商品状态
                product.setIsRecycle(true);
                dao.updateById(product);

                // 如果商品已上架，则将相关购物车设为失效
                if (product.getIsShow()) {
                    storeCartService.productStatusNotEnable(product.getId());
                }
            }
            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 批量设置商品属性
     * @param request 批量设置请求对象
     * @return Boolean
     */
    @Override
    public Boolean batchSetting(StoreProductBatchSettingRequest request) {
        if (CollUtil.isEmpty(request.getIds())) {
            return false;
        }

        // 查询商品列表
        List<StoreProduct> productList = dao.selectBatchIds(request.getIds());
        if (CollUtil.isEmpty(productList)) {
            throw new CrmebException("未找到商品信息");
        }

        // 批量处理
        Boolean execute = transactionTemplate.execute(e -> {
            for (StoreProduct product : productList) {
                // 设置虚拟销量
                if (request.getSetFicti() != null && request.getSetFicti()) {
                    product.setFicti(request.getFicti());
                }
                
                // 设置快递方式
                if (request.getSetFreightType() != null && request.getSetFreightType()) {
                    product.setFreightType(request.getFreightType());
                    if (request.getFreightType() == 0) { // 统一邮费
                        product.setPostage(request.getPostage());
                        product.setTempId(0); // 清空运费模板ID
                    } else if (request.getFreightType() == 1) { // 运费模板
                        product.setTempId(request.getTempId());
                        product.setPostage(BigDecimal.ZERO); // 清空统一邮费
                    }
                }
                
                dao.updateById(product);
            }
            return Boolean.TRUE;
        });

        return execute;
    }

    /**
     * 根据关键词搜索商品列表
     * @param keywords 搜索关键词
     * @return 商品列表
     */
    @Override
    public List<StoreProduct> getProductListByKeywords(String keywords) {
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 只查询未删除且已上架的商品
        lambdaQueryWrapper.eq(StoreProduct::getIsDel, false);
        lambdaQueryWrapper.eq(StoreProduct::getIsShow, true);
        
        // 添加关键词搜索条件
        if (StrUtil.isNotBlank(keywords)) {
            lambdaQueryWrapper.like(StoreProduct::getStoreName, keywords);
        }
        
        // 限制返回数量
        Page<StoreProduct> page = PageHelper.startPage(1, 50);
        return dao.selectList(lambdaQueryWrapper);
    }

}
