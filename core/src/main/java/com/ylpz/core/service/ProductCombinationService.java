package com.ylpz.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.response.ProductCombinationStatusCount;
import com.ylpz.core.common.vo.ProductCombinationVO;
import com.ylpz.model.product.ProductCombination;

/**
 * 商品搭配Service接口
 */
public interface ProductCombinationService extends IService<ProductCombination> {

    /**
     * 创建商品搭配
     */
    void createCombination(ProductCombinationVO vo);

    /**
     * 更新商品搭配
     */
    void updateCombination(ProductCombinationVO vo);

    /**
     * 删除商品搭配
     */
    void deleteCombination(Long id);
    
    /**
     * 批量删除商品搭配
     */
    void batchDeleteCombinations(List<Long> ids);

    /**
     * 获取商品搭配详情
     */
    ProductCombinationVO getCombinationDetail(Long id);

    /**
     * 分页查询商品搭配列表
     * @param page 页码
     * @param size 每页数量
     * @param name 搭配名称，可为空
     * @param statusType 状态类型：0-全部，1-在售中，2-已售罄，3-仓库中
     * @return 分页结果
     */
    PageInfo<ProductCombinationVO> pageCombination(Integer page, Integer size, String name, Integer statusType);

    /**
     * 更新商品搭配状态
     */
    void updateStatus(Long id, Integer status);

    /**
     * 更新商品上架状态
     * @param id 商品搭配ID
     * @param storeStatus 上架状态：0-暂不售卖放入仓库，1-立即上架
     */
    void updateStoreStatus(Long id, Integer storeStatus);
    
    /**
     * 批量更新商品上架状态
     * @param ids 商品搭配ID列表
     * @param storeStatus 上架状态：0-暂不售卖放入仓库，1-立即上架

     */
    void batchUpdateStoreStatus(List<Long> ids, Integer storeStatus);

    /**
     * 更新是否允许使用满减券
     * @param id 商品搭配ID
     * @param allowDiscount 是否允许：0-不允许，1-允许
     */
    void updateAllowDiscount(Long id, Integer allowDiscount);
    
    /**
     * 获取商品搭配状态统计
     * @return 包含在售中、已售罄、仓库中数量的统计结果
     */
    ProductCombinationStatusCount getStatusCount();
    
    /**
     * 根据商品ID查询相关的组合
     * @param productId 商品ID
     * @return 商品关联的组合列表
     */
    List<ProductCombination> getByProductId(Integer productId);

    /**
     * 自动调整组合商品状态
     * 根据售卖开始时间和结束时间自动调整组合商品的启用/禁用状态
     */
    void autoAdjustCombinationStatus();
}