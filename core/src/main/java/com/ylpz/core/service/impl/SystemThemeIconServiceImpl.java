package com.ylpz.core.service.impl;

import java.util.List;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.SystemThemeIconDao;
import com.ylpz.core.service.SystemThemeIconService;
import com.ylpz.model.system.SystemThemeIcon;

@Service
public class SystemThemeIconServiceImpl extends ServiceImpl<SystemThemeIconDao, SystemThemeIcon>
    implements SystemThemeIconService {

    /**
     * 根据主题ID获取图标列表
     * 
     * @param themeId 主题ID
     * @return List<SystemThemeIcon>
     */
    @Override
    public List<SystemThemeIcon> getListByThemeId(Integer themeId) {
        LambdaQueryWrapper<SystemThemeIcon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemThemeIcon::getThemeId, themeId);
        queryWrapper.orderByAsc(SystemThemeIcon::getSort);
        return list(queryWrapper);
    }

    /**
     * 批量保存主题图标
     * 
     * @param themeId 主题ID
     * @param icons 图标列表
     * @return Boolean
     */
    @Override
    public Boolean saveIcons(Integer themeId, List<SystemThemeIcon> icons) {
        // 先删除原有图标
        deleteByThemeId(themeId);

        // 设置主题ID
        icons.forEach(icon -> icon.setThemeId(themeId));

        return saveBatch(icons);
    }

    /**
     * 删除主题图标
     * 
     * @param themeId 主题ID
     * @return Boolean
     */
    @Override
    public Boolean deleteByThemeId(Integer themeId) {
        LambdaQueryWrapper<SystemThemeIcon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemThemeIcon::getThemeId, themeId);
        return remove(queryWrapper);
    }
}