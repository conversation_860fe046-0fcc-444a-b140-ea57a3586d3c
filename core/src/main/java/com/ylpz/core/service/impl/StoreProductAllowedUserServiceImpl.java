package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreProductAllowedUserDao;
import com.ylpz.core.service.StoreProductAllowedUserService;
import com.ylpz.model.product.StoreProductAllowedUser;

/**
 * 商品允许购买的特定用户服务实现类
 */
@Deprecated
@Service
public class StoreProductAllowedUserServiceImpl extends ServiceImpl<StoreProductAllowedUserDao, StoreProductAllowedUser> implements StoreProductAllowedUserService {

    /**
     * 根据商品ID获取允许购买的用户列表
     * @param productId 商品ID
     * @return 允许购买的用户ID列表
     */
    @Override
    public List<Integer> getAllowedUserIdsByProductId(Integer productId) {
        LambdaQueryWrapper<StoreProductAllowedUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreProductAllowedUser::getProductId, productId);
        lqw.eq(StoreProductAllowedUser::getIsDel, false);
        lqw.select(StoreProductAllowedUser::getUid);
        
        List<StoreProductAllowedUser> list = list(lqw);
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }
        
        return list.stream().map(StoreProductAllowedUser::getUid).collect(Collectors.toList());
    }

    /**
     * 批量保存商品允许购买的用户
     * @param productId 商品ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveAllowedUsers(Integer productId, List<Integer> userIds) {
        // 先删除原有记录
        deleteByProductId(productId);
        
        // 没有指定用户时，直接返回成功
        if (userIds == null || userIds.isEmpty()) {
            return true;
        }
        
        // 批量保存新记录
        List<StoreProductAllowedUser> allowedUsers = new ArrayList<>();
        int now = DateUtil.getNowTime();
        
        for (Integer uid : userIds) {
            if(uid == null){
                continue;
            }
            StoreProductAllowedUser user = new StoreProductAllowedUser();
            user.setProductId(productId);
            user.setUid(uid);
            user.setUserType(1); // 默认为普通用户
            user.setAddTime(now);
            user.setIsDel(false);
            allowedUsers.add(user);
        }
        
        return saveBatch(allowedUsers);
    }

    /**
     * 删除商品所有允许购买的用户
     * @param productId 商品ID
     * @return 是否成功
     */
    @Override
    public boolean deleteByProductId(Integer productId) {
        LambdaQueryWrapper<StoreProductAllowedUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreProductAllowedUser::getProductId, productId);
        return remove(lqw);
    }

    /**
     * 检查用户是否允许购买指定商品
     * @param productId 商品ID
     * @param uid 用户ID
     * @return 是否允许购买
     */
    @Override
    public boolean isUserAllowedToBuy(Integer productId, Integer uid) {
        LambdaQueryWrapper<StoreProductAllowedUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreProductAllowedUser::getProductId, productId);
        lqw.eq(StoreProductAllowedUser::getUid, uid);
        lqw.eq(StoreProductAllowedUser::getIsDel, false);
        
        return count(lqw) > 0;
    }
} 