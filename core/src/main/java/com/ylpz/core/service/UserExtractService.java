package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.response.UserExtractDetailResponse;
import com.ylpz.core.common.response.UserExtractRecordResponse;
import com.github.pagehelper.PageInfo;
import com.ylpz.model.finance.UserExtract;
import com.ylpz.core.common.request.UserExtractRequest;
import com.ylpz.core.common.request.UserExtractSearchRequest;
import com.ylpz.core.common.response.BalanceResponse;
import com.ylpz.core.common.response.UserExtractResponse;

import java.math.BigDecimal;
import java.util.List;

/**
 * UserExtractService 接口
 */
public interface UserExtractService extends IService<UserExtract> {

    List<UserExtract> getList(UserExtractSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取提现列表（包含用户手机号）
     * 
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     * @return 提现详情列表
     */
    List<UserExtractDetailResponse> getListWithDetail(UserExtractSearchRequest request,
            PageParamRequest pageParamRequest);

    /**
     * 提现总金额
     */
    BalanceResponse getBalance(String dateLimit);

    /**
     * 提现总金额
     * 
     * <AUTHOR>
     * @since 2020-05-11
     * @return BalanceResponse
     */
    BigDecimal getWithdrawn(String startTime, String endTime);

    UserExtractResponse getUserExtractByUserId(Integer userId);

    /**
     * 提现审核
     * 
     * @param id          提现申请id
     * @param status      审核状态 -1 已拒绝 0 待审核 1 审核通过 2 提现成功 3 打款失败
     * @param backMessage 驳回原因
     * @return 审核结果
     */
    Boolean updateStatus(Integer id, Integer status, String backMessage);

    /**
     * 修改提现申请
     * 
     * @param id                 申请id
     * @param userExtractRequest 具体参数
     */
    Boolean updateExtract(Integer id, UserExtractRequest userExtractRequest);

    /**
     * 批量审核提现申请
     * 
     * @param ids         提现申请ID列表，逗号分隔
     * @param status      审核状态 -1 已拒绝 0 待审核 1 审核通过 2 提现成功 3 打款失败
     * @param backMessage 驳回原因，当status=-1时必填
     * @return 审核结果
     */
    Boolean batchUpdateStatus(String ids, Integer status, String backMessage);

    /**
     * 更新付款流水号
     * 
     * @param id        提现申请id
     * @param paymentNo 付款流水号
     * @return 更新结果
     */
    Boolean updatePaymentNo(Integer id, String paymentNo);

    /**
     * 获取提现详情，包含用户手机号
     * 
     * @param id 提现申请id
     * @return 提现详情响应对象
     */
    UserExtractDetailResponse getDetailById(Integer id);

    /**
     * 获取提现总金额
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param uid       用户id
     * @return 提现总金额
     */
    BigDecimal getWithdrawnByUserId(String startTime, String endTime, Integer uid);
}
