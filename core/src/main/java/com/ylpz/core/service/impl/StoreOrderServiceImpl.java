package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.model.order.*;
import com.ylpz.model.order.PayTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreDateRangeSqlPram;
import com.ylpz.core.common.request.StoreOrderSearchRequest;
import com.ylpz.core.common.response.StoreOrderCountItemResponse;
import com.ylpz.core.common.response.StoreOrderDetailResponse;
import com.ylpz.core.common.response.StoreOrderInfoResponse;
import com.ylpz.core.common.response.StoreOrderStatisticsResponse;
import com.ylpz.core.common.response.StoreOrderTopItemResponse;
import com.ylpz.core.common.response.StoreOrderFlowResponse;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.core.common.vo.ExpressSheetVo;
import com.ylpz.core.common.vo.LogisticsResultVo;
import com.ylpz.core.common.vo.StoreOrderInfoVo;
import com.ylpz.core.common.vo.dateLimitUtilVo;
import com.ylpz.core.dao.StoreOrderDao;
import com.ylpz.core.service.*;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserCommissionRecord;
import com.ylpz.model.order.StoreOrderAftersale;
import com.ylpz.core.dao.StoreOrderAftersaleDao;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * StoreOrderServiceImpl 接口实现
 */
@Slf4j
@Service
public class StoreOrderServiceImpl extends ServiceImpl<StoreOrderDao, StoreOrder> implements StoreOrderService {

    @Resource
    private StoreOrderDao dao;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreOrderStatusService storeOrderStatusService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Resource
    private StoreOrderAftersaleDao storeOrderAftersaleDao;

    /**
     * 列表
     *
     * @param request          请求参数
     * @param pageParamRequest 分页类参数
     * @return CommonPage<StoreOrderDetailResponse>
     */
    @Override
    public CommonPage<StoreOrderDetailResponse> getAdminList(StoreOrderSearchRequest request, PageParamRequest pageParamRequest) {
        Page<Object> startPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        QueryWrapper<StoreOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("id", "order_id", "uid", "real_name", "total_Price", "pay_price", "pay_type", "create_time", "status"
                , "refund_reason_wap_img", "refund_reason_wap_explain", "refund_reason_wap", "refund_reason", "refund_reason_time"
                , "is_del", "combination_id", "pink_id", "seckill_id", "bargain_id", "verify_code", "remark", "paid", "is_system_del", "shipping_type", "type", "is_alter_price");
        if (StrUtil.isNotBlank(request.getOrderNo())) {
            queryWrapper.and(wrapper -> wrapper.eq("order_id", request.getOrderNo()).or().eq("id", request.getOrderNo()));
        }
        getRequestTimeWhere(queryWrapper, request);
        getStatusWhereNew(queryWrapper, request.getStatus());
        if (!request.getType().equals(2)) {
            queryWrapper.eq("type", request.getType());
        }

        // 添加付款方式查询条件
        if (StrUtil.isNotBlank(request.getPayType())) {
            queryWrapper.eq("pay_type", request.getPayType());
        }

        if (StrUtil.isNotBlank(request.getUid())) {
            queryWrapper.eq("uid", request.getUid());
        }

        // 添加收货人姓名或手机号查询条件
        if (StrUtil.isNotBlank(request.getReceiverKeyword())) {
            queryWrapper.and(wrapper -> wrapper.like("real_name", request.getReceiverKeyword()).or().like("user_phone", request.getReceiverKeyword()));
        }

        queryWrapper.orderByDesc("id");
        //商品名称筛选store_order_info product_name
        if (StrUtil.isNotBlank(request.getStoreName())) {
            Set<Integer> ids = storeOrderInfoService.list(
                            new LambdaQueryWrapper<StoreOrderInfo>().like(StoreOrderInfo::getProductName, request.getStoreName()))
                    .stream().map(StoreOrderInfo::getOrderId).collect(Collectors.toSet());
            queryWrapper.in(CollUtil.isNotEmpty(ids), "id", ids);
        }
        //订单备注
        queryWrapper.like(StrUtil.isNotBlank(request.getRemark()), "remark", request.getRemark());
        //快递单号
        queryWrapper.like(StrUtil.isNotBlank(request.getExpressNo()), "delivery_id", request.getExpressNo());

        List<StoreOrder> orderList = dao.selectList(queryWrapper);
        List<StoreOrderDetailResponse> detailResponseList = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderList)) {
            detailResponseList = formatOrder1(orderList);
        }
        return CommonPage.restPage(CommonPage.copyPageInfo(startPage, detailResponseList));
    }

    /**
     * 格式化订单信息，对外输出一致
     *
     * @param orderList List<StoreOrder> 订单列表
     * @return List<StoreOrderItemResponse>
     */
    private List<StoreOrderDetailResponse> formatOrder1(List<StoreOrder> orderList) {
        List<StoreOrderDetailResponse> detailResponseList = new ArrayList<>();
        if (CollUtil.isEmpty(orderList)) {
            return detailResponseList;
        }

        //订单id集合
        List<Integer> orderIdList = orderList.stream().map(StoreOrder::getId).distinct().collect(Collectors.toList());

        //获取订单详情map
        HashMap<Integer, List<StoreOrderInfoVo>> orderInfoList = storeOrderInfoService.getMapInId(orderIdList);

        //根据用户获取信息
        List<Integer> userIdList = orderList.stream().map(StoreOrder::getUid).distinct().collect(Collectors.toList());
        //订单用户信息
        HashMap<Integer, User> userList = userService.getMapListInUid(userIdList);

        for (StoreOrder storeOrder : orderList) {
            StoreOrderDetailResponse storeOrderItemResponse = new StoreOrderDetailResponse();
            BeanUtils.copyProperties(storeOrder, storeOrderItemResponse);

            storeOrderItemResponse.setProductList(orderInfoList.get(storeOrder.getId()));

            //订单状态
            storeOrderItemResponse.setStatusStr(getStatus(storeOrder));
            //支付方式
            storeOrderItemResponse.setPayTypeStr(getPayType(storeOrder.getPayType()));

            // 添加订单类型信息
            storeOrderItemResponse.setOrderType(getOrderTypeStr(storeOrder));
            // 订单价格
            storeOrderItemResponse.setOrderPrice(storeOrder.getTotalPrice());

            // 设置用户信息
            User orderUser = userList.get(storeOrder.getUid());
            if (orderUser != null) {
                // 如果订单中的用户信息为空，则从用户表中获取
                if (StrUtil.isBlank(storeOrderItemResponse.getRealName())) {
                    storeOrderItemResponse.setRealName(orderUser.getNickname());
                }
                if (StrUtil.isBlank(storeOrderItemResponse.getUserPhone())) {
                    storeOrderItemResponse.setUserPhone(orderUser.getPhone());
                }

                // 设置买家名称
                storeOrderItemResponse.setBuyerName(orderUser.getNickname());

                // 设置买家会员等级
                storeOrderItemResponse.setBuyerLevel(orderUser.getLevel() + "");
            }

            // 确保物流信息正确设置
            storeOrderItemResponse.setDeliveryName(storeOrder.getDeliveryName());
            storeOrderItemResponse.setDeliveryId(storeOrder.getDeliveryId());
            storeOrderItemResponse.setDeliveryCode(storeOrder.getDeliveryCode());

            // 获取发货时间
            if (storeOrder.getStatus() >= 2) { // 待收货、待评价、已完成状态表示已发货
                // 获取发货时间
                List<StoreOrderStatus> orderStatusList = storeOrderStatusService.getByEntity(
                    new StoreOrderStatus().setOid(storeOrder.getId()).setChangeType("delivery_goods"));
                if (orderStatusList != null && !orderStatusList.isEmpty()) {
                    storeOrderItemResponse.setDeliveryTime(orderStatusList.get(0).getCreateTime());
                }
            }

            detailResponseList.add(storeOrderItemResponse);
        }
        return detailResponseList;
    }

    /**
     * 获取订单类型（前端展示）
     *
     * @param storeOrder 订单
     * @return String
     */
    private String getOrderTypeStr(StoreOrder storeOrder) {
        String orderTypeFormat = "[{}订单]{}";
        String orderType = StrUtil.format(orderTypeFormat, "普通", "");
        if (storeOrder.getType().equals(1)) {
            orderType = StrUtil.format(orderTypeFormat, "送礼", "");
        }
        return orderType;
    }

    /**
     * 按开始结束时间分组订单
     *
     * @param date    String 时间范围
     * @param lefTime int 截取创建时间长度
     * @return HashMap<String, Object>
     * <AUTHOR>
     * @since 2020-05-16
     */
    public List<StoreOrder> getOrderGroupByDate(String date, int lefTime) {
        QueryWrapper<StoreOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(pay_price) as pay_price", "left(create_time, " + lefTime + ") as orderId", "count(id) as id");
        if (StringUtils.isNotBlank(date)) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(date);
            queryWrapper.between("create_time", dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        queryWrapper.groupBy("orderId").orderByAsc("orderId");
        return dao.selectList(queryWrapper);
    }

    /**
     * 订单详情（PC）
     *
     * @param orderNo 订单编号
     * @return StoreOrderInfoResponse
     */
    @Override
    public StoreOrderInfoResponse info(String orderNo) {
        StoreOrder storeOrder = getInfoException(orderNo);
        if (storeOrder.getIsSystemDel()) {
            throw new CrmebException("未找到对应订单信息");
        }
        StoreOrderInfoResponse storeOrderInfoResponse = new StoreOrderInfoResponse();
        BeanUtils.copyProperties(storeOrder, storeOrderInfoResponse);
        List<StoreOrderInfoVo> orderInfos = storeOrderInfoService.getOrderListByOrderId(storeOrder.getId());
        storeOrderInfoResponse.setOrderInfo(orderInfos);
        storeOrderInfoResponse.setPayTypeStr(getPayType(storeOrder.getPayType()));
        storeOrderInfoResponse.setStatusStr(getStatus(storeOrder));

        //用户信息
        User user = userService.getById(storeOrder.getUid());
        storeOrderInfoResponse.setRealName(user.getNickname());
        storeOrderInfoResponse.setUserPhone(user.getPhone());
        // 设置买家名称
        storeOrderInfoResponse.setBuyerName(user.getNickname());
        // 设置买家会员等级
        storeOrderInfoResponse.setBuyerLevel(user.getLevel() + "");

        UserCommissionRecord brokerageRecord = userCommissionRecordService.getByLinkIdAndLinkType(orderNo, null);
        if (ObjectUtil.isNotNull(brokerageRecord)) {
            User spread = userService.getById(brokerageRecord.getUid());
            storeOrderInfoResponse.setSpreadName(spread.getNickname());
        }

        storeOrderInfoResponse.setProTotalPrice(storeOrder.getProTotalPrice());
        
        // 添加订单流程状态信息
        StoreOrderFlowResponse orderFlow = new StoreOrderFlowResponse();
        
        // 下单状态（始终为已完成）
        orderFlow.setOrderStatus(1);
        orderFlow.setOrderTime(storeOrder.getCreateTime());
        orderFlow.setOrderUser(user.getNickname());
        
        // 付款状态
        if (storeOrder.getPaid()) {
            orderFlow.setPayStatus(1);
            orderFlow.setPayTime(storeOrder.getPayTime());
            // 获取支付方式描述
            String payTypeStr = getPayType(storeOrder.getPayType());
            orderFlow.setPayType(payTypeStr);
        }
        
        // 发货状态 - 精确判断哪些状态表示已发货
        if (isOrderShipped(storeOrder)) {
            orderFlow.setDeliveryStatus(1);
            // 获取发货时间
            List<StoreOrderStatus> orderStatusList = storeOrderStatusService.getByEntity(
                    new StoreOrderStatus().setOid(storeOrder.getId()).setChangeType(OrderStatusEnum.MERCHANT_SHIPPED.getCode() + ""));
            if (orderStatusList != null && !orderStatusList.isEmpty()) {
                orderFlow.setDeliveryTime(orderStatusList.get(0).getCreateTime());
            }
            orderFlow.setExpressName(storeOrder.getDeliveryName());
        }
        
        // 交易完成状态 - 精确判断哪些状态表示交易完成
        if (isOrderCompleted(storeOrder)) {
            orderFlow.setCompleteStatus(1);
            // 获取交易完成时间
            List<StoreOrderStatus> orderStatusList = storeOrderStatusService.getByEntity(
                new StoreOrderStatus().setOid(storeOrder.getId()).setChangeType(OrderStatusEnum.COMPLETED.getCode() + ""));
            if (orderStatusList != null && !orderStatusList.isEmpty()) {
                orderFlow.setCompleteTime(orderStatusList.get(0).getCreateTime());
            } else {
                // 如果没有找到交易完成记录，使用系统当前时间
                orderFlow.setCompleteTime(storeOrder.getUpdateTime());
            }
        }
        
        storeOrderInfoResponse.setOrderFlow(orderFlow);
        
        // 查询售后信息
        StoreOrderAftersale aftersale = getAftersaleByOrderId(storeOrder.getId());
        if (aftersale != null) {
            storeOrderInfoResponse.setAftersaleId(aftersale.getId());
            storeOrderInfoResponse.setAftersaleType(aftersale.getAftersaleType());
            storeOrderInfoResponse.setAftersaleReason(aftersale.getAftersaleReason());
            storeOrderInfoResponse.setAftersaleImages(aftersale.getAftersaleImages());
            storeOrderInfoResponse.setAftersaleExplain(aftersale.getAftersaleExplain());
            storeOrderInfoResponse.setAftersaleStatus(aftersale.getAftersaleStatus());
            storeOrderInfoResponse.setFrontendAftersaleReason(aftersale.getFrontendAftersaleReason());
            storeOrderInfoResponse.setAftersaleRefundPrice(aftersale.getRefundPrice());
            storeOrderInfoResponse.setAftersaleCreateTime(aftersale.getCreateTime());
        } else {
            // 如果没有售后信息，设置默认值
            storeOrderInfoResponse.setAftersaleId(0);
            storeOrderInfoResponse.setAftersaleType(0);
            storeOrderInfoResponse.setAftersaleStatus(0);
        }
        
        return storeOrderInfoResponse;
    }

    /**
     * 订单备注
     *
     * @param orderNo 订单编号
     * @param mark    备注
     * @return Boolean
     */
    @Override
    public Boolean mark(String orderNo, String mark) {
        StoreOrder storeOrder = getInfoException(orderNo);
        storeOrder.setRemark(mark);
        return updateById(storeOrder);
    }

    @Override
    public LogisticsResultVo getLogisticsInfo(String orderNo) {
        return null;
    }

    /**
     * 改价
     *
     * @param orderNo  订单编号
     * @param price    修改后的价格
     * @param oldPrice 原支付金额
     */
    private Boolean orderEditPrice(String orderNo, BigDecimal price, BigDecimal oldPrice) {
        LambdaUpdateWrapper<StoreOrder> luw = new LambdaUpdateWrapper<>();
        luw.set(StoreOrder::getPayPrice, price);
        luw.set(StoreOrder::getBeforePayPrice, oldPrice);
        luw.set(StoreOrder::getIsAlterPrice, 1);
        luw.eq(StoreOrder::getOrderId, orderNo);
        luw.eq(StoreOrder::getPaid, false);
        return update(luw);
    }

    /**
     * 根据时间参数统计订单销售额
     *
     * @param dateLimit 时间区间
     * @param type      类型
     * @return 统计订单信息
     */
    @Override
    public StoreOrderStatisticsResponse orderStatisticsByTime(String dateLimit, Integer type) {
        StoreOrderStatisticsResponse response = new StoreOrderStatisticsResponse();
        // 根据开始时间和结束时间获取时间差 再根据时间差获取上一个时间段 查询当前和上一个时间段的数据 进行比较且返回
        dateLimitUtilVo dateRange = DateUtil.getDateLimit(dateLimit);
        String dateStartD = dateRange.getStartTime();
        String dateEndD = dateRange.getEndTime();
        int days = DateUtil.daysBetween(
                DateUtil.strToDate(dateStartD, Constants.DATE_FORMAT_DATE),
                DateUtil.strToDate(dateEndD, Constants.DATE_FORMAT_DATE)
        );
        // 同时间区间的上一个时间起点
        String perDateStart = DateUtil.addDay(
                DateUtil.strToDate(dateStartD, Constants.DATE_FORMAT_DATE), -days, Constants.DATE_FORMAT_START);
        // 当前时间区间
        String dateStart = DateUtil.addDay(
                DateUtil.strToDate(dateStartD, Constants.DATE_FORMAT_DATE), 0, Constants.DATE_FORMAT_START);
        String dateEnd = DateUtil.addDay(
                DateUtil.strToDate(dateEndD, Constants.DATE_FORMAT_DATE), 0, Constants.DATE_FORMAT_END);

        // 上一个时间段查询
        List<StoreOrder> orderPerList = getOrderPayedByDateLimit(perDateStart, dateStart);

        // 当前时间段
        List<StoreOrder> orderCurrentList = getOrderPayedByDateLimit(dateStart, dateEnd);
        double increasePrice = 0;
        if (type == 1) {
            double perSumPrice = orderPerList.stream().mapToDouble(e -> e.getPayPrice().doubleValue()).sum();
            double currentSumPrice = orderCurrentList.stream().mapToDouble(e -> e.getPayPrice().doubleValue()).sum();

            response.setChart(dao.getOrderStatisticsPriceDetail(new StoreDateRangeSqlPram(dateStart, dateEnd)));
            response.setTime(BigDecimal.valueOf(currentSumPrice).setScale(2, BigDecimal.ROUND_HALF_UP));
            // 当前营业额和上一个同比营业额增长区间
            increasePrice = currentSumPrice - perSumPrice;
            if (increasePrice <= 0) response.setGrowthRate(0);
            else if (perSumPrice == 0) response.setGrowthRate((int) increasePrice * 100);
            else response.setGrowthRate((int) ((increasePrice * perSumPrice) * 100));
        } else if (type == 2) {
            response.setChart(dao.getOrderStatisticsOrderCountDetail(new StoreDateRangeSqlPram(dateStart, dateEnd)));
            response.setTime(BigDecimal.valueOf(orderCurrentList.size()));
            increasePrice = orderCurrentList.size() - orderPerList.size();
            if (increasePrice <= 0) response.setGrowthRate(0);
            else if (orderPerList.size() == 0) response.setGrowthRate((int) increasePrice);
            else response.setGrowthRate((int) ((increasePrice / orderPerList.size()) * 100));
        }
        response.setIncreaseTime(increasePrice + "");
        response.setIncreaseTimeStatus(increasePrice >= 0 ? 1 : 2);
        return response;
    }

    @Override
    public StoreOrder getByOderId(String orderId) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrder::getOrderId, orderId);
        return dao.selectOne(lqw);
    }

    /**
     * 获取面单默认配置信息
     *
     * @return ExpressSheetVo
     */
    @Override
    public ExpressSheetVo getDeliveryInfo() {
        return systemConfigService.getDeliveryInfo();
    }

    /**
     * @param userId           用户uid
     * @param pageParamRequest 分页参数
     * @return List
     */
    @Override
    public List<StoreOrder> findPaidListByUid(Integer userId, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<StoreOrder> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreOrder::getUid, userId);
        lqw.eq(StoreOrder::getPaid, true);
        lqw.eq(StoreOrder::getIsDel, false);
        lqw.orderByDesc(StoreOrder::getId);
        return dao.selectList(lqw);
    }

    /**
     * 获取订单总数量
     *
     * @param uid 用户uid
     * @return Integer
     */
    @Override
    public Integer getOrderCountByUid(Integer uid) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrder::getPaid, true);
        lqw.eq(StoreOrder::getIsDel, false);
        lqw.eq(StoreOrder::getUid, uid);
        return Math.toIntExact(dao.selectCount(lqw));
    }

    /**
     * 获取用户总消费金额
     *
     * @param userId 用户uid
     * @return BigDecimal
     */
    @Override
    public BigDecimal getSumPayPriceByUid(Integer userId) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreOrder::getPayPrice);
        lqw.eq(StoreOrder::getPaid, true);
        lqw.eq(StoreOrder::getIsDel, false);
        lqw.eq(StoreOrder::getUid, userId);
        List<StoreOrder> orderList = dao.selectList(lqw);
        return orderList.stream().map(StoreOrder::getPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取订单数量(时间)
     *
     * @param uid 用户uid
     * @return Integer
     */
    @Override
    public Integer getOrderCountByUidAndDate(Integer uid, String date) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrder::getPaid, true);
        lqw.eq(StoreOrder::getIsDel, false);
        lqw.eq(StoreOrder::getUid, uid);
        if (StrUtil.isNotBlank(date)) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(date);
            lqw.between(StoreOrder::getCreateTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        return Math.toIntExact(dao.selectCount(lqw));
    }

    /**
     * 获取用户消费金额(时间)
     *
     * @param userId 用户uid
     * @return BigDecimal
     */
    @Override
    public BigDecimal getSumPayPriceByUidAndDate(Integer userId, String date) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.select(StoreOrder::getPayPrice);
        lqw.eq(StoreOrder::getPaid, true);
        lqw.eq(StoreOrder::getIsDel, false);
        lqw.eq(StoreOrder::getUid, userId);
        if (StrUtil.isNotBlank(date)) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(date);
            lqw.between(StoreOrder::getCreateTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        List<StoreOrder> orderList = dao.selectList(lqw);
        return orderList.stream().map(StoreOrder::getPayPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 获取订单状态数量
     *
     * @return StoreOrderCountItemResponse
     */
    @Override
    public StoreOrderCountItemResponse getOrderStatusNum(String dateLimit, Integer type) {
        StoreOrderCountItemResponse response = new StoreOrderCountItemResponse();
        if (type.equals(2)) {
            type = null;
        }
        // 全部订单
        response.setAll(getCount(dateLimit, "ALL", type));
        // 待付款订单
        response.setUnPaid(getCount(dateLimit, OrderStatusEnum.UNPAID.getCode().toString(), type));
        // 待发货订单
        response.setNotShipped(getCount(dateLimit, OrderStatusEnum.UNSHIPPED.getCode().toString(), type));
        // 待收货订单
        response.setSpike(getCount(dateLimit, OrderStatusEnum.SHIPPED.getCode().toString(), type));
        // 待评价订单
        response.setBargain(getCount(dateLimit, OrderStatusEnum.UNREVIEWED.getCode().toString(), type));
        // 交易完成订单
        response.setComplete(getCount(dateLimit, OrderStatusEnum.COMPLETED.getCode().toString(), type));
        return response;
    }

    /**
     * 获取订单统计数据
     *
     * @param dateLimit 时间端
     * @return StoreOrderTopItemResponse
     */
    @Override
    public StoreOrderTopItemResponse getOrderData(String dateLimit) {
        StoreOrderTopItemResponse itemResponse = new StoreOrderTopItemResponse();
        // 订单数量
        itemResponse.setCount(getCount(dateLimit, "ALL"));
        // 订单金额
        itemResponse.setAmount(getAmount(dateLimit, ""));
        // 微信支付金额
        itemResponse.setWeChatAmount(getAmount(dateLimit, PayTypeEnum.WE_CHAT.getCode()));
        // 余额支付金额
        itemResponse.setYueAmount(getAmount(dateLimit, PayTypeEnum.YUE.getCode()));
        return itemResponse;
    }

    /**
     * 订单删除
     *
     * @param orderNo 订单编号
     * @return Boolean
     */
    @Override
    public Boolean delete(String orderNo) {
        StoreOrder storeOrder = getInfoException(orderNo);
        if (!storeOrder.getIsDel()) {
            throw new CrmebException("您选择的的订单存在用户未删除的订单，无法删除用户未删除的订单！");
        }
        if (storeOrder.getIsSystemDel()) {
            throw new CrmebException("此订单已经被删除了!");
        }
        storeOrder.setIsSystemDel(true);
        return updateById(storeOrder);
    }

    /**
     * 订单取消
     *
     * @param orderNo 订单编号
     * @param reason 取消原因
     * @return Boolean
     */
    @Override
    public Boolean cancel(String orderNo, String reason) {
        StoreOrder storeOrder = getInfoException(orderNo);
        
        // 只有未支付或待发货的订单可以取消
        if (storeOrder.getStatus() != OrderStatusEnum.UNPAID.getCode() &&
            storeOrder.getStatus() != OrderStatusEnum.UNSHIPPED.getCode()) {
            throw new CrmebException("当前订单状态不能取消订单！");
        }
        
        // 更新订单状态为已取消
        storeOrder.setStatus(OrderStatusEnum.CLOSED.getCode());
        
        // 记录订单状态变更
        StoreOrderStatus storeOrderStatus = new StoreOrderStatus();
        storeOrderStatus.setOid(storeOrder.getId());
        storeOrderStatus.setChangeType(OrderStatusEnum.CLOSED.getCode().toString());
        storeOrderStatus.setChangeMessage(StrUtil.isBlank(reason) ? "管理员取消订单" : "管理员取消订单，原因：" + reason);
        storeOrderStatusService.save(storeOrderStatus);
        
        // 如果订单已支付，需要退款处理
        if (storeOrder.getPaid()) {
            // 将退款任务加入Redis队列，异步处理退款 todo
            redisUtil.lPush(Constants.ORDER_TASK_REDIS_KEY_AFTER_CANCEL_BY_USER, storeOrder.getId());
        }
        
        return updateById(storeOrder);
    }

    /**
     * 通过日期获取订单数量
     *
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    @Override
    public Integer getOrderNumByDate(String date) {
        QueryWrapper<StoreOrder> wrapper = new QueryWrapper<>();
        wrapper.select("id");
        wrapper.eq("paid", 1);
        wrapper.apply("date_format(create_time, '%Y-%m-%d') = {0}", date);
        return Math.toIntExact(dao.selectCount(wrapper));
    }

    /**
     * 通过日期获取支付订单金额
     *
     * @param date 日期，yyyy-MM-dd格式
     * @return BigDecimal
     */
    @Override
    public BigDecimal getPayOrderAmountByDate(String date) {
        QueryWrapper<StoreOrder> wrapper = new QueryWrapper<>();
        wrapper.select("IFNULL(sum(pay_price), 0) as pay_price");
        wrapper.eq("paid", 1);
        wrapper.apply("date_format(create_time, '%Y-%m-%d') = {0}", date);
        StoreOrder storeOrder = dao.selectOne(wrapper);
        return storeOrder.getPayPrice();
    }

    /**
     * 通过日期获取支付订单金额
     *
     * @param startDate 日期
     * @param endDate   日期
     * @return BigDecimal
     */
    @Override
    public BigDecimal getPayOrderAmountByPeriod(String startDate, String endDate) {
        QueryWrapper<StoreOrder> wrapper = new QueryWrapper<>();
        wrapper.select("IFNULL(sum(pay_price), 0) as pay_price");
        wrapper.eq("paid", 1);
        wrapper.apply("date_format(create_time, '%Y-%m-%d') between {0} and {1}", startDate, endDate);
        StoreOrder storeOrder = dao.selectOne(wrapper);
        return storeOrder.getPayPrice();
    }

    /**
     * 获取待发货订单数量
     *
     * @return Integer
     */
    @Override
    public Integer getNotShippingNum() {
        return getCount("", "Unshipped");
    }

    /**
     * 获取退款中订单数量
     */
    @Override
    public Integer getRefundingNum() {
        return getCount("", "Refunding");
    }

///////////////////////////////////////////////////////////////////////////////////////////////////// 以下为自定义方法

    /**
     * 根据时间参数获取有效订单   todo 有效订单的查询待完善
     *
     * @return 有效订单列表
     */
    private List<StoreOrder> getOrderPayedByDateLimit(String startTime, String endTime) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrder::getIsDel, false).eq(StoreOrder::getPaid, true).eq(StoreOrder::getStatus, 4).ne(StoreOrder::getProcessingMethod, ProcessingMethodEnum.REFUND_ONLY.getCode())
                .between(StoreOrder::getCreateTime, startTime, endTime);
        return dao.selectList(lqw);
    }

    private StoreOrder getInfoException(String orderNo) {
        LambdaQueryWrapper<StoreOrder> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrder::getOrderId, orderNo);
        StoreOrder storeOrder = dao.selectOne(lqw);
        if (ObjectUtil.isNull(storeOrder)) {
            throw new CrmebException("没有找到订单信息");
        }
        return storeOrder;
    }

    /**
     * 获取订单总数
     *
     * @param dateLimit 时间端
     * @param status    String 状态
     * @return Integer
     */
    private Integer getCount(String dateLimit, String status) {
        //总数只计算时间
        QueryWrapper<StoreOrder> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(dateLimit)) {
            dateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(dateLimit);
            queryWrapper.between("create_time", dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        getStatusWhereNew(queryWrapper, status);
        return Math.toIntExact(dao.selectCount(queryWrapper));
    }

    /**
     * 获取订单总数
     *
     * @param dateLimit 时间端
     * @param status    String 状态
     * @return Integer
     */
    private Integer getCount(String dateLimit, String status, Integer type) {
        //总数只计算时间
        QueryWrapper<StoreOrder> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(dateLimit)) {
            dateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(dateLimit);
            queryWrapper.between("create_time", dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        getStatusWhereNew(queryWrapper, status);
        if (ObjectUtil.isNotNull(type)) {
            queryWrapper.eq("type", type);
        }
        return Math.toIntExact(dao.selectCount(queryWrapper));
    }

    /**
     * 获取订单金额
     *
     * @param dateLimit 时间端
     * @param type      支付类型
     * @return Integer
     */
    private BigDecimal getAmount(String dateLimit, String type) {
        QueryWrapper<StoreOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("sum(pay_price) as pay_price");
        if (StringUtils.isNotBlank(type)) {
            queryWrapper.eq("pay_type", type);
        }
        queryWrapper.isNotNull("pay_time");
        queryWrapper.eq("paid", 1);
        if (StringUtils.isNotBlank(dateLimit)) {
            dateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(dateLimit);
            queryWrapper.between("create_time", dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        StoreOrder storeOrder = dao.selectOne(queryWrapper);
        if (ObjectUtil.isNull(storeOrder)) {
            return BigDecimal.ZERO;
        }
        return storeOrder.getPayPrice();
    }

    /**
     * 获取request的where条件
     *
     * @param queryWrapper QueryWrapper<StoreOrder> 表达式
     * @param request      StoreOrderSearchRequest 请求参数
     */
    private void getRequestTimeWhere(QueryWrapper<StoreOrder> queryWrapper, StoreOrderSearchRequest request) {
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            dateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(request.getDateLimit());
            queryWrapper.between("create_time", dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        } else if (StringUtils.isNotBlank(request.getOrderTimeStart()) || StringUtils.isNotBlank(request.getOrderTimeEnd())) {
            // 使用精确的订单下单时间范围查询
            if (StringUtils.isNotBlank(request.getOrderTimeStart()) && StringUtils.isNotBlank(request.getOrderTimeEnd())) {
                // 如果开始和结束时间都存在
                int compareDateResult = DateUtil.compareDate(request.getOrderTimeEnd(), request.getOrderTimeStart(), Constants.DATE_FORMAT);
                if (compareDateResult == -1) {
                    throw new CrmebException("开始时间不能大于结束时间！");
                }
                // 将字符串时间转换为Date类型，确保类型匹配
                Date startDate = DateUtil.strToDate(request.getOrderTimeStart(), Constants.DATE_FORMAT);
                Date endDate = DateUtil.strToDate(request.getOrderTimeEnd(), Constants.DATE_FORMAT);
                if (startDate != null && endDate != null) {
                    queryWrapper.between("create_time", startDate, endDate);
                }
            } else if (StringUtils.isNotBlank(request.getOrderTimeStart())) {
                // 如果只有开始时间
                Date startDate = DateUtil.strToDate(request.getOrderTimeStart(), Constants.DATE_FORMAT);
                if (startDate != null) {
                    queryWrapper.ge("create_time", startDate);
                }
            } else if (StringUtils.isNotBlank(request.getOrderTimeEnd())) {
                // 如果只有结束时间
                Date endDate = DateUtil.strToDate(request.getOrderTimeEnd(), Constants.DATE_FORMAT);
                if (endDate != null) {
                    queryWrapper.le("create_time", endDate);
                }
            }
        }

        // 处理支付时间查询条件
        if (StringUtils.isNotBlank(request.getPayTimeStart()) || StringUtils.isNotBlank(request.getPayTimeEnd())) {
            // 使用精确的订单支付时间范围查询
            if (StringUtils.isNotBlank(request.getPayTimeStart()) && StringUtils.isNotBlank(request.getPayTimeEnd())) {
                // 如果开始和结束时间都存在
                int compareDateResult = DateUtil.compareDate(request.getPayTimeEnd(), request.getPayTimeStart(), Constants.DATE_FORMAT);
                if (compareDateResult == -1) {
                    throw new CrmebException("支付开始时间不能大于支付结束时间！");
                }
                // 将字符串时间转换为Date类型，确保类型匹配
                Date payStartDate = DateUtil.strToDate(request.getPayTimeStart(), Constants.DATE_FORMAT);
                Date payEndDate = DateUtil.strToDate(request.getPayTimeEnd(), Constants.DATE_FORMAT);
                if (payStartDate != null && payEndDate != null) {
                    queryWrapper.between("pay_time", payStartDate, payEndDate);
                }
            } else if (StringUtils.isNotBlank(request.getPayTimeStart())) {
                // 如果只有开始时间
                Date payStartDate = DateUtil.strToDate(request.getPayTimeStart(), Constants.DATE_FORMAT);
                if (payStartDate != null) {
                    queryWrapper.ge("pay_time", payStartDate);
                }
            } else if (StringUtils.isNotBlank(request.getPayTimeEnd())) {
                // 如果只有结束时间
                Date payEndDate = DateUtil.strToDate(request.getPayTimeEnd(), Constants.DATE_FORMAT);
                if (payEndDate != null) {
                    queryWrapper.le("pay_time", payEndDate);
                }
            }
            // 支付时间查询时，只查询已支付的订单
            queryWrapper.eq("paid", 1);
        }
    }


    /**
     * 获取订单状态Where条件
     *
     * @param queryWrapper 查询条件
     * @param status       状态
     */
    private void getStatusWhereNew(QueryWrapper<StoreOrder> queryWrapper, String status) {
        if (StrUtil.isBlank(status)) {
            return;
        }
        switch (status) {
            case "All": //全部
                break;
            case "Unpaid": //代付款
                queryWrapper.eq("paid", 0).eq("status", OrderStatusEnum.UNPAID.getCode()).eq("is_del", 0);
                break;
            case "Unshipped": //待发货
                queryWrapper.eq("paid", 1).eq("status", OrderStatusEnum.UNSHIPPED.getCode()).eq("is_del", 0);
                break;
            case "Shipped": //待收货
                queryWrapper.eq("paid", 1).eq("status", OrderStatusEnum.SHIPPED.getCode()).eq("is_del", 0);
                break;
            case "Unreviewed": //待评价
                queryWrapper.eq("paid", 1).eq("status", OrderStatusEnum.UNREVIEWED.getCode()).eq("is_del", 0);
                break;
            case "Completed": //交易完成
                queryWrapper.eq("paid", 1).eq("status", OrderStatusEnum.COMPLETED.getCode()).eq("is_del", 0);
                break;
            case "AfterSales": //售后
                queryWrapper.eq("paid", 1).eq("status", OrderStatusEnum.AFTER_SALES.getCode()).eq("is_del", 0);
                break;
            case "Refunding": //退款中
                queryWrapper.eq("paid", 1).eq("after_sale_status", 7).eq("is_del", 0);
                break;
            case "Deleted": //已删除
                queryWrapper.eq("is_del", 1);
                break;
            case "Closed": //已取消
                queryWrapper.eq("status", OrderStatusEnum.CLOSED.getCode()).eq("is_del", 0);
                break;
            case "ExpressOrders": // 打单发货相关订单（待发货、已发货、已完成）
                queryWrapper.eq("is_del", 0).eq("paid", 1)
                        .and(i -> i
                                .or(c -> c.eq("status", OrderStatusEnum.UNSHIPPED.getCode()).eq("shipping_type", 1)) // 待发货
                                .or().eq("status", OrderStatusEnum.SHIPPED.getCode()) // 已发货
                                .or().eq("status", OrderStatusEnum.COMPLETED.getCode()) // 已完成
                        );
                break;
            default:
                queryWrapper.eq("paid", 1).eq("is_del", 0);
                break;
        }
    }

    /**
     * 获取订单状态
     *
     * @param storeOrder 订单信息
     * @return 订单状态
     */
    private Map<String, String> getStatus(StoreOrder storeOrder) {
        Map<String, String> resultMap = new HashMap<>(2);
        if (storeOrder == null) {
            resultMap.put("key", "");
            resultMap.put("value", "");
            return resultMap;
        }

        Integer status = storeOrder.getStatus();
        String key = String.valueOf(status);
        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(status);
        String value = (orderStatusEnum != null) ? orderStatusEnum.getDesc() : "未知状态";

        resultMap.put("key", key);
        resultMap.put("value", value);

        return resultMap;
    }

    /**
     * 获取支付文字
     *
     * @param payType String 支付方式
     */
    private String getPayType(String payType) {
        if (StrUtil.isBlank(payType)) {
            return "其他支付";
        }

        PayTypeEnum payTypeEnum = PayTypeEnum.getByCode(payType);
        if (payTypeEnum == null) {
            return "其他支付";
        }
        return payTypeEnum.getDesc();
    }

    @Override
    public void updateOrderStatus(String orderIds, String shipped) {
        //更新订单状态为已发货
        StoreOrder storeOrder = new StoreOrder();
        storeOrder.setStatus(OrderStatusEnum.SHIPPED.getCode());
        this.update(storeOrder, new LambdaUpdateWrapper<StoreOrder>().in(StoreOrder::getOrderId, orderIds.split(",")));
        //更新订单store_order_status
        StoreOrder order;
        for (String orderId : orderIds.split(",")) {
            StoreOrderStatus orderStatus = new StoreOrderStatus();
            order = getOne(new LambdaUpdateWrapper<StoreOrder>().eq(StoreOrder::getOrderId, orderId));
            orderStatus.setOid(order.getId());
            orderStatus.setChangeType(OrderStatusEnum.SHIPPED.getCode()+"");
            orderStatus.setChangeMessage("发货");
            orderStatus.setCreateTime(new Date());
            storeOrderStatusService.save(orderStatus);
        }
    }

    /**
     * 判断订单是否已发货c
     *
     * @param storeOrder 订单信息
     * @return 是否已发货
     */
    private boolean isOrderShipped(StoreOrder storeOrder) {
        if (storeOrder == null || storeOrder.getStatus() == null) {
            return false;
        }

        Integer status = storeOrder.getStatus();

        // 明确已发货的状态
        if (status.equals(OrderStatusEnum.SHIPPED.getCode()) ||           // 2: 待收货
            status.equals(OrderStatusEnum.UNREVIEWED.getCode()) ||        // 3: 待评价
            status.equals(OrderStatusEnum.COMPLETED.getCode()) ||         // 4: 已完成
            status.equals(OrderStatusEnum.MERCHANT_SHIPPED.getCode())) {  // 9: 商家已发货
            return true;
        }

        // 对于复杂状态，通过订单状态记录来判断是否曾经发货
        if (status.equals(OrderStatusEnum.CLOSED.getCode()) ||            // 5: 已取消
            status.equals(OrderStatusEnum.AFTER_SALES.getCode()) ||       // 6: 售后
            status.equals(OrderStatusEnum.REFUND_ALL.getCode()) ||        // 7: 全额退款
            status.equals(OrderStatusEnum.REFUND_PARTIAL.getCode())) {    // 8: 部分退款

            // 查询是否有发货记录
            List<StoreOrderStatus> orderStatusList = storeOrderStatusService.getByEntity(
                    new StoreOrderStatus().setOid(storeOrder.getId()).setChangeType(OrderStatusEnum.MERCHANT_SHIPPED.getCode() + ""));
            return CollUtil.isNotEmpty(orderStatusList);
        }

        // 明确未发货的状态
        return false; // 0: 待付款, 1: 待发货
    }

    /**
     * 判断订单是否已完成交易
     *
     * @param storeOrder 订单信息
     * @return 是否已完成交易
     */
    private boolean isOrderCompleted(StoreOrder storeOrder) {
        if (storeOrder == null || storeOrder.getStatus() == null) {
            return false;
        }

        Integer status = storeOrder.getStatus();

        // 明确已完成交易的状态
        if (status.equals(OrderStatusEnum.COMPLETED.getCode())) {  // 4: 已完成
            return true;
        }

        // 部分退款状态也可能算作交易完成（商品已交付，只是退了部分款）
        if (status.equals(OrderStatusEnum.REFUND_PARTIAL.getCode()) || status.equals(OrderStatusEnum.REFUND_ALL.getCode()) || status.equals(OrderStatusEnum.AFTER_SALES.getCode())) {  // 8: 部分退款 7: 全额退款  // 6: 售后
            // 可以根据业务需求决定是否算作交易完成
            // 这里假设部分退款仍然算作交易完成，因为商品已经交付
            return true;
        }

        // 对于其他复杂状态，可以通过查询订单状态记录来判断是否曾经完成过交易

        // 其他状态都不算交易完成
        return false; // 0: 待付款, 1: 待发货, 2: 待收货, 3: 待评价, 5: 已取消, 9: 商家已发货
    }

    /**
     * 根据订单ID查询售后信息
     *
     * @param orderId 订单ID
     * @return 售后信息
     */
    private StoreOrderAftersale getAftersaleByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreOrderAftersale> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreOrderAftersale::getOrderId, orderId);
        lqw.orderByDesc(StoreOrderAftersale::getCreateTime);
        lqw.last("limit 1");
        return storeOrderAftersaleDao.selectOne(lqw);
    }

    /**
     * 计算用户销售金额（自购订单）
     * 优化版本：使用user_commission_record表统计，避免重复查询订单表
     */
    @Override
    public Double calculateUserSalesAmount(Integer userId, String startDate, String endDate) {
        if (userId == null) {
            return 0.0;
        }

        try {
            // 使用user_commission_record表统计销售金额
            LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserCommissionRecord::getOrderAmount)
                       .eq(UserCommissionRecord::getUid, userId)
                       .eq(UserCommissionRecord::getType, 1) // 增加类型
                       .in(UserCommissionRecord::getStatus, 1, 2) // 待结算和已结算状态
                       .like(UserCommissionRecord::getUserLevel, "SVIP"); // 仅统计SVIP用户的订单

            if (StrUtil.isNotBlank(startDate)) {
                queryWrapper.ge(UserCommissionRecord::getPayTime, startDate + " 00:00:00");
            }
            if (StrUtil.isNotBlank(endDate)) {
                queryWrapper.le(UserCommissionRecord::getPayTime, endDate + " 23:59:59");
            }

            List<UserCommissionRecord> records = userCommissionRecordService.list(queryWrapper);
            return records.stream()
                        .map(UserCommissionRecord::getOrderAmount)
                        .filter(Objects::nonNull)
                        .mapToDouble(BigDecimal::doubleValue)
                        .sum();

        } catch (Exception e) {
            log.error("计算用户销售金额失败: userId={}, error={}", userId, e.getMessage());
            return 0.0;
        }
    }

    /**
     * 计算用户下线销售金额
     * 优化版本：使用user_commission_record表统计，避免重复查询订单表
     */
    @Override
    public Double calculateDownlineSalesAmount(Integer userId, String startDate, String endDate) {
        if (userId == null) {
            return 0.0;
        }

        try {
            // 获取用户的所有下级用户ID
            List<Integer> downlineUserIds = userService.getDownlineUserIds(userId);
            if (downlineUserIds.isEmpty()) {
                return 0.0;
            }

            // 获取用户成为SVIP的时间，确保只统计成为SVIP之后的下线订单
            User user = userService.getById(userId);
            if (user == null || user.getSvipTime() == null) {
                return 0.0;
            }

            // 如果开始时间早于成为SVIP的时间，则从成为SVIP的时间开始统计
            String svipTimeStr = DateUtil.formatDate(user.getSvipTime());
            if (startDate.compareTo(svipTimeStr) < 0) {
                startDate = svipTimeStr;
            }

            // 使用user_commission_record表统计下线销售金额
            LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserCommissionRecord::getOrderAmount)
                       .in(UserCommissionRecord::getUid, downlineUserIds)
                       .eq(UserCommissionRecord::getType, 1) // 增加类型
                       .in(UserCommissionRecord::getStatus, 1, 2); // 待结算和已结算状态

            if (StrUtil.isNotBlank(startDate)) {
                queryWrapper.ge(UserCommissionRecord::getPayTime, startDate + " 00:00:00");
            }
            if (StrUtil.isNotBlank(endDate)) {
                queryWrapper.le(UserCommissionRecord::getPayTime, endDate + " 23:59:59");
            }

            List<UserCommissionRecord> records = userCommissionRecordService.list(queryWrapper);
            return records.stream()
                        .map(UserCommissionRecord::getOrderAmount)
                        .filter(Objects::nonNull)
                        .mapToDouble(BigDecimal::doubleValue)
                        .sum();

        } catch (Exception e) {
            log.error("计算用户下线销售金额失败: userId={}, error={}", userId, e.getMessage());
            return 0.0;
        }
    }
}

