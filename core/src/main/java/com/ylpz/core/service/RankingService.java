package com.ylpz.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ylpz.core.common.request.RankingRequest;
import com.ylpz.core.common.response.RankingDetailResponse;
import com.ylpz.core.common.response.RankingResponse;
import com.ylpz.core.common.response.RankingStatisticsResponse;
import com.ylpz.core.common.response.RankingYearResponse;

import java.util.List;

/**
 * 排行榜服务接口
 */
public interface RankingService {

    /**
     * 分页查询排行榜列表
     *
     * @param request 查询条件
     * @param page    页码
     * @param limit   每页数量
     * @return 排行榜列表
     */
    IPage<RankingResponse> getRankingList(RankingRequest request, Integer page, Integer limit);

    /**
     * 获取排行榜统计数据
     *
     * @param request 查询条件
     * @return 统计数据
     */
    RankingStatisticsResponse getRankingStatistics(RankingRequest request);

    /**
     * 获取排行榜详细信息
     *
     * @param rankingId 排行榜ID
     * @return 排行榜详细信息
     */
    RankingDetailResponse getRankingDetail(Integer rankingId);

    /**
     * 生成排行榜
     *
     * @param rankType  排行类型
     * @param startDate 开始日期
     * @param endDate   结束日期
     * @return 排行榜ID
     */
    Integer generateRanking(String rankType, String startDate, String endDate);

    /**
     * 手动发放排行榜奖励
     *
     * @param rankingId 排行榜ID
     * @return 是否成功
     */
    Boolean distributeReward(Integer rankingId);

    /**
     * 批量发放排行榜奖励
     *
     * @param rankingIds 排行榜ID列表
     * @return 成功发放的数量
     */
    Integer batchDistributeReward(List<Integer> rankingIds);

    /**
     * 自动生成当期排行榜（定时任务调用）
     */
    void autoGenerateRanking();

    /**
     * 获取历史年度列表
     *
     * @return 历史年度列表
     */
    List<RankingYearResponse> getHistoryYears();
}
