package com.ylpz.core.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.request.StoreInfoRequest;
import com.ylpz.core.common.response.StoreInfoResponse;
import com.ylpz.core.dao.StoreInfoDao;
import com.ylpz.core.service.StoreInfoService;
import com.ylpz.core.service.SystemAttachmentService;
import com.ylpz.model.system.StoreInfo;

/**
 * 店铺信息服务实现类
 */
@Service
public class StoreInfoServiceImpl extends ServiceImpl<StoreInfoDao, StoreInfo> implements StoreInfoService {

    @Resource
    private StoreInfoDao storeInfoDao;

    @Resource
    private SystemAttachmentService systemAttachmentService;



    /**
     * 获取店铺信息
     * @return 店铺信息响应对象
     */
    @Override
    public StoreInfoResponse getStoreInfo() {
        // 获取店铺信息，始终获取ID为1的记录
        StoreInfo storeInfo = getById(1);
        
        // 如果不存在，则创建默认店铺信息
        if (storeInfo == null) {
            storeInfo = new StoreInfo();
            storeInfo.setStoreName("默认店铺");
            storeInfo.setStoreCategory("传播食养文化, 福泽万家健康");
            storeInfo.setMiniAppLogo("");
            storeInfo.setMiniAppLoginLogo("");
            storeInfo.setCreateTime(new Date());
            storeInfo.setUpdateTime(new Date());
            save(storeInfo);
        }
        
        // 转换为响应对象
        StoreInfoResponse response = new StoreInfoResponse();
        BeanUtils.copyProperties(storeInfo, response);
        return response;
    }

    /**
     * 更新店铺信息
     * @param request 店铺信息请求对象
     * @return 更新结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateStoreInfo(StoreInfoRequest request) {
        // 获取店铺信息，始终获取ID为1的记录
        StoreInfo storeInfo = getById(1);
        
        // 如果不存在，则创建新的店铺信息
        if (storeInfo == null) {
            storeInfo = new StoreInfo();
            storeInfo.setId(1);
            storeInfo.setCreateTime(new Date());
        }
        
        // 更新店铺信息
        storeInfo.setStoreName(request.getStoreName());
        storeInfo.setStoreCategory(request.getStoreCategory());
        storeInfo.setMiniAppLogo(systemAttachmentService.clearPrefix(request.getMiniAppLogo()));
        storeInfo.setMiniAppLoginLogo(systemAttachmentService.clearPrefix(request.getMiniAppLoginLogo()));
        storeInfo.setUpdateTime(new Date());
        
        // 保存或更新
        return saveOrUpdate(storeInfo);
    }
} 