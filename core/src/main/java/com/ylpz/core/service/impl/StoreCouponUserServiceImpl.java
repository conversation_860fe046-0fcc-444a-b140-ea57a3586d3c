package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.vo.MyRecord;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.CouponConstants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.vo.StoreCouponOrderVo;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.coupon.StoreCouponUser;
import com.ylpz.core.common.request.StoreCouponUserRequest;
import com.ylpz.core.common.request.StoreCouponUserSearchRequest;
import com.ylpz.core.common.response.StoreCouponUserResponse;
import com.ylpz.core.common.response.UserCouponResponse;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.user.User;
import com.ylpz.core.dao.StoreCouponUserDao;
import com.ylpz.core.service.StoreCouponService;
import com.ylpz.core.service.StoreCouponUserService;
import com.ylpz.core.service.StoreOrderService;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.core.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * StoreCouponUserService 实现类
 */
@Slf4j
@Service
public class StoreCouponUserServiceImpl extends ServiceImpl<StoreCouponUserDao, StoreCouponUser> implements StoreCouponUserService {

    @Resource
    private StoreCouponUserDao dao;

    @Autowired
    private StoreCouponService storeCouponService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreProductService storeProductService;
    
    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private RedisUtil redisUtil;

    /**
    * 列表
    * @param request 请求参数
    * @param pageParamRequest 分页类参数
    * @return List<StoreCouponUser>
    */
    @Override
    public PageInfo<StoreCouponUserResponse> getList(StoreCouponUserSearchRequest request, PageParamRequest pageParamRequest) {
        Page<StoreCouponUser> storeCouponUserPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<StoreCouponUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCouponUser::getIsDel, false);
        if (ObjectUtil.isNotNull(request.getStatus())) {
            lambdaQueryWrapper.eq(StoreCouponUser::getStatus, request.getStatus());
        }
        if (ObjectUtil.isNotNull(request.getUid())) {
            lambdaQueryWrapper.eq(StoreCouponUser::getUid, request.getUid());
        }
        if (StrUtil.isNotBlank(request.getName())) {
            lambdaQueryWrapper.like(StoreCouponUser::getName, request.getName());
        }
        if (StrUtil.isNotBlank(request.getPhone())) {
            List<Integer> userIdList = userService.findIdByPhone(request.getPhone());
            if (CollUtil.isNotEmpty(userIdList)) {
                lambdaQueryWrapper.in(StoreCouponUser::getUid, userIdList);
            }else{
                return CommonPage.copyPageInfo(storeCouponUserPage, new ArrayList<>());
            }
        }

        lambdaQueryWrapper.orderByDesc(StoreCouponUser::getId);
        List<StoreCouponUser> storeCouponUserList = dao.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(storeCouponUserList)) {
            return CommonPage.copyPageInfo(storeCouponUserPage, CollUtil.newArrayList());
        }
        List<StoreCouponUserResponse> responseList = storeCouponUserList.stream().map(e -> {
            StoreCouponUserResponse storeCouponUserResponse = new StoreCouponUserResponse();
            BeanUtils.copyProperties(e, storeCouponUserResponse);
            // 用户信息单独处理
            User user = userService.getById(e.getUid());
            if (ObjectUtil.isNotNull(user) && user.getNickname() != null) {
                // 这里根据实际情况处理用户信息
                // 如果StoreCouponUserResponse没有setUserInfo方法，可以直接设置相关字段
                // 例如：
                storeCouponUserResponse.setUserNickname(user.getNickname());
                storeCouponUserResponse.setUserAvatar(user.getAvatar());
                storeCouponUserResponse.setUserPhone(user.getPhone());
            }
            return storeCouponUserResponse;
        }).collect(Collectors.toList());
        return CommonPage.copyPageInfo(storeCouponUserPage, responseList);
    }

    /**
     * 处理优惠券使用时间
     * @param storeCoupon 优惠券信息
     * @return 包含开始时间和结束时间的数组 [startTime, endTime]
     */
    private Date[] processUseTime(StoreCoupon storeCoupon) {
        Date startTime = storeCoupon.getUseStartTime();
        Date endTime = storeCoupon.getUseEndTime();

        if (storeCoupon.getUseTimeType() == 1) {
            // 固定时间类型，需要检查是否已过期
            if (endTime != null) {
                Date now = DateUtil.nowDateTime();
                if (endTime.before(now)) {
                    throw new CrmebException("优惠券使用时间已过期，无法发送！");
                }
            }
        } else {
            // 非固定时间，根据useTimeType处理
            String endTimeStr;
            if (storeCoupon.getUseTimeType() == 2) {
                startTime = DateUtil.nowDateTime();
                // 领取后天数
                endTimeStr = DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT_DATE), storeCoupon.getDay(), Constants.DATE_FORMAT_DATE);
                endTime = DateUtil.strToDate(endTimeStr, Constants.DATE_FORMAT_DATE);
            } else if (storeCoupon.getUseTimeType() == 3) {
                // 领取后增加天数后可用
                // 先计算起始时间（当前时间 + 等待天数）
                String startTimeStr = DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT_DATE), storeCoupon.getAfterDays(), Constants.DATE_FORMAT_DATE);
                startTime = DateUtil.strToDate(startTimeStr, Constants.DATE_FORMAT_DATE);
                // 再计算结束时间（起始时间 + 可用天数）
                endTimeStr = DateUtil.addDay(startTimeStr, storeCoupon.getDay(), Constants.DATE_FORMAT_DATE);
                endTime = DateUtil.strToDate(endTimeStr, Constants.DATE_FORMAT_DATE);
            }
        }

        return new Date[]{startTime, endTime};
    }

    /**
     * PC领取优惠券
     *
     * @param request 优惠券参数
     * @return Boolean
     */
    @Override
    public Boolean receive(StoreCouponUserRequest request) {
        // 获取优惠券信息
        StoreCoupon storeCoupon = storeCouponService.getInfoException(request.getCouponId());

        List<Integer> uidList = CrmebUtil.stringToArray(request.getUid());
        if (CollUtil.isEmpty(uidList)) {
            throw new CrmebException("请选择用户");
        }
        
        // 过滤不符合条件的用户
        List<Integer> filterUidList = getFilterUidList(storeCoupon, uidList);
        if (CollUtil.isEmpty(filterUidList)) {
            throw new CrmebException("没有符合条件的用户");
        }

        // 判断是否限量
        if (storeCoupon.getIsLimited() && storeCoupon.getLastTotal() < filterUidList.size()) {
            throw new CrmebException("优惠券剩余数量不足");
        }

        // 处理使用时间
        Date[] useTimes = processUseTime(storeCoupon);
        Date startTime = useTimes[0];
        Date endTime = useTimes[1];

        // 批量创建优惠券记录
        List<StoreCouponUser> storeCouponUserList = new ArrayList<>();
        for (Integer uid : filterUidList) {
            StoreCouponUser storeCouponUser = createCouponUserRecord(storeCoupon, uid, startTime, endTime, request.getRemark());
            storeCouponUserList.add(storeCouponUser);
        }

        // 使用事务保存优惠券记录并减少剩余数量
        return saveCouponUsers(storeCouponUserList, storeCoupon.getId(), storeCoupon.getIsLimited(), filterUidList.size());
    }

    /**
     * 给用户发送优惠券
     * @param uid 用户ID
     * @param couponId 优惠券ID
     * @param remark 备注信息
     * @return Boolean 是否发送成功
     */
    @Override
    public Boolean send(Integer uid, Integer couponId, String remark) {
        if (uid == null || couponId == null) {
            return false;
        }
        
        try {
            // 获取优惠券信息
            StoreCoupon storeCoupon = storeCouponService.getById(couponId);
            if (ObjectUtil.isNull(storeCoupon) || storeCoupon.getIsDel() || !storeCoupon.getStatus()) {
                log.error("优惠券信息不存在或者已失效：couponId={}", couponId);
                return false;
            }
    
            // 看是否有剩余数量
            if (storeCoupon.getIsLimited() && storeCoupon.getLastTotal() < 1) {
                log.error("优惠券已经被领完了：couponId={}", couponId);
                return false;
            }
            
            // 检查用户是否符合领取条件
            if (!isUserEligible(storeCoupon, uid)) {
                return false;
            }
            
            // 处理使用时间
            Date[] useTimes = processUseTime(storeCoupon);
            Date startTime = useTimes[0];
            Date endTime = useTimes[1];
    
            // 创建新的优惠券记录
            StoreCouponUser storeCouponUser = createCouponUserRecord(storeCoupon, uid, startTime, endTime, remark);
            
            // 保存记录并更新优惠券数量
            List<StoreCouponUser> couponUserList = new ArrayList<>();
            couponUserList.add(storeCouponUser);
            return saveCouponUsers(couponUserList, couponId, storeCoupon.getIsLimited(), 1);
        } catch (Exception e) {
            log.error("发送优惠券异常：uid={}, couponId={}, 错误信息：{}", uid, couponId, e.getMessage());
            return false;
        }
    }

    /**
     * 判断用户是否符合领取优惠券的条件
     * @param storeCoupon 优惠券信息
     * @param uid 用户ID
     * @return 是否符合条件
     */
    private boolean isUserEligible(StoreCoupon storeCoupon, Integer uid) {
        // 如果不限制客户，直接返回true
        if (storeCoupon.getCustomerLimitType() == 1) {
            return true;
        }
        
        if (storeCoupon.getCustomerLimitType() == 2) {
            // 指定会员等级
            List<Integer> levelIds = CrmebUtil.stringToArray(storeCoupon.getCustomerLevelIds());
            if (CollUtil.isEmpty(levelIds)) {
                log.error("此优惠券未指定可领取的会员等级：couponId={}", storeCoupon.getId());
                return false;
            }
            
            // 获取用户会员等级ID
            Integer userLevelId = userService.getUserLevelId(uid);
            if (userLevelId == null || !levelIds.contains(userLevelId)) {
                log.error("用户会员等级不符合优惠券领取条件：uid={}, couponId={}", uid, storeCoupon.getId());
                return false;
            }
            return true;
        } else if (storeCoupon.getCustomerLimitType() == 3) {
            // 指定用户标签
            List<Integer> tagIds = CrmebUtil.stringToArray(storeCoupon.getCustomerTagIds());
            if (CollUtil.isEmpty(tagIds)) {
                log.error("此优惠券未指定可领取的用户标签：couponId={}", storeCoupon.getId());
                return false;
            }
            
            // 获取用户标签ID列表
            List<Integer> userTagIds = userService.getUserTagIds(uid);
            if (CollUtil.isEmpty(userTagIds) || !CollUtil.containsAny(userTagIds, tagIds)) {
                log.error("用户标签不符合优惠券领取条件：uid={}, couponId={}", uid, storeCoupon.getId());
                return false;
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * 创建优惠券用户记录
     * @param storeCoupon 优惠券信息
     * @param uid 用户ID
     * @param startTime 使用开始时间
     * @param endTime 使用结束时间
     * @param remark 备注信息
     * @return 创建的优惠券用户记录
     */
    private StoreCouponUser createCouponUserRecord(StoreCoupon storeCoupon, Integer uid, Date startTime, Date endTime, String remark) {
        StoreCouponUser storeCouponUser = new StoreCouponUser();
        // 复制优惠券信息到用户优惠券记录
        BeanUtils.copyProperties(storeCoupon, storeCouponUser);
        // 设置用户ID
        storeCouponUser.setUid(uid);
        // 设置状态为未使用
        storeCouponUser.setStatus(0);
        // 设置使用时间
        storeCouponUser.setUseStartTime(startTime);
        storeCouponUser.setUseEndTime(endTime);
        // 清除ID，让数据库自动生成
        storeCouponUser.setId(null);
        storeCouponUser.setCouponId(storeCoupon.getId());
        return storeCouponUser;
    }
    
    /**
     * 保存优惠券用户记录并更新优惠券数量
     * @param storeCouponUserList 优惠券用户记录列表
     * @param couponId 优惠券ID
     * @param isLimited 是否限量
     * @param count 发放数量
     * @return 是否保存成功
     */
    private Boolean saveCouponUsers(List<StoreCouponUser> storeCouponUserList, Integer couponId, Boolean isLimited, Integer count) {
        return transactionTemplate.execute(e -> {
            boolean save;
            if (storeCouponUserList.size() > 1) {
                save = saveBatch(storeCouponUserList);
            } else {
                save = save(storeCouponUserList.get(0));
            }
            
            if (save && isLimited) {
                // 如果是批量发送，使用updateById方式减少剩余数量
                if (count > 1) {
                    StoreCoupon coupon = storeCouponService.getById(couponId);
                    coupon.setLastTotal(coupon.getLastTotal() - count);
                    storeCouponService.updateById(coupon);
                } else {
                    // 如果是单个发送，使用deduction方法
                    storeCouponService.deduction(couponId, count, true);
                }
            }
            return save;
        });
    }

    /**
     * 获取根据限制类型获取符合条件的用户ID列表
     * @param storeCoupon 优惠券信息
     * @param uidList 用户ID列表
     * @return 符合条件的用户ID列表
     */
    private List<Integer> getFilterUidList(StoreCoupon storeCoupon, List<Integer> uidList) {
        if (storeCoupon.getCustomerLimitType() == 1) {
            // 不限制客户
            return uidList;
        }
        
        List<Integer> filterUidList = new ArrayList<>();
        for (Integer uid : uidList) {
            if (isUserEligible(storeCoupon, uid)) {
                filterUidList.add(uid);
            }
        }
        
        return filterUidList;
    }

    /**
     * 根据uid获取列表
     * @param uid uid
     * @param pageParamRequest 分页参数
     * @return 优惠券列表
     */
    @Override
    public List<StoreCouponUser> findListByUid(Integer uid, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 StoreCouponUser 类的多条件查询
        LambdaQueryWrapper<StoreCouponUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.eq(StoreCouponUser::getUid, uid);
        lambdaQueryWrapper.orderByDesc(StoreCouponUser::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据uid和状态获取列表
     * @param uid uid
     * @param status 优惠券状态：0-可用(未使用)，1-已用(已使用)，2-已过期(已失效)，null-查询全部
     * @param pageParamRequest 分页参数
     * @return 优惠券列表
     */
    @Override
    public List<StoreCouponUser> findListByUidAndStatus(Integer uid, Integer status, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 StoreCouponUser 类的多条件查询
        LambdaQueryWrapper<StoreCouponUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();

        lambdaQueryWrapper.eq(StoreCouponUser::getUid, uid);

        // 如果传入了状态参数，则按状态筛选
        if (status != null) {
            lambdaQueryWrapper.eq(StoreCouponUser::getStatus, status);
        }

        lambdaQueryWrapper.orderByDesc(StoreCouponUser::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取可用优惠券数量
     * @param uid 用户uid
     * @return Integer
     */
    @Override
    public Integer getUseCount(Integer uid) {
        LambdaQueryWrapper<StoreCouponUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCouponUser::getUid, uid);
        lambdaQueryWrapper.eq(StoreCouponUser::getStatus, 0);
        lambdaQueryWrapper.eq(StoreCouponUser::getIsDel, false);
        return dao.selectCount(lambdaQueryWrapper).intValue();
    }

    /**
     * 发送优惠券过期提醒
     * @param coupon 优惠券
     */
    @Override
    public void sendExpireNotice(StoreCoupon coupon) {
        if (coupon.getExpireNotice() == null || !coupon.getExpireNotice() || coupon.getExpireNoticeDays() == null) {
            return;
        }
        
        // 计算即将过期的日期
        String expireDate = DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT), coupon.getExpireNoticeDays(), Constants.DATE_FORMAT);
        Date expireTime = DateUtil.strToDate(expireDate + " 23:59:59", Constants.DATE_FORMAT + " HH:mm:ss");
        
        // 查询即将过期的优惠券
        LambdaQueryWrapper<StoreCouponUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCouponUser::getIsDel, false);
        lambdaQueryWrapper.eq(StoreCouponUser::getStatus, 0);
        lambdaQueryWrapper.eq(StoreCouponUser::getUseEndTime, expireTime);
        
        List<StoreCouponUser> couponUserList = dao.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(couponUserList)) {
            return;
        }
        
        // TODO: 发送过期提醒通知
        log.info("发送优惠券过期提醒，共{}张优惠券即将过期", couponUserList.size());
    }

    /**
     * 优惠券过期定时任务
     */
    @Override
    public void overdueTask() {
        // 获取当前时间
        String nowDate = DateUtil.nowDate(Constants.DATE_FORMAT_DATE);
        LambdaQueryWrapper<StoreCouponUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCouponUser::getStatus, CouponConstants.STORE_COUPON_USER_STATUS_USABLE);
        lqw.eq(StoreCouponUser::getIsDel, false);
        lqw.le(StoreCouponUser::getUseEndTime, nowDate);
        List<StoreCouponUser> storeCouponUserList = dao.selectList(lqw);
        if (CollUtil.isEmpty(storeCouponUserList)) {
            return;
        }
        Boolean execute = transactionTemplate.execute(e -> {
            storeCouponUserList.forEach(i -> {
                i.setStatus(CouponConstants.STORE_COUPON_USER_STATUS_LAPSED);
            });
            updateBatchById(storeCouponUserList);
            return Boolean.TRUE;
        });
        if (!execute) {
            log.error("优惠券过期定时任务——修改优惠券状态失败");
        }
    }
    
    /**
     * 根据订单ID获取订单使用的优惠券
     * @param orderId 订单ID
     * @return 优惠券列表
     */
    @Override
    public List<StoreCouponUser> getByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreCouponUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCouponUser::getOid, orderId);
        lqw.eq(StoreCouponUser::getStatus, CouponConstants.STORE_COUPON_USER_STATUS_USED);
        return dao.selectList(lqw);
    }
    
    /**
     * 根据订单ID获取订单使用的优惠券详细信息
     * @param orderId 订单ID
     * @return 订单使用的优惠券详细信息
     */
    @Override
    public List<StoreCouponUser> getOrderCouponDetail(Integer orderId) {
        // 获取订单关联的优惠券
        List<StoreCouponUser> couponUsers = getByOrderId(orderId);
        if (CollUtil.isEmpty(couponUsers)) {
            return new ArrayList<>();
        }

        // 组装返回数据
        return couponUsers;
    }

    /**
     * 根据用户ID获取用户优惠券详细信息列表
     * @param uid 用户ID
     * @param status 优惠券状态：0-可用(未使用)，1-已用(已使用)，2-已过期(已失效)，null-查询全部
     * @param pageParamRequest 分页参数
     * @return 用户优惠券详细信息列表
     */
    @Override
    public List<UserCouponResponse> getUserCouponList(Integer uid, Integer status, PageParamRequest pageParamRequest) {
        // 获取用户优惠券列表
        List<StoreCouponUser> couponUserList = findListByUidAndStatus(uid, status, pageParamRequest);
        if (CollUtil.isEmpty(couponUserList)) {
            return new ArrayList<>();
        }

        // 转换为响应对象
        List<UserCouponResponse> responseList = new ArrayList<>();
        Date now = new Date();

        for (StoreCouponUser couponUser : couponUserList) {
            UserCouponResponse response = new UserCouponResponse();
            BeanUtils.copyProperties(couponUser, response);

            // 设置优惠券门槛类型描述
            if (couponUser.getCouponType() != null) {
                switch (couponUser.getCouponType()) {
                    case 1:
                        response.setCouponTypeDesc("有门槛(满减券)");
                        break;
                    case 2:
                        response.setCouponTypeDesc("无门槛");
                        break;
                    default:
                        response.setCouponTypeDesc("未知");
                        break;
                }
            }

            // 设置使用类型描述
            if (couponUser.getUseType() != null) {
                switch (couponUser.getUseType()) {
                    case 1:
                        response.setUseTypeDesc("全部商品可用");
                        break;
                    case 2:
                        response.setUseTypeDesc("指定商品可用");
                        break;
                    case 3:
                        response.setUseTypeDesc("指定商品不可用");
                        break;
                    default:
                        response.setUseTypeDesc("未知");
                        break;
                }
            }

            // 设置优惠券类型描述
            if (couponUser.getType() != null) {
                switch (couponUser.getType()) {
                    case 1:
                        response.setTypeDesc("满减券");
                        break;
                    case 2:
                        response.setTypeDesc("新人专享券");
                        break;
                    case 3:
                        response.setTypeDesc("会员专享券");
                        break;
                    default:
                        response.setTypeDesc("未知");
                        break;
                }
            }

            // 设置状态描述
            if (couponUser.getStatus() != null) {
                switch (couponUser.getStatus()) {
                    case 0:
                        response.setStatusDesc("未使用");
                        break;
                    case 1:
                        response.setStatusDesc("已使用");
                        break;
                    case 2:
                        response.setStatusDesc("已失效");
                        break;
                    default:
                        response.setStatusDesc("未知");
                        break;
                }
            }

            // 计算剩余有效天数和是否即将过期
            if (couponUser.getUseEndTime() != null && couponUser.getStatus() != null && couponUser.getStatus() == 0) {
                long diffTime = couponUser.getUseEndTime().getTime() - now.getTime();
                int remainingDays = (int) (diffTime / (24 * 60 * 60 * 1000));
                response.setRemainingDays(Math.max(0, remainingDays));
                response.setIsExpiringSoon(remainingDays <= 7 && remainingDays > 0);
            } else {
                response.setRemainingDays(0);
                response.setIsExpiringSoon(false);
            }

            responseList.add(response);
        }

        return responseList;
    }
}

