package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.StoreActivityCouponDao;
import com.ylpz.core.service.StoreActivityCouponService;
import com.ylpz.model.activity.StoreActivityCoupon;

import cn.hutool.core.util.ObjectUtil;

/**
 * 活动优惠券关联表 Service 实现类
 */
@Service
public class StoreActivityCouponServiceImpl extends ServiceImpl<StoreActivityCouponDao, StoreActivityCoupon>
    implements StoreActivityCouponService {

    @Resource
    private StoreActivityCouponDao dao;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 根据活动ID获取优惠券ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    @Override
    public List<Integer> getCouponIdsByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityCoupon> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityCoupon::getActivityId, activityId)
            .eq(StoreActivityCoupon::getIsDel, false);
        List<StoreActivityCoupon> list = list(lambdaQueryWrapper);
        List<Integer> couponIds = new ArrayList<>();
        for (StoreActivityCoupon activityCoupon : list) {
            couponIds.add(activityCoupon.getCouponId());
        }
        return couponIds;
    }

    /**
     * 保存活动优惠券关联
     * 
     * @param activityId 活动ID
     * @param couponIds 优惠券ID列表
     * @return Boolean
     */
    @Override
    public Boolean saveActivityCoupon(Integer activityId, List<Integer> couponIds) {
        if (ObjectUtil.isEmpty(couponIds)) {
            return Boolean.TRUE;
        }
        return transactionTemplate.execute(e -> {
            // 删除原有关联
            deleteByActivityId(activityId);
            // 保存新关联
            List<StoreActivityCoupon> activityCouponList = new ArrayList<>();
            for (Integer couponId : couponIds) {
                StoreActivityCoupon activityCoupon = new StoreActivityCoupon();
                activityCoupon.setActivityId(activityId);
                activityCoupon.setCouponId(couponId);
                activityCouponList.add(activityCoupon);
            }
            return saveBatch(activityCouponList);
        });
    }

    /**
     * 删除活动优惠券关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    @Override
    public Boolean deleteByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityCoupon> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityCoupon::getActivityId, activityId)
            .eq(StoreActivityCoupon::getIsDel, false);
        List<StoreActivityCoupon> list = list(lambdaQueryWrapper);
        for (StoreActivityCoupon activityCoupon : list) {
            activityCoupon.setIsDel(true);
        }
        return updateBatchById(list);
    }
} 