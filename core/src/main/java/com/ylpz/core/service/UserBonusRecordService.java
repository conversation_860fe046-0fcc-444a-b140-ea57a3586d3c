package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.user.UserBonusRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 用户奖励金记录服务接口
 */
public interface UserBonusRecordService extends IService<UserBonusRecord> {

    /**
     * 获取奖励金记录列表
     *
     * @param linkId 关联ID
     * @param bonusType 奖励类型
     * @param userPhone 用户手机号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<UserBonusRecord> getBonusList(String linkId, String bonusType, 
                                         String userPhone, Date startTime, 
                                         Date endTime, PageParamRequest pageParamRequest);

    /**
     * 统计奖励金合计
     *
     * @return 奖励金合计
     */
    BigDecimal sumBonusTotal();
    
    /**
     * 按类型统计奖励金
     *
     * @return 各类型奖励金统计
     */
    Map<String, BigDecimal> getBonusByTypeStatistics();
    
    /**
     * 发放排行榜奖励金
     * 
     * @param rankType 排行榜类型（周排行、月排行、季度排行）
     * @param rankDate 排行日期
     * @return 是否发放成功
     */
    boolean distributeRankBonus(String rankType, Date rankDate);

    /**
     * 发放推广升级奖励金
     * 
     * @param uid       被推广用户ID
     * @param spreadUid 推广人用户ID
     * @return 是否发放成功
     */
    boolean distributeUpgradeBonus(Integer uid, Integer spreadUid);

    /**
     * 发放推广充值奖励金
     * 
     * @param uid            充值用户ID
     * @param spreadUid      推广人用户ID
     * @param rechargeAmount 充值金额
     * @return 是否发放成功
     */
    boolean distributeRechargeBonus(Integer uid, Integer spreadUid, BigDecimal rechargeAmount);

    /**
     * 发放首单购买奖励金
     * 
     * @param uid         购买用户ID
     * @param spreadUid   推广人用户ID
     * @param orderNo     订单号
     * @param orderAmount 订单金额
     * @return 是否发放成功
     */
    boolean distributeFirstOrderBonus(Integer uid, Integer spreadUid, String orderNo, BigDecimal orderAmount);
} 