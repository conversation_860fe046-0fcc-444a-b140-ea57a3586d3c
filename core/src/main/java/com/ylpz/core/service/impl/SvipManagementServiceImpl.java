package com.ylpz.core.service.impl;

import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import com.ylpz.core.common.constants.MemberLevelConstants;
import org.apache.poi.ss.usermodel.CellStyle;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SvipAddRequest;
import com.ylpz.core.common.request.SvipLevelAdjustRequest;
import com.ylpz.core.common.request.SvipMemberRequest;
import com.ylpz.core.common.request.SvipUpdateRequest;
import com.ylpz.core.common.request.UserSpreadUpdateRequest;
import com.ylpz.core.common.response.SvipMemberResponse;
import com.ylpz.core.dao.StoreOrderDao;
import com.ylpz.core.dao.SvipInfoDao;
import com.ylpz.core.dao.UserCommissionRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.OrderService;
import com.ylpz.core.service.SvipManagementService;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.UserSpreadService;
import com.ylpz.model.user.SvipInfo;
import com.ylpz.model.user.User;

import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import lombok.extern.slf4j.Slf4j;

/**
 * SVIP会员管理服务实现类
 */
@Slf4j
@Service
public class SvipManagementServiceImpl implements SvipManagementService {

    @Autowired
    private UserService userService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    @Autowired
    private UserSpreadService userSpreadService;

    @Autowired
    private UserDao userDao;

    @Autowired
    private SvipInfoDao svipInfoDao;

    /**
     * 添加SVIP会员
     *
     * @param request 添加请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addSvip(SvipAddRequest request) {
        // 保存SVIP信息
        SvipInfo svipInfo = new SvipInfo();
        svipInfo.setPhone(request.getPhone());
        svipInfo.setExtField1(request.getExtField1());
        svipInfo.setExtField2(request.getExtField2());
        svipInfo.setExtField3(request.getExtField3());
        svipInfo.setExtField4(request.getExtField4());
        svipInfo.setExtField5(request.getExtField5());
        svipInfo.setCreateTime(new Date());
        svipInfo.setUpdateTime(new Date());

        // 保存SVIP信息
        svipInfoDao.insert(svipInfo);
        return true;
    }

    /**
     * 更新SVIP会员信息
     *
     * @param request 更新请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateSvip(SvipUpdateRequest request) {
        if (request.getUid() == null) {
            return false;
        }

        User user = userService.getById(request.getUid());
        if (ObjectUtil.isNull(user) || !MemberLevelConstants.isVipOrAbove(user.getLevel())) {
            return false;
        }

        // 更新SVIP时间
        if (request.getSvipTime() != null) {
            user.setSvipTime(request.getSvipTime());
        }

        // 更新备注
        if (request.getRemark() != null) {
            user.setMark(request.getRemark());
        }

        // 更新用户信息
        return userService.updateById(user);
    }

    /**
     * 下载SVIP会员导入模板
     *
     * @param response HTTP响应对象
     */
    @Override
    public void downloadTemplate(HttpServletResponse response) {
        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("SVIP会员导入模板", StandardCharsets.UTF_8.name());
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建Excel写入器
            ExcelWriter writer = ExcelUtil.getWriter(true);

            // 设置表头
            writer.addHeaderAlias("phone", "手机号");
            writer.addHeaderAlias("extField1", "扩展字段1");
            writer.addHeaderAlias("extField2", "扩展字段2");
            writer.addHeaderAlias("extField3", "扩展字段3");
            writer.addHeaderAlias("extField4", "扩展字段4");
            writer.addHeaderAlias("extField5", "扩展字段5");

            // 设置样式
            CellStyle style = writer.getStyleSet().getHeadCellStyle();
            style.setFillForegroundColor((short) 22);

            // 写入示例数据
            List<SvipInfo> list = new ArrayList<>();
            SvipInfo example = new SvipInfo();
            example.setPhone("13800138000");
            example.setExtField1("扩展字段1示例");
            example.setExtField2("扩展字段2示例");
            example.setExtField3("扩展字段3示例");
            example.setExtField4("扩展字段4示例");
            example.setExtField5("扩展字段5示例");
            list.add(example);

            // 写入Excel
            writer.write(list, true);
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            IoUtil.close(out);
        } catch (IOException e) {
            log.error("下载SVIP会员导入模板失败", e);
        }
    }

    /**
     * 批量导入SVIP会员
     *
     * @param file 导入文件
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean importSvip(MultipartFile file) {
        if (file == null) {
            return false;
        }

        try {
            // 读取Excel文件
            ExcelReader reader = ExcelUtil.getReader(file.getInputStream());
            // 读取所有数据
            List<SvipInfo> svipInfoList = reader.readAll(SvipInfo.class);

            if (svipInfoList == null || svipInfoList.isEmpty()) {
                return false;
            }

            Date now = new Date();
            List<SvipInfo> list = new ArrayList<>();
            for (SvipInfo svipInfo : svipInfoList) {
                // 检查手机号是否为空
                if (StrUtil.isBlank(svipInfo.getPhone())) {
                    continue;
                }

                // 设置创建时间和更新时间
                svipInfo.setCreateTime(now);
                svipInfo.setUpdateTime(now);
                list.add(svipInfo);
            }
            svipInfoDao.insertOrUpdate(list);
            return true;
        } catch (Exception e) {
            log.error("导入SVIP会员失败", e);
            return false;
        }
    }

    /**
     * 调整会员等级
     *
     * @param request 调整等级请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean adjustLevel(SvipLevelAdjustRequest request) {
        if (request.getUid() == null || request.getMemberLevel() == null) {
            return false;
        }

        User user = userService.getById(request.getUid());
        if (ObjectUtil.isNull(user)) {
            return false;
        }

        // 记录原会员等级
        Integer oldLevel = user.getLevel();

        // 设置新的会员等级
        user.setLevel(request.getMemberLevel());

        // 如果调整为SVIP会员，记录升级时间
        if (MemberLevelConstants.isSvip(request.getMemberLevel())) {
            user.setSvipTime(new Date());
        } else if (MemberLevelConstants.isSvip(oldLevel)) { // 如果原来是SVIP会员，现在降级，则清除SVIP时间
            user.setSvipTime(null);
        }

        // 如果需要解除下级关系（升级为SVIP时）
        if (request.getUnbindRelation() && request.getMemberLevel().equals(MemberLevelConstants.Level.SVIP)) {
            // 如果有上级关系
            if (user.getSpreadUid() != null && user.getSpreadUid() > 0) {
                // 创建关系变更请求
                UserSpreadUpdateRequest spreadUpdateRequest = new UserSpreadUpdateRequest();
                spreadUpdateRequest.setUid(user.getId());
                spreadUpdateRequest.setSpreadUid(0); // 解除上级关系
                spreadUpdateRequest.setChangeMessage("升级为SVIP会员,系统自动解除原关系");
                spreadUpdateRequest.setStatus(1); // 有效状态

                // 如果有指定转移的SVIP手机号
                if (StrUtil.isNotBlank(request.getTransferPhone())) {
                    User transferUser = userService.getByPhone(request.getTransferPhone());
                    if (transferUser != null) {
                        spreadUpdateRequest.setSpreadUid(transferUser.getId());
                        spreadUpdateRequest.setChangeMessage("升级为SVIP会员,系统自动转移关系");
                    }
                }

                // 更新推广关系
                userSpreadService.updateUserSpread(spreadUpdateRequest);
            }
        }

        // 更新用户信息
        return userService.updateById(user);
    }

    /**
     * 获取SVIP会员列表
     *
     * @param request          查询条件
     * @param pageParamRequest 分页参数
     * @return SVIP会员列表
     */
    @Override
    public PageInfo<SvipMemberResponse> getList(SvipMemberRequest request, PageParamRequest pageParamRequest) {
        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();

        // SVIP会员等级条件
        queryWrapper.eq(User::getLevel, MemberLevelConstants.Level.SVIP);

        // 根据手机号筛选
        if (StrUtil.isNotBlank(request.getMobile())) {
            queryWrapper.like(User::getPhone, request.getMobile());
        }

        // 根据昵称筛选
        if (StrUtil.isNotBlank(request.getNickname())) {
            queryWrapper.like(User::getNickname, request.getNickname());
        }

        // 根据用户ID筛选
        if (request.getUid() != null) {
            queryWrapper.eq(User::getId, request.getUid());
        }

        // 根据时间范围筛选（SVIP升级时间）
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = cn.hutool.core.date.DateUtil.parse(request.getStartTime(), Constants.DATE_FORMAT);
            queryWrapper.ge(User::getSvipTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = cn.hutool.core.date.DateUtil.parse(request.getEndTime(), Constants.DATE_FORMAT);
            queryWrapper.le(User::getSvipTime, endTime);
        }

        // 分页查询
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<User> userList = userDao.selectList(queryWrapper);

        // 转换为响应对象
        List<SvipMemberResponse> responseList = new ArrayList<>();
        for (User user : userList) {
            SvipMemberResponse response = convertToSvipMemberResponse(user);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<User> userPageInfo = new PageInfo<>(userList);
        PageInfo<SvipMemberResponse> pageInfo = new PageInfo<>();
        pageInfo.setList(responseList);
        pageInfo.setTotal(userPageInfo.getTotal());
        pageInfo.setPageNum(userPageInfo.getPageNum());
        pageInfo.setPageSize(userPageInfo.getPageSize());
        pageInfo.setPages(userPageInfo.getPages());

        return pageInfo;
    }

    /**
     * 将User对象转换为SvipMemberResponse对象
     */
    private SvipMemberResponse convertToSvipMemberResponse(User user) {
        SvipMemberResponse response = new SvipMemberResponse();

        // 设置基本信息
        response.setUid(user.getId());
        response.setNickname(user.getNickname());
        response.setAvatar(user.getAvatar());
        response.setPhone(user.getPhone());
        response.setSvipTime(user.getSvipTime());
        response.setCreateTime(user.getCreateTime());
        response.setMark(user.getMark());

        // 查询推广人数
        LambdaQueryWrapper<User> spreadWrapper = new LambdaQueryWrapper<>();
        spreadWrapper.eq(User::getSpreadUid, user.getId());

        int spreadCount = userDao.selectCount(spreadWrapper).intValue();
        response.setSpreadCount(spreadCount);

        // 获取用户消费总金额
        BigDecimal consumeAmount = orderService.getUserOrderAmount(user.getId());
        response.setConsumeAmount(consumeAmount);

        // 获取用户佣金总额
        BigDecimal commissionAmount = userCommissionRecordService.getUserCommissionAmount(user.getId());
        response.setBrokerageAmount(commissionAmount);

        return response;
    }
} 