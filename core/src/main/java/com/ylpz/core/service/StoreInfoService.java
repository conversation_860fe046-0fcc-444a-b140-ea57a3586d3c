package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.StoreInfoRequest;
import com.ylpz.core.common.response.StoreInfoResponse;
import com.ylpz.model.system.StoreInfo;

/**
 * 店铺信息服务接口
 */
public interface StoreInfoService extends IService<StoreInfo> {

    /**
     * 获取店铺信息
     * @return 店铺信息响应对象
     */
    StoreInfoResponse getStoreInfo();

    /**
     * 更新店铺信息
     * @param request 店铺信息请求对象
     * @return 更新结果
     */
    boolean updateStoreInfo(StoreInfoRequest request);
} 