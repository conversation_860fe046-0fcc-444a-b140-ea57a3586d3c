package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.core.common.request.StoreCouponRequest;
import com.ylpz.core.common.request.StoreCouponSearchRequest;
import com.ylpz.core.common.response.StoreCouponFrontResponse;
import com.ylpz.core.common.response.StoreCouponInfoResponse;
import com.ylpz.core.common.response.StoreCouponResponse;
import com.ylpz.core.common.response.StoreCouponDataOverviewResponse;

import java.util.List;

/**
 * StoreCouponService 接口
 */
public interface StoreCouponService extends IService<StoreCoupon> {

    List<StoreCoupon> getList(StoreCouponSearchRequest request, PageParamRequest pageParamRequest);
    
    /**
     * 获取优惠券分页列表带使用数量和支付金额
     * @param request 搜索参数
     * @param pageParamRequest 分页参数
     * @return 优惠券列表带使用信息
     */
    List<StoreCouponResponse> getListWithUseInfo(StoreCouponSearchRequest request, PageParamRequest pageParamRequest);

    boolean create(StoreCouponRequest request);
    
    /**
     * 修改优惠券
     * @param id 优惠券ID
     * @param request 修改参数
     * @return 是否成功
     */
    boolean update(Integer id, StoreCouponRequest request);

    StoreCoupon getInfoException(Integer id);

    StoreCouponInfoResponse info(Integer id);

    /**
     * 根据优惠券id获取
     * @param ids 优惠券id集合
     * @return 优惠券列表
     */
    List<StoreCoupon> getByIds(List<Integer> ids);

    /**
     * 扣减数量
     * @param id 优惠券id
     * @param num 数量
     * @param isLimited 是否限量
     */
    Boolean deduction(Integer id, Integer num, Boolean isLimited);

    /**
     * 发送优惠券列表
     * @param searchAndPageRequest 搜索分页参数
     * @return 优惠券列表
     */
    List<StoreCoupon> getSendList(SearchAndPageRequest searchAndPageRequest);

    /**
     * 删除优惠券
     * @param id 优惠券id
     * @return Boolean
     */
    Boolean delete(Integer id);

    /**
     * 移动端优惠券列表
     * @param type 类型，1-通用，2-商品，3-品类
     * @param productId 产品id，搜索产品指定优惠券
     * @param pageParamRequest 分页参数
     * @return List<StoreCouponFrontResponse>
     */
    List<StoreCouponFrontResponse> getH5List(Integer type, Integer productId, PageParamRequest pageParamRequest);

    /**
     * 修改优惠券状态
     * @param id 优惠券id
     * @param status 状态
     */
    Boolean updateStatus(Integer id, Boolean status);


    /**
     * 获取会员生日优惠券列表
     * @param birthSendType 生日发券类型 1-生日当天 2-生日当周 3-生日当月
     * @return List<StoreCoupon>
     */
    List<StoreCoupon> findMemberBirthdayCouponList(Integer birthSendType);

    /**
     * 获取会员每月指定日期优惠券列表
     * @param day 每月发券日期
     * @return List<StoreCoupon>
     */
    List<StoreCoupon> findMemberMonthDayCouponList(Integer day);

    /**
     * 获取即将过期的优惠券列表（用于过期提醒）
     * @return List<StoreCoupon>
     */
    List<StoreCoupon> findNearExpireCoupons();

    /**
     * 获取优惠券数据概览
     * @param couponId 优惠券ID
     * @return StoreCouponDataOverviewResponse
     */
    StoreCouponDataOverviewResponse getDataOverview(Integer couponId);

    /**
     * 停发商品优惠券
     * @param productId 商品ID
     * @param couponId 优惠券ID
     * @return Boolean
     */
    Boolean stopProductCoupon(Integer productId, Integer couponId);
}
