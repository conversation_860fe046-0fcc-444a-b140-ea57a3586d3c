package com.ylpz.core.service;

import com.ylpz.model.user.User;

/**
 * 用户自动升级服务接口
 * 
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface UserAutoUpgradeService {

    /**
     * 检查并执行用户自动升级
     * 根据用户经验值自动升级用户等级
     * 
     * @param user 用户对象
     * @return 是否发生了升级
     */
    Boolean checkAndUpgradeUser(User user);

    /**
     * 批量检查所有用户的升级条件
     * 定时任务调用，检查所有符合条件的用户并执行升级
     * 
     * @return 升级统计信息
     */
    String batchCheckAndUpgradeUsers();

    /**
     * 检查用户是否满足升级条件
     * 
     * @param user 用户对象
     * @return 如果满足升级条件，返回目标等级ID；否则返回null
     */
    Integer checkUpgradeCondition(User user);

    /**
     * 执行用户升级操作
     * 
     * @param user 用户对象
     * @param targetLevelId 目标等级ID
     * @return 是否升级成功
     */
    Boolean executeUpgrade(User user, Integer targetLevelId);

    /**
     * 记录升级日志
     * 
     * @param user 用户对象
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @param upgradeReason 升级原因
     */
    void recordUpgradeLog(User user, Integer oldLevel, Integer newLevel, String upgradeReason);

    /**
     * 触发升级后的奖励逻辑
     * 包括发放升级奖励、通知推广人等
     * 
     * @param user 用户对象
     * @param oldLevel 原等级
     * @param newLevel 新等级
     */
    void triggerUpgradeRewards(User user, Integer oldLevel, Integer newLevel);
}
