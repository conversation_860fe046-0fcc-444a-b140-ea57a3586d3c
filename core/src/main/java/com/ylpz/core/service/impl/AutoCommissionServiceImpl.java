package com.ylpz.core.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.dao.StoreOrderAftersaleDao;
import com.ylpz.core.service.*;
import com.ylpz.model.order.OrderStatusEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderAftersale;
import com.ylpz.model.order.StoreOrderAftersaleRecord;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserCommissionRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 自动返佣服务实现类
 */
@Slf4j
@Service
public class AutoCommissionServiceImpl implements AutoCommissionService {

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    @Autowired
    private StoreOrderAftersaleService storeOrderAftersaleService;

    @Autowired
    private StoreOrderAftersaleDao storeOrderAftersaleDao;

    @Autowired
    private OrderExperienceService orderExperienceService;


    /**
     * 执行返佣任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeCommissionTask(Date targetDate) {
        Map<String, Object> result = new HashMap<>();

        try {
            Date processDate = targetDate != null ? targetDate : new Date();
            log.info("开始执行自动返佣任务，处理日期: {}", DateUtil.formatDateTime(processDate));

            // 1. 获取系统配置参数
            SystemParamSetting afterSaleTimeSetting = getSystemParamSetting("order_after_sale_time");
            if (afterSaleTimeSetting == null) {
                result.put("success", false);
                result.put("message", "未找到买家申请售后期限配置");
                return result;
            }

            // 解析售后期限天数
            int afterSaleDays = parseAfterSaleDays(afterSaleTimeSetting.getConfigValue());
            if (afterSaleDays <= 0) {
                result.put("success", false);
                result.put("message", "售后期限配置无效");
                return result;
            }

            // 2. 获取SVIP自购返佣配置
            SystemParamSetting svipAutoRefundSetting = getSystemParamSetting("svip_auto_refund");
            boolean svipAutoRefundEnabled = isSvipAutoRefundEnabled(svipAutoRefundSetting);

            // 3. 获取返佣比例配置
            SystemParamSetting commissionRatioSetting = getSystemParamSetting("commission_ratio");
            if (commissionRatioSetting == null) {
                result.put("success", false);
                result.put("message", "未找到返佣比例配置");
                return result;
            }

            // 4. 计算截止时间（确认收货后超过X天）
            Date cutoffDate = DateUtil.offsetDay(processDate, -afterSaleDays);

            // 5. 查询符合条件的订单
            List<StoreOrder> eligibleOrders = getEligibleOrders(cutoffDate);

            // 6. 处理每个订单的返佣
            int processedCount = 0;
            int successCount = 0;
            int failedCount = 0;
            BigDecimal totalCommissionAmount = BigDecimal.ZERO;

            for (StoreOrder order : eligibleOrders) {
                try {
                    // 处理返佣
                    Map<String, Object> orderResult = processOrderCommission(order, commissionRatioSetting, svipAutoRefundEnabled);
                    processedCount++;

                    if ((Boolean) orderResult.get("success")) {
                        successCount++;
                        BigDecimal orderCommission = (BigDecimal) orderResult.get("commissionAmount");
                        if (orderCommission != null) {
                            totalCommissionAmount = totalCommissionAmount.add(orderCommission);
                        }

                        // 处理经验值增加
                        try {
                            User user = userService.getById(order.getUid());
                            if (user != null) {
                                orderExperienceService.addExperienceForOrder(order, user);
                            }
                        } catch (Exception e) {
                            log.error("处理订单经验值失败，订单ID: {}, 错误: {}", order.getId(), e.getMessage(), e);
                            // 经验值处理失败不影响返佣处理
                        }
                    } else {
                        failedCount++;
                    }
                } catch (Exception e) {
                    log.error("处理订单返佣失败，订单ID: {}, 错误: {}", order.getId(), e.getMessage(), e);
                    failedCount++;
                }
            }

            result.put("success", true);
            result.put("message", "自动返佣任务执行完成");
            result.put("processDate", DateUtil.formatDateTime(processDate));
            result.put("cutoffDate", DateUtil.formatDateTime(cutoffDate));
            result.put("totalOrders", eligibleOrders.size());
            result.put("processedCount", processedCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("totalCommissionAmount", totalCommissionAmount);

            log.info("自动返佣任务执行完成，共处理 {} 个订单，成功 {} 个，失败 {} 个，总返佣金额: {}",
                    processedCount, successCount, failedCount, totalCommissionAmount);

        } catch (Exception e) {
            log.error("执行自动返佣任务失败", e);
            result.put("success", false);
            result.put("message", "执行任务失败：" + e.getMessage());
        }

        return result;
    }


    // ==================== 私有辅助方法 ====================

    /**
     * 获取系统参数配置
     */
    private SystemParamSetting getSystemParamSetting(String configCode) {
        LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemParamSetting::getConfigCode, configCode)
                .eq(SystemParamSetting::getStatus, true);
        return systemParamSettingService.getOne(queryWrapper);
    }


    /**
     * 解析售后期限天数
     */
    private int parseAfterSaleDays(String configValue) {
        try {
            if (StrUtil.isBlank(configValue)) {
                return 0;
            }

            JSONObject config = JSONUtil.parseObj(configValue);
            // 支持字符串和数字两种格式
            Object valueObj = config.get("value");
            if (valueObj instanceof String) {
                return Integer.parseInt((String) valueObj);
            } else if (valueObj instanceof Number) {
                return ((Number) valueObj).intValue();
            }
            return 0;
        } catch (Exception e) {
            log.error("解析售后期限配置失败: {}", configValue, e);
            return 0;
        }
    }

    /**
     * 检查SVIP自购返佣是否启用
     */
    private boolean isSvipAutoRefundEnabled(SystemParamSetting setting) {
        if (setting == null) {
            return false;
        }

        try {
            JSONObject config = JSONUtil.parseObj(setting.getConfigValue());
            return config.getBool("enabled", false);
        } catch (Exception e) {
            log.error("解析SVIP自购返佣配置失败: {}", setting.getConfigValue(), e);
            return false;
        }
    }

    /**
     * 查询符合返佣条件的订单
     */
    private List<StoreOrder> getEligibleOrders(Date cutoffDate) {
        LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();

        // 基本条件：已支付、未删除、未完成返佣
        queryWrapper.eq(StoreOrder::getPaid, true)
                .eq(StoreOrder::getIsDel, false)
                .eq(StoreOrder::getIsCommissionCompleted, false); // 关键优化：只查询未完成返佣的订单

        // 用户等级条件：SVIP用户(3)或其下级用户
        queryWrapper.and(wrapper ->
                wrapper.eq(StoreOrder::getUserLevel, MemberLevelConstants.Level.SVIP) // SVIP用户自购
                        .or()
                        .exists("SELECT 1 FROM user u WHERE u.id = store_order.uid AND u.spread_uid IN " +
                                "(SELECT id FROM user WHERE level = " + MemberLevelConstants.Level.SVIP + ")") // SVIP的下级用户
        );

        // 订单状态条件：已完成
        queryWrapper.eq(StoreOrder::getStatus, OrderStatusEnum.COMPLETED.getCode());

        // 确认收货时间条件（这里简化处理，实际应该查询订单状态变更记录）
        queryWrapper.le(StoreOrder::getUpdateTime, cutoffDate);

        // 获取所有符合基本条件的订单
        List<StoreOrder> allOrders = storeOrderService.list(queryWrapper);
        log.info("基础条件筛选出 {} 个订单，开始检查售后条件", allOrders.size());

        // 进一步筛选：检查售后条件
        List<StoreOrder> eligibleOrders = new ArrayList<>();
        for (StoreOrder order : allOrders) {
            if (isOrderEligibleForCommission(order)) {
                eligibleOrders.add(order);
            }
        }

        log.info("售后条件筛选后剩余 {} 个符合返佣条件的订单", eligibleOrders.size());
        return eligibleOrders;
    }

    /**
     * 检查订单是否符合返佣条件
     * 规则：订单状态为已完成，或者有售后但售后处理方式仅限于"换货"(2)或"小额补偿"(3)
     */
    private boolean isOrderEligibleForCommission(StoreOrder order) {
        try {
            // 查询该订单的售后申请
            LambdaQueryWrapper<StoreOrderAftersale> aftersaleWrapper = new LambdaQueryWrapper<>();
            aftersaleWrapper.eq(StoreOrderAftersale::getOrderId, order.getId());
            List<StoreOrderAftersale> aftersaleList = storeOrderAftersaleService.list(aftersaleWrapper);

            // 如果没有售后申请，直接符合条件
            if (aftersaleList.isEmpty()) {
                return true;
            }

            // 如果有售后申请，需要检查所有售后的处理方式
            for (StoreOrderAftersale aftersale : aftersaleList) {
                // 查询该售后申请的处理记录
                List<StoreOrderAftersaleRecord> records = storeOrderAftersaleDao.getRecordsByAftersaleId(aftersale.getId());

                if (!records.isEmpty()) {
                    // 获取最新的处理记录
                    StoreOrderAftersaleRecord latestRecord = records.get(records.size() - 1);
                    Integer processingMethod = latestRecord.getProcessingMethod();

                    // 如果处理方式不是换货(2)或小额补偿(3)，则不符合返佣条件
                    if (processingMethod == null || (processingMethod != 2 && processingMethod != 3)) {
                        log.debug("订单 {} 的售后处理方式为 {}，不符合返佣条件", order.getId(), processingMethod);
                        return false;
                    }
                } else {
                    // 如果售后申请没有处理记录，可能还在处理中，暂不返佣
                    log.debug("订单 {} 的售后申请 {} 没有处理记录，暂不返佣", order.getId(), aftersale.getId());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("检查订单 {} 售后条件时发生异常", order.getId(), e);
            return false;
        }
    }

    /**
     * 处理单个订单的返佣
     */
    private Map<String, Object> processOrderCommission(StoreOrder order, SystemParamSetting commissionRatioSetting, boolean svipAutoRefundEnabled) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 检查是否已经处理过返佣
            if (isCommissionAlreadyProcessed(order.getOrderId())) {
                result.put("success", false);
                result.put("message", "订单已处理过返佣");
                return result;
            }

            // 获取下单用户信息
            User orderUser = userService.getById(order.getUid());
            if (orderUser == null) {
                result.put("success", false);
                result.put("message", "订单用户不存在");
                return result;
            }

            // 解析返佣比例配置
            JSONObject commissionConfig = JSONUtil.parseObj(commissionRatioSetting.getConfigValue());

            boolean processed = false;
            BigDecimal totalCommissionAmount = BigDecimal.ZERO;

            // 处理SVIP用户自购返佣
            if (MemberLevelConstants.isSvip(orderUser.getLevel()) && svipAutoRefundEnabled) {
                // 使用数字标识符获取SVIP返佣比例
                JSONObject svipConfig = commissionConfig.getJSONObject("3"); // 3=SVIP会员
                if (svipConfig != null) {
                    Object purchaseRate = svipConfig.get("purchase");
                    if (purchaseRate instanceof Number) {
                        BigDecimal svipRate = new BigDecimal(purchaseRate.toString());
                        if (svipRate.compareTo(BigDecimal.ZERO) > 0) {
                            BigDecimal commissionAmount = createCommissionRecord(orderUser.getId(), order, svipRate, "SVIP自购返佣");
                            if (commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                                processed = true;
                                totalCommissionAmount = totalCommissionAmount.add(commissionAmount);
                            }
                        }
                    }
                }
            }

            // 处理上级SVIP用户返佣
            if (orderUser.getSpreadUid() != null) {
                User spreadUser = userService.getById(orderUser.getSpreadUid());
                if (spreadUser != null && MemberLevelConstants.isSvip(spreadUser.getLevel())) {
                    BigDecimal spreadRate = getSpreadCommissionRate(orderUser.getLevel(), commissionConfig);
                    if (spreadRate.compareTo(BigDecimal.ZERO) > 0) {
                        BigDecimal commissionAmount = createCommissionRecord(spreadUser.getId(), order, spreadRate, "下级用户购买返佣");
                        if (commissionAmount.compareTo(BigDecimal.ZERO) > 0) {
                            processed = true;
                            totalCommissionAmount = totalCommissionAmount.add(commissionAmount);
                        }
                    }
                }
            }

            result.put("success", processed);
            result.put("message", processed ? "返佣处理成功" : "无需返佣");
            result.put("commissionAmount", totalCommissionAmount);

            // 如果返佣成功，标记订单为已完成返佣
            if (processed) {
                markOrderCommissionCompleted(order.getId());
            }

        } catch (Exception e) {
            log.error("处理订单返佣失败，订单ID: {}", order.getId(), e);
            result.put("success", false);
            result.put("message", "处理失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 标记订单为已完成返佣
     */
    private void markOrderCommissionCompleted(Integer orderId) {
        try {
            StoreOrder order = new StoreOrder();
            order.setId(orderId);
            order.setIsCommissionCompleted(true);
            order.setUpdateTime(new Date());
            storeOrderService.updateById(order);
            log.info("订单 {} 已标记为完成返佣", orderId);
        } catch (Exception e) {
            log.error("标记订单 {} 返佣完成状态失败", orderId, e);
        }
    }

    /**
     * 检查订单是否已处理过返佣
     * 注意：由于添加了is_commission_completed字段，这个方法主要用于双重检查
     */
    private boolean isCommissionAlreadyProcessed(String orderId) {
        // 首先检查订单表的标记字段
        StoreOrder order = storeOrderService.getOne(new LambdaQueryWrapper<StoreOrder>().eq(StoreOrder::getOrderId, orderId));
        if (order != null && Boolean.TRUE.equals(order.getIsCommissionCompleted())) {
            return true;
        }

        // 双重检查：查询返佣记录表
        LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCommissionRecord::getLinkId, orderId)
                .like(UserCommissionRecord::getMark, "自动返佣");
        return userCommissionRecordService.count(queryWrapper) > 0;
    }

    /**
     * 获取推广返佣比例
     */
    private BigDecimal getSpreadCommissionRate(Integer userLevel, JSONObject commissionConfig) {
        try {
            String levelKey = userLevel + "";
            // 根据标准化配置格式解析：{"1":{"purchase":10},"2":{"purchase":8},"3":{"purchase":6}}
            JSONObject levelConfig = commissionConfig.getJSONObject(levelKey);
            if (levelConfig != null) {
                Object purchaseRate = levelConfig.get("purchase");
                if (purchaseRate instanceof Number) {
                    return new BigDecimal(purchaseRate.toString());
                }
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("解析返佣比例配置失败，用户等级: {}, 配置: {}", userLevel, commissionConfig, e);
            return BigDecimal.ZERO;
        }
    }

    /**
     * 创建返佣记录
     */
    private BigDecimal createCommissionRecord(Integer uid, StoreOrder order, BigDecimal rate, String title) {
        try {
            // 计算返佣金额
            BigDecimal commissionAmount = order.getPayPrice()
                    .multiply(rate)
                    .divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);

            if (commissionAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return BigDecimal.ZERO;
            }

            // 创建佣金记录
            UserCommissionRecord record = new UserCommissionRecord();
            record.setUid(uid);
            record.setLinkId(order.getOrderId());
            record.setType(1); // 增加类型
            record.setTitle(title);
            record.setPrice(commissionAmount);
            record.setMark("自动返佣 - 订单号: " + order.getOrderId());
            record.setStatus(1); // 待结算状态
            record.setFrozenTime(0); // 无冻结期
            record.setThawTime(System.currentTimeMillis()); // 立即可用
            record.setCreateTime(new Date());
            record.setUpdateTime(new Date());

            boolean saved = userCommissionRecordService.save(record);
            if (saved) {
                log.info("创建返佣记录成功 - 用户ID: {}, 订单ID: {}, 返佣金额: {}", uid, order.getId(), commissionAmount);
                return commissionAmount;
            }

            return BigDecimal.ZERO;
        } catch (Exception e) {
            log.error("创建返佣记录失败 - 用户ID: {}, 订单ID: {}", uid, order.getId(), e);
            return BigDecimal.ZERO;
        }
    }


}
