package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.activity.StoreActivityProduct;

import java.util.List;

/**
 * 活动商品关联表 Service 接口
 */
public interface StoreActivityProductService extends IService<StoreActivityProduct> {

    /**
     * 根据活动ID获取商品ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    List<Integer> getProductIdsByActivityId(Integer activityId);

    /**
     * 保存活动商品关联
     * 
     * @param activityId 活动ID
     * @param productIds 商品ID列表
     * @return Boolean
     */
    Boolean saveActivityProduct(Integer activityId, List<Integer> productIds);

    /**
     * 删除活动商品关联
     *
     * @param activityId 活动ID
     * @return Boolean
     */
    Boolean deleteByActivityId(Integer activityId);

    /**
     * 根据商品ID获取活动ID列表
     *
     * @param productId 商品ID
     * @return List<Integer>
     */
    List<Integer> getActivityIdsByProductId(Integer productId);
}