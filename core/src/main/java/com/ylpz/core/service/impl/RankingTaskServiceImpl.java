package com.ylpz.core.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.model.user.enums.UserBillEnum;
import com.ylpz.core.service.*;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBill;
import com.ylpz.model.user.UserBonusRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * 奖励金定时任务服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@Service
public class RankingTaskServiceImpl implements RankingTaskService {

    @Autowired
    private UserService userService;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    @Autowired
    private UserBonusRecordService userBonusRecordService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private RankingService rankingService;

    @Autowired
    private UserBillService userBillService;

    /**
     * 处理推广普通会员升级VIP奖励
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processUpgradeToVipBonus(User upgradedUser) {
        if (upgradedUser == null || upgradedUser.getSpreadUid() == null || upgradedUser.getSpreadUid() == 0) {
            log.debug("用户没有推广人，跳过升级VIP奖励: userId={}", upgradedUser.getId());
            return false;
        }

        try {
            // 获取推广人信息
            User promoter = userService.getById(upgradedUser.getSpreadUid());
            if (promoter == null || !isSvipUser(promoter)) {
                log.debug("推广人不是SVIP会员，跳过升级VIP奖励: promoterId={}", upgradedUser.getSpreadUid());
                return false;
            }

            // 获取升级VIP奖励配置
            Map<String, Object> config = getSystemConfig("upgrade_to_vip_bonus");
            if (config == null || !Boolean.TRUE.equals(config.get("enabled"))) {
                log.debug("升级VIP奖励功能未启用");
                return false;
            }

            Double bonusAmount = Double.valueOf(config.get("amount").toString());
            if (bonusAmount <= 0) {
                log.warn("升级VIP奖励金额配置错误: amount={}", bonusAmount);
                return false;
            }

            // 检查是否已经发放过奖励（防重复）
            LambdaQueryWrapper<UserBonusRecord> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(UserBonusRecord::getUid, promoter.getId())
                       .eq(UserBonusRecord::getBonusType, "upgrade")
                       .eq(UserBonusRecord::getSourceType, "推广普通会员升级为VIP")
                       .eq(UserBonusRecord::getLinkId, upgradedUser.getId().toString());

            if (userBonusRecordService.count(checkWrapper) > 0) {
                log.debug("升级VIP奖励已发放过，跳过: promoterId={}, upgradedUserId={}",
                         promoter.getId(), upgradedUser.getId());
                return false;
            }

            // 发放奖励金
            String mark = StrUtil.format("推广用户 {} 升级为VIP会员奖励", upgradedUser.getNickname());
            return grantBonus(promoter.getId(), bonusAmount, "upgrade", "推广普通会员升级为VIP",
                            upgradedUser.getId().toString(), mark);

        } catch (Exception e) {
            log.error("处理升级VIP奖励失败: upgradedUserId={}, error={}", upgradedUser.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理推广VIP会员升级SVIP奖励
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processUpgradeToSvipBonus(User upgradedUser) {
        if (upgradedUser == null || upgradedUser.getSpreadUid() == null || upgradedUser.getSpreadUid() == 0) {
            log.debug("用户没有推广人，跳过升级SVIP奖励: userId={}", upgradedUser.getId());
            return false;
        }

        try {
            // 获取推广人信息
            User promoter = userService.getById(upgradedUser.getSpreadUid());
            if (promoter == null || !isSvipUser(promoter)) {
                log.debug("推广人不是SVIP会员，跳过升级SVIP奖励: promoterId={}", upgradedUser.getSpreadUid());
                return false;
            }

            // 获取升级SVIP奖励配置
            Map<String, Object> config = getSystemConfig("upgrade_to_svip_bonus");
            if (config == null || !Boolean.TRUE.equals(config.get("enabled"))) {
                log.debug("升级SVIP奖励功能未启用");
                return false;
            }

            Double bonusAmount = Double.valueOf(config.get("amount").toString());
            if (bonusAmount <= 0) {
                log.warn("升级SVIP奖励金额配置错误: amount={}", bonusAmount);
                return false;
            }

            // 检查是否已经发放过奖励（防重复）
            LambdaQueryWrapper<UserBonusRecord> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(UserBonusRecord::getUid, promoter.getId())
                       .eq(UserBonusRecord::getBonusType, "upgrade")
                       .eq(UserBonusRecord::getSourceType, "推广VIP会员升级为SVIP")
                       .eq(UserBonusRecord::getLinkId, upgradedUser.getId().toString());

            if (userBonusRecordService.count(checkWrapper) > 0) {
                log.debug("升级SVIP奖励已发放过，跳过: promoterId={}, upgradedUserId={}",
                         promoter.getId(), upgradedUser.getId());
                return false;
            }

            // 发放奖励金
            String mark = StrUtil.format("推广用户 {} 升级为SVIP会员奖励", upgradedUser.getNickname());
            return grantBonus(promoter.getId(), bonusAmount, "upgrade", "推广VIP会员升级为SVIP",
                            upgradedUser.getId().toString(), mark);

        } catch (Exception e) {
            log.error("处理升级SVIP奖励失败: upgradedUserId={}, error={}", upgradedUser.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理推广新用户首单奖励
     * 通过定时任务调用，检查已完成且过了售后期的首单
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processFirstOrderBonus(User newUser, Double firstOrderAmount) {
        if (newUser == null || newUser.getSpreadUid() == null || newUser.getSpreadUid() == 0) {
            log.debug("用户没有推广人，跳过首单奖励: userId={}", newUser.getId());
            return false;
        }

        if (firstOrderAmount == null || firstOrderAmount <= 0) {
            log.debug("首单金额无效，跳过首单奖励: userId={}, amount={}", newUser.getId(), firstOrderAmount);
            return false;
        }

        try {
            // 获取推广人信息
            User promoter = userService.getById(newUser.getSpreadUid());
            if (promoter == null || !isVipUser(promoter)) {
                log.debug("推广人不是VIP会员，跳过首单奖励: promoterId={}", newUser.getSpreadUid());
                return false;
            }

            // 获取首单奖励配置
            Map<String, Object> config = getSystemConfig("first_order_bonus");
            if (config == null || !Boolean.TRUE.equals(config.get("enabled"))) {
                log.debug("首单奖励功能未启用");
                return false;
            }

            Double bonusRatio = Double.valueOf(config.get("ratio").toString());
            if (bonusRatio <= 0) {
                log.warn("首单奖励比例配置错误: ratio={}", bonusRatio);
                return false;
            }

            // 计算奖励金额
            Double bonusAmount = firstOrderAmount * bonusRatio / 100;

            // 检查是否已经发放过奖励（防重复）
            LambdaQueryWrapper<UserBonusRecord> checkWrapper = new LambdaQueryWrapper<>();
            checkWrapper.eq(UserBonusRecord::getUid, promoter.getId())
                       .eq(UserBonusRecord::getBonusType, "first_order")
                       .eq(UserBonusRecord::getSourceType, "推广新用户首单购买")
                       .eq(UserBonusRecord::getLinkId, newUser.getId().toString());

            if (userBonusRecordService.count(checkWrapper) > 0) {
                log.debug("首单奖励已发放过，跳过: promoterId={}, newUserId={}",
                         promoter.getId(), newUser.getId());
                return false;
            }

            // 发放奖励金
            String mark = StrUtil.format("推广新用户 {} 首单购买奖励，订单金额: {}, 奖励比例: {}%",
                                       newUser.getNickname(), firstOrderAmount, bonusRatio);
            return grantBonus(promoter.getId(), bonusAmount, "first_order", "推广新用户首单购买",
                            newUser.getId().toString(), mark);

        } catch (Exception e) {
            log.error("处理首单奖励失败: newUserId={}, error={}", newUser.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量处理首单奖励
     * 定时任务调用，检查所有符合条件的首单并发放奖励
     */
    public String batchProcessFirstOrderBonus() {
        log.info("开始执行首单奖励批量处理任务");

        int totalChecked = 0;
        int totalProcessed = 0;
        int errorCount = 0;

        try {
            // 获取首单奖励配置
            Map<String, Object> config = getSystemConfig("first_order_bonus");
            if (config == null || !Boolean.TRUE.equals(config.get("enabled"))) {
                return "首单奖励功能未启用";
            }

            // 获取售后期限配置
            Integer afterSaleDays = getAfterSaleDays();
            if (afterSaleDays == null || afterSaleDays <= 0) {
                return "售后期限配置错误";
            }

            // 计算截止时间（当前时间减去售后期限）
            String cutoffDate = DateUtil.formatDate(DateUtil.offsetDay(new Date(), -afterSaleDays));

            // 查找符合条件的首单
            List<StoreOrder> eligibleFirstOrders = getEligibleFirstOrders(cutoffDate);
            totalChecked = eligibleFirstOrders.size();

            for (StoreOrder order : eligibleFirstOrders) {
                try {
                    User user = userService.getById(order.getUid());
                    if (user != null) {
                        Double orderAmount = order.getPayPrice().doubleValue();
                        if (processFirstOrderBonus(user, orderAmount)) {
                            totalProcessed++;
                            log.info("首单奖励处理成功: 用户ID={}, 订单ID={}, 金额={}",
                                   user.getId(), order.getOrderId(), orderAmount);
                        }
                    }
                } catch (Exception e) {
                    errorCount++;
                    log.error("处理首单奖励失败: 订单ID={}, 错误信息={}", order.getOrderId(), e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("批量首单奖励处理任务执行异常", e);
            return StrUtil.format("任务执行异常: {}", e.getMessage());
        }

        String result = StrUtil.format("首单奖励批量处理完成 - 检查订单数: {}, 处理成功数: {}, 错误数: {}",
                                     totalChecked, totalProcessed, errorCount);
        log.info(result);
        return result;
    }

    /**
     * 检查用户是否为SVIP会员
     */
    @Override
    public Boolean isSvipUser(User user) {
        return MemberLevelConstants.isSvip(user != null ? user.getLevel() : null);
    }

    /**
     * 检查用户是否为VIP会员
     */
    @Override
    public Boolean isVipUser(User user) {
        return MemberLevelConstants.isVipOrAbove(user != null ? user.getLevel() : null);
    }

    /**
     * 发放奖励金
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean grantBonus(Integer userId, Double amount, String bonusType, String sourceType, String linkId, String mark) {
        if (userId == null || amount == null || amount <= 0) {
            log.warn("发放奖励金参数无效: userId={}, amount={}", userId, amount);
            return false;
        }

        try {
            User user = userService.getById(userId);
            if (user == null) {
                log.error("用户不存在: userId={}", userId);
                return false;
            }

            BigDecimal bonusAmount = BigDecimal.valueOf(amount);
            BigDecimal newBalance = user.getNowMoney().add(bonusAmount);

            // 创建奖励金记录
            UserBonusRecord bonusRecord = new UserBonusRecord();
            bonusRecord.setUid(userId);
            bonusRecord.setLinkId(linkId);
            bonusRecord.setBonusType(bonusType);
            bonusRecord.setSourceType(sourceType);
            bonusRecord.setPrice(bonusAmount);
            bonusRecord.setBalance(newBalance);
            bonusRecord.setMark(mark);
            bonusRecord.setStatus(1); // 已发放
            bonusRecord.setUserPhone(user.getPhone());
            bonusRecord.setUserLevel(getUserLevelName(user.getLevel()));
            bonusRecord.setCreateTime(new Date());
            bonusRecord.setUpdateTime(new Date());

            // 保存奖励金记录
            boolean saveResult = userBonusRecordService.save(bonusRecord);
            if (!saveResult) {
                log.error("保存奖励金记录失败: userId={}, amount={}", userId, amount);
                return false;
            }

            // 创建UserBill记录
            UserBill userBill = new UserBill();
            userBill.setUid(userId);
            userBill.setLinkId(linkId);
            userBill.setPm(1); // 收入
            userBill.setTitle(UserBillEnum.BONUS.getTitle());
            userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
            userBill.setType(UserBillEnum.BONUS.getType());
            userBill.setNumber(bonusAmount);
            userBill.setBalance(newBalance);
            userBill.setMark(mark);
            userBill.setStatus(1); // 有效
            userBill.setCreateTime(new Date());
            userBill.setUpdateTime(new Date());

            // 保存UserBill记录
            boolean billSaveResult = userBillService.save(userBill);
            if (!billSaveResult) {
                log.error("保存UserBill记录失败: userId={}, amount={}", userId, amount);
                return false;
            }

            // 更新用户余额
            boolean updateResult = userService.operationNowMoney(userId, bonusAmount, user.getNowMoney(), "add");
            if (!updateResult) {
                log.error("更新用户余额失败: userId={}, amount={}", userId, amount);
                return false;
            }

            // 更新用户可提现金额（奖励金可以提现）
            BigDecimal currentWithdrawablePrice = user.getWithdrawablePrice() == null ? BigDecimal.ZERO : user.getWithdrawablePrice();
            boolean withdrawableUpdateResult = userService.operationWithdrawablePrice(userId, bonusAmount, currentWithdrawablePrice, "add");
            if (!withdrawableUpdateResult) {
                log.error("更新用户可提现金额失败: userId={}, amount={}", userId, amount);
                return false;
            }

            log.info("奖励金发放成功: userId={}, amount={}, bonusType={}, sourceType={}, 可提现金额已更新",
                   userId, amount, bonusType, sourceType);
            return true;

        } catch (Exception e) {
            log.error("发放奖励金失败: userId={}, amount={}, error={}", userId, amount, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取用户等级名称
     */
    private String getUserLevelName(Integer level) {
        return MemberLevelConstants.getLevelName(level);
    }

    /**
     * 获取系统参数配置
     */
    @Override
    public Map<String, Object> getSystemConfig(String configCode) {
        try {
            LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SystemParamSetting::getConfigCode, configCode)
                       .eq(SystemParamSetting::getStatus, 1);

            SystemParamSetting setting = systemParamSettingService.getOne(queryWrapper);
            if (setting == null || StrUtil.isBlank(setting.getConfigValue())) {
                return null;
            }

            JSONObject jsonObject = JSONUtil.parseObj(setting.getConfigValue());
            return jsonObject.toBean(Map.class);
        } catch (Exception e) {
            log.error("获取系统配置失败: configCode={}, error={}", configCode, e.getMessage());
            return null;
        }
    }

    /**
     * 生成销售榜单并发放奖励
     * 使用现有的RankingService来生成排行榜，然后发放额外的推广奖励
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> generateRankingAndRewards(String rankType) {
        Map<String, Object> result = new HashMap<>();
        result.put("success", false);
        result.put("message", "");
        result.put("rankType", rankType);
        result.put("rewardCount", 0);
        result.put("totalRewardAmount", 0.0);

        try {
            // 获取排行榜奖励配置
            String configCode = rankType + "_ranking_bonus";
            Map<String, Object> config = getSystemConfig(configCode);
            if (config == null || !Boolean.TRUE.equals(config.get("enabled"))) {
                result.put("message", "排行榜奖励功能未启用");
                return result;
            }

            // 计算统计时间范围
            Map<String, String> dateRange = calculateDateRange(rankType);
            String startDate = dateRange.get("startDate");
            String endDate = dateRange.get("endDate");

            // 使用现有的RankingService生成排行榜
            String rankTypeMapping = mapRankType(rankType);
            Integer rankingId = rankingService.generateRanking(rankTypeMapping, startDate, endDate);

            if (rankingId == null) {
                result.put("message", "生成排行榜失败，可能该期间无销售数据");
                return result;
            }

            // 使用现有的RankingService发放排行榜奖励
            Boolean distributeResult = rankingService.distributeReward(rankingId);

            if (distributeResult) {
                result.put("success", true);
                result.put("message", StrUtil.format("{}排行榜生成并发放奖励完成，排行榜ID: {}", getRankTypeName(rankType), rankingId));
                result.put("rankingId", rankingId);
            } else {
                result.put("message", "排行榜奖励发放失败");
            }

        } catch (Exception e) {
            log.error("生成销售榜单并发放奖励失败: rankType={}, error={}", rankType, e.getMessage(), e);
            result.put("message", "处理失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 映射排行榜类型
     */
    private String mapRankType(String rankType) {
        switch (rankType) {
            case "weekly": return "week";
            case "monthly": return "month";
            case "quarterly": return "quarter";
            default: return rankType;
        }
    }

    /**
     * 统计用户销售数据
     * 包含自购订单和下线购买订单金额，仅统计成为SVIP之后的数据
     */
    @Override
    public Double calculateUserSalesAmount(Integer userId, String startDate, String endDate) {
        if (userId == null) {
            return 0.0;
        }

        try {
            // 统计自购订单金额（StoreOrderService已经处理了SVIP条件筛选）
            Double selfSalesAmount = storeOrderService.calculateUserSalesAmount(userId, startDate, endDate);

            // 统计下线购买订单金额（StoreOrderService已经处理了SVIP时间筛选）
            Double downlineSalesAmount = storeOrderService.calculateDownlineSalesAmount(userId, startDate, endDate);

            return (selfSalesAmount != null ? selfSalesAmount : 0.0) +
                   (downlineSalesAmount != null ? downlineSalesAmount : 0.0);

        } catch (Exception e) {
            log.error("统计用户销售数据失败: userId={}, error={}", userId, e.getMessage());
            return 0.0;
        }
    }

    /**
     * 执行定时任务 - 周榜奖励发放
     */
    @Override
    public String executeWeeklyRankingTask() {
        log.info("开始执行周榜奖励发放任务");
        Map<String, Object> result = generateRankingAndRewards("weekly");
        String message = result.get("message").toString();
        log.info("周榜奖励发放任务完成: {}", message);
        return message;
    }

    /**
     * 执行定时任务 - 月榜奖励发放
     */
    @Override
    public String executeMonthlyRankingTask() {
        log.info("开始执行月榜奖励发放任务");
        Map<String, Object> result = generateRankingAndRewards("monthly");
        String message = result.get("message").toString();
        log.info("月榜奖励发放任务完成: {}", message);
        return message;
    }

    /**
     * 执行定时任务 - 季度榜奖励发放
     */
    @Override
    public String executeQuarterlyRankingTask() {
        log.info("开始执行季度榜奖励发放任务");
        Map<String, Object> result = generateRankingAndRewards("quarterly");
        String message = result.get("message").toString();
        log.info("季度榜奖励发放任务完成: {}", message);
        return message;
    }

    /**
     * 检查并处理所有待处理的奖励任务
     */
    @Override
    public Map<String, Object> processAllPendingRewards() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("processedTasks", new HashMap<String, String>());

        try {
            Map<String, String> processedTasks = new HashMap<>();

            // 处理首单奖励任务
            String firstOrderResult = batchProcessFirstOrderBonus();
            processedTasks.put("firstOrderBonus", firstOrderResult);

            // 这里可以添加其他需要定期检查的奖励任务
            // 例如：检查待处理的升级奖励等

            processedTasks.put("status", "所有待处理奖励任务检查完成");
            result.put("processedTasks", processedTasks);

        } catch (Exception e) {
            log.error("处理待处理奖励任务失败", e);
            result.put("success", false);
            result.put("error", e.getMessage());
        }

        return result;
    }

    /**
     * 获取符合条件的首单订单
     * 条件：已完成、已支付、过了售后期、是用户的首单、用户有推广人且推广人是VIP
     */
    private List<StoreOrder> getEligibleFirstOrders(String cutoffDate) {
        try {
            // 基础查询条件：已支付、已完成、过了售后期
            LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreOrder::getPaid, true)
                       .eq(StoreOrder::getIsDel, false)
                       .eq(StoreOrder::getStatus, 2) // 已完成订单
                       .le(StoreOrder::getUpdateTime, cutoffDate + " 23:59:59");

            List<StoreOrder> allOrders = storeOrderService.list(queryWrapper);
            List<StoreOrder> eligibleOrders = new ArrayList<>();

            for (StoreOrder order : allOrders) {
                try {
                    // 检查是否为用户的首单
                    if (!isUserFirstOrder(order.getUid(), order.getId())) {
                        continue;
                    }

                    // 检查用户是否有推广人
                    User user = userService.getById(order.getUid());
                    if (user == null || user.getSpreadUid() == null || user.getSpreadUid() == 0) {
                        continue;
                    }

                    // 检查推广人是否为VIP
                    User promoter = userService.getById(user.getSpreadUid());
                    if (promoter == null || !isVipUser(promoter)) {
                        continue;
                    }

                    // 检查是否已经发放过首单奖励
                    if (hasFirstOrderBonusBeenGranted(promoter.getId(), user.getId())) {
                        continue;
                    }

                    // 检查订单是否有售后问题
                    if (hasAfterSaleIssue(order)) {
                        continue;
                    }

                    eligibleOrders.add(order);
                } catch (Exception e) {
                    log.error("检查订单首单条件失败: orderId={}, error={}", order.getOrderId(), e.getMessage());
                }
            }

            return eligibleOrders;
        } catch (Exception e) {
            log.error("获取符合条件的首单订单失败", e);
            return new ArrayList<>();
        }
    }

    /**
     * 检查是否为用户的首单
     */
    private Boolean isUserFirstOrder(Integer userId, Integer currentOrderId) {
        try {
            LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreOrder::getUid, userId)
                       .eq(StoreOrder::getPaid, true)
                       .eq(StoreOrder::getIsDel, false)
                       .orderByAsc(StoreOrder::getCreateTime);

            List<StoreOrder> userOrders = storeOrderService.list(queryWrapper);
            return !userOrders.isEmpty() && userOrders.get(0).getId().equals(currentOrderId);
        } catch (Exception e) {
            log.error("检查用户首单失败: userId={}, orderId={}", userId, currentOrderId);
            return false;
        }
    }

    /**
     * 检查是否已经发放过首单奖励
     */
    private Boolean hasFirstOrderBonusBeenGranted(Integer promoterId, Integer newUserId) {
        try {
            LambdaQueryWrapper<UserBonusRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserBonusRecord::getUid, promoterId)
                       .eq(UserBonusRecord::getBonusType, "first_order")
                       .eq(UserBonusRecord::getSourceType, "推广新用户首单购买")
                       .eq(UserBonusRecord::getLinkId, newUserId.toString());

            return userBonusRecordService.count(queryWrapper) > 0;
        } catch (Exception e) {
            log.error("检查首单奖励发放状态失败: promoterId={}, newUserId={}", promoterId, newUserId);
            return true; // 出错时返回true，避免重复发放
        }
    }

    /**
     * 检查订单是否有售后问题
     */
    private Boolean hasAfterSaleIssue(StoreOrder order) {
        try {
            // 这里可以调用现有的售后检查逻辑
            // 暂时简化处理，认为已完成的订单没有售后问题
            return false;
        } catch (Exception e) {
            log.error("检查订单售后状态失败: orderId={}", order.getOrderId());
            return true; // 出错时返回true，避免发放奖励
        }
    }

    /**
     * 获取售后期限天数
     */
    private Integer getAfterSaleDays() {
        try {
            Map<String, Object> config = getSystemConfig("first_order_bonus");
            if (config != null && config.containsKey("afterSaleDays")) {
                return Integer.valueOf(config.get("afterSaleDays").toString());
            }
            return 7; // 默认7天售后期
        } catch (Exception e) {
            log.error("获取售后期限配置失败", e);
            return 7; // 默认7天
        }
    }

    /**
     * 计算日期范围
     */
    private Map<String, String> calculateDateRange(String rankType) {
        Map<String, String> dateRange = new HashMap<>();
        Date now = new Date();

        switch (rankType) {
            case "weekly":
                // 上周一到上周日
                Date lastWeekStart = DateUtil.beginOfWeek(DateUtil.offsetWeek(now, -1));
                Date lastWeekEnd = DateUtil.endOfWeek(DateUtil.offsetWeek(now, -1));
                dateRange.put("startDate", DateUtil.formatDate(lastWeekStart));
                dateRange.put("endDate", DateUtil.formatDate(lastWeekEnd));
                break;
            case "monthly":
                // 上个月1号到上个月最后一天
                Date lastMonthStart = DateUtil.beginOfMonth(DateUtil.offsetMonth(now, -1));
                Date lastMonthEnd = DateUtil.endOfMonth(DateUtil.offsetMonth(now, -1));
                dateRange.put("startDate", DateUtil.formatDate(lastMonthStart));
                dateRange.put("endDate", DateUtil.formatDate(lastMonthEnd));
                break;
            case "quarterly":
                // 上个季度第一天到上个季度最后一天
                Date lastQuarterStart = DateUtil.beginOfQuarter(DateUtil.offsetMonth(now, -3));
                Date lastQuarterEnd = DateUtil.endOfQuarter(DateUtil.offsetMonth(now, -3));
                dateRange.put("startDate", DateUtil.formatDate(lastQuarterStart));
                dateRange.put("endDate", DateUtil.formatDate(lastQuarterEnd));
                break;
            default:
                dateRange.put("startDate", DateUtil.formatDate(now));
                dateRange.put("endDate", DateUtil.formatDate(now));
        }

        return dateRange;
    }



    /**
     * 获取排行榜类型名称
     */
    private String getRankTypeName(String rankType) {
        switch (rankType) {
            case "weekly": return "周榜";
            case "monthly": return "月榜";
            case "quarterly": return "季度榜";
            default: return "排行榜";
        }
    }
}
