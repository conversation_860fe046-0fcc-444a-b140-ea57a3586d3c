package com.ylpz.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ylpz.core.common.request.ExternalRefundRequest;
import com.ylpz.core.common.response.ExternalRefundResponse;
import com.ylpz.core.common.utils.RSAProvider;
import com.ylpz.core.config.RSAConfig;
import com.ylpz.core.service.ExternalRefundService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.HashMap;
import java.util.Map;

/**
 * 外部退款服务实现类
 */
@Slf4j
@Service
public class ExternalRefundServiceImpl implements ExternalRefundService {

    @Autowired
    private RSAProvider rsaProvider;
    
    @Autowired
    private RSAConfig rsaConfig;

    /**
     * 调用外部退款接口
     * @param request 退款请求参数
     * @return 退款响应结果
     */
    @Override
    public ExternalRefundResponse refundOrder(ExternalRefundRequest request) {
        try {
            // 设置随机字符串
            request.setNonce(RandomUtil.randomString(16));
            // 设置时间戳
            request.setTimestamp(Instant.now().getEpochSecond());
            
            // 生成签名
            String signContent = request.getOrderNo() + request.getNonce() + request.getTimestamp();
            request.setSignature(rsaProvider.encrypt(signContent));
            
            // 将请求对象转换为Map
            Map<String, Object> formParams = BeanUtil.beanToMap(request);
            
            // 发送请求
            String url = rsaConfig.getApiUrl() + "/order/refundOrder";
            log.info("发送退款请求: {}, 参数: {}", url, formParams);
            
            // 使用Hutool的HttpRequest以表单形式发送POST请求
            HttpResponse response = HttpRequest.post(url)
                    .contentType("application/x-www-form-urlencoded")
                    .form(formParams)  // 使用form方法设置表单参数
                    .timeout(10000) // 设置超时时间为10秒
                    .execute();
                    
            String responseBody = response.body();
            log.info("退款请求响应: {}", responseBody);
            
            // 解析结果
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            
            if (jsonResponse.getInteger("code") == 0) {
                return ExternalRefundResponse.success(
                        jsonResponse.getString("tradeNo"),
                        jsonResponse.getLong("refundTime")
                );
            } else {
                return ExternalRefundResponse.fail(jsonResponse.getString("message"));
            }
            
        } catch (Exception e) {
            log.error("调用外部退款接口异常", e);
            return ExternalRefundResponse.fail("退款接口调用失败: " + e.getMessage());
        }
    }
} 