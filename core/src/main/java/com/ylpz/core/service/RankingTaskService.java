package com.ylpz.core.service;

import com.ylpz.model.user.User;

import java.util.Map;

/**
 * 奖励金定时任务服务接口
 *
 * <AUTHOR>
 * @since 2025-01-10
 */
public interface RankingTaskService {

    /**
     * 处理推广普通会员升级VIP奖励
     * 当推广的普通会员升级为VIP时，给推广人(SVIP用户)发放奖励金
     *
     * @param upgradedUser 升级的用户
     * @return 是否处理成功
     */
    Boolean processUpgradeToVipBonus(User upgradedUser);

    /**
     * 处理推广VIP会员升级SVIP奖励
     * 当推广的VIP会员通过审核升级为SVIP时，给推广人(SVIP用户)发放奖励金
     *
     * @param upgradedUser 升级的用户
     * @return 是否处理成功
     */
    Boolean processUpgradeToSvipBonus(User upgradedUser);

    /**
     * 处理推广新用户首单奖励
     * 当推广的新用户完成首次订单购买且过了售后期时，给推广人(VIP用户)发放奖励金
     *
     * @param newUser 新用户
     * @param firstOrderAmount 首单金额
     * @return 是否处理成功
     */
    Boolean processFirstOrderBonus(User newUser, Double firstOrderAmount);

    /**
     * 批量处理首单奖励
     * 定时任务调用，检查所有符合条件的首单并发放奖励
     *
     * @return 处理结果
     */
    String batchProcessFirstOrderBonus();

    /**
     * 生成销售榜单并发放奖励
     * 根据配置生成周榜、月榜、季度榜，并自动发放奖励
     *
     * @param rankType 榜单类型：week-周榜，month-月榜，quarter-季度榜
     * @return 处理结果
     */
    Map<String, Object> generateRankingAndRewards(String rankType);

    /**
     * 统计用户销售数据
     * 包含自购订单和下线购买订单金额，仅统计成为SVIP之后的数据
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售金额
     */
    Double calculateUserSalesAmount(Integer userId, String startDate, String endDate);

    /**
     * 检查用户是否为SVIP会员
     *
     * @param user 用户对象
     * @return 是否为SVIP会员
     */
    Boolean isSvipUser(User user);

    /**
     * 检查用户是否为VIP会员
     *
     * @param user 用户对象
     * @return 是否为VIP会员
     */
    Boolean isVipUser(User user);

    /**
     * 发放奖励金
     *
     * @param userId 用户ID
     * @param amount 奖励金额
     * @param bonusType 奖励类型
     * @param sourceType 来源类型
     * @param linkId 关联ID
     * @param mark 备注
     * @return 是否发放成功
     */
    Boolean grantBonus(Integer userId, Double amount, String bonusType, String sourceType, String linkId, String mark);

    /**
     * 获取系统参数配置
     *
     * @param configCode 配置代码
     * @return 配置值的JSON对象
     */
    Map<String, Object> getSystemConfig(String configCode);

    /**
     * 执行定时任务 - 周榜奖励发放
     * 每周一执行，生成上周销售榜单并发放奖励
     *
     * @return 执行结果
     */
    String executeWeeklyRankingTask();

    /**
     * 执行定时任务 - 月榜奖励发放
     * 每月1号执行，生成上月销售榜单并发放奖励
     *
     * @return 执行结果
     */
    String executeMonthlyRankingTask();

    /**
     * 执行定时任务 - 季度榜奖励发放
     * 每季度第一天执行，生成上季度销售榜单并发放奖励
     *
     * @return 执行结果
     */
    String executeQuarterlyRankingTask();

    /**
     * 检查并处理所有待处理的奖励任务
     * 主定时任务入口，检查各种奖励条件并处理
     *
     * @return 处理结果统计
     */
    Map<String, Object> processAllPendingRewards();
}
