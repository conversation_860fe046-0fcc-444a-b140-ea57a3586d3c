package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.codec.digest.DigestUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.request.ShippingTemplatesFreeRequest;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.dao.ShippingTemplatesFreeDao;
import com.ylpz.core.service.ShippingTemplatesFreeService;
import com.ylpz.model.express.ShippingTemplatesFree;

/**
 * ShippingTemplatesFreeServiceImpl 接口实现
 */
@Service
public class ShippingTemplatesFreeServiceImpl extends ServiceImpl<ShippingTemplatesFreeDao, ShippingTemplatesFree>
    implements ShippingTemplatesFreeService {

    @Resource
    private ShippingTemplatesFreeDao dao;

    /**
     * 保存包邮模板
     * 
     * @param request 包邮模板请求对象
     * @return 是否保存成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(List<ShippingTemplatesFreeRequest> request) {
        deleteAll();
        List<Integer> cityIdList;
        List<String> cityNameList;
        ArrayList<ShippingTemplatesFree> shippingTemplatesFrees = new ArrayList<>();
        for (ShippingTemplatesFreeRequest shippingTemplatesRegionRequest : request) {
            String uniqueKey = DigestUtils.md5Hex(shippingTemplatesRegionRequest.toString());
            cityIdList = CrmebUtil.stringToArray(shippingTemplatesRegionRequest.getCityId());
            cityNameList = CrmebUtil.stringToArrayStr(shippingTemplatesRegionRequest.getTitle());
            for (Integer cityId : cityIdList) {
                ShippingTemplatesFree shippingTemplatesFree = new ShippingTemplatesFree();
                shippingTemplatesFree.setCityId(cityId);
                shippingTemplatesFree.setTitle(cityNameList.get(cityIdList.indexOf(cityId)));
                shippingTemplatesFree.setUniqid(uniqueKey);
                shippingTemplatesFree.setStatus(0);
                shippingTemplatesFrees.add(shippingTemplatesFree);
            }
        }
        // 批量保存模板数据
        saveBatch(shippingTemplatesFrees);
        return true;
    }

    public Boolean deleteAll() {
        LambdaQueryWrapper<ShippingTemplatesFree> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        return dao.delete(lambdaQueryWrapper) > 0;
    }

    /**
     * 根据包邮金额分组获取包邮模板
     * 
     * @return 包邮模板列表
     */
    @Override
    public List<ShippingTemplatesFree> getShippingTemplatesFreeGroupByPrice() {
        return dao.selectList(new LambdaQueryWrapper<ShippingTemplatesFree>().eq(ShippingTemplatesFree::getStatus, 0)
            .orderByAsc(ShippingTemplatesFree::getPrice));
    }

    /**
     * 分组返回包邮模板数据
     * 
     * @return 按照包邮金额分组的模板数据
     */
    @Override
    public Map<BigDecimal, List<ShippingTemplatesFree>> getGroupShippingTemplatesFreeData() {
        // 获取所有启用的包邮模板
        LambdaQueryWrapper<ShippingTemplatesFree> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingTemplatesFree::getStatus, 1);
        queryWrapper.orderByAsc(ShippingTemplatesFree::getPrice);
        List<ShippingTemplatesFree> list = list(queryWrapper);

        // 按照包邮金额分组
        return list.stream().collect(Collectors.groupingBy(ShippingTemplatesFree::getPrice));
    }
}
