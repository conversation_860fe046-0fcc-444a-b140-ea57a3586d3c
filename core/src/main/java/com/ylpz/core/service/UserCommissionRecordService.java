package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.user.UserCommissionRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 用户佣金返现记录服务接口
 */
public interface UserCommissionRecordService extends IService<UserCommissionRecord> {

    /**
     * 获取佣金返现记录列表
     *
     * @param linkId 订单ID
     * @param status 状态
     * @param userPhone 用户手机号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    PageInfo<UserCommissionRecord> getCommissionList(String linkId, Integer status, 
                                                   String userPhone, Date startTime, 
                                                   Date endTime, PageParamRequest pageParamRequest);

    
    /**
     * 统计已结算佣金返现总额和销售额
     * 只统计已结算状态的数据，退款订单不计入销售额内
     *
     * @return 统计数据，包含返现总额和销售额合计
     */
    Map<String, BigDecimal> getSettledCommissionAndSalesTotal();
    
    /**
     * 获取用户佣金总额
     * 
     * @param uid 用户ID
     * @return 用户佣金总额
     */
    BigDecimal getUserCommissionAmount(Integer uid);
    
    /**
     * 获取指定日期的佣金返现金额
     * 
     * @param date 日期字符串，格式为yyyy-MM-dd
     * @return 佣金返现金额
     */
    BigDecimal getBrokerageAmountByDate(String date);

    /**
     * 佣金解冻
     * 将达到解冻时间的佣金记录状态更新为已完成
     */
    void brokerageThaw();

    
    /**
     * 根据关联ID和关联类型查询佣金记录
     * 
     * @param linkId 关联ID
     * @param linkType 关联类型
     * @return 佣金记录
     */
    UserCommissionRecord getByLinkIdAndLinkType(String linkId, String linkType);
}