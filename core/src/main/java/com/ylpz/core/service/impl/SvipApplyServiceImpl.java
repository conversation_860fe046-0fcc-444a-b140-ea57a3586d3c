package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.service.SystemAdminService;
import com.ylpz.model.system.SystemAdmin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SvipAddRequest;
import com.ylpz.core.common.request.SvipApplyAuditRequest;
import com.ylpz.core.common.request.SvipApplyRequest;
import com.ylpz.core.common.request.SvipApplySearchRequest;
import com.ylpz.core.common.response.SvipApplyResponse;
import com.ylpz.core.dao.UserSvipApplyDao;
import com.ylpz.core.service.SvipApplyService;
import com.ylpz.core.service.SvipManagementService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.RankingTaskService;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserSvipApply;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * SVIP申请服务实现类
 */
@Slf4j
@Service
public class SvipApplyServiceImpl extends ServiceImpl<UserSvipApplyDao, UserSvipApply> implements SvipApplyService {
    @Resource
    private UserSvipApplyDao userSvipApplyDao;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemAdminService systemAdminService;

    @Autowired
    private RankingTaskService rankingTaskService;

    /**
     * 提交SVIP申请
     *
     * @param request 申请请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean submitApply(SvipApplyRequest request) {
        // 检查用户是否存在
        User user = userService.getById(request.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户是否已经是SVIP
        if (MemberLevelConstants.isSvip(user.getLevel())) {
            throw new RuntimeException("用户已经是SVIP会员");
        }

        // 检查是否有待审核的申请
        LambdaQueryWrapper<UserSvipApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSvipApply::getUid, request.getUid());
        queryWrapper.eq(UserSvipApply::getStatus, 0); // 待审核
        queryWrapper.eq(UserSvipApply::getIsDel, false);
        long count = count(queryWrapper);
        if (count > 0) {
            throw new RuntimeException("用户已有待审核的SVIP申请");
        }

        // 创建申请记录
        UserSvipApply apply = new UserSvipApply();
        apply.setUid(request.getUid());
        apply.setApplyInfo(JSONUtil.toJsonStr(request.getApplyInfo()));
        apply.setGrowth(request.getGrowth());
        apply.setApplyTime(new Date());
        apply.setStatus(0); // 待审核
        apply.setCreateTime(new Date());
        apply.setUpdateTime(new Date());
        apply.setIsDel(false);

        return save(apply);
    }

    /**
     * 获取SVIP申请列表
     *
     * @param request 查询条件
     * @param pageParamRequest 分页参数
     * @return 申请列表
     */
    @Override
    public PageInfo<SvipApplyResponse> getApplyList(SvipApplySearchRequest request, PageParamRequest pageParamRequest) {
        LambdaQueryWrapper<UserSvipApply> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSvipApply::getIsDel, false);

        // 状态筛选
        if (request.getStatus() != null) {
            queryWrapper.eq(UserSvipApply::getStatus, request.getStatus());
        }

        // 时间范围筛选
        if (StrUtil.isNotBlank(request.getStartTime())) {
            queryWrapper.ge(UserSvipApply::getApplyTime, request.getStartTime());
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            queryWrapper.le(UserSvipApply::getApplyTime, request.getEndTime());
        }

        // 关键字筛选（需要关联用户表）
        if (StrUtil.isNotBlank(request.getKeywords())) {
            List<Integer> uidList = userService.findIdListLikeName(request.getKeywords());
            if (uidList != null && !uidList.isEmpty()) {
                queryWrapper.in(UserSvipApply::getUid, uidList);
            } else {
                // 如果没有匹配的用户，返回空列表
                return new PageInfo<>(new ArrayList<>());
            }
        }

        // 按申请时间倒序排序
        queryWrapper.orderByDesc(UserSvipApply::getApplyTime);

        // 分页查询
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<UserSvipApply> applyList = userSvipApplyDao.selectList(queryWrapper);
        PageInfo<UserSvipApply> pageInfo = new PageInfo<>(applyList);

        // 转换为响应对象
        List<SvipApplyResponse> responseList = new ArrayList<>();
        for (UserSvipApply apply : applyList) {
            SvipApplyResponse response = convertToResponse(apply);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<SvipApplyResponse> result = new PageInfo<>();
        result.setList(responseList);
        result.setTotal(pageInfo.getTotal());
        result.setPageNum(pageInfo.getPageNum());
        result.setPageSize(pageInfo.getPageSize());
        result.setPages(pageInfo.getPages());

        return result;
    }

    /**
     * 获取SVIP申请详情
     *
     * @param id 申请ID
     * @return 申请详情
     */
    @Override
    public SvipApplyResponse getApplyDetail(Integer id) {
        UserSvipApply apply = getById(id);
        if (ObjectUtil.isNull(apply) || apply.getIsDel()) {
            return null;
        }

        return convertToResponse(apply);
    }

    /**
     * 审核SVIP申请
     *
     * @param request 审核请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean auditApply(SvipApplyAuditRequest request) {
        // 获取申请记录
        UserSvipApply apply = getById(request.getApplyId());
        if (ObjectUtil.isNull(apply) || apply.getIsDel()) {
            throw new RuntimeException("申请记录不存在");
        }

        // 检查是否已审核
        if (apply.getStatus() != 0) {
            throw new RuntimeException("申请已审核，不能重复审核");
        }

        // 获取当前登录用户信息
        Integer currentUserId = 1;
        String currentUserName = "系统管理员";
        SystemAdmin info = systemAdminService.getInfo();
        if (info != null) {
            currentUserId = info.getId();
            currentUserName = info.getRealName();
        }

        // 更新申请记录
        apply.setStatus(request.getIsApproved() ? 1 : 2); // 1-已通过，2-已拒绝
        apply.setAuditorId(currentUserId);
        apply.setAuditorName(currentUserName);
        apply.setAuditTime(new Date());
        apply.setUpdateTime(new Date());

        // 如果拒绝，记录拒绝原因
        if (!request.getIsApproved()) {
            apply.setRejectReason(request.getRejectReason());
        } else {
            // 如果通过，自动升级用户为SVIP
            User user = userService.getById(apply.getUid());
            if (user != null) {
                Integer oldLevel = user.getLevel();

                // 解析申请信息中的用户资料
                try {
                    if (StrUtil.isNotBlank(apply.getApplyInfo())) {
                        JSONObject applyInfoJson = JSONUtil.parseObj(apply.getApplyInfo());

                        // 更新用户等级为SVIP，并设置SVIP时间，同时更新用户资料
                        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
                        luw.set(User::getLevel, MemberLevelConstants.Level.SVIP);
                        luw.set(User::getSvipTime, new Date()); // 设置成为SVIP的时间
                        //如果升级svip后，将脱落绑定关系
                        luw.set(User::getSpreadUid, null);

                        // 从申请信息中提取并更新用户资料
                        if (applyInfoJson.containsKey("realName") && StrUtil.isNotBlank(applyInfoJson.getStr("realName"))) {
                            luw.set(User::getRealName, applyInfoJson.getStr("realName"));
                            log.info("更新用户真实姓名: userId={}, realName={}", apply.getUid(), applyInfoJson.getStr("realName"));
                        }

                        if (applyInfoJson.containsKey("idcard") && StrUtil.isNotBlank(applyInfoJson.getStr("idcard"))) {
                            luw.set(User::getCardId, applyInfoJson.getStr("idcard"));
                            log.info("更新用户身份证号: userId={}, cardId={}", apply.getUid(), applyInfoJson.getStr("idcard"));
                        }

                        if (applyInfoJson.containsKey("phone") && StrUtil.isNotBlank(applyInfoJson.getStr("phone"))) {
                            luw.set(User::getPhone, applyInfoJson.getStr("phone"));
                            log.info("更新用户手机号: userId={}, phone={}", apply.getUid(), applyInfoJson.getStr("phone"));
                        }

                        luw.eq(User::getId, apply.getUid());
                        userService.update(luw);

                        log.info("SVIP审批通过，用户资料更新成功: userId={}", apply.getUid());
                    } else {
                        // 如果没有申请信息，只更新等级
                        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
                        luw.set(User::getLevel, MemberLevelConstants.Level.SVIP);
                        luw.set(User::getSvipTime, new Date());
                        luw.set(User::getSpreadUid, null);
                        luw.eq(User::getId, apply.getUid());
                        userService.update(luw);

                        log.warn("SVIP审批通过，但申请信息为空，仅更新等级: userId={}", apply.getUid());
                    }
                } catch (Exception e) {
                    log.error("解析申请信息失败，仅更新用户等级: userId={}, applyInfo={}, error={}",
                            apply.getUid(), apply.getApplyInfo(), e.getMessage(), e);

                    // 解析失败时，仍然更新用户等级
                    LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
                    luw.set(User::getLevel, MemberLevelConstants.Level.SVIP);
                    luw.set(User::getSvipTime, new Date());
                    luw.set(User::getSpreadUid, null);
                    luw.eq(User::getId, apply.getUid());
                    userService.update(luw);
                }

                // 触发SVIP升级奖励逻辑
                try {
                    // 重新获取用户信息（包含更新后的等级）
                    User updatedUser = userService.getById(apply.getUid());
                    if (updatedUser != null) {
                        // 调用奖励服务处理推广奖励
                        rankingTaskService.processUpgradeToSvipBonus(updatedUser);
                    }
                } catch (Exception e) {
                    log.error("处理SVIP升级奖励失败: userId={}, error={}", apply.getUid(), e.getMessage(), e);
                }
            }
        }

        return updateById(apply);
    }

    /**
     * 将实体对象转换为响应对象
     *
     * @param apply 实体对象
     * @return 响应对象
     */
    private SvipApplyResponse convertToResponse(UserSvipApply apply) {
        SvipApplyResponse response = new SvipApplyResponse();
        BeanUtil.copyProperties(apply, response);

        // 获取用户信息
        User user = userService.getById(apply.getUid());
        if (user != null) {
            response.setNickname(user.getNickname());
            response.setPhone(user.getPhone());
        }

        return response;
    }
}