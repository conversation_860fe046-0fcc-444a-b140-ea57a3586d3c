package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserLevel;

import java.util.List;

/**
 * UserLevelService 接口实现
 * +----------------------------------------------------------------------
 * | CRMEB [ CRMEB赋能开发者，助力企业发展 ]
 * +----------------------------------------------------------------------
 * | Copyright (c) 2016~2022 https://www.crmeb.com All rights reserved.
 * +----------------------------------------------------------------------
 * | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权
 * +----------------------------------------------------------------------
 * | Author: CRMEB Team <<EMAIL>>
 * +----------------------------------------------------------------------
 */
public interface UserLevelService extends IService<UserLevel> {

    /**
     * 用户等级列表
     * @param pageParamRequest 分页参数
     * @return List
     */
    List<UserLevel> getList(PageParamRequest pageParamRequest);

    /**
     * 经验升级
     * @param user 用户
     * @return Boolean
     */
    Boolean upLevel(User user);

    /**
     * 经验降级
     * @param user 用户
     * @return Boolean
     */
    Boolean downLevel(User user);

    /**
     * 删除（通过系统等级id）
     * @param levelId 系统等级id
     * @return Boolean
     */
    Boolean deleteByLevelId(Integer levelId);

    /**
     * 检查用户等级是否需要注册信息（获取手机号）
     * @param user 用户对象
     * @return Boolean 是否需要获取手机号
     */
    Boolean checkUserLevelNeedInfo(User user);

    /**
     * 处理用户完善资料后的优惠券发放
     * @param user 用户对象
     * @return Boolean 是否成功发放优惠券
     */
    Boolean processUserLevelCoupon(User user);

    /**
     * 处理用户生日赠送的优惠券
     * @param user 用户对象
     * @return Boolean 是否成功发放生日券
     */
    Boolean processBirthCoupon(User user);
}
