package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.DiscountConstants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreDiscountUserRequest;
import com.ylpz.core.common.request.StoreDiscountUserSearchRequest;
import com.ylpz.core.common.response.StoreDiscountUserResponse;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreDiscountUserDao;
import com.ylpz.core.service.StoreDiscountService;
import com.ylpz.core.service.StoreDiscountUserService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.discount.StoreDiscount;
import com.ylpz.model.discount.StoreDiscountUser;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * StoreDiscountUserServiceImpl 接口实现
 */
@Service
public class StoreDiscountUserServiceImpl extends ServiceImpl<StoreDiscountUserDao, StoreDiscountUser>
    implements StoreDiscountUserService {

    @Resource
    private StoreDiscountUserDao dao;

    @Autowired
    private StoreDiscountService storeDiscountService;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 列表
     * 
     * @param request 请求参数
     * @param pageParamRequest 分页类参数
     * @return List<StoreDiscountUserResponse>
     */
    @Override
    public List<StoreDiscountUserResponse> getList(StoreDiscountUserSearchRequest request,
        PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 带 StoreDiscountUser 类的多条件查询
        LambdaQueryWrapper<StoreDiscountUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (ObjectUtil.isNotNull(request.getUid())) {
            lambdaQueryWrapper.eq(StoreDiscountUser::getUid, request.getUid());
        }
        if (ObjectUtil.isNotNull(request.getDiscountId())) {
            lambdaQueryWrapper.eq(StoreDiscountUser::getDiscountId, request.getDiscountId());
        }
        if (StrUtil.isNotBlank(request.getName())) {
            lambdaQueryWrapper.like(StoreDiscountUser::getName, request.getName());
        }
        if (ObjectUtil.isNotNull(request.getStatus())) {
            lambdaQueryWrapper.eq(StoreDiscountUser::getStatus, request.getStatus());
        }
        if (ObjectUtil.isNotNull(request.getMinPrice())) {
            lambdaQueryWrapper.eq(StoreDiscountUser::getMinPrice, request.getMinPrice());
        }
        lambdaQueryWrapper.orderByDesc(StoreDiscountUser::getId);
        List<StoreDiscountUser> discountUserList = dao.selectList(lambdaQueryWrapper);

        ArrayList<StoreDiscountUserResponse> responseList = new ArrayList<>();
        for (StoreDiscountUser discountUser : discountUserList) {
            StoreDiscountUserResponse discountUserResponse = new StoreDiscountUserResponse();
            BeanUtils.copyProperties(discountUser, discountUserResponse);
            responseList.add(discountUserResponse);
        }
        return responseList;
    }

    /**
     * 领取折扣
     * 
     * @param request StoreDiscountUserRequest 新增参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean receive(StoreDiscountUserRequest request) {
        // 判断折扣是否存在
        StoreDiscount discount = storeDiscountService.getInfoException(request.getDiscountId());
        if (!discount.getStatus()) {
            throw new CrmebException("折扣已失效！");
        }

        // 判断是否有剩余数量
        if (discount.getIsLimited() && discount.getLastTotal() < 1) {
            throw new CrmebException("折扣已经被领完了！");
        }

        Integer uid = userService.getUserId();
        if (uid.equals(0)) {
            throw new CrmebException("请先登录");
        }

        // 看是否领取过
        LambdaQueryWrapper<StoreDiscountUser> tempLambda = new LambdaQueryWrapper<>();
        tempLambda.eq(StoreDiscountUser::getUid, uid).eq(StoreDiscountUser::getDiscountId, request.getDiscountId());
        List<StoreDiscountUser> tempList = dao.selectList(tempLambda);
        if (CollUtil.isNotEmpty(tempList)) {
            throw new CrmebException("已经领取过该折扣！");
        }

        StoreDiscountUser storeDiscountUser = new StoreDiscountUser();
        BeanUtils.copyProperties(discount, storeDiscountUser);
        storeDiscountUser.setDiscountId(discount.getId());
        storeDiscountUser.setUid(uid);

        // 判断是否固定领取时间
        if (!discount.getIsFixedTime()) {
            storeDiscountUser.setStartTime(DateUtil.nowDateTimeReturnDate(Constants.DATE_FORMAT));
            String endTime =
                DateUtil.addDay(DateUtil.nowDate(Constants.DATE_FORMAT), discount.getDay(), Constants.DATE_FORMAT);
            storeDiscountUser.setEndTime(DateUtil.strToDate(endTime, Constants.DATE_FORMAT));
        }
        storeDiscountUser.setType(DiscountConstants.STORE_DISCOUNT_USER_TYPE_GET);
        storeDiscountUser.setStatus(DiscountConstants.STORE_DISCOUNT_USER_STATUS_USABLE);

        Boolean execute = transactionTemplate.execute(e -> {
            save(storeDiscountUser);
            if (discount.getIsLimited()) {
                storeDiscountService.deduction(discount.getId(), 1, true);
            }
            return Boolean.TRUE;
        });
        if (!execute) {
            throw new CrmebException("领取失败！");
        }
        return execute;
    }

    /**
     * 根据uid获取对应的map
     * 
     * @param uid 用户id
     * @return HashMap<Integer, StoreDiscountUser>
     */
    @Override
    public HashMap<Integer, StoreDiscountUser> getMapByUserId(Integer uid) {
        HashMap<Integer, StoreDiscountUser> map = new HashMap<>();
        LambdaQueryWrapper<StoreDiscountUser> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreDiscountUser::getUid, uid);
        lqw.eq(StoreDiscountUser::getStatus, DiscountConstants.STORE_DISCOUNT_USER_STATUS_USABLE);
        List<StoreDiscountUser> discountUserList = dao.selectList(lqw);
        if (CollUtil.isEmpty(discountUserList)) {
            return map;
        }
        for (StoreDiscountUser discountUser : discountUserList) {
            map.put(discountUser.getDiscountId(), discountUser);
        }
        return map;
    }
}