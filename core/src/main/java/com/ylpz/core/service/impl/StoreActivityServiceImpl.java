package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreActivityRequest;
import com.ylpz.core.common.request.StoreActivitySearchRequest;
import com.ylpz.core.common.response.ProductMarketingActivityResponse;
import com.ylpz.core.common.response.StoreActivityResponse;
import com.ylpz.core.common.response.StoreActivityStatisticsResponse;
import com.ylpz.core.common.utils.ActivityDescriptionProcessor;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreActivityDao;
import com.ylpz.core.service.*;
import com.ylpz.model.activity.StoreActivity;
import com.ylpz.model.article.Article;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.discount.StoreDiscount;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.model.seckill.StoreSeckill;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.stream.Collectors;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 活动管理Service实现类
 */
@Service
public class StoreActivityServiceImpl extends ServiceImpl<StoreActivityDao, StoreActivity>
    implements StoreActivityService {

    @Resource
    private StoreActivityDao dao;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private ArticleService articleService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreCouponService storeCouponService;

    @Autowired
    private StoreSeckillService storeSeckillService;

    @Autowired
    private StoreDiscountService storeDiscountService;

    @Autowired
    private StoreActivityDiscountService storeActivityDiscountService;

    @Autowired
    private StoreActivityProductService storeActivityProductService;

    @Autowired
    private StoreActivityCouponService storeActivityCouponService;

    @Autowired
    private StoreActivitySeckillService storeActivitySeckillService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private ActivityDescriptionProcessor activityDescriptionProcessor;

    /**
     * 活动列表
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return List<StoreActivityResponse>
     */
    @Override
    public List<StoreActivityResponse> getList(StoreActivitySearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 带 StoreActivity 类的多条件查询
        LambdaQueryWrapper<StoreActivity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivity::getIsDel, false);

        if (StrUtil.isNotBlank(request.getName())) {
            lambdaQueryWrapper.like(StoreActivity::getName, request.getName());
        }

        // 根据活动状态筛选
        if (StrUtil.isNotBlank(request.getStatus())) {
            Date now = new Date();
            switch (request.getStatus()) {
                case "ongoing":
                    // 进行中：当前时间在开始时间和结束时间之间，且状态为开启
                    lambdaQueryWrapper.eq(StoreActivity::getStatus, true)
                                     .le(StoreActivity::getStartTime, now)
                                     .gt(StoreActivity::getStopTime, now);
                    break;
                case "not_started":
                    // 未开始：开始时间大于当前时间，且状态为开启
                    lambdaQueryWrapper.eq(StoreActivity::getStatus, true)
                                     .gt(StoreActivity::getStartTime, now);
                    break;
                case "ended":
                    // 已结束：结束时间小于等于当前时间
                    lambdaQueryWrapper.le(StoreActivity::getStopTime, now);
                    break;
            }
        }

        if (ObjectUtil.isNotNull(request.getArticleId())) {
            lambdaQueryWrapper.eq(StoreActivity::getArticleId, request.getArticleId());
        }

        lambdaQueryWrapper.orderByDesc(StoreActivity::getSort).orderByDesc(StoreActivity::getId);
        List<StoreActivity> activityList = dao.selectList(lambdaQueryWrapper);

        ArrayList<StoreActivityResponse> responseList = new ArrayList<>();
        for (StoreActivity activity : activityList) {
            StoreActivityResponse response = new StoreActivityResponse();
            BeanUtils.copyProperties(activity, response);
            // 处理轮播图
            if (activity.getImages() != null) {
                response.setImages(CrmebUtil.stringToArrayStr(activity.getImages()));
            }
            // 获取文章信息
            if (activity.getArticleId() > 0) {
                Article article = articleService.getById(activity.getArticleId());
                response.setArticle(article);
            }
            // 获取商品信息
            List<Integer> productIds = storeActivityProductService.getProductIdsByActivityId(activity.getId());
            if (ObjectUtil.isNotEmpty(productIds)) {
                List<StoreProduct> productList = storeProductService.listByIds(productIds);
                response.setProductList(productList);
            }
            // 获取优惠券信息
            List<Integer> couponIds = storeActivityCouponService.getCouponIdsByActivityId(activity.getId());
            if (ObjectUtil.isNotEmpty(couponIds)) {
                List<StoreCoupon> couponList = storeCouponService.listByIds(couponIds);
                response.setCouponList(couponList);
            }
            // 获取秒杀信息
            List<Integer> seckillIds = storeActivitySeckillService.getSeckillIdsByActivityId(activity.getId());
            if (ObjectUtil.isNotEmpty(seckillIds)) {
                List<StoreSeckill> seckillList = storeSeckillService.listByIds(seckillIds);
                response.setSeckillList(seckillList);
            }
            // 获取折扣信息
            List<Integer> discountIds = storeActivityDiscountService.getDiscountIdsByActivityId(activity.getId());
            if (ObjectUtil.isNotEmpty(discountIds)) {
                List<StoreDiscount> discountList = storeDiscountService.listByIds(discountIds);
                response.setDiscountList(discountList);
            }
            responseList.add(response);
        }
        return responseList;
    }

    /**
     * 新增活动
     * 
     * @param request 活动请求对象
     * @return Boolean
     */
    @Override
    public Boolean create(StoreActivityRequest request) {
        StoreActivity activity = new StoreActivity();
        BeanUtils.copyProperties(request, activity);
        // 主图
        activity.setImage(systemAttachmentService.clearPrefix(activity.getImage()));
        // 轮播图
        activity.setImages(systemAttachmentService.clearPrefix(activity.getImages()));
        // 氛围图
        activity.setAtmosphereIcon(systemAttachmentService.clearPrefix(activity.getAtmosphereIcon()));
        // 未领取弹窗图
        if (activity.getUnclaimedPopupImage() != null) {
            activity.setUnclaimedPopupImage(systemAttachmentService.clearPrefix(activity.getUnclaimedPopupImage()));
        }
        // 已领取弹窗图
        if (activity.getClaimedPopupImage() != null) {
            activity.setClaimedPopupImage(systemAttachmentService.clearPrefix(activity.getClaimedPopupImage()));
        }
        // 设置默认内容类型
        if (activity.getContentType() == null) {
            activity.setContentType(2); // 默认为富文本模式
        }

        // 处理活动详情图：前端传入imagesAdmin，后端自动处理生成images
        if (request.getImagesAdmin() != null && 1 == activity.getContentType()) {
            // 清理URL前缀
            String cleanImagesAdmin = systemAttachmentService.clearPrefix(request.getImagesAdmin());
            activity.setImagesAdmin(cleanImagesAdmin);

            // 根据图片信息处理images（图片模式：contentType=1）
            String processedImages = activityDescriptionProcessor.processDescription(
                    cleanImagesAdmin, 1);
            activity.setImages(processedImages);
        } else {
            activity.setImages(request.getImagesAdmin());
        }

        // 处理活动详情
        if (request.getDescription() != null) {
            activity.setDescription(systemAttachmentService.clearPrefix(request.getDescription()));
        }

        // 设置活动开始时间和结束时间
        Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
        Date stopTime = DateUtil.strToDate(request.getStopTime(), Constants.DATE_FORMAT);
        activity.setStartTime(startTime);
        activity.setStopTime(stopTime);

        // 根据时间范围设置活动状态
        Date now = new Date();
        boolean shouldBeActive = now.after(startTime) && now.before(stopTime);
        activity.setStatus(shouldBeActive);

        return transactionTemplate.execute(e -> {
            save(activity);
            // 保存活动商品关联
            if (ObjectUtil.isNotEmpty(request.getProductIds())) {
                storeActivityProductService.saveActivityProduct(activity.getId(), request.getProductIds());
            }
            // 保存活动优惠券关联
            if (ObjectUtil.isNotEmpty(request.getCouponIds())) {
                storeActivityCouponService.saveActivityCoupon(activity.getId(), request.getCouponIds());
            }
            // 保存活动秒杀关联
            if (ObjectUtil.isNotEmpty(request.getSeckillIds())) {
                storeActivitySeckillService.saveActivitySeckill(activity.getId(), request.getSeckillIds());
            }
            // 保存活动折扣关联
            if (ObjectUtil.isNotEmpty(request.getDiscountIds())) {
                storeActivityDiscountService.saveActivityDiscount(activity.getId(), request.getDiscountIds());
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 修改活动
     * 
     * @param id 活动ID
     * @param request 活动请求对象
     * @return Boolean
     */
    @Override
    public Boolean updateActivity(Integer id, StoreActivityRequest request) {
        StoreActivity activity = getById(id);
        if (ObjectUtil.isNull(activity) || activity.getIsDel()) {
            throw new CrmebException("活动不存在");
        }

        if (activity.getStatus()) {
            throw new CrmebException("请先关闭活动，再修改活动信息");
        }

        StoreActivity storeActivity = new StoreActivity();
        BeanUtils.copyProperties(request, storeActivity);
        storeActivity.setId(id);
        // 主图
        storeActivity.setImage(systemAttachmentService.clearPrefix(storeActivity.getImage()));
        // 轮播图
        storeActivity.setImages(systemAttachmentService.clearPrefix(storeActivity.getImages()));
        // 氛围图
        storeActivity.setAtmosphereIcon(systemAttachmentService.clearPrefix(storeActivity.getAtmosphereIcon()));
        // 未领取弹窗图
        if (storeActivity.getUnclaimedPopupImage() != null) {
            storeActivity.setUnclaimedPopupImage(systemAttachmentService.clearPrefix(storeActivity.getUnclaimedPopupImage()));
        }
        // 已领取弹窗图
        if (storeActivity.getClaimedPopupImage() != null) {
            storeActivity.setClaimedPopupImage(systemAttachmentService.clearPrefix(storeActivity.getClaimedPopupImage()));
        }
        // 设置默认内容类型
        if (storeActivity.getContentType() == null) {
            storeActivity.setContentType(2); // 默认为富文本模式
        }

        // 处理活动轮播图：前端传入imagesAdmin，后端自动处理生成images
        if (request.getImagesAdmin() != null && 1 == activity.getContentType()) {
//            // 先清理旧的分割图片
//            if (activity.getImages() != null) {
//                activityDescriptionProcessor.cleanupDescriptionImages(activity.getImages());
//            }

            // 清理URL前缀
            String cleanImagesAdmin = systemAttachmentService.clearPrefix(request.getImagesAdmin());
            storeActivity.setImagesAdmin(cleanImagesAdmin);

            // 根据图片信息处理images（图片模式：contentType=1）
            String processedImages = activityDescriptionProcessor.processDescription(
                cleanImagesAdmin, 1);
            storeActivity.setImages(processedImages);
        } else {
            activity.setImages(request.getImagesAdmin());
        }

        // 处理活动详情
        if (request.getDescription() != null) {
            storeActivity.setDescription(systemAttachmentService.clearPrefix(request.getDescription()));
        }
        // 设置活动开始时间和结束时间
        Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
        Date stopTime = DateUtil.strToDate(request.getStopTime(), Constants.DATE_FORMAT);
        storeActivity.setStartTime(startTime);
        storeActivity.setStopTime(stopTime);

        // 根据新的时间范围重新计算活动状态
        Date now = new Date();
        boolean shouldBeActive = now.after(startTime) && now.before(stopTime);
        storeActivity.setStatus(shouldBeActive);

        return transactionTemplate.execute(e -> {
            updateById(storeActivity);
            // 保存活动商品关联
            if (ObjectUtil.isNotEmpty(request.getProductIds())) {
                storeActivityProductService.saveActivityProduct(id, request.getProductIds());
            }
            // 保存活动优惠券关联
            if (ObjectUtil.isNotEmpty(request.getCouponIds())) {
                storeActivityCouponService.saveActivityCoupon(id, request.getCouponIds());
            }
            // 保存活动秒杀关联
            if (ObjectUtil.isNotEmpty(request.getSeckillIds())) {
                storeActivitySeckillService.saveActivitySeckill(id, request.getSeckillIds());
            }
            // 保存活动折扣关联
            if (ObjectUtil.isNotEmpty(request.getDiscountIds())) {
                storeActivityDiscountService.saveActivityDiscount(id, request.getDiscountIds());
            }
            return Boolean.TRUE;
        });
    }

    /**
     * 删除活动
     * 
     * @param id 活动ID
     * @return Boolean
     */
    @Override
    public Boolean deleteById(Integer id) {
        StoreActivity activity = getById(id);
        if (ObjectUtil.isNull(activity) || activity.getIsDel()) {
            throw new CrmebException("活动不存在");
        }

        return transactionTemplate.execute(e -> {
//            // 删除活动轮播图中的分割图片
//            if (activity.getImages() != null) {
//                activityDescriptionProcessor.cleanupDescriptionImages(activity.getImages());
//            }

            activity.setIsDel(true);
            updateById(activity);
            // 删除活动商品关联
            storeActivityProductService.deleteByActivityId(id);
            // 删除活动优惠券关联
            storeActivityCouponService.deleteByActivityId(id);
            // 删除活动秒杀关联
            storeActivitySeckillService.deleteByActivityId(id);
            // 删除活动折扣关联
            storeActivityDiscountService.deleteByActivityId(id);
            return Boolean.TRUE;
        });
    }

    /**
     * 活动详情
     * 
     * @param id 活动ID
     * @return StoreActivityResponse
     */
    @Override
    public StoreActivityResponse getDetail(Integer id) {
        StoreActivity activity = getById(id);
        if (ObjectUtil.isNull(activity) || activity.getIsDel()) {
            throw new CrmebException("活动不存在");
        }

        StoreActivityResponse response = new StoreActivityResponse();
        BeanUtils.copyProperties(activity, response);
        // 处理轮播图
        if (activity.getImages() != null) {
            response.setImages(CrmebUtil.stringToArrayStr(activity.getImages()));
        }
        // 获取文章信息
        if (activity.getArticleId() > 0) {
            Article article = articleService.getById(activity.getArticleId());
            response.setArticle(article);
        }
        // 获取商品信息
        List<Integer> productIds = storeActivityProductService.getProductIdsByActivityId(id);
        if (ObjectUtil.isNotEmpty(productIds)) {
            List<StoreProduct> productList = storeProductService.listByIds(productIds);
            response.setProductList(productList);
        }
        // 获取优惠券信息
        List<Integer> couponIds = storeActivityCouponService.getCouponIdsByActivityId(id);
        if (ObjectUtil.isNotEmpty(couponIds)) {
            List<StoreCoupon> couponList = storeCouponService.listByIds(couponIds);
            response.setCouponList(couponList);
        }
        // 获取秒杀信息
        List<Integer> seckillIds = storeActivitySeckillService.getSeckillIdsByActivityId(id);
        if (ObjectUtil.isNotEmpty(seckillIds)) {
            List<StoreSeckill> seckillList = storeSeckillService.listByIds(seckillIds);
            response.setSeckillList(seckillList);
        }
        // 获取折扣信息
        List<Integer> discountIds = storeActivityDiscountService.getDiscountIdsByActivityId(id);
        if (ObjectUtil.isNotEmpty(discountIds)) {
            List<StoreDiscount> discountList = storeDiscountService.listByIds(discountIds);
            response.setDiscountList(discountList);
        }

        return response;
    }

    /**
     * 修改活动状态
     * 
     * @param id 活动ID
     * @param status 状态
     * @return Boolean
     */
    @Override
    public Boolean updateStatus(Integer id, Boolean status) {
        StoreActivity activity = getById(id);
        if (ObjectUtil.isNull(activity) || activity.getIsDel()) {
            throw new CrmebException("活动不存在");
        }

        activity.setStatus(status);
        return updateById(activity);
    }

    /**
     * 获取活动统计信息
     *
     * @return StoreActivityStatisticsResponse
     */
    @Override
    public StoreActivityStatisticsResponse getStatistics() {
        Date now = new Date();

        // 进行中的活动：当前时间在开始时间和结束时间之间，且状态为开启
        LambdaQueryWrapper<StoreActivity> ongoingWrapper = new LambdaQueryWrapper<>();
        ongoingWrapper.eq(StoreActivity::getIsDel, false)
                     .eq(StoreActivity::getStatus, true)
                     .le(StoreActivity::getStartTime, now)
                     .gt(StoreActivity::getStopTime, now);
        Long ongoingCount = count(ongoingWrapper);

        // 未开始的活动：开始时间大于当前时间，且状态为开启
        LambdaQueryWrapper<StoreActivity> notStartedWrapper = new LambdaQueryWrapper<>();
        notStartedWrapper.eq(StoreActivity::getIsDel, false)
                        .eq(StoreActivity::getStatus, true)
                        .gt(StoreActivity::getStartTime, now);
        Long notStartedCount = count(notStartedWrapper);

        // 已结束的活动：结束时间小于等于当前时间
        LambdaQueryWrapper<StoreActivity> endedWrapper = new LambdaQueryWrapper<>();
        endedWrapper.eq(StoreActivity::getIsDel, false);
        //.le(StoreActivity::getStopTime, now) 或者 status==0
        endedWrapper.and(i -> i.le(StoreActivity::getStopTime, now).or().eq(StoreActivity::getStatus, false));
        Long endedCount = count(endedWrapper);

        return new StoreActivityStatisticsResponse(ongoingCount, notStartedCount, endedCount);
    }

    /**
     * 根据商品ID获取商品优惠券信息
     *
     * @param productId 商品ID
     * @return ProductMarketingActivityResponse
     */
    @Override
    public ProductMarketingActivityResponse getProductCouponsByProductId(Integer productId) {
        // 获取商品信息
        StoreProduct product = storeProductService.getById(productId);
        if (ObjectUtil.isNull(product) || product.getIsDel()) {
            throw new CrmebException("商品不存在");
        }

        ProductMarketingActivityResponse response = new ProductMarketingActivityResponse();
        response.setProductId(productId);
        response.setProductName(product.getStoreName());
        response.setProductImage(product.getImage());

        // 直接查询适用于该商品的优惠券
        List<ProductMarketingActivityResponse.CouponActivityInfo> couponList = new ArrayList<>();

        // 查询所有有效的优惠券
        LambdaQueryWrapper<StoreCoupon> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreCoupon::getIsDel, false)
                   .eq(StoreCoupon::getStatus, true);
        List<StoreCoupon> allCoupons = storeCouponService.list(queryWrapper);

        for (StoreCoupon coupon : allCoupons) {
            // 判断优惠券是否适用于该商品
            if (!isCouponApplicableToProduct(coupon, productId)) {
                continue;
            }

            ProductMarketingActivityResponse.CouponActivityInfo couponInfo =
                new ProductMarketingActivityResponse.CouponActivityInfo();

            // 设置优惠券活动名称（使用优惠券名称）
            couponInfo.setActivityName(coupon.getName());
            // 设置优惠券id
            couponInfo.setActivityId(coupon.getId());

            // 设置活动类型（优惠券类型描述）
            String couponTypeDesc = getCouponTypeDesc(coupon.getType());
            couponInfo.setActivityType(couponTypeDesc);

            // 设置优惠内容
            String discountContent = buildDiscountContent(coupon);
            couponInfo.setDiscountContent(discountContent);

            // 设置状态（根据优惠券的有效期判断）
            String status = getCouponStatus(coupon);
            couponInfo.setStatus(status);

            couponList.add(couponInfo);
        }

        response.setCouponList(couponList);
        return response;
    }

    /**
     * 获取优惠券类型描述
     */
    private String getCouponTypeDesc(Integer type) {
        switch (type) {
            case 1:
                return "满减券";
            case 2:
                return "新人专享券";
            case 3:
                return "会员专享券";
            default:
                return "优惠券";
        }
    }

    /**
     * 构建优惠内容描述
     */
    private String buildDiscountContent(StoreCoupon coupon) {
        StringBuilder content = new StringBuilder();

        if (coupon.getCouponType() == 1) {
            // 有门槛
            content.append("满").append(coupon.getMinPrice()).append("元");
        }
        content.append("减").append(coupon.getMoney()).append("元");

        return content.toString();
    }

    /**
     * 获取优惠券状态
     */
    private String getCouponStatus(StoreCoupon coupon) {
        Date now = new Date();
        // 检查领取时间
        if (coupon.getReceiveStartTime() != null && coupon.getReceiveStartTime().after(now)) {
            return "未开始";
        }

        if (coupon.getReceiveEndTime() != null && coupon.getReceiveEndTime().before(now)) {
            return "已结束";
        }

        // 检查使用时间
        if (coupon.getUseTimeType() == 1) {
            // 固定时间
            if (coupon.getUseStartTime() != null && coupon.getUseStartTime().after(now)) {
                return "未开始";
            }
            if (coupon.getUseEndTime() != null && coupon.getUseEndTime().before(now)) {
                return "已结束";
            }
        }

        return "进行中";
    }

    /**
     * 判断优惠券是否适用于指定商品
     * @param coupon 优惠券
     * @param productId 商品ID
     * @return 是否适用
     */
    private boolean isCouponApplicableToProduct(StoreCoupon coupon, Integer productId) {
        Integer useType = coupon.getUseType();
        String primaryKey = coupon.getPrimaryKey();

        switch (useType) {
            case 1:
                // 全部商品可用
                return true;

            case 2:
                // 指定商品可用
                if (StrUtil.isBlank(primaryKey)) {
                    return false;
                }
                List<String> allowedProductIds = Arrays.asList(primaryKey.split(","));
                return allowedProductIds.contains(productId.toString());

            case 3:
                // 指定商品不可用
                if (StrUtil.isBlank(primaryKey)) {
                    return true; // 如果没有指定不可用商品，则所有商品都可用
                }
                List<String> excludedProductIds = Arrays.asList(primaryKey.split(","));
                return !excludedProductIds.contains(productId.toString());

            default:
                return false;
        }
    }
}