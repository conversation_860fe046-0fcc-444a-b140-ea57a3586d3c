package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.StoreActivityProductDao;
import com.ylpz.core.service.StoreActivityProductService;
import com.ylpz.model.activity.StoreActivityProduct;

import cn.hutool.core.util.ObjectUtil;

/**
 * 活动商品关联表 Service 实现类
 */
@Service
public class StoreActivityProductServiceImpl extends ServiceImpl<StoreActivityProductDao, StoreActivityProduct>
    implements StoreActivityProductService {

    @Resource
    private StoreActivityProductDao dao;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 根据活动ID获取商品ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    @Override
    public List<Integer> getProductIdsByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityProduct::getActivityId, activityId)
            .eq(StoreActivityProduct::getIsDel, false);
        List<StoreActivityProduct> list = list(lambdaQueryWrapper);
        List<Integer> productIds = new ArrayList<>();
        for (StoreActivityProduct activityProduct : list) {
            productIds.add(activityProduct.getProductId());
        }
        return productIds;
    }

    /**
     * 保存活动商品关联
     * 
     * @param activityId 活动ID
     * @param productIds 商品ID列表
     * @return Boolean
     */
    @Override
    public Boolean saveActivityProduct(Integer activityId, List<Integer> productIds) {
        if (ObjectUtil.isEmpty(productIds)) {
            return Boolean.TRUE;
        }
        return transactionTemplate.execute(e -> {
            // 删除原有关联
            deleteByActivityId(activityId);
            // 保存新关联
            List<StoreActivityProduct> activityProductList = new ArrayList<>();
            for (Integer productId : productIds) {
                StoreActivityProduct activityProduct = new StoreActivityProduct();
                activityProduct.setActivityId(activityId);
                activityProduct.setProductId(productId);
                activityProductList.add(activityProduct);
            }
            return saveBatch(activityProductList);
        });
    }

    /**
     * 删除活动商品关联
     *
     * @param activityId 活动ID
     * @return Boolean
     */
    @Override
    public Boolean deleteByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityProduct::getActivityId, activityId)
            .eq(StoreActivityProduct::getIsDel, false);
        List<StoreActivityProduct> list = list(lambdaQueryWrapper);
        for (StoreActivityProduct activityProduct : list) {
            activityProduct.setIsDel(true);
        }
        return updateBatchById(list);
    }

    /**
     * 根据商品ID获取活动ID列表
     *
     * @param productId 商品ID
     * @return List<Integer>
     */
    @Override
    public List<Integer> getActivityIdsByProductId(Integer productId) {
        LambdaQueryWrapper<StoreActivityProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityProduct::getProductId, productId)
            .eq(StoreActivityProduct::getIsDel, false);
        List<StoreActivityProduct> list = list(lambdaQueryWrapper);
        List<Integer> activityIds = new ArrayList<>();
        for (StoreActivityProduct activityProduct : list) {
            activityIds.add(activityProduct.getActivityId());
        }
        return activityIds;
    }
}