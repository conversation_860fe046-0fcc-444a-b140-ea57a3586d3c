package com.ylpz.core.service;

import java.util.HashMap;
import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreDiscountUserRequest;
import com.ylpz.core.common.request.StoreDiscountUserSearchRequest;
import com.ylpz.core.common.response.StoreDiscountUserResponse;
import com.ylpz.model.discount.StoreDiscountUser;

/**
 * StoreDiscountUserService 接口
 */
public interface StoreDiscountUserService extends IService<StoreDiscountUser> {

    List<StoreDiscountUserResponse> getList(StoreDiscountUserSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 领取折扣
     * @param request StoreDiscountUserRequest 新增参数
     */
    Boolean receive(StoreDiscountUserRequest request);

    /**
     * 根据uid获取对应的map
     * @param uid 用户id
     * @return HashMap<Integer, StoreDiscountUser>
     */
    HashMap<Integer, StoreDiscountUser> getMapByUserId(Integer uid);
} 