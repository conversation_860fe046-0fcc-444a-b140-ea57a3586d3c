package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.BonusTypeConstants;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.common.constants.SystemParamSettingConstants;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.dao.UserBonusRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.SystemParamSettingService;
import com.ylpz.core.service.UserBonusRecordService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBonusRecord;
import com.ylpz.model.user.UserSalesRank;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

/**
 * 用户奖励金记录服务实现类
 */
@Service
public class UserBonusRecordServiceImpl extends ServiceImpl<UserBonusRecordDao, UserBonusRecord>
        implements UserBonusRecordService {

    // 链接类型常量
    private static final String LINK_TYPE_USER = "user";
    private static final String LINK_TYPE_ORDER = "order";

    @Resource
    private UserBonusRecordDao userBonusRecordDao;
    
    @Resource
    private UserDao userDao;
    
    @Resource
    private UserService userService;
    
    @Resource
    private SystemParamSettingService systemParamSettingService;

    /**
     * 获取奖励金记录列表
     *
     * @param linkId 关联ID
     * @param bonusType 奖励类型
     * @param userPhone 用户手机号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    @Override
    public PageInfo<UserBonusRecord> getBonusList(String linkId, String bonusType, 
                                                String userPhone, Date startTime, 
                                                Date endTime, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        
        Map<String, Object> params = new HashMap<>();
        params.put("linkId", linkId);
        params.put("bonusType", bonusType);
        params.put("userPhone", userPhone);
        params.put("startTime", startTime);
        params.put("endTime", endTime);
        
        List<UserBonusRecord> list = userBonusRecordDao.getBonusList(params);
        return new PageInfo<>(list);
    }

    /**
     * 统计奖励金合计
     *
     * @return 奖励金合计
     */
    @Override
    public BigDecimal sumBonusTotal() {
        return userBonusRecordDao.sumBonusTotal();
    }
    
    /**
     * 按类型统计奖励金
     *
     * @return 各类型奖励金统计
     */
    @Override
    public Map<String, BigDecimal> getBonusByTypeStatistics() {
        List<Map<String, Object>> typeStats = userBonusRecordDao.sumBonusByType();
        Map<String, BigDecimal> result = new HashMap<>();
        
        // 初始化所有奖励类型为0
        result.put(BonusTypeConstants.BONUS_TYPE_UPGRADE, BigDecimal.ZERO);
        result.put(BonusTypeConstants.BONUS_TYPE_RECHARGE, BigDecimal.ZERO);
        result.put(BonusTypeConstants.BONUS_TYPE_FIRST_ORDER, BigDecimal.ZERO);
        result.put(BonusTypeConstants.BONUS_TYPE_RANK, BigDecimal.ZERO);
        result.put(BonusTypeConstants.BONUS_TYPE_OTHER, BigDecimal.ZERO);
        
        // 填充实际统计数据
        for (Map<String, Object> stat : typeStats) {
            String type = (String) stat.get("type");
            BigDecimal amount = new BigDecimal(stat.get("amount").toString());
            result.put(type, amount);
        }
        
        return result;
    }
    
    /**
     * 发放排行榜奖励金
     *
     * @param rankType 排行榜类型（周排行、月排行、季度排行）
     * @param rankDate 排行日期
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeRankBonus(String rankType, Date rankDate) {
        // 1. 确定奖励来源类型
        String sourceType;
        String bonusType = BonusTypeConstants.BONUS_TYPE_RANK;
        
        if ("week".equals(rankType)) {
            sourceType = BonusTypeConstants.BONUS_SOURCE_WEEKLY_RANK;
        } else if ("month".equals(rankType)) {
            sourceType = BonusTypeConstants.BONUS_SOURCE_MONTHLY_RANK;
        } else if ("quarter".equals(rankType)) {
            sourceType = BonusTypeConstants.BONUS_SOURCE_QUARTERLY_RANK;
        } else {
            return false;
        }

        // 2. 获取排行榜配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);
        if (CollectionUtils.isEmpty(configs)) {
            return false;
        }

        // 筛选出对应排行榜类型的配置
        List<SystemParamSetting> matchedConfigs = configs.stream().filter(config -> {
            JSONObject configValue = JSON.parseObject(config.getConfigValue());
            return configValue != null && sourceType.equals(configValue.getString("sourceType"));
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(matchedConfigs)) {
            return false;
        }

        // 3. 获取排行榜数据
        List<UserSalesRank> rankList = getSalesRankList(rankType, rankDate);
        if (CollectionUtils.isEmpty(rankList)) {
            return false;
        }

        // 4. 遍历排行榜数据，发放奖励
        for (UserSalesRank rank : rankList) {
            Integer rankPosition = rank.getRank();
            if (rankPosition == null || rankPosition <= 0) {
                continue;
            }

            // 查找对应排名的奖励配置
            SystemParamSetting matchedConfig = null;
            for (SystemParamSetting config : matchedConfigs) {
                JSONObject configValue = JSON.parseObject(config.getConfigValue());
                if (configValue != null && rankPosition == configValue.getInteger("rankPosition")) {
                    matchedConfig = config;
                    break;
                }
            }

            // 如果没有找到对应排名的奖励配置，跳过
            if (matchedConfig == null) {
                continue;
            }

            // 获取奖励金额
            JSONObject configValue = JSON.parseObject(matchedConfig.getConfigValue());
            BigDecimal bonusAmount = configValue.getBigDecimal("bonusAmount");
            if (bonusAmount == null || bonusAmount.compareTo(BigDecimal.ZERO) <= 0) {
                continue;
            }

            // 创建奖励记录
            UserBonusRecord bonusRecord = new UserBonusRecord();
            bonusRecord.setUid(rank.getUid());
            bonusRecord.setLinkId(rank.getUid().toString());
            bonusRecord.setBonusType(bonusType);
            bonusRecord.setSourceType(sourceType);
            bonusRecord.setPrice(bonusAmount);
            bonusRecord.setBalance(bonusAmount);
            bonusRecord.setStatus(BonusTypeConstants.BONUS_STATUS_COMPLETE);
            bonusRecord.setMark("排名第" + rankPosition + "名，奖励金额：" + bonusAmount);
            bonusRecord.setCreateTime(new Date());
            bonusRecord.setUpdateTime(new Date());
            
            // 获取用户信息
            User user = userService.getById(rank.getUid());
            if (user != null) {
                bonusRecord.setUserPhone(user.getPhone());
                bonusRecord.setUserLevel(String.valueOf(user.getLevel()));
            }
            
            // 保存奖励记录
            save(bonusRecord);
            
            // 更新用户奖励金余额
            updateUserBonus(rank.getUid(), bonusAmount);
        }

        return true;
    }
    
    /**
     * 发放推广升级奖励金
     *
     * @param uid       被推广用户ID
     * @param spreadUid 推广人用户ID
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeUpgradeBonus(Integer uid, Integer spreadUid) {
        if (uid == null || spreadUid == null) {
            return false;
        }

        // 获取被推广用户信息
        User user = userService.getById(uid);
        if (user == null) {
            return false;
        }

        // 获取推广人信息
        User spreadUser = userService.getById(spreadUid);
        if (spreadUser == null) {
            return false;
        }

        // 确定奖励来源类型和奖励金额
        String sourceType;
        String configCode;
        
        if (MemberLevelConstants.isVip(user.getLevel())) {
            // 升级为VIP
            sourceType = BonusTypeConstants.BONUS_SOURCE_UPGRADE_TO_VIP;
            configCode = SystemParamSettingConstants.BonusConfig.UPGRADE_TO_VIP_BONUS;
        } else if (MemberLevelConstants.isSvip(user.getLevel())) {
            // 升级为SVIP
            sourceType = BonusTypeConstants.BONUS_SOURCE_UPGRADE_TO_SVIP;
            configCode = SystemParamSettingConstants.BonusConfig.UPGRADE_TO_SVIP_BONUS;
        } else {
            return false;
        }

        // 获取奖励金配置
        SystemParamSetting config = findSystemParamSettingByCode(configCode);
        if (config == null) {
            return false;
        }

        // 解析奖励金额
        JSONObject configValue = JSON.parseObject(config.getConfigValue());
        BigDecimal bonusAmount = configValue.getBigDecimal("bonusAmount");
        if (bonusAmount == null || bonusAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 创建奖励记录
        UserBonusRecord bonusRecord = new UserBonusRecord();
        bonusRecord.setUid(spreadUid);
        bonusRecord.setLinkId(uid.toString());
        bonusRecord.setBonusType(BonusTypeConstants.BONUS_TYPE_UPGRADE);
        bonusRecord.setSourceType(sourceType);
        bonusRecord.setPrice(bonusAmount);
        bonusRecord.setBalance(bonusAmount);
        bonusRecord.setStatus(BonusTypeConstants.BONUS_STATUS_COMPLETE);
        bonusRecord.setMark("推广用户" + user.getNickname() + "升级为" + getUserLevelName(user.getLevel()) + "，奖励金额：" + bonusAmount);
        bonusRecord.setCreateTime(new Date());
        bonusRecord.setUpdateTime(new Date());
        bonusRecord.setUserPhone(spreadUser.getPhone());
        bonusRecord.setUserLevel(String.valueOf(user.getLevel()));
        
        // 保存奖励记录
        save(bonusRecord);
        
        // 更新用户奖励金余额
        updateUserBonus(spreadUid, bonusAmount);

        return true;
    }
    
    /**
     * 发放推广充值奖励金
     *
     * @param uid            充值用户ID
     * @param spreadUid      推广人用户ID
     * @param rechargeAmount 充值金额
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeRechargeBonus(Integer uid, Integer spreadUid, BigDecimal rechargeAmount) {
        if (uid == null || spreadUid == null || rechargeAmount == null || rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 获取充值用户信息
        User user = userService.getById(uid);
        if (user == null) {
            return false;
        }

        // 获取推广人信息
        User spreadUser = userService.getById(spreadUid);
        if (spreadUser == null) {
            return false;
        }

        // 获取充值奖励配置
        SystemParamSetting config = findSystemParamSettingByCode(SystemParamSettingConstants.BonusConfig.FIRST_ORDER_BONUS);
        if (config == null) {
            return false;
        }

        // 解析奖励比例
        JSONObject configValue = JSON.parseObject(config.getConfigValue());
        BigDecimal bonusRate = configValue.getBigDecimal("bonusRate");
        if (bonusRate == null || bonusRate.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 计算奖励金额
        BigDecimal bonusAmount = rechargeAmount.multiply(bonusRate).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN);
        if (bonusAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 创建奖励记录
        UserBonusRecord bonusRecord = new UserBonusRecord();
        bonusRecord.setUid(spreadUid);
        bonusRecord.setLinkId(uid.toString());
        bonusRecord.setBonusType(BonusTypeConstants.BONUS_TYPE_RECHARGE);
        bonusRecord.setSourceType(BonusTypeConstants.BONUS_SOURCE_RECHARGE);
        bonusRecord.setPrice(bonusAmount);
        bonusRecord.setBalance(bonusAmount);
        bonusRecord.setStatus(BonusTypeConstants.BONUS_STATUS_COMPLETE);
        bonusRecord.setMark("推广用户" + user.getNickname() + "充值" + rechargeAmount + "元，奖励金额：" + bonusAmount);
        bonusRecord.setCreateTime(new Date());
        bonusRecord.setUpdateTime(new Date());
        bonusRecord.setUserPhone(spreadUser.getPhone());
        bonusRecord.setUserLevel(String.valueOf(user.getLevel()));
        
        // 保存奖励记录
        save(bonusRecord);
        
        // 更新用户奖励金余额
        updateUserBonus(spreadUid, bonusAmount);

        return true;
    }
    
    /**
     * 发放首单购买奖励金
     *
     * @param uid         购买用户ID
     * @param spreadUid   推广人用户ID
     * @param orderNo     订单号
     * @param orderAmount 订单金额
     * @return 是否发放成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributeFirstOrderBonus(Integer uid, Integer spreadUid, String orderNo, BigDecimal orderAmount) {
        if (uid == null || spreadUid == null || StringUtils.isEmpty(orderNo) || 
            orderAmount == null || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 获取购买用户信息
        User user = userService.getById(uid);
        if (user == null) {
            return false;
        }

        // 获取推广人信息
        User spreadUser = userService.getById(spreadUid);
        if (spreadUser == null) {
            return false;
        }

        // 获取首单奖励配置
        SystemParamSetting config = findSystemParamSettingByCode(SystemParamSettingConstants.BonusConfig.FIRST_ORDER_BONUS);
        if (config == null) {
            return false;
        }

        // 解析奖励比例
        JSONObject configValue = JSON.parseObject(config.getConfigValue());
        BigDecimal bonusRate = configValue.getBigDecimal("bonusRate");
        if (bonusRate == null || bonusRate.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 计算奖励金额
        BigDecimal bonusAmount = orderAmount.multiply(bonusRate).divide(new BigDecimal(100), 2, BigDecimal.ROUND_DOWN);
        if (bonusAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }

        // 创建奖励记录
        UserBonusRecord bonusRecord = new UserBonusRecord();
        bonusRecord.setUid(spreadUid);
        bonusRecord.setLinkId(orderNo);
        bonusRecord.setBonusType(BonusTypeConstants.BONUS_TYPE_FIRST_ORDER);
        bonusRecord.setSourceType(BonusTypeConstants.BONUS_SOURCE_FIRST_ORDER);
        bonusRecord.setPrice(bonusAmount);
        bonusRecord.setBalance(bonusAmount);
        bonusRecord.setStatus(BonusTypeConstants.BONUS_STATUS_COMPLETE);
        bonusRecord.setMark("推广用户" + user.getNickname() + "首单购买，订单金额" + orderAmount + "元，奖励金额：" + bonusAmount);
        bonusRecord.setCreateTime(new Date());
        bonusRecord.setUpdateTime(new Date());
        bonusRecord.setUserPhone(spreadUser.getPhone());
        bonusRecord.setUserLevel(String.valueOf(user.getLevel()));
        
        // 保存奖励记录
        save(bonusRecord);
        
        // 更新用户奖励金余额
        updateUserBonus(spreadUid, bonusAmount);

        return true;
    }
    
    /**
     * 根据用户等级获取等级名称
     *
     * @param level 用户等级
     * @return 等级名称
     */
    private String getUserLevelName(Integer level) {
        return MemberLevelConstants.getLevelName(level);
    }
    
    /**
     * 获取排行榜数据
     *
     * @param rankType 排行榜类型
     * @param rankDate 排行日期
     * @return 排行榜数据
     */
    private List<UserSalesRank> getSalesRankList(String rankType, Date rankDate) {
        // 这里需要根据实际情况实现排行榜数据获取逻辑
        // 可以调用相应的排行榜服务获取数据
        return null;
    }
    
    /**
     * 更新用户奖励金余额
     *
     * @param uid   用户ID
     * @param price 奖励金额
     */
    private void updateUserBonus(Integer uid, BigDecimal price) {
        // 这里需要根据实际情况实现用户奖励金余额更新逻辑
        // 可以调用用户服务更新用户奖励金余额
    }
    
    /**
     * 根据配置代码查找系统参数设置
     * 
     * @param code 配置代码
     * @return 系统参数设置
     */
    private SystemParamSetting findSystemParamSettingByCode(String code) {
        if (StringUtils.isEmpty(code)) {
            return null;
        }
        
        // 从系统参数设置列表中查找指定代码的配置
        List<SystemParamSetting> configList = systemParamSettingService.getList();
        if (CollectionUtils.isEmpty(configList)) {
            return null;
        }
        
        // 筛选出匹配的配置
        for (SystemParamSetting config : configList) {
            if (code.equals(config.getConfigCode())) {
                return config;
            }
        }
        
        return null;
    }
} 