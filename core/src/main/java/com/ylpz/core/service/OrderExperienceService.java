package com.ylpz.core.service;

import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.user.User;

/**
 * 2025-07-21
 * 订单经验值服务接口
 * 处理订单支付成功后的经验值增加逻辑
 */
public interface OrderExperienceService {

    /**
     * 订单支付成功后增加经验值
     * 根据用户当前会员等级和订单金额计算并增加经验值
     *
     * @param order 订单信息
     * @param user 用户信息
     * @return 是否成功增加经验值
     */
    Boolean addExperienceForOrder(StoreOrder order, User user);
}
