package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.*;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.*;
import com.ylpz.core.common.response.*;
import com.ylpz.core.common.token.FrontTokenComponent;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.core.common.vo.dateLimitUtilVo;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.*;
import com.ylpz.model.finance.UserExtract;
import com.ylpz.model.finance.enums.UserExtractStatusEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.record.UserVisitRecord;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.*;
import com.ylpz.model.user.enums.UserBillEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户表 服务实现类
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserDao, User> implements UserService {

    private Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    @Resource
    private UserDao userDao;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private FrontTokenComponent tokenComponet;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private UserTagService userTagService;

    @Autowired
    private UserGroupService userGroupService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserSignService userSignService;

    @Autowired
    private StoreCouponUserService storeCouponUserService;

    @Autowired
    private StoreCouponService storeCouponService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserIntegralRecordService userIntegralRecordService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    @Autowired
    private StoreProductRelationService storeProductRelationService;

    @Autowired
    private UserExperienceRecordService userExperienceRecordService;

    @Autowired
    private UserVisitRecordService userVisitRecordService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    @Autowired
    private UserSpreadService userSpreadService;

    @Autowired
    private UserExtractService  userExtractService;

    /**
     * 分页显示用户表
     *
     * @param request          搜索条件
     * @param pageParamRequest 分页参数
     */
    @Override
    public PageInfo<UserResponse> getList(UserSearchRequest request, PageParamRequest pageParamRequest) {
        Page<User> pageUser = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        Map<String, Object> map = CollUtil.newHashMap();

        if (request.getIsPromoter() != null) {
            map.put("isPromoter", request.getIsPromoter() ? 1 : 0);
        }

        if (!StringUtils.isBlank(request.getGroupId())) {
            map.put("groupId", request.getGroupId());
        }

        if (!StringUtils.isBlank(request.getLabelId())) {
            String tagIdSql = CrmebUtil.getFindInSetSql("u.tag_id", request.getLabelId());
            map.put("tagIdSql", tagIdSql);
        }

        if (!StringUtils.isBlank(request.getLevel())) {
            map.put("level", request.getLevel());
        }

        if (StringUtils.isNotBlank(request.getUserType())) {
            map.put("userType", request.getUserType());
        }

        if (StringUtils.isNotBlank(request.getSex())) {
            lambdaQueryWrapper.eq(User::getSex, request.getSex());
            map.put("sex", Integer.valueOf(request.getSex()));
        }

        if (StringUtils.isNotBlank(request.getCountry())) {
            map.put("country", request.getCountry());
            // 根据省市查询
            if (StrUtil.isNotBlank(request.getCity())) {
                request.setProvince(request.getProvince().replace("省", ""));
                request.setCity(request.getCity().replace("市", ""));
                map.put("addres", request.getProvince() + "," + request.getCity());
            }
        }

        if (StrUtil.isNotBlank(request.getPayCount())) {
            map.put("payCount", Integer.valueOf(request.getPayCount()));
        }

        if (request.getStatus() != null) {
            map.put("status", request.getStatus() ? 1 : 0);
        }

        // 添加成为会员时间查询条件
        if (StrUtil.isNotBlank(request.getMemberTimeStart())) {
            map.put("memberTimeStart", request.getMemberTimeStart());
        }

        if (StrUtil.isNotBlank(request.getMemberTimeEnd())) {
            map.put("memberTimeEnd", request.getMemberTimeEnd());
        }

        dateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());

        if (!StringUtils.isBlank(dateLimit.getStartTime())) {
            map.put("startTime", dateLimit.getStartTime());
            map.put("endTime", dateLimit.getEndTime());
            map.put("accessType", request.getAccessType());
        }
        if (request.getKeywords() != null) {
            map.put("keywords", request.getKeywords());
        }
        List<User> userList = userDao.findAdminList(map);
        List<UserResponse> userResponses = new ArrayList<>();
        for (User user : userList) {
            UserResponse userResponse = new UserResponse();
            BeanUtils.copyProperties(user, userResponse);
            userResponse.setUid(user.getId());
            // 获取分组信息
            if (!StringUtils.isBlank(user.getGroupId())) {
                userResponse.setGroupName(userGroupService.getGroupNameInId(user.getGroupId()));
                userResponse.setGroupId(user.getGroupId());
            }

            // 获取标签信息
            if (!StringUtils.isBlank(user.getTagId())) {
                userResponse.setTagName(userTagService.getGroupNameInId(user.getTagId()));
                userResponse.setTagId(user.getTagId());
            }
            // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
            if (MemberLevelConstants.isSvip(user.getLevel())) {
                userResponse.setSpreadNickname("养乐铺子");
            } else {
                // 获取推广人信息
                if (null == user.getSpreadUid() || user.getSpreadUid() == 0) {
                    userResponse.setSpreadNickname("无");
                } else {
                    User spreadUser = getById(user.getSpreadUid());
                    if (spreadUser != null) {
                        userResponse.setSpreadNickname(spreadUser.getNickname());
                    }
                }
            }
            userResponse.setPhone(CrmebUtil.maskMobile(userResponse.getPhone()));
            userResponses.add(userResponse);
        }
        return CommonPage.copyPageInfo(pageUser, userResponses);
    }

    /**
     * 操作余额
     */
    @Override
    public Boolean updateIntegralMoney(UserOperateIntegralMoneyRequest request) {
        if (ObjectUtil.isNull(request.getMoneyValue())) {
            throw new CrmebException("至少输入一个金额");
        }
        if (request.getMoneyValue().compareTo(BigDecimal.ZERO) < 1) {
            throw new CrmebException("修改值不能等小于等于0");
        }

        User user = getById(request.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }
        // 减少时要判断小于0的情况,添加时判断是否超过数据限制
        if (request.getMoneyType().equals(2) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
            if (user.getNowMoney().subtract(request.getMoneyValue()).compareTo(BigDecimal.ZERO) < 0) {
                throw new CrmebException("余额扣减后不能小于0");
            }
        }
        if (request.getMoneyType().equals(1) && request.getMoneyValue().compareTo(BigDecimal.ZERO) != 0) {
            if (user.getNowMoney().add(request.getMoneyValue()).compareTo(new BigDecimal("99999999.99")) > 0) {
                throw new CrmebException("余额添加后后不能大于99999999.99");
            }
        }

        Boolean execute = transactionTemplate.execute(e -> {
            // 处理余额
            if (request.getMoneyValue().compareTo(BigDecimal.ZERO) > 0) {
                // 生成UserBill
                UserBill userBill = new UserBill();
                userBill.setUid(user.getId());
                userBill.setLinkId("0");
                
                // 根据操作类型设置标题和类型
                UserBillEnum billEnum;
                if (request.getOperationType() == 1) {
                    billEnum = UserBillEnum.RECHARGE; // 会员充值
                } else {
                    billEnum = UserBillEnum.ADMIN_ADJUST; // 后台调整
                }
                userBill.setTitle(billEnum.getTitle());
                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                userBill.setNumber(request.getMoneyValue());
                userBill.setStatus(1);
                userBill.setCreateTime(DateUtil.nowDateTime());

                if (request.getMoneyType() == 1) {// 增加
                    userBill.setPm(1);
                    userBill.setType(billEnum.getType());
                    userBill.setBalance(user.getNowMoney().add(request.getMoneyValue()));
                    
                    // 使用自定义备注或默认备注
                    String mark = StrUtil.isNotBlank(request.getRemark()) ? 
                            request.getRemark() : 
                            StrUtil.format("{}增加了{}余额", billEnum.getTitle(), request.getMoneyValue());
                    userBill.setMark(mark);

                    userBillService.save(userBill);
                    operationNowMoney(user.getId(), request.getMoneyValue(), user.getNowMoney(), "add");
                } else {
                    userBill.setPm(0);
                    userBill.setType(billEnum.getType());
                    userBill.setBalance(user.getNowMoney().subtract(request.getMoneyValue()));
                    
                    // 使用自定义备注或默认备注
                    String mark = StrUtil.isNotBlank(request.getRemark()) ? 
                            request.getRemark() : 
                            StrUtil.format("{}减少了{}余额", billEnum.getTitle(), request.getMoneyValue());
                    userBill.setMark(mark);

                    userBillService.save(userBill);
                    operationNowMoney(user.getId(), request.getMoneyValue(), user.getNowMoney(), "sub");
                }
            }

            // 处理成长值（只有增加余额且是会员充值时才处理）
            if (request.getMoneyType() == 1 && request.getOperationType() == 1) {
                try {
                    addRechargeExperienceForAdmin(user, request.getMoneyValue());
                } catch (Exception ex) {
                    logger.error("处理充值经验值失败", ex);
                    // 经验值处理失败不影响余额操作
                }
            }
            return Boolean.TRUE;
        });

        if (!execute) {
            throw new CrmebException("修改余额失败");
        }
        return execute;
    }

    /**
     * 处理后台充值经验值
     */
    private void addRechargeExperienceForAdmin(User user, BigDecimal rechargeAmount) {
        try {
            // 检查用户等级是否支持会员充值经验值
            if (!isRechargeExperienceEnabledForUserLevel(user.getLevel())) {
                return;
            }

            // 计算应增加的经验值
            Integer addExperience = calculateRechargeExperienceForAdmin(rechargeAmount);
            if (addExperience <= 0) {
                return;
            }

            // 创建经验值记录
            UserExperienceRecord experienceRecord = new UserExperienceRecord();
            experienceRecord.setUid(user.getId());
            experienceRecord.setLinkId("0");
            experienceRecord.setLinkType(ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_SYSTEM);
            experienceRecord.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_ADD);
            experienceRecord.setTitle(ExperienceRecordConstants.EXPERIENCE_SOURCE_RECHARGE);
            experienceRecord.setExperience(addExperience);
            experienceRecord.setBalance(user.getExperience() + addExperience);
            experienceRecord.setMark(StrUtil.format("会员充值{}元，获得{}经验值", rechargeAmount, addExperience));
            experienceRecord.setStatus(ExperienceRecordConstants.EXPERIENCE_RECORD_STATUS_CREATE);
            experienceRecord.setCreateTime(DateUtil.nowDateTime());

            // 更新用户经验值
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setExperience(user.getExperience() + addExperience);

            // 保存经验值记录
            userExperienceRecordService.save(experienceRecord);
            // 更新用户经验值
            updateById(updateUser);
            // 检查用户升级
            User updatedUser = getById(user.getId());
            userLevelService.upLevel(updatedUser);

        } catch (Exception e) {
            throw new RuntimeException("处理充值经验值失败", e);
        }
    }

    /**
     * 检查用户等级是否支持会员充值经验值（后台操作）
     */
    private Boolean isRechargeExperienceEnabledForUserLevel(Integer userLevel) {
        if (userLevel == null) {
            return false;
        }

        try {
            if (userLevel <= 1) {
                userLevel = userLevel++;
            }

            SystemUserLevel systemUserLevel = systemUserLevelService.getByLevelId(userLevel);
            if (systemUserLevel == null) {
                return false;
            }

            // 检查经验值来源配置是否包含"会员充值"
            String experienceSource = systemUserLevel.getExperienceSource();
            if (StrUtil.isBlank(experienceSource)) {
                return false;
            }

            // 检查是否包含"会员充值"
            return experienceSource.contains("会员充值");

        } catch (Exception e) {
            logger.error("检查用户等级会员充值经验值配置失败，等级：{}", userLevel, e);
            return false;
        }
    }

    /**
     * 根据充值金额计算经验值（后台操作）
     * 根据系统参数设置表中的member_recharge配置计算
     */
    private Integer calculateRechargeExperienceForAdmin(BigDecimal rechargeAmount) {
        if (rechargeAmount == null || rechargeAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }

        try {
            // 从系统参数设置表获取会员充值经验值配置
            SystemParamSetting setting = getSystemParamSettingByCode(SystemParamSettingConstants.ExperienceConfig.MEMBER_RECHARGE);
            if (setting == null || !setting.getStatus()) {
                logger.info("会员充值经验值配置未启用或不存在");
                return 0;
            }

            // 解析配置值
            JSONObject configJson = JSONUtil.parseObj(setting.getConfigValue());
            if (configJson == null) {
                logger.warn("会员充值经验值配置格式错误：{}", setting.getConfigValue());
                return 0;
            }

            // 获取配置参数：number（每充值金额）和ratio（获得经验值比例）
            Integer number = configJson.getInt("number");
            Integer ratio = configJson.getInt("ratio");

            if (number == null || number <= 0 || ratio == null || ratio <= 0) {
                logger.warn("会员充值经验值配置参数无效，number: {}, ratio: {}", number, ratio);
                return 0;
            }

            // 计算经验值：(充值金额 / number) * ratio
            BigDecimal experienceDecimal = rechargeAmount.divide(new BigDecimal(number), 2, RoundingMode.DOWN)
                                                        .multiply(new BigDecimal(ratio));

            return experienceDecimal.intValue();

        } catch (Exception e) {
            logger.error("计算充值经验值失败，充值金额：{}", rechargeAmount, e);
            return 0;
        }
    }



    /**
     * 更新用户金额
     *
     * @param user  用户
     * @param price 金额
     * @param type  增加add、扣减sub
     * @return 更新后的用户对象
     */
    @Override
    public Boolean updateNowMoney(User user, BigDecimal price, String type) {
        LambdaUpdateWrapper<User> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        if (type.equals("add")) {
            lambdaUpdateWrapper.set(User::getNowMoney, user.getNowMoney().add(price));
        } else {
            lambdaUpdateWrapper.set(User::getNowMoney, user.getNowMoney().subtract(price));
        }
        lambdaUpdateWrapper.eq(User::getId, user.getId());
        if (type.equals("sub")) {
            lambdaUpdateWrapper.apply(StrUtil.format(" now_money - {} >= 0", price));
        }
        return update(lambdaUpdateWrapper);
    }

    /**
     * 会员分组
     *
     * @param id           String id
     * @param groupIdValue Integer 分组Id
     */
    @Override
    public Boolean group(String id, String groupIdValue) {
        if (StrUtil.isBlank(id))
            throw new CrmebException("会员编号不能为空");
        if (StrUtil.isBlank(groupIdValue))
            throw new CrmebException("分组id不能为空");

        // 循环id处理
        List<Integer> idList = CrmebUtil.stringToArray(id);
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<User> list = getListInUid(idList);
        if (CollUtil.isEmpty(list))
            throw new CrmebException("没有找到用户信息");
        if (list.size() < idList.size()) {
            throw new CrmebException("没有找到用户信息");
        }
        for (User user : list) {
            user.setGroupId(groupIdValue);
        }
        return updateBatchById(list);
    }

    /**
     * 用户id in list
     *
     * @param uidList List<Integer> id
     * <AUTHOR>
     * @since 2020-04-28
     */
    private List<User> getListInUid(List<Integer> uidList) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getId, uidList);
        return userDao.selectList(lambdaQueryWrapper);
    }

    /**
     * 修改密码
     *
     * @param request PasswordRequest 密码
     * @return boolean
     */
    @Override
    public Boolean password(PasswordRequest request) {
        // 检测验证码
        checkValidateCode(request.getPhone(), request.getValidateCode());

        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(User::getAccount, request.getPhone());
        User user = userDao.selectOne(lambdaQueryWrapper);

        // 密码
        user.setPwd(CrmebUtil.encryptPassword(request.getPassword(), user.getAccount()));
        return update(user, lambdaQueryWrapper);
    }

    /**
     * 获取个人资料
     *
     * @return User
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public User getInfo() {
        if (getUserId() == 0) {
            return null;
        }
        return getById(getUserId());
    }

    /**
     * 获取个人资料
     *
     * @return User
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public User getInfoException() {
        User user = getInfo();
        if (user == null) {
            throw new CrmebException("用户信息不存在！");
        }

        if (!user.getStatus()) {
            throw new CrmebException("用户已经被禁用！");
        }
        return user;
    }

    /**
     * 获取当前用户id
     *
     * @return Integer
     */
    @Override
    public Integer getUserIdException() {
        Integer id = tokenComponet.getUserId();
        if (null == id) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }
        return id;
    }

    /**
     * 获取当前用户id
     *
     * @return Integer
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public Integer getUserId() {
        Integer id = tokenComponet.getUserId();
        if (null == id) {
            return 0;
        }
        return id;
    }

    /**
     * 按开始结束时间查询每日新增用户数量
     *
     * @param date String 时间范围
     * @return HashMap<String, Object>
     */
    @Override
    public Map<Object, Object> getAddUserCountGroupDate(String date) {
        Map<Object, Object> map = new HashMap<>();
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(id) as id", "left(create_time, 10) as create_time");
        if (StringUtils.isNotBlank(date)) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(date);
            queryWrapper.between("create_time", dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        queryWrapper.groupBy("left(create_time, 10)").orderByAsc("create_time");
        List<User> list = userDao.selectList(queryWrapper);
        if (list.size() < 1) {
            return map;
        }

        for (User user : list) {
            map.put(DateUtil.dateToStr(user.getCreateTime(), Constants.DATE_FORMAT_DATE), user.getId());
        }
        return map;
    }

    /**
     * 换绑手机号校验
     */
    @Override
    public Boolean updatePhoneVerify(UserBindingPhoneUpdateRequest request) {
        // 检测验证码
        checkValidateCode(request.getPhone(), request.getCaptcha());

        // 删除验证码
        redisUtil.delete(getValidateCodeRedisKey(request.getPhone()));

        User user = getInfoException();

        if (!user.getPhone().equals(request.getPhone())) {
            throw new CrmebException("手机号不是当前用户手机号");
        }

        return Boolean.TRUE;
    }

    /**
     * 换绑手机号
     */
    @Override
    public Boolean updatePhone(UserBindingPhoneUpdateRequest request) {
        // 检测验证码
        checkValidateCode(request.getPhone(), request.getCaptcha());

        // 删除验证码
        redisUtil.delete(getValidateCodeRedisKey(request.getPhone()));

        // 检测当前手机号是否已经是账号
        User user = getByPhone(request.getPhone());
        if (null != user) {
            throw new CrmebException("此手机号码已被注册");
        }

        // 查询手机号信息
        User bindUser = getInfoException();
        bindUser.setAccount(request.getPhone());
        bindUser.setPhone(request.getPhone());
        return updateById(bindUser);
    }

    /**
     * 用户中心
     * 
     * @return UserCenterResponse
     */
    @Override
    public UserCenterResponse getUserCenter() {
        User currentUser = getInfo();
        if (ObjectUtil.isNull(currentUser)) {
            throw new CrmebException("您的登录已过期，请先登录");
        }
        UserCenterResponse userCenterResponse = new UserCenterResponse();
        BeanUtils.copyProperties(currentUser, userCenterResponse);
        // 优惠券数量
        userCenterResponse.setCouponCount(storeCouponUserService.getUseCount(currentUser.getId()));
        // 收藏数量
        userCenterResponse.setCollectCount(storeProductRelationService.getCollectCountByUid(currentUser.getId()));

        userCenterResponse.setVip(false);
        if (userCenterResponse.getLevel() > 0) {
            SystemUserLevel systemUserLevel = systemUserLevelService.getByLevelId(currentUser.getLevel());
            if (ObjectUtil.isNotNull(systemUserLevel)) {
                userCenterResponse.setVip(true);
                userCenterResponse.setVipIcon(systemUserLevel.getIcon());
                userCenterResponse.setVipName(systemUserLevel.getName());
            }
        }
        // 充值开关
        String rechargeSwitch = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_RECHARGE_SWITCH);
        if (StrUtil.isNotBlank(rechargeSwitch)) {
            userCenterResponse.setRechargeSwitch(Boolean.valueOf(rechargeSwitch));
        }

        // 判断是否展示我的推广，1.分销模式是否开启
        String funcStatus = systemConfigService.getValueByKey(SysConfigConstants.CONFIG_KEY_BROKERAGE_FUNC_STATUS);
        if (!funcStatus.equals("1")) {
            userCenterResponse.setIsPromoter(false);
        }

        // 保存用户访问记录
        UserVisitRecord visitRecord = new UserVisitRecord();
        visitRecord.setDate(cn.hutool.core.date.DateUtil.date().toString("yyyy-MM-dd"));
        visitRecord.setUid(getUserId());
        visitRecord.setVisitType(4);
        userVisitRecordService.save(visitRecord);
        return userCenterResponse;
    }

    /**
     * 根据用户id获取用户列表 map模式
     *
     * @return HashMap<Integer, User>
     */
    @Override
    public HashMap<Integer, User> getMapListInUid(List<Integer> uidList) {
        List<User> userList = getListInUid(uidList);
        return getMapByList(userList);
    }

    /**
     * 根据用户id获取用户列表 map模式
     *
     * @return HashMap<Integer, User>
     * <AUTHOR>
     * @since 2020-04-28
     */
    private HashMap<Integer, User> getMapByList(List<User> list) {
        HashMap<Integer, User> map = new HashMap<>();
        if (null == list || list.size() < 1) {
            return map;
        }

        for (User user : list) {
            map.put(user.getId(), user);
        }

        return map;
    }

    /**
     * 重置连续签到天数
     *
     * @param userId Integer 用户id
     * <AUTHOR>
     * @since 2020-04-28
     */
    @Override
    public void repeatSignNum(Integer userId) {
        User user = new User();
        user.setId(userId);
        user.setSignNum(0);
        updateById(user);
    }

    /**
     * 会员标签
     *
     * @param id         String id
     * @param tagIdValue Integer 标签Id
     */
    @Override
    public Boolean tag(String id, String tagIdValue) {
        if (StrUtil.isBlank(id))
            throw new CrmebException("会员编号不能为空");
//        if (StrUtil.isBlank(tagIdValue))
//            throw new CrmebException("标签id不能为空");

        // 循环id处理
        List<Integer> idList = CrmebUtil.stringToArray(id);
        idList = idList.stream().distinct().collect(Collectors.toList());
        List<User> list = getListInUid(idList);
        if (CollUtil.isEmpty(list))
            throw new CrmebException("没有找到用户信息");
        if (list.size() < 1) {
            throw new CrmebException("没有找到用户信息");
        }
        for (User user : list) {
            user.setTagId(tagIdValue);
        }
        return updateBatchById(list);
    }

    /**
     * 根据用户id获取自己本身的推广用户
     *
     * @param userIdList List<Integer> 用户id集合
     * @return List<User>
     */
    @Override
    public List<Integer> getSpreadPeopleIdList(List<Integer> userIdList) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.select(User::getId); // 查询用户id
        lambdaQueryWrapper.in(User::getSpreadUid, userIdList); // xx的下线集合
        List<User> list = userDao.selectList(lambdaQueryWrapper);

        if (null == list || list.size() < 1) {
            return new ArrayList<>();
        }
        return list.stream().map(User::getId).distinct().collect(Collectors.toList());
    }

    /**
     * 根据用户id获取自己本身的推广用户
     */
    @Override
    public List<UserSpreadPeopleItemResponse> getSpreadPeopleList(
            List<Integer> userIdList, String keywords, String sortKey, String isAsc,
            PageParamRequest pageParamRequest) {

        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        Map<String, Object> map = new HashMap<>();
        map.put("userIdList", userIdList.stream().map(String::valueOf).distinct().collect(Collectors.joining(",")));
        if (StringUtils.isNotBlank(keywords)) {
            map.put("keywords", "%" + keywords + "%");
        }
        map.put("sortKey", "create_time");
        if (StringUtils.isNotBlank(sortKey)) {
            map.put("sortKey", sortKey);
        }
        map.put("sortValue", Constants.SORT_DESC);
        if (isAsc.toLowerCase().equals(Constants.SORT_ASC)) {
            map.put("sortValue", Constants.SORT_ASC);
        }

        return userDao.getSpreadPeopleList(map);
    }

    /**
     * 检测手机验证码
     */
    private void checkValidateCode(String phone, String value) {
        Object validateCode = redisUtil.get(getValidateCodeRedisKey(phone));
        if (validateCode == null) {
            throw new CrmebException("验证码已过期");
        }

        if (!validateCode.toString().equals(value)) {
            throw new CrmebException("验证码错误");
        }
    }

    /**
     * 检测手机验证码key
     *
     * @param phone String 手机号
     * @return String
     */
    @Override
    public String getValidateCodeRedisKey(String phone) {
        return SmsConstants.SMS_VALIDATE_PHONE + phone;
    }

    /**
     * 更新推广员推广数
     *
     * @param uid  uid
     * @param type add or sub
     */
    public Boolean updateSpreadCountByUid(Integer uid, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql("spread_count = spread_count + 1");
        } else {
            updateWrapper.setSql("spread_count = spread_count - 1");
        }
        updateWrapper.eq("id", uid);
        return update(updateWrapper);
    }

    /**
     * 添加/扣减佣金
     *
     * @param uid            用户id
     * @param price          金额
     * @param brokeragePrice 历史金额
     * @param type           类型：add—添加，sub—扣减
     * @return Boolean
     */
    @Override
    public Boolean operationBrokerage(Integer uid, BigDecimal price, BigDecimal brokeragePrice, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql(StrUtil.format("brokerage_price = brokerage_price + {}", price));
        } else {
            updateWrapper.setSql(StrUtil.format("brokerage_price = brokerage_price - {}", price));
            updateWrapper.last(StrUtil.format(" and (brokerage_price - {} >= 0)", price));
        }
        updateWrapper.eq("id", uid);
        updateWrapper.eq("brokerage_price", brokeragePrice);
        return update(updateWrapper);
    }

    /**
     * 添加/扣减余额
     *
     * @param uid      用户id
     * @param price    金额
     * @param nowMoney 历史金额
     * @param type     类型：add—添加，sub—扣减
     */
    @Override
    public Boolean operationNowMoney(Integer uid, BigDecimal price, BigDecimal nowMoney, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql(StrUtil.format("now_money = now_money + {}", price));
        } else {
            updateWrapper.setSql(StrUtil.format("now_money = now_money - {}", price));
            updateWrapper.last(StrUtil.format(" and (now_money - {} >= 0)", price));
        }
        updateWrapper.eq("id", uid);
        updateWrapper.eq("now_money", nowMoney);
        return update(updateWrapper);
    }

    /**
     * 添加/扣减积分
     *
     * @param uid         用户id
     * @param integral    积分
     * @param nowIntegral 历史积分
     * @param type        类型：add—添加，sub—扣减
     * @return Boolean
     */
    @Override
    public Boolean operationIntegral(Integer uid, Integer integral, Integer nowIntegral, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql(StrUtil.format("integral = integral + {}", integral));
        } else {
            updateWrapper.setSql(StrUtil.format("integral = integral - {}", integral));
            updateWrapper.last(StrUtil.format(" and (integral - {} >= 0)", integral));
        }
        updateWrapper.eq("id", uid);
        updateWrapper.eq("integral", nowIntegral);
        return update(updateWrapper);
    }

    /**
     * PC后台分销员列表
     *
     * @param keywords    搜索参数
     * @param dateLimit   时间参数
     * @param pageRequest 分页参数
     * @return PageInfo
     */
    @Override
    public PageInfo<User> getAdminSpreadPeopleList(String keywords, String dateLimit, PageParamRequest pageRequest) {
        Page<User> pageUser = PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        // id,头像，昵称，姓名，电话，推广用户数，推广订单数，推广订单额，佣金总金额，已提现金额，提现次数，未提现金额，上级推广人
        lqw.select(User::getId, User::getNickname, User::getRealName, User::getPhone, User::getAvatar,
                User::getSpreadCount, User::getBrokeragePrice, User::getSpreadUid, User::getPromoterTime);
        lqw.eq(User::getIsPromoter, true);
        if (StrUtil.isNotBlank(keywords)) {
            lqw.and(i -> i.eq(User::getId, keywords) // 用户账号
                    .or().like(User::getNickname, keywords) // 昵称
                    .or().like(User::getPhone, keywords)); // 手机号码
        }
        if (StrUtil.isNotBlank(dateLimit)) {
            dateLimitUtilVo dateLimitUtilVo = DateUtil.getDateLimit(dateLimit);
            lqw.between(User::getPromoterTime, dateLimitUtilVo.getStartTime(), dateLimitUtilVo.getEndTime());
        }
        lqw.orderByDesc(User::getId);
        List<User> userList = userDao.selectList(lqw);
        return CommonPage.copyPageInfo(pageUser, userList);
    }

    /**
     * 检测能否绑定关系
     *
     * @param user      当前用户
     * @param spreadUid 推广员Uid
     * @param type      用户类型:new-新用户，old—老用户
     * @return Boolean
     *         1.判断分销功能是否启用
     *         2.判断分销模式
     *         3.根据不同的分销模式校验
     *         4.指定分销，只有分销员才可以分销，需要spreadUid是推广员才可以绑定
     *         5.人人分销，可以直接绑定
     *         *推广关系绑定，下级不能绑定自己的上级为下级，A->B->A(❌)
     */
    public Boolean checkBingSpread(User user, Integer spreadUid, String type) {
        if (ObjectUtil.isNull(spreadUid)) {
            return false;
        }
        if (spreadUid <= 0 || user.getSpreadUid() > 0) {
            return false;
        }
        if (ObjectUtil.isNotNull(user.getId()) && user.getId().equals(spreadUid)) {
            return false;
        }
        // 判断分销功能是否启用
        String isOpen = systemConfigService.getValueByKey(Constants.CONFIG_KEY_STORE_BROKERAGE_IS_OPEN);
        if (StrUtil.isBlank(isOpen) || isOpen.equals("0")) {
            return false;
        }
        if (type.equals("old")) {
            // 判断分销关系绑定类型（所有、新用户）
            String bindType = systemConfigService.getValueByKey(Constants.CONFIG_KEY_DISTRIBUTION_TYPE);
            if (StrUtil.isBlank(bindType) || bindType.equals("1")) {
                return false;
            }
            if (user.getSpreadUid().equals(spreadUid)) {
                return false;
            }
        }
        // 查询推广员
        User spreadUser = getById(spreadUid);
        if (ObjectUtil.isNull(spreadUser) || !spreadUser.getStatus()) {
            return false;
        }
        // 指定分销不是推广员不绑定
        if (!spreadUser.getIsPromoter()) {
            return false;
        }
        // 下级不能绑定自己的上级为自己的下级
        if (ObjectUtil.isNotNull(user.getId()) && spreadUser.getSpreadUid().equals(user.getId())) {
            return false;
        }
        return true;
    }

    /**
     * 获取用户好友关系，spread_uid往下两级的用户信息
     *
     * @return List<User>
     */
    private List<User> getUserRelation(Integer userId) {
        List<User> userList = new ArrayList<>();
        User currUser = userDao.selectById(userId);
        if (currUser.getSpreadUid() > 0) {
            User spUser1 = userDao.selectById(currUser.getSpreadUid());
            if (null != spUser1) {
                userList.add(spUser1);
                if (spUser1.getSpreadUid() > 0) {
                    User spUser2 = userDao.selectById(spUser1.getSpreadUid());
                    if (null != spUser2) {
                        userList.add(spUser2);
                    }
                }
            }
        }
        return userList;
    }

    /**
     * 根据条件获取会员对应信息列表
     *
     * @param userId           用户id
     * @param type             0=消费记录，1=积分明细，2=签到记录，3=持有优惠券，4=余额变动，5=好友关系
     * @param pageParamRequest 分页参数
     * @return Object
     */
    @Override
    public Object getInfoByCondition(Integer userId, Integer type, PageParamRequest pageParamRequest) {
        switch (type) {
            case 0:
                return storeOrderService.findPaidListByUid(userId, pageParamRequest);
            case 1:
                AdminIntegralSearchRequest fmsq = new AdminIntegralSearchRequest();
                fmsq.setUid(userId);
                return userIntegralRecordService.findAdminList(fmsq, pageParamRequest);
            case 2:
                UserSign userSign = new UserSign();
                userSign.setUid(userId);
                return userSignService.getListByCondition(userSign, pageParamRequest);
            case 3:
                StoreCouponUserSearchRequest scur = new StoreCouponUserSearchRequest();
                scur.setUid(userId);
                return storeCouponUserService.findListByUid(userId, pageParamRequest);
            case 4:
                FundsMonitorSearchRequest fmsqq = new FundsMonitorSearchRequest();
                fmsqq.setUid(userId);
                fmsqq.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                return userBillService.getList(fmsqq, pageParamRequest);
            case 5:
                return getUserRelation(userId);
        }

        return new ArrayList<>();
    }

    /**
     * 会员详情顶部数据
     *
     * @param userId Integer 用户id
     * @return Object
     */
    @Override
    public TopDetail getTopDetail(Integer userId) {
        TopDetail topDetail = new TopDetail();
        User currentUser = userDao.selectById(userId);
        topDetail.setUser(currentUser);
        topDetail.setBalance(currentUser.getNowMoney());
        topDetail.setIntegralCount(currentUser.getIntegral());
        topDetail
                .setMothConsumeCount(storeOrderService.getSumPayPriceByUidAndDate(userId, Constants.SEARCH_DATE_MONTH));
        topDetail.setAllConsumeCount(storeOrderService.getSumPayPriceByUid(userId));
        topDetail.setMothOrderCount(storeOrderService.getOrderCountByUidAndDate(userId, Constants.SEARCH_DATE_MONTH));
        topDetail.setAllOrderCount(storeOrderService.getOrderCountByUid(userId));
        return topDetail;
    }

    /**
     * 根据推广级别和其他参数当前用户下的推广列表
     *
     * @param request 推广列表参数
     * @return 当前用户的推广人列表
     */
    @Override
    public PageInfo<User> getUserListBySpreadLevel(RetailShopStairUserRequest request,
            PageParamRequest pageParamRequest) {
        if (request.getType().equals(1)) {// 一级推广人
            return getFirstSpreadUserListPage(request, pageParamRequest);
        }
        if (request.getType().equals(2)) {// 二级推广人
            return getSecondSpreadUserListPage(request, pageParamRequest);
        }
        return getAllSpreadUserListPage(request, pageParamRequest);
    }

    // 分页获取一级推广员
    private PageInfo<User> getFirstSpreadUserListPage(RetailShopStairUserRequest request,
            PageParamRequest pageParamRequest) {
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getId, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount,
                User::getPayCount);
        queryWrapper.eq(User::getSpreadUid, request.getUid());
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(
                    e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getId, request.getNickName())
                            .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    // 分页获取二级推广员
    private PageInfo<User> getSecondSpreadUserListPage(RetailShopStairUserRequest request,
            PageParamRequest pageParamRequest) {
        // 先获取一级推广员
        List<User> firstUserList = getSpreadListBySpreadIdAndType(request.getUid(), 1);
        if (CollUtil.isEmpty(firstUserList)) {
            return new PageInfo<>(CollUtil.newArrayList());
        }
        List<Integer> userIds = firstUserList.stream().map(User::getId).distinct().collect(Collectors.toList());
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getId, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount,
                User::getPayCount);
        queryWrapper.in(User::getSpreadUid, userIds);
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(
                    e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getId, request.getNickName())
                            .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    // 分页获取所有推广员
    private PageInfo<User> getAllSpreadUserListPage(RetailShopStairUserRequest request,
            PageParamRequest pageParamRequest) {
        // 先所有一级推广员
        List<User> firstUserList = getSpreadListBySpreadIdAndType(request.getUid(), 0);
        if (CollUtil.isEmpty(firstUserList)) {
            return new PageInfo<>(CollUtil.newArrayList());
        }
        List<Integer> userIds = firstUserList.stream().map(User::getId).distinct().collect(Collectors.toList());
        Page<User> userPage = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(User::getId, User::getAvatar, User::getNickname, User::getIsPromoter, User::getSpreadCount,
                User::getPayCount);
        queryWrapper.in(User::getId, userIds);
        if (StrUtil.isNotBlank(request.getNickName())) {
            queryWrapper.and(
                    e -> e.like(User::getNickname, request.getNickName()).or().eq(User::getId, request.getNickName())
                            .or().eq(User::getPhone, request.getNickName()));
        }
        List<User> userList = userDao.selectList(queryWrapper);
        return CommonPage.copyPageInfo(userPage, userList);
    }

    /**
     * 根据推广级别和其他参数获取推广列表
     *
     * @param request 推广层级和推广时间参数
     * @return 推广订单列表
     */
    @Override
    public PageInfo<SpreadOrderResponse> getOrderListBySpreadLevel(RetailShopStairUserRequest request,
            PageParamRequest pageParamRequest) {
        // 获取推广人列表
        if (ObjectUtil.isNull(request.getType())) {
            request.setType(0);
        }
        // 查询条件：用户ID和订单类型
        LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
        if (request.getUid() != null) {
            queryWrapper.eq(UserCommissionRecord::getUid, request.getUid());
        }
        queryWrapper.eq(UserCommissionRecord::getType, BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
        queryWrapper.eq(UserCommissionRecord::getStatus, BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
        queryWrapper.orderByDesc(UserCommissionRecord::getCreateTime);
        
        Page<UserCommissionRecord> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<UserCommissionRecord> recordList = userCommissionRecordService.list(queryWrapper);
        PageInfo<UserCommissionRecord> recordPageInfo = CommonPage.copyPageInfo(page, recordList);
        
        List<SpreadOrderResponse> responseList = recordPageInfo.getList().stream().map(e -> {
            SpreadOrderResponse response = new SpreadOrderResponse();
            StoreOrder storeOrder = storeOrderService.getByOderId(e.getLinkId());
            if (storeOrder != null) {
                response.setId(storeOrder.getId());
                response.setOrderId(storeOrder.getOrderId());
                response.setRealName(storeOrder.getRealName());
                response.setUserPhone(storeOrder.getUserPhone());
                response.setPrice(e.getPrice());
                response.setUpdateTime(e.getUpdateTime());
            }
            return response;
        }).filter(response -> response.getId() != null).collect(Collectors.toList());

        return CommonPage.copyPageInfo(recordPageInfo, responseList);
    }

    /**
     * 获取推广人列表
     *
     * @param spreadUid 父Uid
     * @param type      类型 0 = 全部 1=一级推广人 2=二级推广人
     */
    private List<User> getSpreadListBySpreadIdAndType(Integer spreadUid, Integer type) {
        // 获取一级推广人
        List<User> userList = getSpreadListBySpreadId(spreadUid);
        if (CollUtil.isEmpty(userList))
            return userList;
        if (type.equals(1))
            return userList;
        // 获取二级推广人
        List<User> userSecondList = CollUtil.newArrayList();
        userList.forEach(user -> {
            List<User> childUserList = getSpreadListBySpreadId(user.getId());
            if (CollUtil.isNotEmpty(childUserList)) {
                userSecondList.addAll(childUserList);
            }
        });
        if (type.equals(2)) {
            return userSecondList;
        }
        userList.addAll(userSecondList);
        return userList;
    }

    /**
     * 获取推广人列表
     *
     * @param spreadUid 父Uid
     */
    private List<User> getSpreadListBySpreadId(Integer spreadUid) {
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(User::getSpreadUid, spreadUid);
        return userDao.selectList(queryWrapper);
    }

    /**
     * 根据用户id清除用户当前推广人
     *
     * @param userId 当前推广人id
     * @return 清除推广结果
     */
    @Override
    public boolean clearSpread(Integer userId) {
        User teamUser = getById(userId);
        User user = new User();
        user.setId(userId);
        user.setPath("/0/");
        user.setSpreadUid(0);
        user.setSpreadTime(null);
        Boolean execute = transactionTemplate.execute(e -> {
            userDao.updateById(user);
            if (teamUser.getSpreadUid() > 0) {
                updateSpreadCountByUid(teamUser.getSpreadUid(), "sub");
            }
            return Boolean.TRUE;
        });
        return execute;
    }

    /**
     * 推广人排行
     *
     * @param type             String 类型
     * @param pageParamRequest PageParamRequest 分页
     * @return List<User>
     */
    @Override
    public List<User> getTopSpreadPeopleListByDate(String type, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("count(spread_count) as spread_count, spread_uid")
                .gt("spread_uid", 0)
                .eq("status", true);
        if (StrUtil.isNotBlank(type)) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(type);
            queryWrapper.between("spread_time", dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        queryWrapper.groupBy("spread_uid").orderByDesc("spread_count");
        List<User> spreadVoList = userDao.selectList(queryWrapper);
        if (spreadVoList.size() < 1) {
            return null;
        }

        List<Integer> spreadIdList = spreadVoList.stream().map(User::getSpreadUid).collect(Collectors.toList());
        if (spreadIdList.size() < 1) {
            return null;
        }

        ArrayList<User> userList = new ArrayList<>();
        // 查询用户
        HashMap<Integer, User> userVoList = getMapListInUid(spreadIdList);

        // 解决排序问题
        for (User spreadVo : spreadVoList) {
            User user = new User();
            User userVo = userVoList.get(spreadVo.getSpreadUid());
            user.setId(spreadVo.getSpreadUid());
            user.setAvatar(userVo.getAvatar());
            user.setSpreadCount(spreadVo.getSpreadCount());
            if (StringUtils.isBlank(userVo.getNickname())) {
                user.setNickname(userVo.getPhone().substring(0, 2) + "****" + userVo.getPhone().substring(7));
            } else {
                user.setNickname(userVo.getNickname());
            }
            userList.add(user);
        }

        return userList;
    }

    /**
     * 推广人排行
     *
     * @param minPayCount int 最小消费次数
     * @param maxPayCount int 最大消费次数
     * @return Integer
     */
    @Override
    public Integer getCountByPayCount(int minPayCount, int maxPayCount) {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.between(User::getPayCount, minPayCount, maxPayCount);
        return Math.toIntExact(userDao.selectCount(lambdaQueryWrapper));
    }

    /**
     * 绑定推广关系（登录状态）
     * 
     * @param spreadUid 推广人id
     */
    @Override
    public void bindSpread(Integer spreadUid) {
        // 新用户会在注册的时候单独绑定，此处只处理登录用户
        if (ObjectUtil.isNull(spreadUid) || spreadUid == 0) {
            return;
        }
        User user = getInfo();
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("当前用户未登录,请先登录");
        }

        bindSpread(user, spreadUid);
    }

    private Boolean bindSpread(User user, Integer spreadUid) {

        Boolean checkBingSpread = checkBingSpread(user, spreadUid, "old");
        if (!checkBingSpread)
            return false;

        user.setSpreadUid(spreadUid);
        user.setSpreadTime(DateUtil.nowDateTime());

        Boolean execute = transactionTemplate.execute(e -> {
            updateById(user);
            updateSpreadCountByUid(spreadUid, "add");
            return Boolean.TRUE;
        });
        if (!execute) {
            logger.error(StrUtil.format("绑定推广人时出错，userUid = {}, spreadUid = {}", user.getId(), spreadUid));
        }
        return execute;

    }

    /**
     * 更新推广人
     *
     * @param request 请求参数
     * @return Boolean
     */
    @Override
    public Boolean editSpread(UserUpdateSpreadRequest request) {
        Integer userId = request.getUserId();
        Integer spreadUid = request.getSpreadUid();
        if (userId.equals(spreadUid)) {
            throw new CrmebException("上级推广人不能为自己");
        }
        User user = getById(userId);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }
        if (user.getSpreadUid().equals(spreadUid)) {
            throw new CrmebException("当前推广人已经是所选人");
        }
        Integer oldSprUid = user.getSpreadUid();

        User spreadUser = getById(spreadUid);
        if (ObjectUtil.isNull(spreadUser)) {
            throw new CrmebException("上级用户不存在");
        }
        if (spreadUser.getSpreadUid().equals(userId)) {
            throw new CrmebException("当前用户已是推广人的上级");
        }

        User tempUser = new User();
        tempUser.setId(userId);
        tempUser.setSpreadUid(spreadUid);
        tempUser.setSpreadTime(DateUtil.nowDateTime());
        Boolean execute = transactionTemplate.execute(e -> {
            updateById(tempUser);
            updateSpreadCountByUid(spreadUid, "add");
            if (oldSprUid > 0) {
                updateSpreadCountByUid(oldSprUid, "sub");
            }
            return Boolean.TRUE;
        });
        return execute;
    }

    /**
     * 更新用户积分
     *
     * @param user     用户
     * @param integral 积分
     * @param type     增加add、扣减sub
     * @return 更新后的用户对象
     */
    @Override
    public Boolean updateIntegral(User user, Integer integral, String type) {
        LambdaUpdateWrapper<User> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        if (type.equals("add")) {
            lambdaUpdateWrapper.set(User::getIntegral, user.getIntegral() + integral);
        } else {
            lambdaUpdateWrapper.set(User::getIntegral, user.getIntegral() - integral);
        }
        lambdaUpdateWrapper.eq(User::getId, user.getId());
        if (type.equals("sub")) {
            lambdaUpdateWrapper.apply(StrUtil.format(" integral - {} >= 0", integral));
        }
        return update(lambdaUpdateWrapper);
    }

    /**
     * 清除User Group id
     *
     * @param groupId 待清除的GroupId
     */
    @Override
    public void clearGroupByGroupId(String groupId) {
        LambdaUpdateWrapper<User> upw = Wrappers.lambdaUpdate();
        upw.set(User::getGroupId, "").eq(User::getGroupId, groupId);
        update(upw);
    }

    /**
     * 更新用户
     *
     * @param userRequest 用户参数
     * @return Boolean
     */
    @Override
    public Boolean updateUser(UserUpdateRequest userRequest) {
        User tempUser = getById(userRequest.getUid());
        User user = new User();
        BeanUtils.copyProperties(userRequest, user);
        if (!tempUser.getIsPromoter() && user.getIsPromoter()) {
            user.setPromoterTime(cn.hutool.core.date.DateUtil.date());
        }
        return updateById(user);
    }

    /**
     * 根据手机号查询用户
     * 
     * @param phone 用户手机号
     * @return 用户信息
     */
    @Override
    public User getByPhone(String phone) {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.eq(User::getPhone, phone);
        return userDao.selectOne(lqw);
    }

    /**
     * 后台修改用户手机号
     * 
     * @param id    用户uid
     * @param phone 手机号
     * @return Boolean
     */
    @Override
    public Boolean updateUserPhone(Integer id, String phone) {
        boolean matchPhone = ReUtil.isMatch(RegularConstants.PHONE_TWO, phone);
        if (!matchPhone) {
            throw new CrmebException("手机号格式错误，请输入正确得手机号");
        }
        User user = getById(id);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("对应用户不存在");
        }
        if (phone.equals(user.getPhone())) {
            throw new CrmebException("手机号与之前一致");
        }

        // 检测当前手机号是否已经是账号
        User tempUser = getByPhone(phone);
        if (ObjectUtil.isNotNull(tempUser)) {
            throw new CrmebException("此手机号码已被注册");
        }

        User newUser = new User();
        newUser.setId(id);
        newUser.setPhone(phone);
        newUser.setAccount(phone);
        return userDao.updateById(newUser) > 0;
    }

    /**
     * 根据昵称匹配用户，返回id集合
     * 
     * @param nikeName 需要匹配得昵称
     * @return List
     */
    @Override
    public List<Integer> findIdListLikeName(String nikeName) {
        LambdaQueryWrapper<User> lqw = Wrappers.lambdaQuery();
        lqw.select(User::getId);
        lqw.like(User::getNickname, nikeName);
        List<User> userList = userDao.selectList(lqw);
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }
        return userList.stream().map(User::getId).collect(Collectors.toList());
    }
    
    /**
     * 根据手机号查找用户ID列表
     * 
     * @param phone 手机号
     * @return 用户ID列表
     */
    @Override
    public List<Integer> findIdByPhone(String phone) {
        LambdaQueryWrapper<User> lqw = new LambdaQueryWrapper<>();
        lqw.select(User::getId);
        lqw.eq(User::getPhone, phone);
        List<User> userList = userDao.selectList(lqw);
        if (CollUtil.isEmpty(userList)) {
            return new ArrayList<>();
        }
        return userList.stream().map(User::getId).collect(Collectors.toList());
    }

    /**
     * 清除对应的用户等级
     *
     * @param levelId 等级id
     */
    @Override
    public Boolean removeLevelByLevelId(Integer levelId) {
        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
        luw.set(User::getLevel, MemberLevelConstants.Level.NONE);
        luw.eq(User::getLevel, levelId);
        return update(luw);
    }

    /**
     * 更新用户会员等级
     *
     * @param request request
     * @return Boolean
     */
    @Override
    public Boolean updateUserLevel(UpdateUserLevelRequest request) {
        User user = getById(request.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        SystemUserLevel systemUserLevel = systemUserLevelService.getByLevelId(request.getLevelId());
        if (ObjectUtil.isNull(systemUserLevel)) {
            throw new CrmebException("系统会员等级不存在，请先配置");
        }

        // 检查用户当前等级与目标等级是否相同
        Integer currentLevel = user.getLevel() != null ? user.getLevel() : 0;
        Integer targetLevel = systemUserLevel.getGrade();

        if (currentLevel.equals(targetLevel)) {
            throw new CrmebException("用户等级与修改前相同");
        }

        // 升级或降级处理
        String operationType = currentLevel < targetLevel ? "升级" : "降级";

        // 创建用户会员等级记录
        UserLevel newLevel = new UserLevel();
        newLevel.setUid(user.getId());
        newLevel.setLevelId(systemUserLevel.getId());
        newLevel.setGrade(systemUserLevel.getGrade());
        newLevel.setStatus(true);
        newLevel.setMark(StrUtil.format("尊敬的用户 {},在{}管理员{}会员等级成为{}", user.getNickname(),
                DateUtil.nowDateTimeStr(), operationType, systemUserLevel.getName()));
        newLevel.setDiscount(systemUserLevel.getDiscount());
        newLevel.setCreateTime(DateUtil.nowDateTime());

        return transactionTemplate.execute(e -> {
            // 更新用户等级为grade值而不是levelId
            updateLevel(user.getId(), systemUserLevel.getGrade());

            // 调整用户经验值到当前等级的最低门槛
            updateUserExperienceToLevelThreshold(user, systemUserLevel);

            userLevelService.save(newLevel);

            // 处理推广关系自动脱落逻辑
            userSpreadService.processLevelUpgradeSpreadDetachment(user.getId(), currentLevel, targetLevel);

            return Boolean.TRUE;
        });
    }

    /**
     * 获取用户总人数
     */
    @Override
    public Integer getTotalNum() {
        LambdaQueryWrapper<User> lqw = Wrappers.lambdaQuery();
        lqw.select(User::getId);
        return Math.toIntExact(userDao.selectCount(lqw));
    }

    /**
     * 根据日期段获取注册用户数量
     * 
     * @param startDate 日期
     * @param endDate   日期
     * @return UserOverviewResponse
     */
    @Override
    public Integer getRegisterNumByPeriod(String startDate, String endDate) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("id");
        wrapper.apply("date_format(create_time, '%Y-%m-%d') between {0} and {1}", startDate, endDate);
        return Math.toIntExact(userDao.selectCount(wrapper));
    }

    /**
     * 获取用户性别数据
     * 
     * @return List
     */
    @Override
    public List<User> getSexData() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("sex", "count(id) as pay_count");
        wrapper.groupBy("sex");
        return userDao.selectList(wrapper);
    }

    /**
     * 获取用户渠道数据
     * 
     * @return List
     */
    @Override
    public List<User> getChannelData() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("user_type", "count(id) as pay_count");
        wrapper.groupBy("user_type");
        return userDao.selectList(wrapper);
    }

    /**
     * 获取所有用户的id跟地址
     * 
     * @return List
     */
    @Override
    public List<User> findIdAndAddresList() {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("id", "addres");
        return userDao.selectList(wrapper);
    }

    /**
     * 修改个人资料
     * 
     * @param request 修改信息
     */
    @Override
    public Boolean editUser(UserEditRequest request) {
        User user = getInfo();
        user.setAvatar(systemAttachmentService.clearPrefix(request.getAvatar()));
        user.setNickname(request.getNickname());
        return updateById(user);
    }

    /**
     * 获取用户详情
     *
     * @param id 用户uid
     */
    @Override
    public User getInfoByUid(Integer id) {
        User user = getById(id);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }
        return user;
    }

    /**
     * 获取会员详情信息
     *
     * @param id 用户uid
     * @return MemberDetailResponse
     */
    @Override
    public MemberDetailResponse getMemberDetail(Integer id) {
        User user = getById(id);
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("用户不存在");
        }

        MemberDetailResponse response = new MemberDetailResponse();

        // 基本信息
        response.setUid(user.getId());
        response.setNickname(user.getNickname());
        response.setAvatar(user.getAvatar());
        response.setPhone(user.getPhone());
        response.setSex(user.getSex());
        response.setBirthday(user.getBirthday());
        response.setLevel(user.getLevel());
        response.setCreateTime(user.getCreateTime());
        response.setVipTime(user.getVipTime());
        response.setSvipTime(user.getSvipTime());
        response.setStatus(user.getStatus());
        response.setIsPromoter(user.getIsPromoter());
        response.setSpreadQrcode(user.getSpreadQrcode());
        response.setMark(user.getMark());
        response.setLastViewTime(user.getLastLoginTime());

        // 获取会员等级名称
        String memberLevel = getUserLevel(id);
        response.setMemberLevel(memberLevel);
        // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
        if (MemberLevelConstants.isSvip(user.getLevel())) {
            response.setSpreadNickname("养乐铺子");
        } else {
            // 获取推广人信息
            User spreadUser = getById(user.getSpreadUid());
            if (spreadUser != null) {
                response.setSpreadNickname(spreadUser.getNickname());
            } else {
                response.setSpreadNickname("无");
            }
        }

        // 获取分组信息
        if (!StringUtils.isBlank(user.getGroupId())) {
            response.setGroupName(userGroupService.getGroupNameInId(user.getGroupId()));
        }

        // 获取标签信息
        if (!StringUtils.isBlank(user.getTagId())) {
            response.setTagName(userTagService.getGroupNameInId(user.getTagId()));
            response.setTagId(user.getTagId()); // 设置tagId字段供前端使用
        }

        // 资产信息
        response.setNowMoney(user.getNowMoney());
        response.setExperience(user.getExperience());
        response.setWithdrawablePrice(user.getWithdrawablePrice());

        // 获取可用优惠券数量
        response.setAvailableCoupons(storeCouponUserService.getUseCount(id));

        // 获取推广用户数量
        response.setNormalSpreadCount(getSpreadCountByLevel(id, MemberLevelConstants.Level.NORMAL)); // 普通用户
        response.setVipSpreadCount(getSpreadCountByLevel(id, MemberLevelConstants.Level.VIP)); // VIP用户

        // 业绩概览 - 订单相关统计
        response.setTotalConsume(storeOrderService.getSumPayPriceByUid(id));
        response.setTotalOrders(storeOrderService.getOrderCountByUid(id));

        // 获取最近下单时间
        response.setLastOrderTime(getLastOrderTime(id));

        // 获取累计充值金额
        response.setTotalRecharge(getTotalRechargeAmount(id));

        // 获取累计返现金额
        response.setTotalBrokerage(getTotalBrokerageAmount(id));

        // 获取累计提现金额
        response.setTotalWithdraw(getTotalWithdrawAmount(id));

        // 获取累计奖励金
        response.setTotalBonus(getTotalBonusAmount(id));

        // 设置地区信息（可以根据实际需求从用户地址中提取）
        response.setRegion(user.getAddres());

        return response;
    }

    /**
     * 获取指定等级的推广用户数量
     *
     * @param uid 用户ID
     * @param level 等级
     * @return 推广用户数量
     */
    private Integer getSpreadCountByLevel(Integer uid, Integer level) {
        try {
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(User::getSpreadUid, uid)
                       .eq(User::getLevel, level)
                       .eq(User::getStatus, true);
            return Math.toIntExact(userDao.selectCount(queryWrapper));
        } catch (Exception e) {
            log.error("获取推广用户数量失败: uid={}, level={}, error={}", uid, level, e.getMessage());
            return 0;
        }
    }

    /**
     * 获取最近下单时间
     *
     * @param uid 用户ID
     * @return 最近下单时间
     */
    private Date getLastOrderTime(Integer uid) {
        try {
            LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(StoreOrder::getCreateTime)
                       .eq(StoreOrder::getUid, uid)
                       .eq(StoreOrder::getIsDel, false)
                       .orderByDesc(StoreOrder::getCreateTime)
                       .last("LIMIT 1");
            StoreOrder order = storeOrderService.getOne(queryWrapper);
            return order != null ? order.getCreateTime() : null;
        } catch (Exception e) {
            log.error("获取最近下单时间失败: uid={}, error={}", uid, e.getMessage());
            return null;
        }
    }

    /**
     * 获取累计充值金额
     *
     * @param uid 用户ID
     * @return 累计充值金额
     */
    private BigDecimal getTotalRechargeAmount(Integer uid) {
        try {
            LambdaQueryWrapper<UserBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserBill::getNumber)
                       .eq(UserBill::getUid, uid)
                       .eq(UserBill::getCategory, "now_money")
                       .eq(UserBill::getType, "recharge")
                       .eq(UserBill::getPm, 1); // 收入
            List<UserBill> bills = userBillService.list(queryWrapper);
            return bills.stream()
                       .map(UserBill::getNumber)
                       .filter(Objects::nonNull)
                       .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("获取累计充值金额失败: uid={}, error={}", uid, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取累计返现金额
     *
     * @param uid 用户ID
     * @return 累计返现金额
     */
    private BigDecimal getTotalBrokerageAmount(Integer uid) {
        try {
            LambdaQueryWrapper<UserBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserBill::getNumber)
                       .eq(UserBill::getUid, uid)
                       .eq(UserBill::getCategory, "now_money")
                       .in(UserBill::getType, "brokerage", "bonus")
                       .eq(UserBill::getPm, 1); // 收入
            List<UserBill> bills = userBillService.list(queryWrapper);
            return bills.stream()
                       .map(UserBill::getNumber)
                       .filter(Objects::nonNull)
                       .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("获取累计返现金额失败: uid={}, error={}", uid, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取累计提现金额
     *
     * @param uid 用户ID
     * @return 累计提现金额
     */
    private BigDecimal getTotalWithdrawAmount(Integer uid) {
        try {
            LambdaQueryWrapper<UserBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserBill::getNumber)
                       .eq(UserBill::getUid, uid)
                       .eq(UserBill::getCategory, "now_money")
                       .eq(UserBill::getType, "extract")
                       .eq(UserBill::getPm, 0); // 支出
            List<UserBill> bills = userBillService.list(queryWrapper);
            return bills.stream()
                       .map(UserBill::getNumber)
                       .filter(Objects::nonNull)
                       .reduce(BigDecimal.ZERO, BigDecimal::add);
        } catch (Exception e) {
            log.error("获取累计提现金额失败: uid={}, error={}", uid, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    /**
     * 获取累计奖励金
     *
     * @param uid 用户ID
     * @return 累计奖励金
     */
    private BigDecimal getTotalBonusAmount(Integer uid) {
        try {
            // 从UserBill表中查询奖励金类型的收入记录
            LambdaQueryWrapper<UserBill> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(UserBill::getNumber)
                       .eq(UserBill::getUid, uid)
                       .eq(UserBill::getCategory, "now_money")
                       .eq(UserBill::getType, "bonus") // 奖励金类型
                       .eq(UserBill::getPm, 1); // 收入
            List<UserBill> bills = userBillService.list(queryWrapper);

            BigDecimal totalFromBill = bills.stream()
                       .map(UserBill::getNumber)
                       .filter(Objects::nonNull)
                       .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 如果系统中有UserBonusRecord表，也可以从该表统计
            // 这里优先使用UserBill表的数据，因为它是账单的权威记录
            return totalFromBill;
        } catch (Exception e) {
            log.error("获取累计奖励金失败: uid={}, error={}", uid, e.getMessage());
            return BigDecimal.ZERO;
        }
    }

    @Override
    public Boolean updateIntegral(Integer uid, Integer integral) {
        // 用户积分 todo
        return null;
    }

    /**
     * 根据日期获取注册用户数量
     * 
     * @param date 日期
     * @return Integer
     */
    @Override
    public Integer getRegisterNumByDate(String date) {
        QueryWrapper<User> wrapper = Wrappers.query();
        wrapper.select("id");
        wrapper.apply("date_format(create_time, '%Y-%m-%d') = {0}", date);
        return Math.toIntExact(userDao.selectCount(wrapper));
    }

    /**
     * 更新用户等级
     *
     * @param uid   用户id
     * @param grade 会员等级数值 (0=无会员等级, 1=普通会员, 2=VIP会员, 3=SVIP会员)
     * @return Boolean
     */
    private Boolean updateLevel(Integer uid, Integer grade) {
        LambdaUpdateWrapper<User> luw = Wrappers.lambdaUpdate();
        luw.set(User::getLevel, grade);
        Date now = new Date();
        if (MemberLevelConstants.Level.VIP.equals( grade)) {
            luw.set(User::getVipTime, now);
        }else if (MemberLevelConstants.Level.SVIP.equals( grade)) {
            luw.set(User::getSvipTime, now);
            luw.set(User::getPromoterTime, now);
        }else if (MemberLevelConstants.Level.NORMAL.equals( grade)) {
            luw.set(User::getVipTime, null);
            luw.set(User::getSvipTime, null);
        }
        luw.eq(User::getId, uid);
        return update(luw);
    }

    /**
     * 调整用户经验值到当前等级的最低门槛
     *
     * @param user 用户信息
     * @param systemUserLevel 目标等级配置
     */
    private void updateUserExperienceToLevelThreshold(User user, SystemUserLevel systemUserLevel) {
        try {
            // 获取目标等级的经验值门槛
            Integer targetExperience = systemUserLevel.getExperience();
            if (targetExperience == null) {
                targetExperience = 0;
            }

            // 获取用户当前经验值
            Integer currentExperience = user.getExperience() != null ? user.getExperience() : 0;

            // 更新用户经验值到目标等级门槛
            LambdaUpdateWrapper<User> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(User::getExperience, targetExperience);
            updateWrapper.eq(User::getId, user.getId());
            update(updateWrapper);

            // 记录经验值变更
            UserExperienceRecord experienceRecord = new UserExperienceRecord();
            experienceRecord.setUid(user.getId());
            experienceRecord.setLinkId("0");
            experienceRecord.setLinkType("system");
            experienceRecord.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_ADD);
            experienceRecord.setTitle("管理员调整等级");
            experienceRecord.setExperience(targetExperience - currentExperience);
            experienceRecord.setBalance(targetExperience);
            experienceRecord.setMark(StrUtil.format("管理员手动调整用户等级为{}，经验值从{}调整到{}",
                systemUserLevel.getName(), currentExperience, targetExperience));
            experienceRecord.setStatus(ExperienceRecordConstants.EXPERIENCE_RECORD_STATUS_CREATE);
            experienceRecord.setCreateTime(new Date());
            experienceRecord.setUpdateTime(new Date());

            userExperienceRecordService.save(experienceRecord);

            logger.info("用户{}等级调整为{}，经验值从{}调整到{}",
                user.getId(), systemUserLevel.getName(), currentExperience, targetExperience);

        } catch (Exception e) {
            logger.error("调整用户{}经验值到等级门槛失败", user.getId(), e);
            // 经验值调整失败不影响等级更新，只记录错误日志
        }
    }

    /**
     * 获取会员用户列表
     * 
     * @return 会员用户列表
     */
    @Override
    public List<User> getMemberUserList() {
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(User::getIsPromoter, true); // 是会员
        lambdaQueryWrapper.eq(User::getStatus, true); // 状态正常
        return userDao.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取用户会员等级
     *
     * @param userId 用户ID
     * @return 会员等级名称
     */
    @Override
    public String getUserLevel(Integer userId) {
        User user = getById(userId);
        if (user == null) {
            return null;
        }

        // 使用常量类获取等级名称，确保一致性
        return MemberLevelConstants.getLevelName(user.getLevel());
    }

    /**
     * 根据关键字获取用户列表
     * 
     * @param keywords         关键字
     * @param pageParamRequest 分页参数
     * @return List<User>
     */
    @Override
    public List<User> getUserListByKeyword(String keywords, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(keywords)) {
            lambdaQueryWrapper.like(User::getNickname, keywords)
                    .or().like(User::getPhone, keywords)
                    .or().like(User::getRealName, keywords);
        }
        lambdaQueryWrapper.select(User::getId, User::getNickname, User::getAvatar, User::getPhone, User::getRealName);
        lambdaQueryWrapper.orderByDesc(User::getId);
        return userDao.selectList(lambdaQueryWrapper);
    }

    /**
     * 获取用户会员等级ID
     * 
     * @param userId 用户ID
     * @return 会员等级ID
     */
    @Override
    public Integer getUserLevelId(Integer userId) {
        User user = getById(userId);
        if (user == null) {
            return null;
        }
        return user.getLevel();
    }

    /**
     * 获取用户标签ID列表
     * 
     * @param userId 用户ID
     * @return 用户标签ID列表
     */
    @Override
    public List<Integer> getUserTagIds(Integer userId) {
        User user = getById(userId);
        if (user == null || StrUtil.isBlank(user.getTagId())) {
            return CollUtil.newArrayList();
        }
        return CrmebUtil.stringToArray(user.getTagId());
    }

    /**
     * 根据会员等级ID列表获取用户ID列表
     * 
     * @param levelIds 会员等级ID列表
     * @return 用户ID列表
     */
    @Override
    public List<Integer> getUserIdsByLevelIds(List<Integer> levelIds) {
        if (CollUtil.isEmpty(levelIds)) {
            return CollUtil.newArrayList();
        }
        LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(User::getLevel, levelIds);
        lambdaQueryWrapper.select(User::getId);
        List<User> userList = userDao.selectList(lambdaQueryWrapper);
        return userList.stream().map(User::getId).collect(Collectors.toList());
    }

    /**
     * 根据用户标签ID列表获取用户ID列表
     * 
     * @param tagIds 用户标签ID列表
     * @return 用户ID列表
     */
    @Override
    public List<Integer> getUserIdsByTagIds(List<Integer> tagIds) {
        if (CollUtil.isEmpty(tagIds)) {
            return CollUtil.newArrayList();
        }

        List<Integer> userIds = new ArrayList<>();
        // 由于标签是以逗号分隔存储的，需要循环查询每个标签ID
        for (Integer tagId : tagIds) {
            LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(User::getTagId, tagId.toString())
                    .or().like(User::getTagId, "," + tagId.toString())
                    .or().like(User::getTagId, tagId.toString() + ",");
            lambdaQueryWrapper.select(User::getId);
            List<User> userList = userDao.selectList(lambdaQueryWrapper);
            List<Integer> ids = userList.stream().map(User::getId).collect(Collectors.toList());
            userIds.addAll(ids);
        }

        // 去重
        return userIds.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 根据查询条件批量更新用户标签
     * 
     * @param request 查询条件
     * @param tagId   标签ID
     * @return Boolean
     */
    @Override
    public Boolean batchUpdateTagBySearchRequest(UserSearchRequest request, String tagId) {
        if (StrUtil.isBlank(tagId)) {
            throw new CrmebException("标签id不能为空");
        }

        // 先通过请求条件查询匹配的用户
        Map<String, Object> map = CollUtil.newHashMap();

        if (request.getIsPromoter() != null) {
            map.put("isPromoter", request.getIsPromoter() ? 1 : 0);
        }

        if (!StringUtils.isBlank(request.getGroupId())) {
            map.put("groupId", request.getGroupId());
        }

        if (!StringUtils.isBlank(request.getLabelId())) {
            String tagIdSql = CrmebUtil.getFindInSetSql("u.tag_id", request.getLabelId());
            map.put("tagIdSql", tagIdSql);
        }

        if (!StringUtils.isBlank(request.getLevel())) {
            map.put("level", request.getLevel());
        }

        if (StringUtils.isNotBlank(request.getUserType())) {
            map.put("userType", request.getUserType());
        }

        if (StringUtils.isNotBlank(request.getSex())) {
            map.put("sex", Integer.valueOf(request.getSex()));
        }

        if (StringUtils.isNotBlank(request.getCountry())) {
            map.put("country", request.getCountry());
            // 根据省市查询
            if (StrUtil.isNotBlank(request.getCity())) {
                request.setProvince(request.getProvince().replace("省", ""));
                request.setCity(request.getCity().replace("市", ""));
                map.put("addres", request.getProvince() + "," + request.getCity());
            }
        }

        if (StrUtil.isNotBlank(request.getPayCount())) {
            map.put("payCount", Integer.valueOf(request.getPayCount()));
        }

        if (request.getStatus() != null) {
            map.put("status", request.getStatus() ? 1 : 0);
        }

        // 添加成为会员时间查询条件
        if (StrUtil.isNotBlank(request.getMemberTimeStart())) {
            map.put("memberTimeStart", request.getMemberTimeStart());
        }

        if (StrUtil.isNotBlank(request.getMemberTimeEnd())) {
            map.put("memberTimeEnd", request.getMemberTimeEnd());
        }

        dateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());

        if (!StringUtils.isBlank(dateLimit.getStartTime())) {
            map.put("startTime", dateLimit.getStartTime());
            map.put("endTime", dateLimit.getEndTime());
            map.put("accessType", request.getAccessType());
        }
        if (request.getKeywords() != null) {
            map.put("keywords", request.getKeywords());
        }

        // 根据条件查询符合条件的用户
        List<User> userList = userDao.findAdminList(map);

        if (CollUtil.isEmpty(userList)) {
            return Boolean.TRUE; // 没有符合条件的用户，直接返回成功
        }

        // 批量更新用户标签
        for (User user : userList) {
            user.setTagId(tagId);
        }

        return updateBatchById(userList);
    }

    /**
     * 获取会员余额总计
     * 
     * @return 会员余额总计
     */
    @Override
    public BigDecimal getTotalBalance() {
        // 查询所有用户的余额总和
        return userDao.getTotalBalance();
    }

    /**
     * 获取VIP充值金额总计
     * 
     * @return VIP充值金额总计
     */
    @Override
    public BigDecimal getTotalRecharge() {
        return userDao.getTotalRecharge();
    }

    /**
     * 获取累计返现和差额总计
     * 
     * @return 累计返现和差额总计
     */
    @Override
    public BigDecimal getTotalRefund() {
        return userDao.getTotalRefund();
    }

    /**
     * 获取累计消费金额总计
     * 
     * @return 累计消费金额总计
     */
    @Override
    public BigDecimal getTotalConsume() {
        return userDao.getTotalConsume();
    }

    /**
     * 获取累计提现金额总计
     * 
     * @return 累计提现金额总计
     */
    @Override
    public BigDecimal getTotalWithdrawal() {
        return userDao.getTotalWithdrawal();
    }

    /**
     * 查询会员余额列表
     * 
     * @param keywords         搜索关键词
     * @param level        会员等级
     * @param pageParamRequest 分页参数
     * @return 会员余额列表
     */
    @Override
    public PageInfo<User> getUserBalanceList(String keywords, String level, PageParamRequest pageParamRequest) {
        Page<User> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<User> lambdaQueryWrapper = Wrappers.lambdaQuery();

        if (StrUtil.isNotBlank(keywords)) {
            lambdaQueryWrapper.and(i -> i
                    .like(User::getNickname, keywords) // 会员昵称
                    .or().like(User::getPhone, keywords) // 会员手机号
                    .or().like(User::getRealName, keywords) // 真实姓名
            );
        }

        if (StrUtil.isNotBlank(level)) {
            lambdaQueryWrapper.eq(User::getLoginType, level);
        }

        // 按余额倒序排序
        lambdaQueryWrapper.orderByDesc(User::getNowMoney);

        List<User> users = userDao.selectList(lambdaQueryWrapper);
        return CommonPage.copyPageInfo(page, users);
    }

    /**
     * 获取指定用户余额信息
     * 
     * @param uid 用户ID
     * @return 用户余额信息
     */
    @Override
    public UserBalanceResponse getUserBalanceInfo(Integer uid) {
        User user = getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        // 获取用户充值金额 - 会员充值
        BigDecimal rechargeAmount = userBillService.getSumBigDecimalByTitle(1, uid, Constants.USER_BILL_CATEGORY_MONEY,
                UserBillEnum.RECHARGE.getTitle(), null);
        if (rechargeAmount == null) {
            rechargeAmount = BigDecimal.ZERO;
        }

        // 获取退款
        BigDecimal refundAmount = userBillService.getSumBigDecimalByTitle(1, uid, Constants.USER_BILL_CATEGORY_MONEY, 
                UserBillEnum.REFUND.getTitle(), null);
        if (refundAmount == null) {
            refundAmount = BigDecimal.ZERO;
        }

        // 获取累计充值金额
        BigDecimal totalRechargeAmount = userBillService.getSumBigDecimalByTitle(1, uid, Constants.USER_BILL_CATEGORY_MONEY, 
                UserBillEnum.RECHARGE.getTitle(), null);
        if (totalRechargeAmount == null) {
            totalRechargeAmount = BigDecimal.ZERO;
        }

        // 获取累计佣金返现
        BigDecimal totalRefundAmount = user.getBrokeragePrice();

        // 获取累计消费金额
        BigDecimal totalConsumeAmount = userBillService.getSumBigDecimalByTitle(0, uid, Constants.USER_BILL_CATEGORY_MONEY, 
                UserBillEnum.CONSUME.getTitle(), null);
        if (totalConsumeAmount == null) {
            totalConsumeAmount = BigDecimal.ZERO;
        }

        // 获取累计提现金额,根据提现状态和用户查询提现总金额


        BigDecimal totalWithdrawalAmount = userExtractService.getWithdrawnByUserId(null, null, uid);


        UserBalanceResponse response = new UserBalanceResponse();
        response.setBalance(user.getNowMoney()); // 账号余额
        response.setRechargeAmount(rechargeAmount); // 充值金额
        response.setRefundAmount(refundAmount); // 返现&退款金额
        response.setTotalRechargeAmount(totalRechargeAmount); // 累计充值金额
        response.setTotalRefundAmount(totalRefundAmount); // 累计佣金返现
        response.setTotalConsumeAmount(totalConsumeAmount); // 累计消费金额
        response.setTotalWithdrawalAmount(totalWithdrawalAmount); // 累计提现
        
        return response;
    }

    /**
     * 查询会员余额简化列表（只包含用户、现有金额、充值金额、返现&退款、累计消费金额、累计提现金额）
     * 
     * @param keywords         搜索关键词
     * @param level            会员等级
     * @param pageParamRequest 分页参数
     * @return 会员余额简化列表
     */
    @Override
    public PageInfo<UserBalanceListResponse> getUserBalanceSimpleList(String keywords, String level, PageParamRequest pageParamRequest) {
        Page<User> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        LambdaQueryWrapper<User> lambdaQueryWrapper = Wrappers.lambdaQuery();

        if (StrUtil.isNotBlank(keywords)) {
            lambdaQueryWrapper.and(i -> i
                    .like(User::getNickname, keywords) // 会员昵称
                    .or().like(User::getPhone, keywords) // 会员手机号
                    .or().like(User::getRealName, keywords)); // 真实姓名
        }

        if (StrUtil.isNotBlank(level)) {
            lambdaQueryWrapper.eq(User::getLevel, level);
        }

        // 按余额倒序排序
        lambdaQueryWrapper.orderByDesc(User::getNowMoney);

        List<User> users = userDao.selectList(lambdaQueryWrapper);
        
        // 转换为简化响应对象
        List<UserBalanceListResponse> responseList = users.stream().map(user -> {
            UserBalanceListResponse response = new UserBalanceListResponse();
            response.setId(user.getId());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setPhone(user.getPhone());
            response.setLevel(String.valueOf(user.getLevel()));
            
//            // 设置会员等级
//            String levelName = "普通会员";
//            if (user.getLevel() != null) {
//                if (user.getLevel() == 1) {
//                    levelName = "VIP会员";
//                } else if (user.getLevel() == 2) {
//                    levelName = "SVIP会员";
//                }
//            }
//            response.setLevel(levelName);
            
            // 设置现有金额
            response.setNowMoney(user.getNowMoney());
            
            // 获取用户充值总金额
            BigDecimal recharge = userBillService.getSumBigDecimalByTitle(1, user.getId(), 
                    Constants.USER_BILL_CATEGORY_MONEY, UserBillEnum.RECHARGE.getTitle(), null);
            response.setRechargeAmount(recharge);
            
            // 获取退款和返现
            BigDecimal refund = userBillService.getSumBigDecimalByTitle(1, user.getId(), 
                    Constants.USER_BILL_CATEGORY_MONEY, UserBillEnum.REFUND.getTitle(), null);
            response.setRefundAmount(refund);
            
            // 获取用户消费总金额
            BigDecimal orderStatusSum = userBillService.getSumBigDecimalByTitle(0, user.getId(), 
                    Constants.USER_BILL_CATEGORY_MONEY, UserBillEnum.CONSUME.getTitle(), null);
            response.setConsumeAmount(orderStatusSum);
            
            // 获取提现金额
            //BigDecimal withdrawal = userBillService.getSumBigDecimalByTitle(0, user.getId(),
            //        Constants.USER_BILL_CATEGORY_MONEY, UserBillEnum.EXTRACT.getTitle(), null);

            BigDecimal withdrawal = userExtractService.getWithdrawnByUserId(null, null, user.getId());
            response.setWithdrawalAmount(withdrawal);
            
            return response;
        }).collect(Collectors.toList());
        
        return CommonPage.copyPageInfo(page, responseList);
    }

    /**
     * 添加/扣减可提现金额
     *
     * @param uid              用户id
     * @param price            金额
     * @param withdrawablePrice 历史可提现金额
     * @param type             类型：add—添加，sub—扣减
     * @return Boolean
     */
    @Override
    public Boolean operationWithdrawablePrice(Integer uid, BigDecimal price, BigDecimal withdrawablePrice, String type) {
        UpdateWrapper<User> updateWrapper = new UpdateWrapper<>();
        if (type.equals("add")) {
            updateWrapper.setSql(StrUtil.format("withdrawable_price = withdrawable_price + {}", price));
        } else {
            updateWrapper.setSql(StrUtil.format("withdrawable_price = withdrawable_price - {}", price));
            updateWrapper.last(StrUtil.format(" and (withdrawable_price - {} >= 0)", price));
        }
        updateWrapper.eq("id", uid);
        updateWrapper.eq("withdrawable_price", withdrawablePrice);
        return update(updateWrapper);
    }

    /**
     * 获取用户的所有下级用户ID列表
     *
     * @param userId 用户ID
     * @return 下级用户ID列表
     */
    @Override
    public List<Integer> getDownlineUserIds(Integer userId) {
        if (userId == null) {
            return new ArrayList<>();
        }

        try {
            LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.select(User::getId)
                       .eq(User::getSpreadUid, userId)
                       .eq(User::getStatus, true);

            List<User> downlineUsers = userDao.selectList(queryWrapper);
            return downlineUsers.stream()
                               .map(User::getId)
                               .collect(Collectors.toList());
        } catch (Exception e) {
            log.error(StrUtil.format("获取用户下级用户ID列表失败: userId={}, error={}", userId, e.getMessage()));
            return new ArrayList<>();
        }
    }

    /**
     * 根据配置代码获取系统参数配置
     */
    private SystemParamSetting getSystemParamSettingByCode(String configCode) {
        try {
            LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SystemParamSetting::getConfigCode, configCode);
            queryWrapper.eq(SystemParamSetting::getStatus, true); // 只查询启用的配置
            return systemParamSettingService.getOne(queryWrapper);
        } catch (Exception e) {
            logger.error("获取系统参数配置失败，configCode: {}", configCode, e);
            return null;
        }
    }
}
