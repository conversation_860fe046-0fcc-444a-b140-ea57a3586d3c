package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.response.BatchStockOperationResponse;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.core.common.request.*;
import com.ylpz.core.common.response.StoreProductInfoResponse;
import com.ylpz.core.common.response.StoreProductResponse;
import com.ylpz.core.common.response.StoreProductTabsHeader;
import com.ylpz.core.common.vo.MyRecord;
import com.github.pagehelper.PageInfo;
import org.json.JSONException;

import java.io.IOException;
import java.util.List;

/**
 * StoreProductService 接口
 */
public interface StoreProductService extends IService<StoreProduct> {

    /**
     * 获取产品列表Admin
     * @param request 筛选参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<StoreProductResponse> getAdminList(StoreProductSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 根据id集合获取商品信息
     * @param productIds id集合
     * @return 商品信息
     */
    List<StoreProduct> getListInIds(List<Integer> productIds);

    /**
     * 新增商品
     * @param request 商品请求对象
     * @return Boolean
     */
    Boolean save(StoreProductAddRequest request);

    /**
     * 更新商品信息
     * @param storeProductRequest 商品参数
     * @return 更新结果
     */
    Boolean update(StoreProductAddRequest storeProductRequest);

    /**
     * 产品详情
     * @param id 商品id
     * @return StoreProductResponse
     */
    StoreProductResponse getByProductId(Integer id);

    /**
     * 商品详情（管理端）
     * @param id 商品id
     * @return StoreProductInfoResponse
     */
    StoreProductInfoResponse getInfo(Integer id);

    /**
     * 获取tabsHeader对应数量
     * @return List
     */
    List<StoreProductTabsHeader> getTabsHeader();

    List<Integer> getSecondaryCategoryByProductId(String productId);

    /**
     * 删除商品
     * @param productId 商品id
     * @param type 类型：recycle——回收站 delete——彻底删除
     * @return 删除结果
     */
    Boolean deleteProduct(Integer productId, String type);

    /**
     * 恢复已删除商品
     * @param productId 商品id
     * @return 恢复结果
     */
    Boolean reStoreProduct(Integer productId);

    /**
     * 获取库存tabsHeader对应数量
     * @return  List
     */
    List<StoreProductTabsHeader> getStockTabsHeader();

    /**
     * 后台任务批量操作库存
     */
    void consumeProductStock();

    /**
     * 扣减库存任务操作
     * @param storeProductStockRequest 扣减库存参数
     * @return 执行结果
     */
    boolean doProductStock(StoreProductStockRequest storeProductStockRequest);

    /**
     * 添加/扣减库存
     * @param id 商品id
     * @param num 数量
     * @param type 类型：add—添加，sub—扣减
     */
    Boolean operationStock(Integer id, Integer num, String type);

    /**
     * 批量库存操作
     *
     * @param productIds    商品ID列表
     * @param operationType 操作类型：add—增加，sub—减少，set—设置为指定值
     * @param quantity      操作数量
     * @param remark        操作备注
     * @return 批量操作结果
     */
    BatchStockOperationResponse batchOperationStock(
            List<Integer> productIds, String operationType, Integer quantity, String remark);

    /**
     * 下架
     * @param id 商品id
     */
    Boolean offShelf(Integer id);

    /**
     * 上架
     * @param id 商品id
     * @return Boolean
     */
    Boolean putOnShelf(Integer id);

    /**
     * 批量上架商品
     * @param ids 商品id集合
     * @return Boolean
     */
    Boolean batchPutOnShelf(List<Integer> ids);

    /**
     * 批量下架商品
     * @param ids 商品id集合
     * @return Boolean
     */
    Boolean batchOffShelf(List<Integer> ids);
    
    /**
     * 批量将商品加入回收站
     * @param ids 商品id集合
     * @return Boolean
     */
    Boolean batchRecycle(List<Integer> ids);

    /**
     * 首页商品列表
     * @param type 类型 【1 精品推荐 2 热门榜单 3首发新品 4促销单品】
     * @param pageParamRequest 分页参数
     * @return CommonPage
     */
    List<StoreProduct> getIndexProduct(Integer type, PageParamRequest pageParamRequest);

    /**
     * 获取商品移动端列表
     * @param request 筛选参数
     * @param pageRequest 分页参数
     * @return List
     */
    List<StoreProduct> findH5List(ProductRequest request, PageParamRequest pageRequest);

    /**
     * 获取移动端商品详情
     * @param id 商品id
     * @return StoreProduct
     */
    StoreProduct getH5Detail(Integer id);

    /**
     * 获取购物车商品信息
     * @param productId 商品编号
     * @return StoreProduct
     */
    StoreProduct getCartByProId(Integer productId);

    /**
     * 验证用户是否有权限购买商品
     * @param productId 商品ID
     * @param uid 用户ID
     * @return 是否有权限购买
     */
    boolean validateUserPurchasePermission(Integer productId, Integer uid);

    /**
     * 根据日期获取新增商品数量
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    Integer getNewProductByDate(String date);

    /**
     * 获取所有未删除的商品
     * @return List<StoreProduct>
     */
    List<StoreProduct> findAllProductByNotDelte();

    /**
     * 模糊搜索商品名称
     * @param productName 商品名称
     * @return List
     */
    List<StoreProduct> likeProductName(String productName);

    /**
     * 警戒库存数量
     * @return Integer
     */
    Integer getVigilanceInventoryNum();

    /**
     * 销售中（上架）商品数量
     * @return Integer
     */
    Integer getOnSaleNum();

    /**
     * 未销售（仓库）商品数量
     * @return Integer
     */
    Integer getNotSaleNum();

    /**
     * 获取商品排行榜
     * @return List
     */
    List<StoreProduct> getLeaderboard();

    /**
     * 自动上架商品
     * 检查商品的预售时间，如果时间到达则自动上架
     */
    void autoOnShelf();

    PageInfo<StoreProductResponse> getAdminStockList(StoreProductStockSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 批量设置商品属性
     * @param request 批量设置请求对象
     * @return Boolean
     */
    Boolean batchSetting(StoreProductBatchSettingRequest request);

    /**
     * 根据关键词搜索商品列表
     * @param keywords 搜索关键词
     * @return 商品列表
     */
    List<StoreProduct> getProductListByKeywords(String keywords);
}
