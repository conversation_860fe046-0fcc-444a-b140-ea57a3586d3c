package com.ylpz.core.service.impl;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ylpz.core.common.utils.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.response.ProductCombinationStatusCount;
import com.ylpz.core.common.vo.ProductCombinationSimpleItemVO;
import com.ylpz.core.common.vo.ProductCombinationVO;
import com.ylpz.core.dao.ProductCombinationDetailMapper;
import com.ylpz.core.dao.ProductCombinationMapper;
import com.ylpz.core.dao.StoreProductDao;
import com.ylpz.core.service.CategoryService;
import com.ylpz.core.service.ProductCombinationService;
import com.ylpz.core.service.StoreProductAttrService;
import com.ylpz.core.service.StoreProductAttrValueService;
import com.ylpz.core.service.StoreProductDescriptionService;
import com.ylpz.core.service.StoreProductRelationService;
import com.ylpz.model.product.ProductCombination;
import com.ylpz.model.product.ProductCombinationDetail;
import com.ylpz.model.product.StoreProduct;

import cn.hutool.core.collection.CollUtil;

/**
 * 商品搭配Service实现类
 */
@Slf4j
@Service
public class ProductCombinationServiceImpl extends ServiceImpl<ProductCombinationMapper, ProductCombination>
    implements ProductCombinationService {

    @Resource
    private ProductCombinationDetailMapper combinationDetailMapper;

    @Resource
    private StoreProductDao storeProductDao;

    @Autowired
    private StoreProductAttrService storeProductAttrService;

    @Autowired
    private StoreProductAttrValueService storeProductAttrValueService;

    @Autowired
    private StoreProductDescriptionService storeProductDescriptionService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private StoreProductRelationService storeProductRelationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCombination(ProductCombinationVO vo) {
        // 创建商品搭配
        ProductCombination combination = new ProductCombination();
        BeanUtils.copyProperties(vo, combination);
        combination.setCreateTime(LocalDateTime.now());
        combination.setUpdateTime(LocalDateTime.now());

        // 设置默认值
        if (combination.getActualSales() == null) {
            combination.setActualSales(0);
        }
        if (combination.getAllowDiscount() == null) {
            combination.setAllowDiscount(0); // 默认不允许使用满减券
        }
        if (combination.getStoreStatus() == null) {
            combination.setStoreStatus(1); // 默认立即上架
        }
        if (combination.getSaleTime() == null) {
            combination.setSaleTime(0); // 默认实时开售
        }
        
        // 不使用stock字段，将其设置为0或null
        combination.setStock(0);

        save(combination);

        // 创建商品搭配明细
        if (CollUtil.isNotEmpty(vo.getProductItems())) {
            // 如果提供了商品项列表，直接使用
            saveDetails(combination.getId(), vo.getProductItems());
        } else if (CollUtil.isNotEmpty(vo.getProductIds())) {
            // 如果只提供了商品ID列表，查询商品信息后创建明细
            saveDetailsFromIds(combination.getId(), vo.getProductIds());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCombination(ProductCombinationVO vo) {
        // 更新商品搭配
        ProductCombination combination = getById(vo.getId());
        
        // 保留原始库存值，而不是从VO中更新
        Integer originalStock = combination.getStock();
        
        BeanUtils.copyProperties(vo, combination);
        
        // 恢复原始库存值，忽略VO中的库存值
        combination.setStock(originalStock);
        
        combination.setUpdateTime(LocalDateTime.now());
        Integer storeStatus = vo.getStoreStatus();
        // 如果是放入仓库，需要将saleTime设置为0
        if (storeStatus != null && storeStatus == 0) {
            combination.setSaleTime(0);
        }
        updateById(combination);

        // 删除原有明细
        LambdaQueryWrapper<ProductCombinationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCombinationDetail::getCombinationId, vo.getId());
        combinationDetailMapper.delete(wrapper);

        // 重新创建明细
        if (CollUtil.isNotEmpty(vo.getProductItems())) {
            // 如果提供了商品项列表，直接使用
            saveDetails(combination.getId(), vo.getProductItems());
        } else if (CollUtil.isNotEmpty(vo.getProductIds())) {
            // 如果只提供了商品ID列表，查询商品信息后创建明细
            saveDetailsFromIds(combination.getId(), vo.getProductIds());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCombination(Long id) {
        // 删除商品搭配
        removeById(id);

        // 删除商品搭配明细
        LambdaQueryWrapper<ProductCombinationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCombinationDetail::getCombinationId, id);
        combinationDetailMapper.delete(wrapper);
    }

    @Override
    public void updateStoreStatus(Long id, Integer storeStatus) {
        ProductCombination combination = new ProductCombination();
        combination.setId(id);
        combination.setStoreStatus(storeStatus);

        // 如果是定时上架，则设置定时开售时间
        if (storeStatus != null && storeStatus == 1){
            //立即上架
            combination.setStatus(1);
            combination.setStartTime(LocalDateTime.now());
            //转成数据类型
            combination.setSaleTime(DateUtil.getNowTime());
        }else{
            combination.setStatus(0);
            combination.setStartTime(null);
            combination.setEndTime(null);
        }

        combination.setUpdateTime(LocalDateTime.now());
        updateById(combination);
    }

    @Override
    public void updateAllowDiscount(Long id, Integer allowDiscount) {
        ProductCombination combination = new ProductCombination();
        combination.setId(id);
        combination.setAllowDiscount(allowDiscount);
        combination.setUpdateTime(LocalDateTime.now());
        updateById(combination);
    }

    @Override
    public ProductCombinationVO getCombinationDetail(Long id) {
        // 获取商品搭配
        ProductCombination combination = getById(id);
        if (combination == null) {
            return null;
        }

        // 获取商品搭配明细
        LambdaQueryWrapper<ProductCombinationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCombinationDetail::getCombinationId, id);
        List<ProductCombinationDetail> details = combinationDetailMapper.selectList(wrapper);

        // 使用 Java 8 Stream API 将 details 列表转换为 Map，以 productId 为键，detail 为值
        Map<Long, ProductCombinationDetail> detailMap = details.stream().filter(Objects::nonNull) // 过滤掉 null 值
            .collect(Collectors.toMap(ProductCombinationDetail::getProductId, detail -> detail));
        // 转换为VO
        ProductCombinationVO vo = new ProductCombinationVO();
        BeanUtils.copyProperties(combination, vo);
        vo.setProductIds(details.stream().map(ProductCombinationDetail::getProductId).collect(Collectors.toList()));

        // 带 StoreProduct 类的多条件查询
        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 类型搜索
        lambdaQueryWrapper.in(StoreProduct::getId, vo.getProductIds());
        lambdaQueryWrapper.orderByDesc(StoreProduct::getSort).orderByDesc(StoreProduct::getId);

        List<StoreProduct> storeProducts = storeProductDao.selectList(lambdaQueryWrapper);
        List<ProductCombinationSimpleItemVO> productItems = new ArrayList<>();
        
        // 判断是否有任意商品售罄
        boolean anySoldOut = false;

        for (StoreProduct product : storeProducts) {
            ProductCombinationSimpleItemVO item = new ProductCombinationSimpleItemVO();
            item.setProductId(product.getId().longValue());
            item.setImage(product.getImage());
            item.setName(product.getStoreName());
            item.setPrice(product.getPrice());
            
            // 检查单品是否售罄
            if (product.getStock() <= 0) {
                anySoldOut = true;
            }

            // 设置商品数量
            ProductCombinationDetail matchedDetail = detailMap.get(product.getId().longValue());
            if (matchedDetail != null) {
                item.setQuantity(matchedDetail.getQuantity());
            }

            productItems.add(item);
        }
        
        // 设置售罄状态
        vo.setIsSoldOut(anySoldOut);
        
        vo.setProductItems(productItems);
        return vo;
    }

    /**
     * 分页查询商品搭配列表
     * @param page 页码
     * @param size 每页数量
     * @param name 搭配名称，可为空
     * @param statusType 状态类型：0-全部，1-在售中，2-已售罄，3-仓库中
     * @return 分页结果
     */
    @Override
    public PageInfo<ProductCombinationVO> pageCombination(Integer page, Integer size, String name, Integer statusType) {
        // 分页查询商品搭配
        LambdaQueryWrapper<ProductCombination> wrapper = new LambdaQueryWrapper<>();
        
        // 搜索条件：名称
        if (StringUtils.hasText(name)) {
            wrapper.like(ProductCombination::getName, name);
        }
        
        // 对于状态查询的优化处理
        if (statusType != null && statusType > 0) {
            if (statusType == 3) {
                wrapper.eq(ProductCombination::getStatus, 0);
            } else {
                // 对于在售中(1)和已售罄(2)状态，都需要先筛选出上架的商品
                wrapper.eq(ProductCombination::getStatus, 1);
                
                // 先查询所有符合名称条件且上架的商品组合ID
                List<ProductCombination> eligibleCombinations = this.list(wrapper);
                
                if (!eligibleCombinations.isEmpty()) {
                    // 获取所有符合条件的组合ID
                    List<Long> combinationIds = eligibleCombinations.stream()
                        .map(ProductCombination::getId)
                        .collect(Collectors.toList());
                    
                    // 一次性查询所有相关组合的明细
                    LambdaQueryWrapper<ProductCombinationDetail> detailWrapper = new LambdaQueryWrapper<>();
                    detailWrapper.in(ProductCombinationDetail::getCombinationId, combinationIds);
                    List<ProductCombinationDetail> allDetails = combinationDetailMapper.selectList(detailWrapper);
                    
                    // 按组合ID分组
                    Map<Long, List<ProductCombinationDetail>> detailsByCombinationId = allDetails.stream()
                        .collect(Collectors.groupingBy(ProductCombinationDetail::getCombinationId));
                    
                    // 获取涉及的所有产品ID
                    List<Long> allProductIds = allDetails.stream()
                        .map(ProductCombinationDetail::getProductId)
                        .distinct()
                        .collect(Collectors.toList());
                    
                    // 一次性查询所有相关产品
                    Map<Long, StoreProduct> productMap = new HashMap<>();
                    if (!allProductIds.isEmpty()) {
                        LambdaQueryWrapper<StoreProduct> productWrapper = new LambdaQueryWrapper<>();
                        productWrapper.in(StoreProduct::getId, allProductIds);
                        List<StoreProduct> products = storeProductDao.selectList(productWrapper);
                        productMap = products.stream()
                            .collect(Collectors.toMap(p -> p.getId().longValue(), p -> p));
                    }
                    
                    // 存储在售中和售罄的组合ID
                    List<Long> onSaleIds = new ArrayList<>();
                    List<Long> soldOutIds = new ArrayList<>();
                    
                    // 判断每个组合是在售中还是已售罄
                    for (ProductCombination combination : eligibleCombinations) {
                        List<ProductCombinationDetail> details = detailsByCombinationId.getOrDefault(combination.getId(), Collections.emptyList());
                        
                        boolean anySoldOut = false;
                        for (ProductCombinationDetail detail : details) {
                            StoreProduct product = productMap.get(detail.getProductId());
                            if (product != null && product.getStock() <= 0 && product.getIsShow() && !product.getIsDel()) {
                                anySoldOut = true;
                                break;
                            }
                        }
                        
                        if (anySoldOut) {
                            soldOutIds.add(combination.getId());
                        } else if (!details.isEmpty()) {
                            onSaleIds.add(combination.getId());
                        }
                    }
                    
                    // 根据状态类型选择相应的ID列表
                    List<Long> idsToInclude = statusType == 1 ? onSaleIds : soldOutIds;
                    
                    // 重新设置查询条件
                    wrapper = new LambdaQueryWrapper<>();
                    
                    // 添加名称搜索条件
                    if (StringUtils.hasText(name)) {
                        wrapper.like(ProductCombination::getName, name);
                    }
                    
                    // 如果没有符合条件的ID，添加一个不存在的ID确保查询结果为空
                    if (idsToInclude.isEmpty()) {
                        idsToInclude.add(-1L);
                    }
                    
                    wrapper.in(ProductCombination::getId, idsToInclude);
                } else {
                    // 如果没有符合条件的上架商品，添加一个不可能满足的条件
                    wrapper.eq(ProductCombination::getId, -1L);
                }
            }
        }
        
        // 添加排序条件
        wrapper.orderByDesc(ProductCombination::getCreateTime);
        
        // 进行分页查询
        Page<ProductCombination> productCombinationPage = PageHelper.startPage(page, size);
        List<ProductCombination> list = this.list(wrapper);
        
        // 转换为VO对象 - 使用批量查询优化
        List<ProductCombinationVO> combinationVOS = new ArrayList<>();
        if (!list.isEmpty()) {
            // 获取所有组合ID
            List<Long> combinationIds = list.stream()
                .map(ProductCombination::getId)
                .collect(Collectors.toList());
            
            // 一次性查询所有详情
            LambdaQueryWrapper<ProductCombinationDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.in(ProductCombinationDetail::getCombinationId, combinationIds);
            List<ProductCombinationDetail> allDetails = combinationDetailMapper.selectList(detailWrapper);
            
            // 按组合ID分组
            Map<Long, List<ProductCombinationDetail>> detailsByCombinationId = allDetails.stream()
                .collect(Collectors.groupingBy(ProductCombinationDetail::getCombinationId));
            
            // 获取所有相关产品ID
            List<Long> allProductIds = allDetails.stream()
                .map(ProductCombinationDetail::getProductId)
                .distinct()
                .collect(Collectors.toList());
            
            // 一次性查询所有相关产品
            Map<Long, StoreProduct> productMap = new HashMap<>();
            if (!allProductIds.isEmpty()) {
                LambdaQueryWrapper<StoreProduct> productWrapper = new LambdaQueryWrapper<>();
                productWrapper.in(StoreProduct::getId, allProductIds);
                List<StoreProduct> products = storeProductDao.selectList(productWrapper);
                productMap = products.stream()
                    .collect(Collectors.toMap(p -> p.getId().longValue(), p -> p));
            }
            
            // 构建每个组合的VO对象
            for (ProductCombination combination : list) {
                ProductCombinationVO vo = new ProductCombinationVO();
                BeanUtils.copyProperties(combination, vo);
                
                // 获取该组合的详情
                List<ProductCombinationDetail> details = detailsByCombinationId.getOrDefault(combination.getId(), Collections.emptyList());
                List<Long> productIds = details.stream()
                    .map(ProductCombinationDetail::getProductId)
                    .collect(Collectors.toList());
                vo.setProductIds(productIds);
                
                // 构建产品项列表
                List<ProductCombinationSimpleItemVO> productItems = new ArrayList<>();
                boolean anySoldOut = false;
                
                for (ProductCombinationDetail detail : details) {
                    StoreProduct product = productMap.get(detail.getProductId());
                    if (product != null) {
                        ProductCombinationSimpleItemVO item = new ProductCombinationSimpleItemVO();
                        item.setProductId(product.getId().longValue());
                        item.setImage(product.getImage());
                        item.setName(product.getStoreName());
                        item.setPrice(product.getPrice());
                        item.setUnitName(product.getUnitName());
                        item.setQuantity(detail.getQuantity());
                        
                        // 检查单品是否售罄
                        if (product.getStock() <= 0) {
                            anySoldOut = true;
                        }
                        
                        productItems.add(item);
                    }
                }
                
                vo.setProductItems(productItems);
                vo.setIsSoldOut(anySoldOut);
                
                combinationVOS.add(vo);
            }
        }
        
        return CommonPage.copyPageInfo(productCombinationPage, combinationVOS);
    }

    public ProductCombinationVO getCombination(Long id) {
        // 获取商品搭配
        ProductCombination combination = getById(id);
        if (combination == null) {
            return null;
        }

        // 获取商品搭配明细
        LambdaQueryWrapper<ProductCombinationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ProductCombinationDetail::getCombinationId, id);
        List<ProductCombinationDetail> details = combinationDetailMapper.selectList(wrapper);

        // 使用 Java 8 Stream API 将 details 列表转换为 Map，以 productId 为键，detail 为值
        Map<Long, ProductCombinationDetail> detailMap = details.stream().filter(Objects::nonNull) // 过滤掉 null 值
            .collect(Collectors.toMap(ProductCombinationDetail::getProductId, detail -> detail));
        // 转换为VO
        ProductCombinationVO vo = new ProductCombinationVO();
        BeanUtils.copyProperties(combination, vo);
        vo.setProductIds(details.stream().map(ProductCombinationDetail::getProductId).collect(Collectors.toList()));

        LambdaQueryWrapper<StoreProduct> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        // 类型搜索
        lambdaQueryWrapper.in(CollUtil.isNotEmpty(vo.getProductIds()), StoreProduct::getId, vo.getProductIds());
        lambdaQueryWrapper.orderByDesc(StoreProduct::getSort).orderByDesc(StoreProduct::getId);

        List<StoreProduct> storeProducts = storeProductDao.selectList(lambdaQueryWrapper);
        List<ProductCombinationSimpleItemVO> productItems = new ArrayList<>();
        
        // 判断是否有任意商品售罄
        boolean anySoldOut = false;

        for (StoreProduct product : storeProducts) {
            ProductCombinationSimpleItemVO item = new ProductCombinationSimpleItemVO();
            item.setProductId(product.getId().longValue());
            item.setImage(product.getImage());
            item.setName(product.getStoreName());
            item.setPrice(product.getPrice());
            item.setUnitName(product.getUnitName());
            
            // 检查单品是否售罄
            if (product.getStock() <= 0) {
                anySoldOut = true;
            }
            
            // 设置商品数量
            ProductCombinationDetail matchedDetail = detailMap.get(product.getId().longValue());
            if (matchedDetail != null) {
                item.setQuantity(matchedDetail.getQuantity());
            }
            productItems.add(item);
        }
        
        // 设置售罄状态
        vo.setIsSoldOut(anySoldOut);
        
        vo.setProductItems(productItems);
        return vo;
    }

    @Override
    public void updateStatus(Long id, Integer status) {
        ProductCombination combination = new ProductCombination();
        combination.setId(id);
        combination.setStatus(status);
        combination.setUpdateTime(LocalDateTime.now());
        updateById(combination);
    }

    /**
     * 保存商品搭配明细 - 从商品项列表
     */
    private void saveDetails(Long combinationId, List<ProductCombinationSimpleItemVO> productItems) {
        if (CollUtil.isEmpty(productItems)) {
            return;
        }

        for (ProductCombinationSimpleItemVO item : productItems) {
            ProductCombinationDetail detail = new ProductCombinationDetail();
            detail.setCombinationId(combinationId);
            detail.setProductId(item.getProductId()); // 已经是Long类型
            // 获取商品数量，默认为1
            detail.setQuantity(item.getQuantity() != null ? item.getQuantity() : 1);
            detail.setCreateTime(LocalDateTime.now());
            detail.setUpdateTime(LocalDateTime.now());
            combinationDetailMapper.insert(detail);
        }
    }

    /**
     * 保存商品搭配明细 - 从商品ID列表
     */
    private void saveDetailsFromIds(Long combinationId, List<Long> productIds) {
        if (CollUtil.isEmpty(productIds)) {
            return;
        }

        for (Long productId : productIds) {
            ProductCombinationDetail detail = new ProductCombinationDetail();
            detail.setCombinationId(combinationId);
            detail.setProductId(productId);
            // 默认数量为1
            detail.setQuantity(1);
            detail.setCreateTime(LocalDateTime.now());
            detail.setUpdateTime(LocalDateTime.now());
            combinationDetailMapper.insert(detail);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDeleteCombinations(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        this.removeBatchByIds(ids);

        // 删除原有明细
        LambdaQueryWrapper<ProductCombinationDetail> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ProductCombinationDetail::getCombinationId, ids);
        combinationDetailMapper.delete(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpdateStoreStatus(List<Long> ids, Integer storeStatus) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        for (Long id : ids) {
            updateStoreStatus(id, storeStatus);
        }
    }

    @Override
    public ProductCombinationStatusCount getStatusCount() {
        ProductCombinationStatusCount statusCount = new ProductCombinationStatusCount();

        // 仓库中：未上架
        LambdaQueryWrapper<ProductCombination> inStorageWrapper = new LambdaQueryWrapper<>();
        inStorageWrapper.ne(ProductCombination::getStoreStatus, 1);
        statusCount.setInStorageCount(count(inStorageWrapper));

        // 获取所有上架的商品组合
        LambdaQueryWrapper<ProductCombination> allOnSaleWrapper = new LambdaQueryWrapper<>();
        allOnSaleWrapper.eq(ProductCombination::getStoreStatus, 1);
        List<ProductCombination> allOnSaleCombinations = this.list(allOnSaleWrapper);

        // 在售中数量（所有单品都有库存的组合）
        long onSaleCount = 0;
        // 售罄数量（任一单品库存为0的组合）
        long soldOutCount = 0;

        for (ProductCombination combination : allOnSaleCombinations) {
            // 获取商品组合的明细
            LambdaQueryWrapper<ProductCombinationDetail> detailWrapper = new LambdaQueryWrapper<>();
            detailWrapper.eq(ProductCombinationDetail::getCombinationId, combination.getId());
            List<ProductCombinationDetail> details = combinationDetailMapper.selectList(detailWrapper);
            
            // 检查每个单品是否有售罄
            boolean anySoldOut = false;
            for (ProductCombinationDetail detail : details) {
                StoreProduct product = storeProductDao.selectById(detail.getProductId());
                if (product != null && product.getStock() <= 0) {
                    anySoldOut = true;
                    break;
                }
            }
            
            // 如果有任何单品售罄，则计入售罄数量，否则计入在售数量
            if (anySoldOut) {
                soldOutCount++;
            } else if (!details.isEmpty()) {
                onSaleCount++;
            }
        }
        
        statusCount.setOnSaleCount(onSaleCount);
        statusCount.setSoldOutCount(soldOutCount);

        return statusCount;
    }
    
    @Override
    public List<ProductCombination> getByProductId(Integer productId) {
        // 获取商品关联的所有组合明细
        LambdaQueryWrapper<ProductCombinationDetail> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(ProductCombinationDetail::getProductId, productId);
        List<ProductCombinationDetail> details = combinationDetailMapper.selectList(detailWrapper);
        
        if (details.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取所有关联的组合ID
        List<Long> combinationIds = details.stream()
                .map(ProductCombinationDetail::getCombinationId)
                .distinct()
                .collect(Collectors.toList());
        
        // 查询组合信息
        LambdaQueryWrapper<ProductCombination> combinationWrapper = new LambdaQueryWrapper<>();
        combinationWrapper.in(ProductCombination::getId, combinationIds);
        //启用状态
        //combinationWrapper.eq(ProductCombination::getStatus, 1);
        List<ProductCombination> combinations = this.list(combinationWrapper);
        
        // 获取所有组合的明细信息
        Map<Long, List<ProductCombinationDetail>> detailMap = new HashMap<>();
        for (ProductCombinationDetail detail : details) {
            detailMap.computeIfAbsent(detail.getCombinationId(), k -> new ArrayList<>()).add(detail);
        }
        return combinations;
    }

    @Override
    public void autoAdjustCombinationStatus() {
        log.info("开始执行组合商品状态自动调整任务");

        try {
            LocalDateTime now = LocalDateTime.now();

            // 查询所有设置了售卖时间的组合商品
            LambdaQueryWrapper<ProductCombination> wrapper = new LambdaQueryWrapper<>();
            wrapper.isNotNull(ProductCombination::getStartTime)
                   .isNotNull(ProductCombination::getEndTime);

            List<ProductCombination> combinations = this.list(wrapper);

            if (CollUtil.isEmpty(combinations)) {
                log.info("没有找到设置了售卖时间的组合商品");
                return;
            }

            log.info("找到 {} 个设置了售卖时间的组合商品，开始检查状态", combinations.size());

            int enabledCount = 0;
            int disabledCount = 0;
            int unchangedCount = 0;

            for (ProductCombination combination : combinations) {
                try {
                    LocalDateTime startTime = combination.getStartTime();
                    LocalDateTime endTime = combination.getEndTime();
                    Integer currentStatus = combination.getStatus();

                    // 判断当前时间是否在售卖时间范围内
                    boolean shouldBeEnabled = (now.isEqual(startTime) || now.isAfter(startTime)) &&
                                            (now.isEqual(endTime) || now.isBefore(endTime));

                    Integer targetStatus = shouldBeEnabled ? 1 : 0;

                    // 如果状态需要改变
                    if (!targetStatus.equals(currentStatus)) {
                        ProductCombination updateCombination = new ProductCombination();
                        updateCombination.setId(combination.getId());
                        updateCombination.setStatus(targetStatus);
                        updateCombination.setUpdateTime(now);

                        updateById(updateCombination);

                        if (targetStatus == 1) {
                            enabledCount++;
                            log.info("组合商品自动启用成功 - ID: {}, 名称: {}, 售卖时间: {} ~ {}",
                                    combination.getId(), combination.getName(), startTime, endTime);
                        } else {
                            disabledCount++;
                            log.info("组合商品自动禁用成功 - ID: {}, 名称: {}, 售卖时间: {} ~ {}",
                                    combination.getId(), combination.getName(), startTime, endTime);
                        }
                    } else {
                        unchangedCount++;
                        log.debug("组合商品状态无需调整 - ID: {}, 名称: {}, 当前状态: {}",
                                combination.getId(), combination.getName(), currentStatus == 1 ? "启用" : "禁用");
                    }

                } catch (Exception e) {
                    log.error("调整组合商品状态失败 - ID: {}, 名称: {}, 错误: {}",
                            combination.getId(), combination.getName(), e.getMessage(), e);
                }
            }

            log.info("组合商品状态自动调整任务完成 - 总数: {}, 启用: {}, 禁用: {}, 无变化: {}",
                    combinations.size(), enabledCount, disabledCount, unchangedCount);

        } catch (Exception e) {
            log.error("组合商品状态自动调整任务执行异常", e);
        }
    }
}