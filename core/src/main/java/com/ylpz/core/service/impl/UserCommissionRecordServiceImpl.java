package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.dao.UserCommissionRecordDao;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.UserBillService;
import com.ylpz.model.user.UserCommissionRecord;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBill;
import com.ylpz.model.user.enums.UserBillEnum;
import com.ylpz.core.common.constants.Constants;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import cn.hutool.core.util.StrUtil;
import com.ylpz.core.common.vo.dateLimitUtilVo;
import com.ylpz.core.common.utils.DateUtil;
import cn.hutool.core.collection.CollUtil;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户佣金返现记录服务实现类
 */
@Service
public class UserCommissionRecordServiceImpl extends ServiceImpl<UserCommissionRecordDao, UserCommissionRecord>
    implements UserCommissionRecordService {

    private static final Logger logger = LoggerFactory.getLogger(UserCommissionRecordServiceImpl.class);

    @Resource
    private UserCommissionRecordDao userCommissionRecordDao;

    @Autowired
    private UserService userService;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 获取佣金返现记录列表
     *
     * @param linkId           订单ID
     * @param status           状态
     * @param userPhone        用户手机号
     * @param startTime        开始时间
     * @param endTime          结束时间
     * @param pageParamRequest 分页参数
     * @return 分页结果
     */
    @Override
    public PageInfo<UserCommissionRecord> getCommissionList(String linkId, Integer status,
                                                            String userPhone, Date startTime,
                                                            Date endTime, PageParamRequest pageParamRequest) {
        Page<UserCommissionRecord> page = PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        Map<String, Object> params = new HashMap<>();
        params.put("linkId", linkId);
        params.put("status", status);
        params.put("userPhone", userPhone);
        params.put("startTime", startTime);
        params.put("endTime", endTime);

        List<UserCommissionRecord> list = userCommissionRecordDao.getCommissionList(params);
        return new PageInfo<>(list);
    }


    /**
     * 统计已结算佣金返现总额和销售额
     * 只统计已结算状态的数据，退款订单不计入销售额内
     *
     * @return 统计数据，包含返现总额和销售额合计
     */
    @Override
    public Map<String, BigDecimal> getSettledCommissionAndSalesTotal() {
        Map<String, BigDecimal> result = new HashMap<>();

        // 已结算佣金返现总额
        BigDecimal settledCommission = userCommissionRecordDao.sumSettledCommission();

        // 已结算订单的销售额合计（不含退款订单）
        BigDecimal salesTotal = userCommissionRecordDao.sumSettledSalesAmount();

        result.put("settledCommission", settledCommission);
        result.put("salesTotal", salesTotal);

        return result;
    }
    
    /**
     * 获取用户佣金总额
     * 
     * @param uid 用户ID
     * @return 用户佣金总额
     */
    @Override
    public BigDecimal getUserCommissionAmount(Integer uid) {
        if (uid == null) {
            return BigDecimal.ZERO;
        }
        
        // 查询用户已结算的佣金总额
        LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCommissionRecord::getUid, uid);
        queryWrapper.eq(UserCommissionRecord::getType, 1); // 增加类型
        queryWrapper.eq(UserCommissionRecord::getStatus, 2); // 已结算状态
        
        List<UserCommissionRecord> records = userCommissionRecordDao.selectList(queryWrapper);
        
        if (records.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 计算总额
        return records.stream()
                .map(UserCommissionRecord::getPrice)
                .filter(price -> price != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 获取指定日期的佣金返现金额
     * 
     * @param date 日期字符串，格式为yyyy-MM-dd
     * @return 佣金返现金额
     */
    @Override
    public BigDecimal getBrokerageAmountByDate(String date) {
        if (date == null) {
            return BigDecimal.ZERO;
        }
        
        // 构建日期范围
        Date startTime = DateUtil.strToDate(date + " 00:00:00", "yyyy-MM-dd HH:mm:ss");
        Date endTime = DateUtil.strToDate(date + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
        
        // 查询条件：指定日期内增加的佣金记录
        LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.between(UserCommissionRecord::getCreateTime, startTime, endTime);
        queryWrapper.eq(UserCommissionRecord::getType, 1); // 增加类型
        
        List<UserCommissionRecord> records = userCommissionRecordDao.selectList(queryWrapper);
        
        if (records.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        // 计算总额
        return records.stream()
                .map(UserCommissionRecord::getPrice)
                .filter(price -> price != null)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 佣金解冻
     * 将达到解冻时间的佣金记录状态更新为已完成
     */
    @Override
    public void brokerageThaw() {
        logger.info("开始执行佣金解冻任务");

        try {
            // 查询所有冻结中且已到解冻时间的佣金记录
            LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
            // 状态为待结算(1)
            queryWrapper.eq(UserCommissionRecord::getStatus, 1);
            // 冻结期已结束
            queryWrapper.lt(UserCommissionRecord::getThawTime, new Date().getTime());

            // 查询需要解冻的记录
            List<UserCommissionRecord> records = userCommissionRecordDao.selectList(queryWrapper);

            if (CollUtil.isNotEmpty(records)) {
                for (UserCommissionRecord record : records) {
                    try {
                        // 使用事务处理佣金解冻
                        Boolean success = transactionTemplate.execute(status -> {
                            // 更新记录状态为已完成
                            record.setStatus(2);
                            record.setUpdateTime(new Date());
                            userCommissionRecordDao.updateById(record);

                            // 更新用户可提现金额
                            User user = userService.getById(record.getUid());
                            if (user != null && record.getType() == 1) { // 只处理增加类型的记录
                                // 更新用户可提现金额
                                userService.operationWithdrawablePrice(
                                    user.getId(),
                                    record.getPrice(),
                                    user.getWithdrawablePrice() == null ? BigDecimal.ZERO : user.getWithdrawablePrice(),
                                    "add"
                                );

                                // 创建UserBill记录
                                UserBill userBill = new UserBill();
                                userBill.setUid(user.getId());
                                userBill.setLinkId(record.getLinkId());
                                userBill.setPm(1); // 收入
                                userBill.setTitle(UserBillEnum.BROKERAGE.getTitle());
                                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                                userBill.setType(UserBillEnum.BROKERAGE.getType());
                                userBill.setNumber(record.getPrice());
                                // 计算解冻后的余额（当前余额 + 解冻金额）
                                BigDecimal newBalance = user.getNowMoney().add(record.getPrice());
                                userBill.setBalance(newBalance);
                                userBill.setMark("佣金解冻到账" + record.getPrice() + "元，订单号：" + record.getLinkId());
                                userBill.setStatus(1); // 有效
                                userBill.setCreateTime(new Date());
                                userBill.setUpdateTime(new Date());

                                // 保存UserBill记录
                                boolean billSaveResult = userBillService.save(userBill);
                                if (!billSaveResult) {
                                    logger.error("保存佣金解冻UserBill记录失败: userId={}, amount={}, linkId={}",
                                            user.getId(), record.getPrice(), record.getLinkId());
                                    throw new RuntimeException("保存佣金解冻账单记录失败");
                                }

                                logger.info("佣金解冻成功 - 用户ID: {}, 金额: {}, 订单号: {}",
                                        user.getId(), record.getPrice(), record.getLinkId());
                            }

                            return true;
                        });

                        if (!success) {
                            logger.error("佣金解冻事务执行失败 - 记录ID: {}, 用户ID: {}", record.getId(), record.getUid());
                        }

                    } catch (Exception e) {
                        logger.error("处理佣金解冻记录时发生异常 - 记录ID: {}, 用户ID: {}", record.getId(), record.getUid(), e);
                        // 继续处理下一条记录，不中断整个任务
                    }
                }

                logger.info("佣金解冻任务完成，共处理{}条记录", records.size());
            } else {
                logger.info("佣金解冻任务完成，无需解冻的记录");
            }
        } catch (Exception e) {
            logger.error("佣金解冻任务执行异常", e);
            throw e;
        }
    }

    /**
     * 根据关联ID查询佣金记录
     * 
     * @param linkId 关联ID
     * @param linkType 关联类型（保留参数以兼容旧代码，但不再使用）
     * @return 佣金记录
     */
    @Override
    public UserCommissionRecord getByLinkIdAndLinkType(String linkId, String linkType) {
        if (StrUtil.isBlank(linkId)) {
            return null;
        }
        
        LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserCommissionRecord::getLinkId, linkId);
        queryWrapper.orderByDesc(UserCommissionRecord::getCreateTime);
        return userCommissionRecordDao.selectOne(queryWrapper);
    }
}