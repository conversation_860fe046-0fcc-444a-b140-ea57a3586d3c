package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.DiscountConstants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.core.common.request.StoreDiscountRequest;
import com.ylpz.core.common.request.StoreDiscountSearchRequest;
import com.ylpz.core.common.response.StoreDiscountFrontResponse;
import com.ylpz.core.common.response.StoreDiscountInfoResponse;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreDiscountDao;
import com.ylpz.core.service.CategoryService;
import com.ylpz.core.service.StoreDiscountService;
import com.ylpz.core.service.StoreDiscountUserService;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.category.Category;
import com.ylpz.model.discount.StoreDiscount;
import com.ylpz.model.discount.StoreDiscountUser;
import com.ylpz.model.product.StoreProduct;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * StoreDiscountServiceImpl 接口实现
 */
@Service
public class StoreDiscountServiceImpl extends ServiceImpl<StoreDiscountDao, StoreDiscount>
    implements StoreDiscountService {

    @Resource
    private StoreDiscountDao dao;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreDiscountUserService storeDiscountUserService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private UserService userService;

    /**
     * 列表
     * 
     * @param request 请求参数
     * @param pageParamRequest 分页类参数
     * @return List<StoreDiscount>
     */
    @Override
    public List<StoreDiscount> getList(StoreDiscountSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 带 StoreDiscount 类的多条件查询
        LambdaQueryWrapper<StoreDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreDiscount::getIsDel, false);

        if (null != request.getType()) {
            lambdaQueryWrapper.eq(StoreDiscount::getType, request.getType());
        }

        if (null != request.getStatus()) {
            lambdaQueryWrapper.eq(StoreDiscount::getStatus, request.getStatus());
        }

        if (StrUtil.isNotBlank(request.getName())) {
            lambdaQueryWrapper.like(StoreDiscount::getName, request.getName());
        }

        lambdaQueryWrapper.orderByDesc(StoreDiscount::getSort).orderByDesc(StoreDiscount::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 保存折扣表
     * 
     * @param request StoreDiscountRequest 新增参数
     */
    @Override
    public boolean create(StoreDiscountRequest request) {
        StoreDiscount storeDiscount = new StoreDiscount();
        BeanUtils.copyProperties(request, storeDiscount);
        //if (storeDiscount.getIsLimited() && (storeDiscount.getTotal() == null || storeDiscount.getTotal() == 0)) {
        //    throw new CrmebException("请输入数量！");
        //}

        if (request.getUseType() > 1 && (StrUtil.isBlank(request.getPrimaryKey()))) {
            throw new CrmebException("请选择商品/分类！");
        }

        storeDiscount.setLastTotal(storeDiscount.getTotal());
        if (request.getIsForever() == null) {
            request.setIsForever(false);
        }
        if (!request.getIsForever()) {
            storeDiscount.setReceiveStartTime(DateUtil.nowDateTime()); // 开始时间设置为当前时间
        } else {
            if (storeDiscount.getReceiveStartTime() == null || storeDiscount.getReceiveEndTime() == null) {
                throw new CrmebException("请选择领取时间范围！");
            }

            int compareDate = DateUtil.compareDate(
                DateUtil.dateToStr(storeDiscount.getReceiveStartTime(), Constants.DATE_FORMAT),
                DateUtil.dateToStr(storeDiscount.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate > -1) {
                throw new CrmebException("请选择正确的领取时间范围！");
            }
        }

        // 非固定时间, 领取后多少天
        if (!request.getIsFixedTime()) {
            if (storeDiscount.getDay() == null || storeDiscount.getDay() == 0) {
                throw new CrmebException("请输入天数！");
            }
            storeDiscount.setUseStartTime(null);
            storeDiscount.setUseEndTime(null);
        }
        return save(storeDiscount);
    }

    /**
     * 获取详情
     * 
     * @param id Integer id
     * @return StoreDiscount
     */
    @Override
    public StoreDiscount getInfoException(Integer id) {
        // 获取折扣信息
        StoreDiscount storeDiscount = getById(id);
        checkException(storeDiscount);

        return storeDiscount;
    }

    /**
     * 检测当前折扣是否正常
     * 
     * @param storeDiscount StoreDiscount 折扣对象
     */
    private void checkException(StoreDiscount storeDiscount) {
        if (storeDiscount == null || storeDiscount.getIsDel() || !storeDiscount.getStatus()) {
            throw new CrmebException("折扣信息不存在或者已失效！");
        }

        // 看是否过期
        if (!(storeDiscount.getReceiveEndTime() == null || "".equals(storeDiscount.getReceiveEndTime()))) {
            // 非永久可领取
            String date = DateUtil.nowDateTimeStr();
            int result = DateUtil.compareDate(date,
                DateUtil.dateToStr(storeDiscount.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (result == 1) {
                // 过期了
                throw new CrmebException("已超过折扣领取最后期限！");
            }
        }

        // 看是否有剩余数量
        if (storeDiscount.getIsLimited()) {
            // 考虑到并发溢出的问题用大于等于
            if (storeDiscount.getLastTotal() < 1) {
                throw new CrmebException("此折扣已经被领完了！");
            }
        }
    }

    /**
     * 折扣详情
     * 
     * @param id Integer 获取可用折扣的商品id
     * @return StoreDiscountInfoResponse
     */
    @Override
    public StoreDiscountInfoResponse info(Integer id) {
        StoreDiscount storeDiscount = getById(id);
        if (ObjectUtil.isNull(storeDiscount) || storeDiscount.getIsDel() || !storeDiscount.getStatus()) {
            throw new CrmebException("折扣信息不存在或者已失效！");
        }

        List<StoreProduct> productList = null;
        List<Category> categoryList = null;
        if (StrUtil.isNotBlank(storeDiscount.getPrimaryKey()) && storeDiscount.getUseType() > 1) {
            List<Integer> primaryIdList = CrmebUtil.stringToArray(storeDiscount.getPrimaryKey());
            if (storeDiscount.getUseType() == 2) {
                productList = storeProductService.getListInIds(primaryIdList);
            }
            if (storeDiscount.getUseType() == 3) {
                categoryList = categoryService.getByIds(primaryIdList);
            }
        }

        StoreDiscountRequest discount = new StoreDiscountRequest();
        BeanUtils.copyProperties(storeDiscount, discount);
        discount.setIsForever(false);
        if (ObjectUtil.isNotNull(discount.getReceiveEndTime())) {
            discount.setIsForever(true);
        }

        return new StoreDiscountInfoResponse(discount, productList, categoryList);
    }

    /**
     * 根据折扣id获取
     * 
     * @param ids 折扣id集合
     * @return List<StoreDiscount>
     */
    @Override
    public List<StoreDiscount> getByIds(List<Integer> ids) {
        LambdaQueryWrapper<StoreDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StoreDiscount::getId, ids);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 扣减数量
     * 
     * @param id 折扣id
     * @param num 数量
     * @param isLimited 是否限量
     */
    @Override
    public Boolean deduction(Integer id, Integer num, Boolean isLimited) {
        UpdateWrapper<StoreDiscount> updateWrapper = new UpdateWrapper<>();
        if (isLimited) {
            updateWrapper.setSql(StrUtil.format("last_total = last_total - {}", num));
            updateWrapper.last(StrUtil.format(" and (last_total - {} >= 0)", num));
        } else {
            updateWrapper.setSql(StrUtil.format("last_total = last_total + {}", num));
        }
        updateWrapper.eq("id", id);
        return update(updateWrapper);
    }

    /**
     * 获取用户注册赠送新人折扣
     * 
     * @return List<StoreDiscount>
     */
    @Override
    public List<StoreDiscount> findRegisterList() {
        String dateStr = DateUtil.nowDate(Constants.DATE_FORMAT);
        LambdaQueryWrapper<StoreDiscount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreDiscount::getType, 2);
        lqw.eq(StoreDiscount::getStatus, true);
        lqw.eq(StoreDiscount::getIsDel, false);
        lqw.le(StoreDiscount::getReceiveStartTime, dateStr);
        List<StoreDiscount> list = dao.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        List<StoreDiscount> discountList = list.stream().filter(discount -> {
            // 是否限量
            if (discount.getIsLimited() && discount.getLastTotal() <= 0) {
                return false;
            }
            // 判断是否达到可领取时间
            if (ObjectUtil.isNotNull(discount.getReceiveStartTime())) {
                // 非永久可领取
                int result = DateUtil.compareDate(dateStr,
                    DateUtil.dateToStr(discount.getReceiveStartTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
                if (result == -1) {
                    // 未开始
                    return false;
                }
            }

            // 是否有领取结束时间
            if (ObjectUtil.isNotNull(discount.getReceiveEndTime())) {
                int compareDate = DateUtil.compareDate(dateStr,
                    DateUtil.dateToStr(discount.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
                if (compareDate > 0) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
        return discountList;
    }

    /**
     * 发送折扣列表
     * 
     * @param request 搜索分页参数
     * @return 折扣列表 PC管理员可发送折扣：手动领取类型，状态开启，且有剩余数量的折扣 只支持折扣名称模糊搜索
     */
    @Override
    public List<StoreDiscount> getSendList(SearchAndPageRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());

        LambdaQueryWrapper<StoreDiscount> lqw = new LambdaQueryWrapper<>();
        lqw.select(StoreDiscount::getId, StoreDiscount::getName, StoreDiscount::getDiscount, StoreDiscount::getMinPrice,
            StoreDiscount::getUseStartTime, StoreDiscount::getUseEndTime, StoreDiscount::getIsFixedTime,
            StoreDiscount::getDay, StoreDiscount::getIsLimited, StoreDiscount::getLastTotal);
        lqw.eq(StoreDiscount::getIsDel, false);
        if (ObjectUtil.isNotNull(request.getType())) {
            lqw.eq(StoreDiscount::getType, request.getType());
        }
        lqw.eq(StoreDiscount::getStatus, true);
        if (StrUtil.isNotBlank(request.getKeywords())) {
            lqw.like(StoreDiscount::getName, request.getKeywords());
        }
        lqw.and(o -> o.eq(StoreDiscount::getIsLimited, false).or().ge(StoreDiscount::getLastTotal, 0));
        lqw.and(o -> o.isNull(StoreDiscount::getReceiveEndTime).or().gt(StoreDiscount::getReceiveEndTime,
            DateUtil.nowDate(Constants.DATE_FORMAT)));
        lqw.orderByDesc(StoreDiscount::getSort, StoreDiscount::getId);
        return dao.selectList(lqw);
    }

    /**
     * 删除折扣
     * 
     * @param id 折扣id
     * @return Boolean
     */
    @Override
    public Boolean delete(Integer id) {
        StoreDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount) || discount.getIsDel()) {
            throw new CrmebException("折扣不存在");
        }
        discount.setIsDel(true);
        return dao.updateById(discount) > 0;
    }

    /**
     * 移动端折扣列表
     * 
     * @param type 类型，1-通用，2-商品，3-品类
     * @param productId 产品id，搜索产品指定折扣
     * @param pageParamRequest 分页参数
     * @return List<StoreDiscountFrontResponse>
     */
    @Override
    public List<StoreDiscountFrontResponse> getH5List(Integer type, Integer productId,
        PageParamRequest pageParamRequest) {
        // 获取折扣列表
        List<StoreDiscount> list = getListByReceive(type, productId, pageParamRequest);
        if (ObjectUtil.isNull(list)) {
            return null;
        }
        // 获取用户当前已领取未使用的折扣
        HashMap<Integer, StoreDiscountUser> discountUserMap = null;
        Integer userId = userService.getUserId();
        if (userId > 0) {
            discountUserMap = storeDiscountUserService.getMapByUserId(userId);
        }
        List<StoreDiscountFrontResponse> storeDiscountFrontResponseArrayList = new ArrayList<>();
        for (StoreDiscount storeDiscount : list) {
            StoreDiscountFrontResponse response = new StoreDiscountFrontResponse();
            BeanUtils.copyProperties(storeDiscount, response);

            if (userId > 0) {
                if (CollUtil.isNotEmpty(discountUserMap) && discountUserMap.containsKey(storeDiscount.getId())) {
                    response.setIsUse(true);
                }
            }

            if (response.getReceiveEndTime() == null) {
                response.setReceiveStartTime(null);
            }

            // 更改使用时间格式，去掉时分秒
            response
                .setUseStartTimeStr(DateUtil.dateToStr(storeDiscount.getUseStartTime(), Constants.DATE_FORMAT_DATE));
            response.setUseEndTimeStr(DateUtil.dateToStr(storeDiscount.getUseEndTime(), Constants.DATE_FORMAT_DATE));
            storeDiscountFrontResponseArrayList.add(response);
        }

        return storeDiscountFrontResponseArrayList;
    }

    /**
     * 修改折扣状态
     * 
     * @param id 折扣id
     * @param status 状态
     */
    @Override
    public Boolean updateStatus(Integer id, Boolean status) {
        StoreDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount)) {
            throw new CrmebException("折扣不存在");
        }
        if (discount.getStatus().equals(status)) {
            throw new CrmebException("折扣状态无需变更");
        }
        StoreDiscount storeDiscount = new StoreDiscount();
        storeDiscount.setId(id);
        storeDiscount.setStatus(status);
        return updateById(storeDiscount);
    }

    /**
     * 用户可领取的折扣
     * 
     * @return List<StoreDiscount>
     */
    private List<StoreDiscount> getListByReceive(Integer type, Integer productId, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        Date date = DateUtil.nowDateTime();
        // 带 StoreDiscount 类的多条件查询
        LambdaQueryWrapper<StoreDiscount> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreDiscount::getIsDel, false);
        lqw.eq(StoreDiscount::getStatus, 1);
        // 剩余数量大于0 或者不设置上限
        lqw.and(i -> i.gt(StoreDiscount::getLastTotal, 0).or().eq(StoreDiscount::getIsLimited, false));
        // 领取时间范围, 结束时间为null则是不限时
        lqw.and(i -> i.isNull(StoreDiscount::getReceiveEndTime)
            .or(p -> p.lt(StoreDiscount::getReceiveStartTime, date).gt(StoreDiscount::getReceiveEndTime, date)));
        // 用户使用时间范围，结束时间为null则是不限时
        lqw.and(i -> i.isNull(StoreDiscount::getUseEndTime).or(p -> p.gt(StoreDiscount::getUseEndTime, date)));
        lqw.eq(StoreDiscount::getType, 1);
        if (productId > 0) {
            // 有商品id 通用折扣可以领取，商品折扣可以领取，分类折扣可以领取
            getPrimaryKeySql(lqw, productId.toString());
        }
        switch (type) {
            case 1:
                lqw.eq(StoreDiscount::getUseType, DiscountConstants.DISCOUNT_USE_TYPE_COMMON);
                break;
            case 2:
                lqw.eq(StoreDiscount::getUseType, DiscountConstants.DISCOUNT_USE_TYPE_PRODUCT);
                break;
            case 3:
                lqw.eq(StoreDiscount::getUseType, DiscountConstants.DISCOUNT_USE_TYPE_CATEGORY);
                break;
        }

        lqw.orderByDesc(StoreDiscount::getSort).orderByDesc(StoreDiscount::getId);
        return dao.selectList(lqw);
    }

    private void getPrimaryKeySql(LambdaQueryWrapper<StoreDiscount> lambdaQueryWrapper, String productIdStr) {
        if (StrUtil.isBlank(productIdStr)) {
            return;
        }

        List<Integer> categoryIdList = storeProductService.getSecondaryCategoryByProductId(productIdStr);
        lambdaQueryWrapper.and(i -> i.and(
            // 通用折扣 商品折扣 品类折扣
            t -> t.eq(StoreDiscount::getUseType, 1).or(
                p -> p.eq(StoreDiscount::getUseType, 2).apply(CrmebUtil.getFindInSetSql("primary_key", productIdStr)))
                .or(c -> c.eq(StoreDiscount::getUseType, 3)
                    .apply(CrmebUtil.getFindInSetSql("primary_key", (ArrayList<Integer>)categoryIdList)))));
    }
}