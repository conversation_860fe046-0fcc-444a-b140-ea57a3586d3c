package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.vo.OrderInfoDetailVo;
import com.ylpz.core.common.vo.StoreOrderInfoVo;
import com.ylpz.core.dao.StoreOrderInfoDao;
import com.ylpz.core.service.StoreOrderInfoService;
import com.ylpz.core.service.StoreProductAttrValueService;
import com.ylpz.core.service.StoreProductReplyService;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.model.order.StoreOrderInfo;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.model.product.StoreProductAttrValue;

import cn.hutool.json.JSONUtil;

/**
 * StoreOrderInfoServiceImpl 接口实现
 */
@Service
public class StoreOrderInfoServiceImpl extends ServiceImpl<StoreOrderInfoDao, StoreOrderInfo>
    implements StoreOrderInfoService {

    @Resource
    private StoreOrderInfoDao dao;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreProductAttrValueService storeProductAttrValueService;

    /**
     * 根据id集合查询数据，返回 map
     * 
     * @param orderList List<Integer> id集合
     * <AUTHOR>
     * @since 2020-04-17
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public HashMap<Integer, List<StoreOrderInfoVo>> getMapInId(List<Integer> orderList) {
        HashMap<Integer, List<StoreOrderInfoVo>> map = new HashMap<>();
        if (orderList.size() < 1) {
            return map;
        }
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.in(StoreOrderInfo::getOrderId, orderList);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return map;
        }
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            // 解析商品详情JSON
            StoreOrderInfoVo StoreOrderInfoVo = new StoreOrderInfoVo();
            BeanUtils.copyProperties(storeOrderInfo, StoreOrderInfoVo, "info");
            if (storeOrderInfo.getInfo() != null && JSONUtil.isJson(storeOrderInfo.getInfo())) {
                StoreOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            } else {
                // 如果info为空或不是JSON格式，通过已知信息查询商品信息
                OrderInfoDetailVo orderInfoDetailVo = new OrderInfoDetailVo();

                // 从订单信息中获取已知数据
                orderInfoDetailVo.setProductId(storeOrderInfo.getProductId());
                orderInfoDetailVo.setProductName(storeOrderInfo.getProductName());
                orderInfoDetailVo.setAttrValueId(storeOrderInfo.getAttrValueId());
                orderInfoDetailVo.setImage(storeOrderInfo.getImage());
                orderInfoDetailVo.setSku(storeOrderInfo.getSku());
                orderInfoDetailVo.setPrice(storeOrderInfo.getPrice());
                orderInfoDetailVo.setPayNum(storeOrderInfo.getPayNum());
                orderInfoDetailVo.setVipPrice(storeOrderInfo.getVipPrice());
                orderInfoDetailVo.setMemberDiscountAmount(storeOrderInfo.getMemberDiscountAmount());
                orderInfoDetailVo.setDiscountAmount(storeOrderInfo.getDiscountAmount());
                orderInfoDetailVo.setSubtotalAmount(storeOrderInfo.getSubtotalAmount());
                orderInfoDetailVo.setCost(storeOrderInfo.getCost());

                // 通过商品ID查询商品信息补充缺失字段
                if (storeOrderInfo.getProductId() != null) {
                    StoreProduct product = storeProductService.getById(storeOrderInfo.getProductId());
                    if (product != null) {
                        // 设置获得积分
                        orderInfoDetailVo.setGiveIntegral(product.getGiveIntegral());

                        // 设置是否单独分佣
                        orderInfoDetailVo.setIsSub(product.getIsSub());

                        // 设置运费模板ID
                        orderInfoDetailVo.setTempId(product.getTempId());
                    }
                }

                // 如果有规格ID，查询规格信息
                if (storeOrderInfo.getAttrValueId() != null) {
                    StoreProductAttrValue attrValue =
                        storeProductAttrValueService.getById(storeOrderInfo.getAttrValueId());
                    if (attrValue != null) {
                        // 设置重量
                        orderInfoDetailVo.setWeight(attrValue.getWeight());

                        // 设置体积
                        orderInfoDetailVo.setVolume(attrValue.getVolume());
                    }
                }

                // 设置默认商品类型为普通商品
                orderInfoDetailVo.setProductType(0);

                // 设置是否评价
                orderInfoDetailVo.setIsReply(storeOrderInfo.getIsReply() ? 1 : 0);

                StoreOrderInfoVo.setInfo(orderInfoDetailVo);
            }

            if (map.containsKey(storeOrderInfo.getOrderId())) {
                map.get(storeOrderInfo.getOrderId()).add(StoreOrderInfoVo);
            } else {
                List<com.ylpz.core.common.vo.StoreOrderInfoVo> storeOrderInfoVoList = new ArrayList<>();
                storeOrderInfoVoList.add(StoreOrderInfoVo);
                map.put(storeOrderInfo.getOrderId(), storeOrderInfoVoList);
            }
        }
        return map;
    }

    /**
     * 根据id集合查询数据，返回 map
     * 
     * @param orderId Integer id
     * <AUTHOR>
     * @since 2020-04-17
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfoVo> getOrderListByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return null;
        }

        List<StoreOrderInfoVo> storeOrderInfoVoList = new ArrayList<>();
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            // 解析商品详情JSON
            StoreOrderInfoVo storeOrderInfoVo = new StoreOrderInfoVo();
            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfoVo, "info");

            // 判断info是否为空或不是JSON格式
            if (storeOrderInfo.getInfo() != null && JSONUtil.isJson(storeOrderInfo.getInfo())) {
                storeOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            } else {
                // 如果info为空或不是JSON格式，通过已知信息查询商品信息
                OrderInfoDetailVo orderInfoDetailVo = new OrderInfoDetailVo();

                // 从订单信息中获取已知数据
                orderInfoDetailVo.setProductId(storeOrderInfo.getProductId());
                orderInfoDetailVo.setProductName(storeOrderInfo.getProductName());
                orderInfoDetailVo.setAttrValueId(storeOrderInfo.getAttrValueId());
                orderInfoDetailVo.setImage(storeOrderInfo.getImage());
                orderInfoDetailVo.setSku(storeOrderInfo.getSku());
                orderInfoDetailVo.setPrice(storeOrderInfo.getPrice());
                orderInfoDetailVo.setPayNum(storeOrderInfo.getPayNum());
                orderInfoDetailVo.setVipPrice(storeOrderInfo.getVipPrice());
                orderInfoDetailVo.setMemberDiscountAmount(storeOrderInfo.getMemberDiscountAmount());
                orderInfoDetailVo.setDiscountAmount(storeOrderInfo.getDiscountAmount());
                orderInfoDetailVo.setSubtotalAmount(storeOrderInfo.getSubtotalAmount());
                orderInfoDetailVo.setCost(storeOrderInfo.getCost());
                // 设置重量
                orderInfoDetailVo.setWeight(storeOrderInfo.getWeight());
                // 设置体积
                orderInfoDetailVo.setVolume(storeOrderInfo.getVolume());
                // 设置获得积分
                orderInfoDetailVo.setGiveIntegral(storeOrderInfo.getGiveIntegral());

                // 设置是否单独分佣
                orderInfoDetailVo.setIsSub(storeOrderInfo.getIsSub());

                // 通过商品ID查询商品信息补充缺失字段
                if (storeOrderInfo.getProductId() != null) {
                    StoreProduct product = storeProductService.getById(storeOrderInfo.getProductId());
                    if (product != null) {
                        // 设置运费模板ID
                        orderInfoDetailVo.setTempId(product.getTempId());
                    }
                }

                // 设置默认商品类型为普通商品
                orderInfoDetailVo.setProductType(0);

                // 设置是否评价
                orderInfoDetailVo.setIsReply(storeOrderInfo.getIsReply() ? 1 : 0);
                storeOrderInfoVo.setInfo(orderInfoDetailVo);
            }

            // 无论是否从JSON解析，都需要更新评价状态
            storeOrderInfoVo.getInfo().setIsReply(
                storeProductReplyService.isReply(storeOrderInfoVo.getUnique(), storeOrderInfoVo.getOrderId()) ? 1 : 0);
            storeOrderInfoVoList.add(storeOrderInfoVo);
        }
        return storeOrderInfoVoList;
    }

    /**
     * 根据id集合查询数据，返回 map
     * 
     * @param orderId 订单id
     * @return HashMap<Integer, StoreCart>
     */
    @Override
    public List<StoreOrderInfoVo> getVoListByOrderId(Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> systemStoreStaffList = dao.selectList(lambdaQueryWrapper);
        if (systemStoreStaffList.size() < 1) {
            return null;
        }

        List<StoreOrderInfoVo> storeOrderInfoVoList = new ArrayList<>();
        for (StoreOrderInfo storeOrderInfo : systemStoreStaffList) {
            // 解析商品详情JSON
            StoreOrderInfoVo storeOrderInfoVo = new StoreOrderInfoVo();
            BeanUtils.copyProperties(storeOrderInfo, storeOrderInfoVo, "info");

            if (storeOrderInfo.getInfo() != null && JSONUtil.isJson(storeOrderInfo.getInfo())) {
                storeOrderInfoVo.setInfo(JSON.parseObject(storeOrderInfo.getInfo(), OrderInfoDetailVo.class));
            } else {
                // 如果info为空或不是JSON格式，通过已知信息查询商品信息
                OrderInfoDetailVo orderInfoDetailVo = new OrderInfoDetailVo();

                // 从订单信息中获取已知数据
                orderInfoDetailVo.setProductId(storeOrderInfo.getProductId());
                orderInfoDetailVo.setProductName(storeOrderInfo.getProductName());
                orderInfoDetailVo.setAttrValueId(storeOrderInfo.getAttrValueId());
                orderInfoDetailVo.setImage(storeOrderInfo.getImage());
                orderInfoDetailVo.setSku(storeOrderInfo.getSku());
                orderInfoDetailVo.setPrice(storeOrderInfo.getPrice());
                orderInfoDetailVo.setPayNum(storeOrderInfo.getPayNum());
                orderInfoDetailVo.setVipPrice(storeOrderInfo.getVipPrice());
                orderInfoDetailVo.setMemberDiscountAmount(storeOrderInfo.getMemberDiscountAmount());
                orderInfoDetailVo.setDiscountAmount(storeOrderInfo.getDiscountAmount());
                orderInfoDetailVo.setSubtotalAmount(storeOrderInfo.getSubtotalAmount());
                orderInfoDetailVo.setCost(storeOrderInfo.getCost());

                // 通过商品ID查询商品信息补充缺失字段
                if (storeOrderInfo.getProductId() != null) {
                    StoreProduct product = storeProductService.getById(storeOrderInfo.getProductId());
                    if (product != null) {
                        // 设置获得积分
                        orderInfoDetailVo.setGiveIntegral(product.getGiveIntegral());

                        // 设置是否单独分佣
                        orderInfoDetailVo.setIsSub(product.getIsSub());

                        // 设置运费模板ID
                        orderInfoDetailVo.setTempId(product.getTempId());
                    }
                }

                // 如果有规格ID，查询规格信息
                if (storeOrderInfo.getAttrValueId() != null) {
                    StoreProductAttrValue attrValue =
                        storeProductAttrValueService.getById(storeOrderInfo.getAttrValueId());
                    if (attrValue != null) {
                        // 设置重量
                        orderInfoDetailVo.setWeight(attrValue.getWeight());

                        // 设置体积
                        orderInfoDetailVo.setVolume(attrValue.getVolume());
                    }
                }

                // 设置默认商品类型为普通商品
                orderInfoDetailVo.setProductType(0);

                // 设置是否评价
                orderInfoDetailVo.setIsReply(storeOrderInfo.getIsReply() ? 1 : 0);

                storeOrderInfoVo.setInfo(orderInfoDetailVo);
            }
            storeOrderInfoVoList.add(storeOrderInfoVo);
        }
        return storeOrderInfoVoList;
    }

    /**
     * 获取订单详情-订单编号
     * 
     * @param orderNo 订单编号
     * @return List
     */
    @Override
    public List<StoreOrderInfo> getListByOrderNo(String orderNo) {
        LambdaQueryWrapper<StoreOrderInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StoreOrderInfo::getOrderNo, orderNo);
        return dao.selectList(lqw);
    }

    /**
     * 根据时间、商品id获取销售件数
     * 
     * @param date 时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return Integer
     */
    @Override
    public Integer getSalesNumByDateAndProductId(String date, Integer proId) {
        return dao.getSalesNumByDateAndProductId(date, proId);
    }

    /**
     * 根据时间、商品id获取销售额
     * 
     * @param date 时间，格式'yyyy-MM-dd'
     * @param proId 商品id
     * @return BigDecimal
     */
    @Override
    public BigDecimal getSalesByDateAndProductId(String date, Integer proId) {
        return dao.getSalesByDateAndProductId(date, proId);
    }

    /**
     * 新增订单详情
     * 
     * @param storeOrderInfos 订单详情集合
     * @return 订单新增结果
     */
    @Override
    public boolean saveOrderInfos(List<StoreOrderInfo> storeOrderInfos) {
        return saveBatch(storeOrderInfos);
    }

    /**
     * 通过订单编号和规格号查询
     * 
     * @param uni 规格号
     * @param orderId 订单编号
     * @return StoreOrderInfo
     */
    @Override
    public StoreOrderInfo getByUniAndOrderId(String uni, Integer orderId) {
        // 带 StoreOrderInfo 类的多条件查询
        LambdaQueryWrapper<StoreOrderInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreOrderInfo::getOrderId, orderId);
        lambdaQueryWrapper.eq(StoreOrderInfo::getUnique, uni);
        return dao.selectOne(lambdaQueryWrapper);
    }
}
