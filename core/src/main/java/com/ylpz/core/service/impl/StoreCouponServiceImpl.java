package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.ylpz.core.common.utils.StringUtils;
import org.apache.poi.util.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.core.common.request.StoreCouponRequest;
import com.ylpz.core.common.request.StoreCouponSearchRequest;
import com.ylpz.core.common.response.StoreCouponFrontResponse;
import com.ylpz.core.common.response.StoreCouponInfoResponse;
import com.ylpz.core.common.response.StoreCouponResponse;
import com.ylpz.core.common.response.StoreCouponDataOverviewResponse;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreCouponDao;
import com.ylpz.core.service.CategoryService;
import com.ylpz.core.service.StoreCouponService;
import com.ylpz.core.service.StoreCouponUserService;
import com.ylpz.core.service.StoreProductService;
import com.ylpz.core.service.StoreOrderService;
import com.ylpz.core.service.StoreOrderInfoService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.category.Category;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.coupon.StoreCouponUser;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderInfo;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.core.common.vo.StoreOrderInfoVo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

/**
 * StoreCouponServiceImpl 接口实现
 */
@Service
public class StoreCouponServiceImpl extends ServiceImpl<StoreCouponDao, StoreCoupon> implements StoreCouponService {

    @Resource
    private StoreCouponDao dao;

    @Autowired
    private StoreProductService storeProductService;

    @Autowired
    private StoreCouponUserService storeCouponUserService;

    @Autowired
    private CategoryService categoryService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderInfoService storeOrderInfoService;


    /**
    * 列表
    * @param request 请求参数
    * @param pageParamRequest 分页类参数
    * @return List<StoreCoupon>
    */
    @Override
    public List<StoreCoupon> getList(StoreCouponSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        //带 StoreCoupon 类的多条件查询
        LambdaQueryWrapper<StoreCoupon> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreCoupon::getIsDel, false);

        if (null != request.getType()) {
            lambdaQueryWrapper.eq(StoreCoupon::getType, request.getType());
        }

        if (null != request.getStatus()) {
            lambdaQueryWrapper.eq(StoreCoupon::getStatus, request.getStatus());
        }

        if (StrUtil.isNotBlank(request.getName())) {
            lambdaQueryWrapper.like(StoreCoupon::getName, request.getName());
        }

        // 按会员等级ID查询
        if (null != request.getMemberLevelId()) {
            lambdaQueryWrapper.and(wrapper -> wrapper
                .eq(StoreCoupon::getType, 3) // 必须是会员专享券
                .and(w -> w
                    .like(StoreCoupon::getMemberLevels, request.getMemberLevelId().toString()) // 完全匹配
                    .or()
                    .like(StoreCoupon::getMemberLevels, request.getMemberLevelId() + ",") // 开头匹配
                    .or()
                    .like(StoreCoupon::getMemberLevels, "," + request.getMemberLevelId() + ",") // 中间匹配
                    .or()
                    .like(StoreCoupon::getMemberLevels, "," + request.getMemberLevelId()) // 结尾匹配
                )
            );
        }

        lambdaQueryWrapper.orderByDesc(StoreCoupon::getSort).orderByDesc(StoreCoupon::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 保存优惠券表
     * @param request StoreCouponRequest 新增参数
     * <AUTHOR>
     * @since 2020-05-18
     */
    @Override
    public boolean create(StoreCouponRequest request) {
        StoreCoupon storeCoupon = new StoreCoupon();
        BeanUtils.copyProperties(request, storeCoupon);
        //if (storeCoupon.getIsLimited() && (storeCoupon.getTotal() == null || storeCoupon.getTotal() == 0)) {
        //    throw new CrmebException("请输入数量！");
        //}

        if (request.getUseType() > 1 && (StrUtil.isBlank(request.getPrimaryKey()))) {
            throw new CrmebException("请选择商品/分类！");
        }

        // 处理领取次数限制
        if (request.getReceiveLimitType() == 2 && (request.getReceiveLimitCount() == null || request.getReceiveLimitCount() < 1)) {
            throw new CrmebException("请设置每人可领取次数！");
        }

        // 处理客户限制
        if (request.getCustomerLimitType() == 2) {
            // 指定会员等级
            if (StrUtil.isBlank(request.getCustomerLevelIds())) {
                throw new CrmebException("请选择可领取的会员等级！");
            }
        } else if (request.getCustomerLimitType() == 3) {
            // 指定用户标签
            if (StrUtil.isBlank(request.getCustomerTagIds())) {
                throw new CrmebException("请选择可领取的用户标签！");
            }
        }

        // 处理优惠券门槛类型
        if (request.getCouponType() == 1) {
            // 有门槛时，minPrice必须大于0
            if (request.getMinPrice() == null || request.getMinPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new CrmebException("有门槛优惠券的最低消费金额必须大于0！");
            }
        } else if (request.getCouponType() == 2) {
            // 无门槛时，minPrice必须设为0
            request.setMinPrice(BigDecimal.ZERO);
            storeCoupon.setMinPrice(BigDecimal.ZERO);
        }

        storeCoupon.setLastTotal(storeCoupon.getTotal());
        if (request.getIsForever() == null) {
            request.setIsForever(false);
        }
        if (!request.getIsForever()) {
            storeCoupon.setReceiveStartTime(DateUtil.nowDateTime()); //开始时间设置为当前时间
        }else{
            if (storeCoupon.getReceiveStartTime() == null || storeCoupon.getReceiveEndTime() == null) {
                throw new CrmebException("请选择领取时间范围！");
            }

            int compareDate = DateUtil.compareDate(DateUtil.dateToStr(storeCoupon.getReceiveStartTime(), Constants.DATE_FORMAT), DateUtil.dateToStr(storeCoupon.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate > -1) {
                throw new CrmebException("请选择正确的领取时间范围！");
            }
        }

        // 会员券发券时间处理
        if (request.getType() == 3) { // 会员券
            if (request.getSendTimeType() == null) {
                // 默认设置为无限制发券
                storeCoupon.setSendTimeType(0);
                storeCoupon.setBirthSendType(null);
                storeCoupon.setMonthSendDay(null);
            } else if (request.getSendTimeType() == 1) { // 生日发券
                if (request.getBirthSendType() == null || request.getBirthSendType() < 1 || request.getBirthSendType() > 3) {
                    throw new CrmebException("请选择正确的生日发券类型！");
                }
                storeCoupon.setMonthSendDay(null);
            } else if (request.getSendTimeType() == 2) { // 每月指定日期
                if (request.getMonthSendDay() == null || request.getMonthSendDay() < 1 || request.getMonthSendDay() > 31) {
                    throw new CrmebException("请选择正确的每月发券日期！");
                }
                storeCoupon.setBirthSendType(null);
            } else {
                throw new CrmebException("请选择正确的发券时间类型！");
            }
        } else {
            // 非会员券不设置发券时间
            storeCoupon.setSendTimeType(0);
            storeCoupon.setBirthSendType(null);
            storeCoupon.setMonthSendDay(null);
        }

        // 处理会员限制
        if (request.getType() == 3 && StrUtil.isNotBlank(request.getMemberLevels())) {
            storeCoupon.setMemberLevels(request.getMemberLevels());
        } else {
            storeCoupon.setMemberLevels(null);
        }

        // 处理过期提醒
        if (request.getExpireNotice() != null && request.getExpireNotice()) {
            if (request.getExpireNoticeDays() == null || request.getExpireNoticeDays() < 1) {
                throw new CrmebException("请设置过期前提醒天数！");
            }
            storeCoupon.setExpireNotice(true);
            storeCoupon.setExpireNoticeDays(request.getExpireNoticeDays());
        } else {
            storeCoupon.setExpireNotice(false);
            storeCoupon.setExpireNoticeDays(null);
        }

        // 处理使用说明
        if (StrUtil.isNotBlank(request.getUseDescription())) {
            storeCoupon.setUseDescription(request.getUseDescription());
        }

        // 用券时间类型处理
        if (request.getUseTimeType() == 1) {
            // 固定时间
            if (storeCoupon.getUseStartTime() == null || storeCoupon.getUseEndTime() == null) {
                throw new CrmebException("请选择使用时间范围！");
            }

            int compareDate = DateUtil.compareDate(DateUtil.dateToStr(storeCoupon.getUseStartTime(), Constants.DATE_FORMAT), DateUtil.dateToStr(storeCoupon.getUseEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate > -1) {
                throw new CrmebException("请选择正确的使用时间范围！");
            }

            // 固定时间不需要day和afterDays
            storeCoupon.setDay(null);
            storeCoupon.setAfterDays(null);
        } else if (request.getUseTimeType() == 2) {
            // 领取后天数
            if (storeCoupon.getDay() == null || storeCoupon.getDay() == 0) {
                throw new CrmebException("请输入可使用天数！");
            }

            // 使用时间清空
            storeCoupon.setUseStartTime(null);
            storeCoupon.setUseEndTime(null);
            storeCoupon.setAfterDays(null);
        } else if (request.getUseTimeType() == 3) {
            // 领取后增加天数后可用
            if (storeCoupon.getDay() == null || storeCoupon.getDay() == 0) {
                throw new CrmebException("请输入可使用天数！");
            }

            if (storeCoupon.getAfterDays() == null) {
                throw new CrmebException("请输入领取后需等待天数！");
            }

            // 使用时间清空
            storeCoupon.setUseStartTime(null);
            storeCoupon.setUseEndTime(null);
        } else {
            throw new CrmebException("请选择正确的用券时间类型！");
        }

        return save(storeCoupon);
    }

    /**
     * 获取详情
     * @param id Integer id
     * @return StoreCoupon
     */
    @Override
    public StoreCoupon getInfoException(Integer id) {
        //获取优惠券信息
        StoreCoupon storeCoupon = getById(id);
        checkException(storeCoupon);

        return storeCoupon;
    }

    /**
     * 检测当前优惠券是否正常
     * @param storeCoupon StoreCoupon 优惠券对象`
     * <AUTHOR>
     * @since 2020-05-18
     */
    private void checkException(StoreCoupon storeCoupon) {
        if (storeCoupon == null || storeCoupon.getIsDel() || !storeCoupon.getStatus()) {
            throw new CrmebException("优惠券信息不存在或者已失效！");
        }

        //看是否过期
        if (!(storeCoupon.getReceiveEndTime() == null || "".equals(storeCoupon.getReceiveEndTime()))) {
            //非永久可领取
            String date = DateUtil.nowDateTimeStr();
            int result = DateUtil.compareDate(date, DateUtil.dateToStr(storeCoupon.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (result == 1) {
                //过期了
                throw new CrmebException("已超过优惠券领取最后期限！");
            }
        }

        //看是否有剩余数量
        if (storeCoupon.getIsLimited()) {
            //考虑到并发溢出的问题用大于等于
            if (storeCoupon.getLastTotal() < 1) {
                throw new CrmebException("此优惠券已经被领完了！");
            }
        }
    }

    /**
     * 优惠券详情
     * @param id Integer 获取可用优惠券的商品id
     * @return StoreCouponInfoResponse
     */
    @Override
    public StoreCouponInfoResponse info(Integer id) {
        StoreCoupon storeCoupon = getById(id);
        if (ObjectUtil.isNull(storeCoupon) || storeCoupon.getIsDel() || !storeCoupon.getStatus()) {
            throw new CrmebException("优惠券信息不存在或者已失效！");
        }

        List<StoreProduct> productList = null;
        List<Category> categoryList = null;
        if (StrUtil.isNotBlank(storeCoupon.getPrimaryKey()) && storeCoupon.getUseType() > 1) {
            List<Integer> primaryIdList = CrmebUtil.stringToArray(storeCoupon.getPrimaryKey());
            productList = storeProductService.getListInIds(primaryIdList);
        }

        StoreCouponRequest coupon = new StoreCouponRequest();
        BeanUtils.copyProperties(storeCoupon, coupon);
        coupon.setIsForever(false);
        if (ObjectUtil.isNotNull(coupon.getReceiveEndTime())) {
            coupon.setIsForever(true);
        }

        return new StoreCouponInfoResponse(coupon, productList, categoryList);
    }

    /**
     * 根据优惠券id获取
     * @param ids 优惠券id集合
     * @return List<StoreCoupon>
     */
    @Override
    public List<StoreCoupon> getByIds(List<Integer> ids) {
        LambdaQueryWrapper<StoreCoupon> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StoreCoupon::getId, ids);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 扣减数量
     * @param id 优惠券id
     * @param num 数量
     * @param isLimited 是否限量
     */
    @Override
    public Boolean deduction(Integer id, Integer num, Boolean isLimited) {
        UpdateWrapper<StoreCoupon> updateWrapper = new UpdateWrapper<>();
        if (isLimited) {
            updateWrapper.setSql(StrUtil.format("last_total = last_total - {}", num));
            updateWrapper.last(StrUtil.format(" and (last_total - {} >= 0)", num));
        } else {
            updateWrapper.setSql(StrUtil.format("last_total = last_total + {}", num));
        }
        updateWrapper.eq("id", id);
        return update(updateWrapper);
    }

    /**
     * 发送优惠券列表
     * @param request 搜索分页参数
     * @return 优惠券列表
     * PC管理员可发送优惠券：手动领取类型，状态开启，且有剩余数量的优惠券
     * 只支持优惠券名称模糊搜索
     */
    @Override
    public List<StoreCoupon> getSendList(SearchAndPageRequest request) {
        PageHelper.startPage(request.getPage(), request.getLimit());

        LambdaQueryWrapper<StoreCoupon> lqw = new LambdaQueryWrapper<>();
        lqw.select(StoreCoupon::getId, StoreCoupon::getName, StoreCoupon::getMoney, StoreCoupon::getMinPrice,
                StoreCoupon::getUseStartTime, StoreCoupon::getUseEndTime, StoreCoupon::getUseTimeType, StoreCoupon::getDay,
                StoreCoupon::getIsLimited, StoreCoupon::getLastTotal);
        lqw.eq(StoreCoupon::getIsDel, false);
        if (ObjectUtil.isNotNull(request.getType())) {
            lqw.eq(StoreCoupon::getType, request.getType());
        }
        lqw.eq(StoreCoupon::getStatus, true);
        if (StrUtil.isNotBlank(request.getKeywords())) {
            lqw.like(StoreCoupon::getName, request.getKeywords());
        }
        lqw.and(o -> o.eq(StoreCoupon::getIsLimited, false).or().ge(StoreCoupon::getLastTotal, 0));
        lqw.and(o -> o.isNull(StoreCoupon::getReceiveEndTime).or().gt(StoreCoupon::getReceiveEndTime, DateUtil.nowDate(Constants.DATE_FORMAT)));
        lqw.orderByDesc(StoreCoupon::getSort, StoreCoupon::getId);
        return dao.selectList(lqw);
    }

    /**
     * 删除优惠券
     * @param id 优惠券id
     * @return Boolean
     */
    @Override
    public Boolean delete(Integer id) {
        StoreCoupon coupon = getById(id);
        if (ObjectUtil.isNull(coupon) || coupon.getIsDel()) {
            throw new CrmebException("优惠券不存在");
        }
        coupon.setIsDel(true);
        return dao.updateById(coupon) > 0;
    }

    /**
     * 移动端优惠券列表
     * @param type 类型，1-通用，2-商品，3-品类
     * @param productId 产品id，搜索产品指定优惠券
     * @param pageParamRequest 分页参数
     * @return List<StoreCouponFrontResponse>
     */
    @Override
    public List<StoreCouponFrontResponse> getH5List(Integer type, Integer productId, PageParamRequest pageParamRequest) {
        List<StoreCoupon> storeCoupons = getListByReceive(type, productId, pageParamRequest);
        if (CollUtil.isEmpty(storeCoupons)) {
            return CollUtil.newArrayList();
        }
        List<StoreCouponFrontResponse> frontResponseList = new ArrayList<>();
        for (StoreCoupon storeCoupon : storeCoupons) {
            //获取可用商品
            StoreCouponFrontResponse response = new StoreCouponFrontResponse();
            BeanUtils.copyProperties(storeCoupon, response);
            frontResponseList.add(response);
        }
        return frontResponseList;
    }

    /**
     * 修改优惠券状态
     * @param id 优惠券id
     * @param status 状态
     */
    @Override
    public Boolean updateStatus(Integer id, Boolean status) {
        StoreCoupon coupon = getById(id);
        if (ObjectUtil.isNull(coupon)) {
            throw new CrmebException("优惠券不存在");
        }
        if (coupon.getStatus().equals(status)) {
            throw new CrmebException("优惠券状态无需变更");
        }
        StoreCoupon storeCoupon = new StoreCoupon();
        storeCoupon.setId(id);
        storeCoupon.setStatus(status);
        return updateById(storeCoupon);
    }

    /**
     * 用户可领取的优惠券
     * @return List<StoreCoupon>
     */
    private List<StoreCoupon> getListByReceive(Integer type, Integer productId, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        //带 StoreCoupon 类的多条件查询
        LambdaQueryWrapper<StoreCoupon> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCoupon::getIsDel, false);
        lqw.eq(StoreCoupon::getStatus, true);
        lqw.eq(type > 0, StoreCoupon::getType, type);
        lqw.gt(StoreCoupon::getLastTotal, 0);
        // 查询当前时间段的优惠券
        lqw.apply(" (is_forever = 1 or (is_forever = 0 and receive_start_time <= now() and receive_end_time >= now()))");
        //设置需要检索的内容
        if (productId > 0) {
            lqw.nested(i -> i.eq(StoreCoupon::getUseType, 1)
                    .or().apply("FIND_IN_SET(" + productId + ", primaryKey) and useType = 2")
                    .or().apply("FIND_IN_SET((select cate_id from store_product where id = " + productId + "), primaryKey) and use_type = 3"));
        } else {
            lqw.eq(StoreCoupon::getUseType, 1);
        }
        lqw.orderByDesc(StoreCoupon::getSort, StoreCoupon::getId);
        return dao.selectList(lqw);
    }

    /**
     * 获取会员生日优惠券列表
     * @param birthSendType 生日发券类型 1-生日当天 2-生日当周 3-生日当月
     * @return List<StoreCoupon>
     */
    @Override
    public List<StoreCoupon> findMemberBirthdayCouponList(Integer birthSendType) {
        LambdaQueryWrapper<StoreCoupon> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCoupon::getType, 3); // 会员券
        lqw.eq(StoreCoupon::getSendTimeType, 1); // 生日发券
        lqw.eq(StoreCoupon::getBirthSendType, birthSendType); // 生日发券类型
        lqw.eq(StoreCoupon::getStatus, true); // 状态开启
        lqw.eq(StoreCoupon::getIsDel, false); // 未删除
        List<StoreCoupon> list = dao.selectList(lqw);
        if (CollUtil.isEmpty(list)) {
            return CollUtil.newArrayList();
        }
        return list.stream().filter(coupon -> {
            if (coupon.getIsLimited() && coupon.getLastTotal() <= 0) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 获取会员每月指定日期优惠券列表
     * @param day 每月发券日期
     * @return List<StoreCoupon>
     */
    @Override
    public List<StoreCoupon> findMemberMonthDayCouponList(Integer day) {
        LambdaQueryWrapper<StoreCoupon> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCoupon::getType, 3); // 会员券
        lqw.eq(StoreCoupon::getSendTimeType, 2); // 每月指定日期发券
        lqw.eq(StoreCoupon::getMonthSendDay, day); // 每月发券日期
        lqw.eq(StoreCoupon::getStatus, true); // 状态开启
        lqw.eq(StoreCoupon::getIsDel, false); // 未删除
        return dao.selectList(lqw);
    }

    /**
     * 获取即将过期的优惠券列表（用于过期提醒）
     * @return List<StoreCoupon>
     */
    @Override
    public List<StoreCoupon> findNearExpireCoupons() {
        // 找出设置了过期提醒的优惠券
        LambdaQueryWrapper<StoreCoupon> lqw = new LambdaQueryWrapper<>();
        lqw.eq(StoreCoupon::getStatus, true); // 状态为开启
        lqw.eq(StoreCoupon::getIsDel, false); // 未删除
        lqw.eq(StoreCoupon::getExpireNotice, true); // 开启了过期提醒
        lqw.isNotNull(StoreCoupon::getExpireNoticeDays); // 过期提醒天数不为空

        List<StoreCoupon> couponList = dao.selectList(lqw);
        List<StoreCoupon> nearExpireCoupons = new ArrayList<>();

        // 当前日期
        Date now = DateUtil.nowDateTime();

        for (StoreCoupon coupon : couponList) {
            // 检查固定时间类型的优惠券
            if (coupon.getUseTimeType() == 1 && coupon.getUseEndTime() != null) {
                // 计算优惠券距离过期的天数
                long diffDays = DateUtil.daysBetween(now, coupon.getUseEndTime());
                // 如果距离过期天数等于设置的提醒天数，则加入列表
                if (diffDays == coupon.getExpireNoticeDays()) {
                    nearExpireCoupons.add(coupon);
                }
            }
            // 对于其他类型的优惠券（领取后天数），需要查询用户优惠券表计算过期时间，这里暂不实现
        }

        return nearExpireCoupons;
    }

    /**
    * 获取优惠券分页列表带使用数量和支付金额
    * @param request 请求参数
    * @param pageParamRequest 分页类参数
    * @return List<StoreCouponResponse>
    */
    @Override
    public List<StoreCouponResponse> getListWithUseInfo(StoreCouponSearchRequest request, PageParamRequest pageParamRequest) {
        // 先获取普通列表
        List<StoreCoupon> couponList = getList(request, pageParamRequest);

        // 如果列表为空，直接返回空列表
        if (CollUtil.isEmpty(couponList)) {
            return new ArrayList<>();
        }

        // 获取优惠券ID列表
        List<Integer> couponIds = couponList.stream()
                .map(StoreCoupon::getId)
                .collect(Collectors.toList());

        // 查询优惠券使用统计信息
        Map<Integer, CouponUsageStats> usageStatsMap = getCouponUsageStats(couponIds);

        // 将couponList转为StoreCouponResponse对象列表，并设置使用统计信息
        List<StoreCouponResponse> responseList = couponList.stream()
                .map(coupon -> {
                    StoreCouponResponse response = new StoreCouponResponse();
                    BeanUtils.copyProperties(coupon, response);

                    // 设置使用统计信息
                    CouponUsageStats stats = usageStatsMap.get(coupon.getId());
                    if (stats != null) {
                        response.setUsedCount(stats.getUsedCount());
                        response.setPayAmount(stats.getPayAmount());
                    } else {
                        response.setUsedCount(0);
                        response.setPayAmount(BigDecimal.ZERO);
                    }

                    return response;
                })
                .collect(Collectors.toList());

        return responseList;
    }

    /**
     * 获取优惠券使用统计信息
     * @param couponIds 优惠券ID列表
     * @return 优惠券使用统计信息Map，key为优惠券ID，value为统计信息
     */
    private Map<Integer, CouponUsageStats> getCouponUsageStats(List<Integer> couponIds) {
        if (CollUtil.isEmpty(couponIds)) {
            return new HashMap<>();
        }

        Map<Integer, CouponUsageStats> statsMap = new HashMap<>();

        // 查询已使用的优惠券记录
        LambdaQueryWrapper<StoreCouponUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(StoreCouponUser::getCouponId, couponIds);
        wrapper.eq(StoreCouponUser::getStatus, 1); // 已使用状态
        wrapper.eq(StoreCouponUser::getIsDel, false);

        List<StoreCouponUser> usedCoupons = storeCouponUserService.list(wrapper);

        if (CollUtil.isEmpty(usedCoupons)) {
            // 如果没有已使用的优惠券，初始化所有优惠券的统计为0
            for (Integer couponId : couponIds) {
                statsMap.put(couponId, new CouponUsageStats(0, BigDecimal.ZERO));
            }
            return statsMap;
        }

        // 按优惠券ID分组统计
        Map<Integer, List<StoreCouponUser>> groupedCoupons = usedCoupons.stream()
                .collect(Collectors.groupingBy(StoreCouponUser::getCouponId));

        // 获取所有关联的订单ID
        List<Integer> orderIds = usedCoupons.stream()
                .map(StoreCouponUser::getOid)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        // 查询订单支付金额
        final Map<Integer, BigDecimal> orderPayAmountMap;
        if (CollUtil.isNotEmpty(orderIds)) {
            LambdaQueryWrapper<StoreOrder> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.in(StoreOrder::getId, orderIds);
            orderWrapper.eq(StoreOrder::getPaid, true); // 已支付
            orderWrapper.eq(StoreOrder::getIsDel, false);

            List<StoreOrder> orders = storeOrderService.list(orderWrapper);
            orderPayAmountMap = orders.stream()
                    .collect(Collectors.toMap(StoreOrder::getId,
                            order -> order.getPayPrice() != null ? order.getPayPrice() : BigDecimal.ZERO));
        } else {
            orderPayAmountMap = new HashMap<>();
        }

        // 计算每个优惠券的统计信息
        for (Integer couponId : couponIds) {
            List<StoreCouponUser> couponUsers = groupedCoupons.get(couponId);
            if (CollUtil.isEmpty(couponUsers)) {
                statsMap.put(couponId, new CouponUsageStats(0, BigDecimal.ZERO));
                continue;
            }

            int usedCount = couponUsers.size();
            BigDecimal totalPayAmount = couponUsers.stream()
                    .map(StoreCouponUser::getOid)
                    .filter(ObjectUtil::isNotNull)
                    .map(orderId -> orderPayAmountMap.getOrDefault(orderId, BigDecimal.ZERO))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            statsMap.put(couponId, new CouponUsageStats(usedCount, totalPayAmount));
        }

        return statsMap;
    }

    /**
     * 优惠券使用统计信息内部类
     */
    private static class CouponUsageStats {
        private Integer usedCount;
        private BigDecimal payAmount;

        public CouponUsageStats(Integer usedCount, BigDecimal payAmount) {
            this.usedCount = usedCount;
            this.payAmount = payAmount;
        }

        public Integer getUsedCount() {
            return usedCount;
        }

        public BigDecimal getPayAmount() {
            return payAmount;
        }
    }

    /**
     * 修改优惠券
     * @param id 优惠券ID
     * @param request 修改参数
     * @return 是否成功
     */
    @Override
    public boolean update(Integer id, StoreCouponRequest request) {
        StoreCoupon existCoupon = getById(id);
        if (existCoupon == null) {
            throw new CrmebException("优惠券不存在！");
        }

        // 已删除的优惠券不能修改
        if (existCoupon.getIsDel()) {
            throw new CrmebException("优惠券已删除，无法修改！");
        }

        StoreCoupon storeCoupon = new StoreCoupon();
        BeanUtils.copyProperties(request, storeCoupon);
        storeCoupon.setId(id);

        //if (storeCoupon.getIsLimited() && (storeCoupon.getTotal() == null || storeCoupon.getTotal() == 0)) {
        //    throw new CrmebException("请输入数量！");
        //}

        if (request.getUseType() > 1 && (StrUtil.isBlank(request.getPrimaryKey()))) {
            throw new CrmebException("请选择商品/分类！");
        }

        // 处理领取次数限制
        if (request.getReceiveLimitType() == 2 && (request.getReceiveLimitCount() == null || request.getReceiveLimitCount() < 1)) {
            throw new CrmebException("请设置每人可领取次数！");
        }

        // 处理客户限制
        if (request.getCustomerLimitType() == 2) {
            // 指定会员等级
            if (StrUtil.isBlank(request.getCustomerLevelIds())) {
                throw new CrmebException("请选择可领取的会员等级！");
            }
        } else if (request.getCustomerLimitType() == 3) {
            // 指定用户标签
            if (StrUtil.isBlank(request.getCustomerTagIds())) {
                throw new CrmebException("请选择可领取的用户标签！");
            }
        }

        // 处理优惠券门槛类型
        if (request.getCouponType() == 1) {
            // 有门槛时，minPrice必须大于0
            if (request.getMinPrice() == null || request.getMinPrice().compareTo(BigDecimal.ZERO) <= 0) {
                throw new CrmebException("有门槛优惠券的最低消费金额必须大于0！");
            }
        } else if (request.getCouponType() == 2) {
            // 无门槛时，minPrice必须设为0
            request.setMinPrice(BigDecimal.ZERO);
            storeCoupon.setMinPrice(BigDecimal.ZERO);
        }

        // 如果修改了总数量，更新剩余数量
        if (existCoupon.getTotal() != null && storeCoupon.getTotal() != null
                && !existCoupon.getTotal().equals(storeCoupon.getTotal())) {
            // 已领取数量 = 原总数 - 原剩余数
            int usedTotal = existCoupon.getTotal() - existCoupon.getLastTotal();
            // 新剩余数量 = 新总数 - 已领取数量
            storeCoupon.setLastTotal(storeCoupon.getTotal() - usedTotal);
            // 确保剩余数量不小于0
            if (storeCoupon.getLastTotal() < 0) {
                storeCoupon.setLastTotal(0);
            }
        }

        if (request.getIsForever() == null) {
            request.setIsForever(false);
        }

        if (request.getType()  == 1 && request.getIsForever()) {
            if (storeCoupon.getReceiveStartTime() == null || storeCoupon.getReceiveEndTime() == null) {
                throw new CrmebException("请选择领取时间范围！");
            }

            int compareDate =
                DateUtil.compareDate(DateUtil.dateToStr(storeCoupon.getReceiveStartTime(), Constants.DATE_FORMAT),
                    DateUtil.dateToStr(storeCoupon.getReceiveEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate > -1) {
                throw new CrmebException("请选择正确的领取时间范围！");
            }
        }

        // 会员券发券时间处理
        if (request.getType() == 3) { // 会员券
            if (request.getSendTimeType() == null) {
                // 默认设置为无限制发券
                storeCoupon.setSendTimeType(0);
                storeCoupon.setBirthSendType(null);
                storeCoupon.setMonthSendDay(null);
            } else if (request.getSendTimeType() == 1) { // 生日发券
                if (request.getBirthSendType() == null || request.getBirthSendType() < 1 || request.getBirthSendType() > 3) {
                    throw new CrmebException("请选择正确的生日发券类型！");
                }
                storeCoupon.setMonthSendDay(null);
            } else if (request.getSendTimeType() == 2) { // 每月指定日期
                if (request.getMonthSendDay() == null || request.getMonthSendDay() < 1 || request.getMonthSendDay() > 31) {
                    throw new CrmebException("请选择正确的每月发券日期！");
                }
                storeCoupon.setBirthSendType(null);
            } else {
                throw new CrmebException("请选择正确的发券时间类型！");
            }
        } else {
            // 非会员券不设置发券时间
            storeCoupon.setSendTimeType(0);
            storeCoupon.setBirthSendType(null);
            storeCoupon.setMonthSendDay(null);
        }

        // 处理会员限制
        if (request.getType() == 3 && StrUtil.isNotBlank(request.getMemberLevels())) {
            storeCoupon.setMemberLevels(request.getMemberLevels());
        } else {
            storeCoupon.setMemberLevels(null);
        }

        // 处理过期提醒
        if (request.getExpireNotice() != null && request.getExpireNotice()) {
            if (request.getExpireNoticeDays() == null || request.getExpireNoticeDays() < 1) {
                throw new CrmebException("请设置过期前提醒天数！");
            }
            storeCoupon.setExpireNotice(true);
            storeCoupon.setExpireNoticeDays(request.getExpireNoticeDays());
        } else {
            storeCoupon.setExpireNotice(false);
            storeCoupon.setExpireNoticeDays(null);
        }

        // 处理使用说明
        if (StrUtil.isNotBlank(request.getUseDescription())) {
            storeCoupon.setUseDescription(request.getUseDescription());
        }

        // 用券时间类型处理
        if (request.getUseTimeType() == 1) {
            // 固定时间
            if (storeCoupon.getUseStartTime() == null || storeCoupon.getUseEndTime() == null) {
                throw new CrmebException("请选择使用时间范围！");
            }

            int compareDate = DateUtil.compareDate(DateUtil.dateToStr(storeCoupon.getUseStartTime(), Constants.DATE_FORMAT),
                    DateUtil.dateToStr(storeCoupon.getUseEndTime(), Constants.DATE_FORMAT), Constants.DATE_FORMAT);
            if (compareDate > -1) {
                throw new CrmebException("请选择正确的使用时间范围！");
            }

            // 固定时间不需要day和afterDays
            storeCoupon.setDay(null);
            storeCoupon.setAfterDays(null);
        } else if (request.getUseTimeType() == 2) {
            // 领取后天数
            if (storeCoupon.getDay() == null || storeCoupon.getDay() == 0) {
                throw new CrmebException("请输入可使用天数！");
            }

            // 使用时间清空
            storeCoupon.setUseStartTime(null);
            storeCoupon.setUseEndTime(null);
            storeCoupon.setAfterDays(null);
        } else if (request.getUseTimeType() == 3) {
            // 领取后增加天数后可用
            if (storeCoupon.getDay() == null || storeCoupon.getDay() == 0) {
                throw new CrmebException("请输入可使用天数！");
            }

            if (storeCoupon.getAfterDays() == null) {
                throw new CrmebException("请输入领取后需等待天数！");
            }

            // 使用时间清空
            storeCoupon.setUseStartTime(null);
            storeCoupon.setUseEndTime(null);
        } else {
            throw new CrmebException("请选择正确的用券时间类型！");
        }

        // 保留创建时间
        storeCoupon.setCreateTime(existCoupon.getCreateTime());
        // 不修改isDel字段
        storeCoupon.setIsDel(existCoupon.getIsDel());

        return updateById(storeCoupon);
    }

    /**
     * 获取优惠券数据概览
     * @param couponId 优惠券ID
     * @return StoreCouponDataOverviewResponse
     */
    @Override
    public StoreCouponDataOverviewResponse getDataOverview(Integer couponId) {
        // 获取优惠券基本信息
        StoreCoupon coupon = getById(couponId);
        if (ObjectUtil.isNull(coupon) || coupon.getIsDel()) {
            throw new CrmebException("优惠券不存在");
        }

        StoreCouponDataOverviewResponse response = new StoreCouponDataOverviewResponse();

        // 设置优惠券基本信息
        StoreCouponDataOverviewResponse.CouponBasicInfo couponInfo = buildCouponBasicInfo(coupon);
        response.setCouponInfo(couponInfo);

        // 获取使用该优惠券的订单数据
        List<StoreCouponUser> usedCoupons = getUsedCouponsByOriginalCouponId(couponId);

        // 设置统计数据
        StoreCouponDataOverviewResponse.CouponStatistics statistics = buildCouponStatistics(usedCoupons);
        response.setStatistics(statistics);

        // 设置商品信息
        List<StoreCouponDataOverviewResponse.CouponProductInfo> products = buildCouponProductInfo(usedCoupons);
        response.setProducts(products);

        return response;
    }

    /**
     * 构建优惠券基本信息
     */
    private StoreCouponDataOverviewResponse.CouponBasicInfo buildCouponBasicInfo(StoreCoupon coupon) {
        StoreCouponDataOverviewResponse.CouponBasicInfo info = new StoreCouponDataOverviewResponse.CouponBasicInfo();
        info.setId(coupon.getId());
        info.setName(coupon.getName());
        info.setMoney(coupon.getMoney());
        info.setMinPrice(coupon.getMinPrice());
        info.setCouponType(coupon.getCouponType());
        info.setType(coupon.getType());

        // 设置类型名称
        String typeName = "";
        switch (coupon.getType()) {
            case 1:
                typeName = "满减券";
                break;
            case 2:
                typeName = "新人专享券";
                break;
            case 3:
                typeName = "会员专享券";
                break;
            default:
                typeName = "未知类型";
        }
        info.setTypeName(typeName);

        // 设置使用条件描述
        String useCondition = "";
        if (coupon.getCouponType() == 1) {
            useCondition = "满" + coupon.getMinPrice() + "元可用";
        } else {
            useCondition = "无门槛";
        }
        info.setUseCondition(useCondition);

        return info;
    }

    /**
     * 获取使用该优惠券的用户优惠券记录
     */
    private List<StoreCouponUser> getUsedCouponsByOriginalCouponId(Integer couponId) {
        LambdaQueryWrapper<StoreCouponUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreCouponUser::getCouponId, couponId);
        wrapper.eq(StoreCouponUser::getStatus, 1); // 已使用
        wrapper.eq(StoreCouponUser::getIsDel, false);
        return storeCouponUserService.list(wrapper);
    }

    /**
     * 构建优惠券统计数据
     */
    private StoreCouponDataOverviewResponse.CouponStatistics buildCouponStatistics(List<StoreCouponUser> usedCoupons) {
        StoreCouponDataOverviewResponse.CouponStatistics statistics = new StoreCouponDataOverviewResponse.CouponStatistics();

        if (CollUtil.isEmpty(usedCoupons)) {
            // 如果没有使用记录，返回空统计
            statistics.setTotalPayAmount(BigDecimal.ZERO);
            statistics.setTotalDiscountAmount(BigDecimal.ZERO);
            statistics.setCostEffectiveRatio("0.00%");
            statistics.setAvgOrderAmount(BigDecimal.ZERO);
            statistics.setUserCount(0);
            statistics.setTotalProductCount(0);
            statistics.setOrderCount(0);
            return statistics;
        }

        // 获取订单ID列表
        List<Integer> orderIds = usedCoupons.stream()
                .map(StoreCouponUser::getOid)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderIds)) {
            // 如果没有关联订单，返回空统计
            statistics.setTotalPayAmount(BigDecimal.ZERO);
            statistics.setTotalDiscountAmount(BigDecimal.ZERO);
            statistics.setCostEffectiveRatio("0.00%");
            statistics.setAvgOrderAmount(BigDecimal.ZERO);
            statistics.setUserCount(0);
            statistics.setTotalProductCount(0);
            statistics.setOrderCount(0);
            return statistics;
        }

        // 获取订单信息
        List<StoreOrder> orders = storeOrderService.listByIds(orderIds);

        // 计算支付总金额
        BigDecimal totalPayAmount = orders.stream()
                .map(StoreOrder::getTotalPrice)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算优惠总金额（优惠券面值总和）
        BigDecimal totalDiscountAmount = usedCoupons.stream()
                .map(StoreCouponUser::getMoney)
                .filter(ObjectUtil::isNotNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 计算费效比
        String costEffectiveRatio = "0.00%";
        if (totalPayAmount.compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal ratio = totalDiscountAmount.divide(totalPayAmount, 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            costEffectiveRatio = ratio.setScale(2, RoundingMode.HALF_UP) + "%";
        }

        // 计算用券笔单价
        BigDecimal avgOrderAmount = BigDecimal.ZERO;
        if (orders.size() > 0) {
            avgOrderAmount = totalPayAmount.divide(new BigDecimal(orders.size()), 2, RoundingMode.HALF_UP);
        }

        // 计算用券客数（去重用户数）
        int userCount = (int) usedCoupons.stream()
                .map(StoreCouponUser::getUid)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .count();

        // 计算购买商品件数
        int totalProductCount = calculateTotalProductCount(orderIds);

        statistics.setTotalPayAmount(totalPayAmount);
        statistics.setTotalDiscountAmount(totalDiscountAmount);
        statistics.setCostEffectiveRatio(costEffectiveRatio);
        statistics.setAvgOrderAmount(avgOrderAmount);
        statistics.setUserCount(userCount);
        statistics.setTotalProductCount(totalProductCount);
        statistics.setOrderCount(orders.size());

        return statistics;
    }

    /**
     * 计算购买商品总件数
     */
    private int calculateTotalProductCount(List<Integer> orderIds) {
        if (CollUtil.isEmpty(orderIds)) {
            return 0;
        }

        // 查询订单详情表来获取商品数量
        return orderIds.stream()
                .mapToInt(orderId -> {
                    try {
                        List<StoreOrderInfoVo> orderInfos = storeOrderInfoService.getOrderListByOrderId(orderId);
                        if (CollUtil.isNotEmpty(orderInfos)) {
                            return orderInfos.stream()
                                    .mapToInt(orderInfo -> {
                                        if (orderInfo.getInfo() != null && orderInfo.getInfo().getPayNum() != null) {
                                            return orderInfo.getInfo().getPayNum();
                                        }
                                        return 0;
                                    })
                                    .sum();
                        }
                        return 0;
                    } catch (Exception e) {
                        return 0;
                    }
                })
                .sum();
    }

    /**
     * 构建优惠券商品信息
     */
    private List<StoreCouponDataOverviewResponse.CouponProductInfo> buildCouponProductInfo(List<StoreCouponUser> usedCoupons) {
        if (CollUtil.isEmpty(usedCoupons)) {
            return new ArrayList<>();
        }

        // 获取订单ID列表
        List<Integer> orderIds = usedCoupons.stream()
                .map(StoreCouponUser::getOid)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .collect(Collectors.toList());

        if (CollUtil.isEmpty(orderIds)) {
            return new ArrayList<>();
        }

        // 查询所有订单的详情信息
        Map<Integer, List<StoreOrderInfoVo>> orderInfoMap = storeOrderInfoService.getMapInId(orderIds);

        // 按商品ID分组统计
        Map<Integer, StoreCouponDataOverviewResponse.CouponProductInfo> productMap = new HashMap<>();

        for (List<StoreOrderInfoVo> orderInfos : orderInfoMap.values()) {
            if (CollUtil.isEmpty(orderInfos)) {
                continue;
            }

            for (StoreOrderInfoVo orderInfo : orderInfos) {
                Integer productId = orderInfo.getProductId();
                if (productId == null) {
                    continue;
                }

                StoreCouponDataOverviewResponse.CouponProductInfo productInfo = productMap.get(productId);
                if (productInfo == null) {
                    productInfo = new StoreCouponDataOverviewResponse.CouponProductInfo();
                    productInfo.setProductId(productId);

                    // 设置商品基本信息
                    if (orderInfo.getInfo() != null) {
                        productInfo.setProductName(orderInfo.getInfo().getProductName());
                        productInfo.setProductImage(orderInfo.getInfo().getImage());
                        productInfo.setProductPrice(orderInfo.getInfo().getPrice());
                        productInfo.setSku(orderInfo.getInfo().getSku());
                    }

                    productInfo.setTotalQuantity(0);
                    productInfo.setBuyerCount(0);
                    productMap.put(productId, productInfo);
                }

                // 累加购买数量
                if (orderInfo.getInfo() != null && orderInfo.getInfo().getPayNum() != null) {
                    productInfo.setTotalQuantity(productInfo.getTotalQuantity() + orderInfo.getInfo().getPayNum());
                }

                // 统计购买人数（这里简化处理，实际应该按用户去重）
                productInfo.setBuyerCount(productInfo.getBuyerCount() + 1);
            }
        }

        // 对购买人数进行去重处理（按用户ID统计）
        for (StoreCouponDataOverviewResponse.CouponProductInfo productInfo : productMap.values()) {
            // 这里简化处理，实际应该查询每个商品的实际购买用户数
            // 可以通过订单表关联用户表来统计去重的用户数
            int actualBuyerCount = calculateActualBuyerCount(productInfo.getProductId(), orderIds);
            productInfo.setBuyerCount(actualBuyerCount);
        }

        return new ArrayList<>(productMap.values());
    }

    /**
     * 计算商品的实际购买人数（去重）
     */
    private int calculateActualBuyerCount(Integer productId, List<Integer> orderIds) {
        if (CollUtil.isEmpty(orderIds) || productId == null) {
            return 0;
        }

        // 查询包含该商品的订单详情
        Map<Integer, List<StoreOrderInfoVo>> orderInfoMap = storeOrderInfoService.getMapInId(orderIds);

        // 收集购买该商品的订单ID
        List<Integer> productOrderIds = new ArrayList<>();
        for (Map.Entry<Integer, List<StoreOrderInfoVo>> entry : orderInfoMap.entrySet()) {
            List<StoreOrderInfoVo> orderInfos = entry.getValue();
            if (CollUtil.isNotEmpty(orderInfos)) {
                for (StoreOrderInfoVo orderInfo : orderInfos) {
                    if (productId.equals(orderInfo.getProductId())) {
                        productOrderIds.add(entry.getKey());
                        break; // 找到该商品就跳出内层循环
                    }
                }
            }
        }

        if (CollUtil.isEmpty(productOrderIds)) {
            return 0;
        }

        // 查询包含该商品的订单
        List<StoreOrder> orders = storeOrderService.listByIds(productOrderIds);

        // 统计购买该商品的用户数（去重）
        return (int) orders.stream()
                .map(StoreOrder::getUid)
                .filter(ObjectUtil::isNotNull)
                .distinct()
                .count();
    }

    /**
     * 停发商品优惠券
     * @param productId 商品ID
     * @param couponId 优惠券ID
     * @return Boolean
     */
    @Override
    public Boolean stopProductCoupon(Integer productId, Integer couponId) {
        // 获取优惠券信息
        StoreCoupon coupon = getById(couponId);
        if (ObjectUtil.isNull(coupon) || coupon.getIsDel()) {
            throw new CrmebException("优惠券不存在");
        }

        // 检查商品是否存在
        StoreProduct product = storeProductService.getById(productId);
        if (ObjectUtil.isNull(product) || product.getIsDel()) {
            throw new CrmebException("商品不存在");
        }

        Integer useType = coupon.getUseType();
        String primaryKey = StringUtils.getValue(coupon.getPrimaryKey());

        if (useType == 1) {
            // 全部商品可用 -> 修改为部分不可用，并将该商品加入不可用列表
            coupon.setUseType(3);
            coupon.setPrimaryKey(productId.toString());
        } else if (useType == 2) {
            // 指定商品可用 -> 将该商品从可用商品列表中移除
            List<String> productIds = new ArrayList<>(Arrays.asList(primaryKey.split(",")));
            String productIdStr = productId.toString();

            if (productIds.contains(productIdStr)) {
                productIds.remove(productIdStr);
                coupon.setPrimaryKey(String.join(",", productIds));

            }
        } else if (useType == 3) {
            // 指定商品不可用 -> 将该商品加入不可用列表（如果还没有的话）
            if (StrUtil.isBlank(primaryKey)) {
                coupon.setPrimaryKey(productId.toString());
            } else {
                List<String> excludedProductIds = new ArrayList<>(Arrays.asList(primaryKey.split(",")));
                String productIdStr = productId.toString();

                if (!excludedProductIds.contains(productIdStr)) {
                    excludedProductIds.add(productIdStr);
                    coupon.setPrimaryKey(String.join(",", excludedProductIds));
                }
                // 如果商品已经在不可用列表中，不需要做任何操作
            }
        }
        return updateById(coupon);
    }
}

