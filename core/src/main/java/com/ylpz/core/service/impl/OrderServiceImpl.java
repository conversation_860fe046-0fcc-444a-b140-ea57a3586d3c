package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.NotifyConstants;
import com.ylpz.core.common.constants.TaskConstants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.OrderRefundApplyRequest;
import com.ylpz.core.common.request.StoreProductReplyAddRequest;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.core.dao.StoreOrderDao;
import com.ylpz.core.delete.OrderUtils;
import com.ylpz.core.service.*;
import com.ylpz.model.order.OrderStatusEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.sms.SmsTemplate;
import com.ylpz.model.system.SystemAdmin;
import com.ylpz.model.system.SystemNotification;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;

/**
 * H5端订单操作
 */
@Service
public class OrderServiceImpl implements OrderService {

    private final Logger logger = LoggerFactory.getLogger(OrderServiceImpl.class);

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreProductReplyService storeProductReplyService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OrderUtils orderUtils;

    @Autowired
    private StoreOrderDao storeOrderDao;


    /**
     * 创建订单商品评价
     *
     * @param request 请求参数
     * @return Boolean
     */
    @Override
    public Boolean reply(StoreProductReplyAddRequest request) {
        if (StrUtil.isBlank(request.getOrderNo())) {
            throw new CrmebException("订单号参数不能为空");
        }
        return storeProductReplyService.create(request);
    }

    /**
     * 订单取消
     *
     * @param id Integer 订单id
     */
    @Override
    public Boolean cancel(Integer id) {
        StoreOrder storeOrder = orderUtils.getInfoById(id);
        //已收货，待评价
        storeOrder.setIsDel(true);
        storeOrder.setIsSystemDel(true);
        boolean result = storeOrderService.updateById(storeOrder);

        //后续操作放入redis
        redisUtil.lPush(Constants.ORDER_TASK_REDIS_KEY_AFTER_CANCEL_BY_USER, id);
        return result;
    }

    /**
     * 获取用户消费总金额
     *
     * @param userId 用户ID
     * @return 消费总金额
     */
    @Override
    public BigDecimal getUserOrderAmount(Integer userId) {
        if (userId == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal amount = storeOrderDao.sumUserOrderAmount(userId);
        return amount == null ? BigDecimal.ZERO : amount;
    }
}
