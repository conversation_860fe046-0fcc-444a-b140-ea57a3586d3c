package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.StoreProductReplyAddRequest;
import com.ylpz.core.common.request.StoreProductReplyCommentRequest;
import com.ylpz.core.common.request.StoreProductReplySearchRequest;
import com.ylpz.core.common.response.ProductDetailReplyResponse;
import com.ylpz.core.common.response.ProductReplyResponse;
import com.ylpz.core.common.response.StoreProductReplyResponse;
import com.ylpz.core.common.vo.MyRecord;
import com.ylpz.core.common.request.PageParamRequest;
import com.github.pagehelper.PageInfo;
import com.ylpz.model.product.StoreProductReply;

/**
 * StoreProductReplyService 接口
 */
public interface StoreProductReplyService extends IService<StoreProductReply> {

    /**
     * 商品评论列表
     * @param request 请求参数
     * @param pageParamRequest 分页参数
     * @return PageInfo
     */
    PageInfo<StoreProductReplyResponse> getList(StoreProductReplySearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 创建订单商品评价
     * @param request 请求参数
     * @return Boolean
     */
    Boolean create(StoreProductReplyAddRequest request);

    /**
     * 添加虚拟评论
     * @param request 评论参数
     * @return 评论结果
     */
    boolean virtualCreate(StoreProductReplyAddRequest request);

    /**
     * 查询是否已经回复
     * @param unique skuId
     * @param orderId 订单id
     * @return Boolean
     */
    Boolean isReply(String unique, Integer orderId);

    /**
     * 删除评论
     * @param id 评论id
     * @return Boolean
     */
    Boolean delete(Integer id);

    /**
     * 商品评论回复
     * @param request 回复参数
     */
    Boolean comment(StoreProductReplyCommentRequest request);
}
