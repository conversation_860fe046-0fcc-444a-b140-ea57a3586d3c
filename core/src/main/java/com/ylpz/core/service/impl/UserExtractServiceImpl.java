package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.UserExtractRequest;
import com.ylpz.core.common.request.UserExtractSearchRequest;
import com.ylpz.core.common.response.BalanceResponse;
import com.ylpz.core.common.response.TransferResponse;
import com.ylpz.core.common.response.UserExtractDetailResponse;
import com.ylpz.core.common.response.UserExtractResponse;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.SecurityUtil;
import com.ylpz.core.common.vo.dateLimitUtilVo;
import com.ylpz.core.dao.UserExtractDao;
import com.ylpz.core.service.*;
import com.ylpz.model.finance.UserExtract;
import com.ylpz.model.finance.enums.UserExtractStatusEnum;
import com.ylpz.model.system.SystemAdmin;
import com.ylpz.model.user.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static java.math.BigDecimal.ZERO;

/**
 * UserExtractServiceImpl 接口实现
 */
@Slf4j
@Service
public class UserExtractServiceImpl extends ServiceImpl<UserExtractDao, UserExtract> implements UserExtractService {

    @Resource
    private UserExtractDao dao;

    @Autowired
    private UserService userService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    @Autowired
    private TransferService transferService;

    @Autowired
    private SystemAdminService systemAdminService;

    @Autowired
    private UserBillService userBillService;

    /**
     * 列表
     * 
     * @param request 请求参数
     * @param pageParamRequest 分页类参数
     * <AUTHOR>
     * @since 2020-05-11
     * @return List<UserExtract>
     */
    @Override
    public List<UserExtract> getList(UserExtractSearchRequest request, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 带 UserExtract 类的多条件查询
        LambdaQueryWrapper<UserExtract> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isBlank(request.getKeywords())) {
            lambdaQueryWrapper.and(i -> i.or().like(UserExtract::getWechat, request.getKeywords()). // 微信号
                or().like(UserExtract::getRealName, request.getKeywords()). // 名称
                or().like(UserExtract::getBankCode, request.getKeywords()). // 银行卡
                or().like(UserExtract::getBankAddress, request.getKeywords()). // 开户行
                or().like(UserExtract::getAlipayCode, request.getKeywords()). // 支付宝
                or().like(UserExtract::getFailMsg, request.getKeywords()) // 失败原因
            );
        }

        // 提现编号查询
        if (!StringUtils.isBlank(request.getExtractNumber())) {
            lambdaQueryWrapper.eq(UserExtract::getId, request.getExtractNumber());
        }

        // 根据查询类型设置不同的状态条件
        if (request.getQueryType() == 1) {
            // 提现审核 - 只查询待审核、审核通过、已拒绝的记录
//            lambdaQueryWrapper.in(UserExtract::getStatus,
//                UserExtractStatusEnum.PENDING.getCode(),      // 0=待审核
//                UserExtractStatusEnum.APPROVED.getCode(),     // 1=审核通过
//                UserExtractStatusEnum.REJECTED.getCode()      // -1=已拒绝
//            );
        } else if (request.getQueryType() == 2) {
            // 提现记录 - 只查询提现成功、打款失败的记录
            lambdaQueryWrapper.in(UserExtract::getStatus,
                UserExtractStatusEnum.SUCCESS.getCode(),      // 2=提现成功
                UserExtractStatusEnum.FAILED.getCode()        // 3=打款失败
            );
            lambdaQueryWrapper.isNotNull(UserExtract::getApproveTime);
        } else {
            throw new CrmebException("查询类型错误");
        }

        // 提现方式
        if (!StringUtils.isBlank(request.getExtractType())) {
            lambdaQueryWrapper.eq(UserExtract::getExtractType, request.getExtractType());
        }

        // 时间范围
        if (StringUtils.isNotBlank(request.getDateLimit())) {
            dateLimitUtilVo dateLimit = DateUtil.getDateLimit(request.getDateLimit());
            lambdaQueryWrapper.between(UserExtract::getCreateTime, dateLimit.getStartTime(), dateLimit.getEndTime());
        }
        // 精确的开始时间和结束时间
        else if (StringUtils.isNotBlank(request.getStartTime()) || StringUtils.isNotBlank(request.getEndTime())) {
            if (StringUtils.isNotBlank(request.getStartTime()) && StringUtils.isNotBlank(request.getEndTime())) {
                // 同时有开始和结束时间
                lambdaQueryWrapper.between(UserExtract::getCreateTime, request.getStartTime(), request.getEndTime());
            } else if (StringUtils.isNotBlank(request.getStartTime())) {
                // 只有开始时间
                lambdaQueryWrapper.ge(UserExtract::getCreateTime, request.getStartTime());
            } else if (StringUtils.isNotBlank(request.getEndTime())) {
                // 只有结束时间
                lambdaQueryWrapper.le(UserExtract::getCreateTime, request.getEndTime());
            }
        }

        // 按创建时间降序排列
        lambdaQueryWrapper.orderByDesc(UserExtract::getCreateTime, UserExtract::getId);

        List<UserExtract> extractList = dao.selectList(lambdaQueryWrapper);
        if (CollUtil.isEmpty(extractList)) {
            return extractList;
        }
        List<Integer> uidList = extractList.stream().map(o -> o.getUid()).distinct().collect(Collectors.toList());
        HashMap<Integer, User> userMap = userService.getMapListInUid(uidList);
        for (UserExtract userExtract : extractList) {
            userExtract.setNickName(Optional.ofNullable(userMap.get(userExtract.getUid()))
                    .map(User::getNickname)
                    .orElse(""));
            if (request.getQueryType() == 1 && (UserExtractStatusEnum.SUCCESS.getCode().equals(userExtract.getStatus()) || UserExtractStatusEnum.FAILED.getCode().equals(userExtract.getStatus()))) {
                //处理为审核通过，审核页面只需要这些状态
                userExtract.setStatus(UserExtractStatusEnum.APPROVED.getCode());
            }
        }
        return extractList;
    }

    /**
     * 获取提现列表（包含用户手机号）
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return 提现详情列表
     */
    @Override
    public List<UserExtractDetailResponse> getListWithDetail(UserExtractSearchRequest request,
        PageParamRequest pageParamRequest) {
        // 获取原始提现列表
        List<UserExtract> extractList = getList(request, pageParamRequest);
        if (CollUtil.isEmpty(extractList)) {
            return new ArrayList<>();
        }

        // 获取所有用户ID
        List<Integer> uidList = extractList.stream().map(UserExtract::getUid).distinct().collect(Collectors.toList());

        // 查询用户信息，获取手机号
        HashMap<Integer, User> userMap = userService.getMapListInUid(uidList);

        // 转换为详情响应对象
        List<UserExtractDetailResponse> detailList = new ArrayList<>(extractList.size());
        for (UserExtract extract : extractList) {
            String phone = "";
            if (userMap.containsKey(extract.getUid())) {
                User user = userMap.get(extract.getUid());
                phone = user.getPhone();
            }

            UserExtractDetailResponse detail = UserExtractDetailResponse.fromUserExtract(extract, phone);
            detailList.add(detail);
        }

        return detailList;
    }

    /**
     * 提现总金额 总佣金 = 已提现佣金 + 未提现佣金 已提现佣金 = 用户成功提现的金额 未提现佣金 = 用户未提现的佣金 = 可提现佣金 + 冻结佣金 = 用户佣金 可提现佣金 = 包括解冻佣金、提现未通过的佣金 = 用户佣金
     * - 冻结期佣金 待提现佣金 = 待审核状态的佣金 冻结佣金 = 用户在冻结期的佣金，不包括退回佣金 退回佣金 = 因退款导致的冻结佣金退回
     */
    @Override
    public BalanceResponse getBalance(String dateLimit) {
        String startTime = "";
        String endTime = "";
        if (StringUtils.isNotBlank(dateLimit)) {
            dateLimitUtilVo dateRage = DateUtil.getDateLimit(dateLimit);
            startTime = dateRage.getStartTime();
            endTime = dateRage.getEndTime();
        }

        // 已提现
        BigDecimal withdrawn = getWithdrawn(startTime, endTime);
        // 待提现(审核中)
        BigDecimal toBeWithdrawn = getWithdrawning(startTime, endTime);
        return new BalanceResponse(withdrawn, null, null, toBeWithdrawn);
    }

    /**
     * 提现总金额
     * 
     * <AUTHOR>
     * @since 2020-05-11
     * @return BalanceResponse
     */
    @Override
    public BigDecimal getWithdrawn(String startTime, String endTime) {
        BigDecimal successNum = getSum(null, UserExtractStatusEnum.SUCCESS.getCode(), startTime, endTime);

        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        lqw.eq(UserExtract::getStatus, UserExtractStatusEnum.FAILED.getCode());
        lqw.isNotNull(UserExtract::getApproveTime);

        List<UserExtract> userExtracts = dao.selectList(lqw);
        BigDecimal failNum = ZERO;
        if (CollUtil.isNotEmpty(userExtracts)) {
            failNum = userExtracts.stream().map(UserExtract::getExtractPrice).reduce(ZERO, BigDecimal::add);
        }

        return successNum.add(failNum);
    }

    /**
     * 审核中总金额
     * 
     * <AUTHOR>
     * @since 2020-05-11
     * @return BalanceResponse
     */
    private BigDecimal getWithdrawning(String startTime, String endTime) {
        return getSum(null, UserExtractStatusEnum.PENDING.getCode(), startTime, endTime);
    }

    /**
     * 根据状态获取总额
     * 
     * @return BigDecimal
     */
    private BigDecimal getSum(Integer userId, int status, String startTime, String endTime) {
        LambdaQueryWrapper<UserExtract> lqw = Wrappers.lambdaQuery();
        if (null != userId) {
            lqw.eq(UserExtract::getUid, userId);
        }
        lqw.eq(UserExtract::getStatus, status);
        if (StringUtils.isNotBlank(startTime) && StringUtils.isNotBlank(endTime)) {
            lqw.between(UserExtract::getCreateTime, startTime, endTime);
        }
        List<UserExtract> userExtracts = dao.selectList(lqw);
        BigDecimal sum = ZERO;
        if (CollUtil.isNotEmpty(userExtracts)) {
            sum = userExtracts.stream().map(UserExtract::getExtractPrice).reduce(ZERO, BigDecimal::add);
        }
        return sum;
    }

    /**
     * 获取用户对应的提现数据
     * 
     * @param userId 用户id
     * @return 提现数据
     */
    @Override
    public UserExtractResponse getUserExtractByUserId(Integer userId) {
        QueryWrapper<UserExtract> qw = new QueryWrapper<>();
        qw.select("SUM(extract_price) as extract_price,count(id) as id, uid");
        qw.ge("status", UserExtractStatusEnum.APPROVED.getCode());
        qw.eq("uid", userId);
        qw.groupBy("uid");
        UserExtract ux = dao.selectOne(qw);
        UserExtractResponse uexr = new UserExtractResponse();
        // uexr.setEuid(ux.getUid());
        if (null != ux) {
            uexr.setExtractCountNum(ux.getId()); // 这里的id其实是数量，借变量传递
            uexr.setExtractCountPrice(ux.getExtractPrice());
        } else {
            uexr.setExtractCountNum(0); // 这里的id其实是数量，借变量传递
            uexr.setExtractCountPrice(ZERO);
        }

        return uexr;
    }

    /**
     * 提现审核
     *
     * @param id 提现申请id
     * @param status 审核状态 -1 已拒绝  1 审核通过
     * @param backMessage 驳回原因
     * @return 审核结果
     */
    @Override
    public Boolean updateStatus(Integer id, Integer status, String backMessage) {
        if (status == UserExtractStatusEnum.REJECTED.getCode() && StringUtils.isBlank(backMessage))
            throw new CrmebException("驳回时请填写驳回原因");

        UserExtract userExtract = getById(id);
        if (ObjectUtil.isNull(userExtract)) {
            throw new CrmebException("提现申请记录不存在");
        }
        if (userExtract.getStatus() != UserExtractStatusEnum.PENDING.getCode()) {
            throw new CrmebException("提现申请已处理过");
        }
        userExtract.setStatus(status);

        // 获取当前登录的管理员信息，记录审核人
        try {
            SystemAdmin currentAdmin = SecurityUtil.getLoginUserVo().getUser();
            if (currentAdmin != null) {
                userExtract.setApproveAdminAccount(currentAdmin.getAccount());
                userExtract.setApproveAdminName(currentAdmin.getRealName());
            }
        } catch (Exception e) {
            log.warn("获取当前登录管理员信息失败: {}", e.getMessage());
        }

        User user = userService.getById(userExtract.getUid());
        if (ObjectUtil.isNull(user)) {
            throw new CrmebException("提现用户数据异常");
        }

        Boolean execute = false;

        userExtract.setUpdateTime(new Date());

        // 拒绝
        if (status == UserExtractStatusEnum.REJECTED.getCode()) {// 未通过时恢复用户总金额
            userExtract.setFailMsg(backMessage);
            userExtract.setFailTime(new Date());
            userExtract.setStatus(UserExtractStatusEnum.REJECTED.getCode());

            execute = transactionTemplate.execute(e -> {
                // 返还可提现金额
                userService.operationWithdrawablePrice(userExtract.getUid(), userExtract.getExtractPrice(),
                        user.getWithdrawablePrice() == null ? BigDecimal.ZERO : user.getWithdrawablePrice(), "add");
                // 返还用户金额
                userService.operationNowMoney(user.getId(), userExtract.getExtractPrice(), user.getNowMoney(), "add");
                updateById(userExtract);
                return Boolean.TRUE;
            });
        }

        // 同意
        if (status == UserExtractStatusEnum.APPROVED.getCode()) {
            // 设置审核通过时间
            userExtract.setApproveTime(new Date());
            userExtract.setStatus(UserExtractStatusEnum.APPROVED.getCode());
            // 先保存审核通过状态
            execute = updateById(userExtract);
            if (execute) {
                // 调用提现接口
                TransferResponse transferResponse = transferService.requestTransfer(userExtract);
                if (transferResponse.getSuccess()) {
                    log.info("提现接口调用成功: {}", transferResponse.getTradeNo());
                } else if(transferResponse.getMessage().contains("提现接口调用失败")){
                    // 提现接口调用失败，需要退回余额
                    log.error("提现接口调用失败: {}，参数：", transferResponse.getMessage(), userExtract);
                    userExtract.setStatus(UserExtractStatusEnum.FAILED.getCode());
                    userExtract.setFailMsg("提现接口调用失败");
                    transactionTemplate.execute(e -> {
                        updateById(userExtract);
                        // 返还可提现金额
                        userService.operationWithdrawablePrice(userExtract.getUid(), userExtract.getExtractPrice(),
                                user.getWithdrawablePrice() == null ? BigDecimal.ZERO : user.getWithdrawablePrice(), "add");
                        // 返还用户金额
                        userService.operationNowMoney(user.getId(), userExtract.getExtractPrice(), user.getNowMoney(), "add");
                        log.info("返还返还可提现金额和返还用户金额成功，提现申请ID: {}", userExtract.getId());
                        return Boolean.TRUE;
                    });

                } else {
                    log.error("提现接口调用失败: {}，参数：", transferResponse.getMessage(), userExtract);
                }
            }
        }

        return execute;
    }

    /**
     * 修改提现申请
     * 
     * @param id 申请id
     * @param userExtractRequest 具体参数
     */
    @Override
    public Boolean updateExtract(Integer id, UserExtractRequest userExtractRequest) {
        UserExtract userExtract = new UserExtract();
        BeanUtils.copyProperties(userExtractRequest, userExtract);
        userExtract.setId(id);
        return updateById(userExtract);
    }

    /**
     * 更新付款流水号
     * 
     * @param id 提现申请id
     * @param paymentNo 付款流水号
     * @return 更新结果
     */
    @Override
    public Boolean updatePaymentNo(Integer id, String paymentNo) {
        if (id == null || StringUtils.isBlank(paymentNo)) {
            throw new CrmebException("参数错误");
        }

        UserExtract userExtract = getById(id);
        if (ObjectUtil.isNull(userExtract)) {
            throw new CrmebException("提现申请记录不存在");
        }

        if (userExtract.getStatus() != UserExtractStatusEnum.APPROVED.getCode()) {
            throw new CrmebException("只能为已通过审核的提现记录添加付款流水号");
        }

        userExtract.setPaymentNo(paymentNo);
        userExtract.setUpdateTime(cn.hutool.core.date.DateUtil.date());

        return updateById(userExtract);
    }

    /**
     * 批量审核提现申请
     * 
     * @param ids 提现申请ID列表，逗号分隔
     * @param status 审核状态 -1 已拒绝  1 审核通过
     * @param backMessage 驳回原因，当status=-1时必填
     * @return 审核结果
     */
    @Override
    public Boolean batchUpdateStatus(String ids, Integer status, String backMessage) {
        if (status == UserExtractStatusEnum.REJECTED.getCode() && StringUtils.isBlank(backMessage)) {
            throw new CrmebException("驳回时请填写驳回原因");
        }

        if (StringUtils.isBlank(ids)) {
            throw new CrmebException("请选择要审核的提现申请");
        }

        String[] idArray = ids.split(",");
        if (idArray.length == 0) {
            throw new CrmebException("请选择要审核的提现申请");
        }

        Boolean result = true;
        for (String idStr : idArray) {
            try {
                Integer id = Integer.valueOf(idStr);
                Boolean updateResult = updateStatus(id, status, backMessage);
                if (!updateResult) {
                    result = false;
                }
            } catch (NumberFormatException e) {
                continue;
            } catch (CrmebException e) {
                continue;
            }
        }

        return result;
    }

    /**
     * 获取提现详情，包含用户手机号
     * 
     * @param id 提现申请id
     * @return 提现详情响应对象
     */
    @Override
    public UserExtractDetailResponse getDetailById(Integer id) {
        UserExtract userExtract = getById(id);
        if (userExtract == null) {
            return null;
        }

        // 获取用户信息
        User user = userService.getById(userExtract.getUid());
        if (user == null) {
            return UserExtractDetailResponse.fromUserExtract(userExtract, "");
        }

        // 返回包含手机号的详情
        return UserExtractDetailResponse.fromUserExtract(userExtract, user.getPhone());
    }

    @Override
    public BigDecimal getWithdrawnByUserId(String startTime, String endTime, Integer uid) {
        BigDecimal successNum = getSum(uid, UserExtractStatusEnum.SUCCESS.getCode(), startTime, endTime);
        return successNum;

    }
}
