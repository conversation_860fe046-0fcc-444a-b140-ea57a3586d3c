package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.StoreActivityDiscountDao;
import com.ylpz.core.service.StoreActivityDiscountService;
import com.ylpz.model.activity.StoreActivityDiscount;

import cn.hutool.core.util.ObjectUtil;

/**
 * 活动折扣关联表 Service 实现类
 */
@Service
public class StoreActivityDiscountServiceImpl extends ServiceImpl<StoreActivityDiscountDao, StoreActivityDiscount>
    implements StoreActivityDiscountService {

    @Resource
    private StoreActivityDiscountDao dao;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 根据活动ID获取折扣ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    @Override
    public List<Integer> getDiscountIdsByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityDiscount::getActivityId, activityId)
            .eq(StoreActivityDiscount::getIsDel, false);
        List<StoreActivityDiscount> list = list(lambdaQueryWrapper);
        List<Integer> discountIds = new ArrayList<>();
        for (StoreActivityDiscount activityDiscount : list) {
            discountIds.add(activityDiscount.getDiscountId());
        }
        return discountIds;
    }

    /**
     * 保存活动折扣关联
     * 
     * @param activityId 活动ID
     * @param discountIds 折扣ID列表
     * @return Boolean
     */
    @Override
    public Boolean saveActivityDiscount(Integer activityId, List<Integer> discountIds) {
        if (ObjectUtil.isEmpty(discountIds)) {
            return Boolean.TRUE;
        }
        return transactionTemplate.execute(e -> {
            // 删除原有关联
            deleteByActivityId(activityId);
            // 保存新关联
            List<StoreActivityDiscount> activityDiscountList = new ArrayList<>();
            for (Integer discountId : discountIds) {
                StoreActivityDiscount activityDiscount = new StoreActivityDiscount();
                activityDiscount.setActivityId(activityId);
                activityDiscount.setDiscountId(discountId);
                activityDiscountList.add(activityDiscount);
            }
            return saveBatch(activityDiscountList);
        });
    }

    /**
     * 删除活动折扣关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    @Override
    public Boolean deleteByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivityDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivityDiscount::getActivityId, activityId)
            .eq(StoreActivityDiscount::getIsDel, false);
        List<StoreActivityDiscount> list = list(lambdaQueryWrapper);
        for (StoreActivityDiscount activityDiscount : list) {
            activityDiscount.setIsDel(true);
        }
        return updateBatchById(list);
    }
} 