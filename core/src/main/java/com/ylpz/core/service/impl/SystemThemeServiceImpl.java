package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SystemThemeRequest;
import com.ylpz.core.dao.SystemThemeDao;
import com.ylpz.core.service.SystemThemeIconService;
import com.ylpz.core.service.SystemThemeService;
import com.ylpz.model.system.SystemTheme;
import com.ylpz.model.system.SystemThemeIcon;

@Service
public class SystemThemeServiceImpl extends ServiceImpl<SystemThemeDao, SystemTheme> implements SystemThemeService {

    @Autowired
    private SystemThemeIconService systemThemeIconService;

    /**
     * 获取主题列表
     * 
     * @param pageParamRequest 分页参数
     * @return List<SystemTheme>
     */
    @Override
    public List<SystemTheme> getList(PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        LambdaQueryWrapper<SystemTheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(SystemTheme::getSort);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 新增主题
     * 
     * @param request 主题请求对象
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean add(SystemThemeRequest request) {
        SystemTheme theme = new SystemTheme();
        BeanUtils.copyProperties(request, theme);

        // 如果设置为默认主题，则将其他主题设置为非默认
        if (request.getIsDefault()) {
            setAllThemeNotDefault();
        }

        boolean save = save(theme);
        if (save && request.getIcons() != null && !request.getIcons().isEmpty()) {
            List<SystemThemeIcon> icons = new ArrayList<>();
            request.getIcons().forEach(icon -> {
                SystemThemeIcon themeIcon = new SystemThemeIcon();
                BeanUtils.copyProperties(icon, themeIcon);
                themeIcon.setThemeId(theme.getId());
                icons.add(themeIcon);
            });
            systemThemeIconService.saveBatch(icons);
        }
        return save;
    }

    /**
     * 编辑主题
     * 
     * @param request 主题请求对象
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean edit(SystemThemeRequest request) {
        if (request.getId() == null) {
            return false;
        }

        SystemTheme theme = getById(request.getId());
        if (theme == null) {
            return false;
        }

        BeanUtils.copyProperties(request, theme);
        theme.setUpdateTime(new Date());

        // 如果设置为默认主题，则将其他主题设置为非默认
        if (request.getIsDefault()) {
            setAllThemeNotDefault();
        }

        boolean update = updateById(theme);
        if (update && request.getIcons() != null) {
            // 先删除原有图标
            systemThemeIconService.deleteByThemeId(theme.getId());

            // 再添加新图标
            if (!request.getIcons().isEmpty()) {
                List<SystemThemeIcon> icons = new ArrayList<>();
                request.getIcons().forEach(icon -> {
                    SystemThemeIcon themeIcon = new SystemThemeIcon();
                    BeanUtils.copyProperties(icon, themeIcon);
                    themeIcon.setThemeId(theme.getId());
                    icons.add(themeIcon);
                });
                systemThemeIconService.saveBatch(icons);
            }
        }
        return update;
    }

    /**
     * 删除主题
     * 
     * @param id 主题ID
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Integer id) {
        SystemTheme theme = getById(id);
        if (theme == null) {
            return false;
        }

        // 默认主题不能删除
        if (theme.getIsDefault()) {
            return false;
        }

        // 删除主题图标
        systemThemeIconService.deleteByThemeId(id);

        return removeById(id);
    }

    /**
     * 设置默认主题
     * 
     * @param id 主题ID
     * @return Boolean
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean setDefault(Integer id) {
        SystemTheme theme = getById(id);
        if (theme == null) {
            return false;
        }

        // 将所有主题设置为非默认
        setAllThemeNotDefault();

        // 将当前主题设置为默认
        theme.setIsDefault(true);
        theme.setUpdateTime(new Date());

        return updateById(theme);
    }

    /**
     * 获取当前主题
     * 
     * @return SystemTheme
     */
    @Override
    public SystemTheme getCurrentTheme() {
        // 获取当前时间
        Date now = new Date();

        // 先查找在时间范围内的主题
        LambdaQueryWrapper<SystemTheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemTheme::getStatus, true);
        queryWrapper.le(SystemTheme::getStartTime, now);
        queryWrapper.ge(SystemTheme::getEndTime, now);
        queryWrapper.orderByDesc(SystemTheme::getSort);
        queryWrapper.last("limit 1");
        SystemTheme theme = getOne(queryWrapper);

        // 如果没有找到，则返回默认主题
        if (theme == null) {
            LambdaQueryWrapper<SystemTheme> defaultQueryWrapper = new LambdaQueryWrapper<>();
            defaultQueryWrapper.eq(SystemTheme::getIsDefault, true);
            defaultQueryWrapper.eq(SystemTheme::getStatus, true);
            theme = getOne(defaultQueryWrapper);
        }

        return theme;
    }

    /**
     * 将所有主题设置为非默认
     */
    private void setAllThemeNotDefault() {
        LambdaQueryWrapper<SystemTheme> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemTheme::getIsDefault, true);
        List<SystemTheme> themes = list(queryWrapper);

        if (themes != null && !themes.isEmpty()) {
            themes.forEach(item -> {
                item.setIsDefault(false);
                item.setUpdateTime(new Date());
            });
            updateBatchById(themes);
        }
    }
}