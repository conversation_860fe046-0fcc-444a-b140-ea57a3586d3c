package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.order.StoreOrderStatus;
import com.ylpz.core.common.request.StoreOrderStatusSearchRequest;

import java.math.BigDecimal;
import java.util.List;

/**
 * StoreOrderStatusService 接口
 */
public interface StoreOrderStatusService extends IService<StoreOrderStatus> {

    /**
     * 订单操作记录列表
     * @param request 请求参数
     * @param pageParamRequest 分页参数
     * @return List
     */
    List<StoreOrderStatus> getList(StoreOrderStatusSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 根据实体参数获取
     * @param storeOrderStatus 订单状态参数
     * @return 订单状态结果
     */
    List<StoreOrderStatus> getByEntity(StoreOrderStatus storeOrderStatus);
}
