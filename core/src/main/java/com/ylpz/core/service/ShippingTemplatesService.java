package com.ylpz.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.ShippingTemplatesRequest;
import com.ylpz.core.common.request.ShippingTemplatesSearchRequest;
import com.ylpz.core.common.vo.ShippingTemplatesVo;
import com.ylpz.model.express.ShippingTemplates;

/**
* ShippingTemplatesService 接口
*/
public interface ShippingTemplatesService extends IService<ShippingTemplates> {

    List<ShippingTemplates> getList(ShippingTemplatesSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 新增运费模板
     * @param request 请求参数
     * @return 新增结果
     */
    Boolean create(ShippingTemplatesRequest request);

    Boolean update(Integer id, ShippingTemplatesRequest request);

    /**
     * 删除模板
     * @param id 模板id
     * @return Boolean
     */
    Boolean remove(Integer id);

    /**
     * 获取模板信息
     * @param id 模板id
     * @return ShippingTemplatesVo
     */
    ShippingTemplatesVo getInfo(Integer id);

    /**
     * 获取启用状态的模板列表
     * @return List<ShippingTemplates>
     */
    List<ShippingTemplates> getEnabledList();
}
