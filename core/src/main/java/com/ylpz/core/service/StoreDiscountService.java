package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SearchAndPageRequest;
import com.ylpz.model.discount.StoreDiscount;
import com.ylpz.core.common.request.StoreDiscountRequest;
import com.ylpz.core.common.request.StoreDiscountSearchRequest;
import com.ylpz.core.common.response.StoreDiscountFrontResponse;
import com.ylpz.core.common.response.StoreDiscountInfoResponse;

import java.util.List;

/**
 * StoreDiscountService 接口
 */
public interface StoreDiscountService extends IService<StoreDiscount> {

    List<StoreDiscount> getList(StoreDiscountSearchRequest request, PageParamRequest pageParamRequest);

    boolean create(StoreDiscountRequest request);

    StoreDiscount getInfoException(Integer id);

    StoreDiscountInfoResponse info(Integer id);

    /**
     * 根据折扣id获取
     * @param ids 折扣id集合
     * @return 折扣列表
     */
    List<StoreDiscount> getByIds(List<Integer> ids);

    /**
     * 扣减数量
     * @param id 折扣id
     * @param num 数量
     * @param isLimited 是否限量
     */
    Boolean deduction(Integer id, Integer num, Boolean isLimited);

    /**
     * 获取用户注册赠送新人折扣
     * @return 折扣列表
     */
    List<StoreDiscount> findRegisterList();

    /**
     * 发送折扣列表
     * @param searchAndPageRequest 搜索分页参数
     * @return 折扣列表
     */
    List<StoreDiscount> getSendList(SearchAndPageRequest searchAndPageRequest);

    /**
     * 删除折扣
     * @param id 折扣id
     * @return Boolean
     */
    Boolean delete(Integer id);

    /**
     * 移动端折扣列表
     * @param type 类型，1-通用，2-商品，3-品类
     * @param productId 产品id，搜索产品指定折扣
     * @param pageParamRequest 分页参数
     * @return List<StoreDiscountFrontResponse>
     */
    List<StoreDiscountFrontResponse> getH5List(Integer type, Integer productId, PageParamRequest pageParamRequest);

    /**
     * 修改折扣状态
     * @param id 折扣id
     * @param status 状态
     */
    Boolean updateStatus(Integer id, Boolean status);
} 