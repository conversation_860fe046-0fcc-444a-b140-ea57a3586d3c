package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.activity.StoreActivityCoupon;

import java.util.List;

/**
 * 活动优惠券关联表 Service 接口
 */
public interface StoreActivityCouponService extends IService<StoreActivityCoupon> {

    /**
     * 根据活动ID获取优惠券ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    List<Integer> getCouponIdsByActivityId(Integer activityId);

    /**
     * 保存活动优惠券关联
     * 
     * @param activityId 活动ID
     * @param couponIds 优惠券ID列表
     * @return Boolean
     */
    Boolean saveActivityCoupon(Integer activityId, List<Integer> couponIds);

    /**
     * 删除活动优惠券关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    Boolean deleteByActivityId(Integer activityId);
} 