package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.model.system.SystemCity;
import com.ylpz.core.common.request.SystemCityRequest;
import com.ylpz.core.common.request.SystemCitySearchRequest;
import com.ylpz.core.common.utils.RedisUtil;
import com.ylpz.core.common.vo.SystemCityTreeVo;
import com.ylpz.core.dao.SystemCityDao;
import com.ylpz.core.service.SystemCityAsyncService;
import com.ylpz.core.service.SystemCityService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.Map;
import java.util.HashMap;
import java.util.ArrayList;

/**
 * SystemCityServiceImpl 接口实现
 */
@Service
public class SystemCityServiceImpl extends ServiceImpl<SystemCityDao, SystemCity> implements SystemCityService {

    @Resource
    private SystemCityDao dao;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private SystemCityAsyncService systemCityAsyncService;

    /**
     * 列表
     * @param request 请求参数
     * @return List<SystemCity>
     */
    @Override
    public Object getList(SystemCitySearchRequest request) {
        // 如果只提供了父级ID但没有区域分类，则直接返回分组数据
        if (request.getRegionType() == null) {
            // 按区域分组返回数据
            return getCitiesByRegionGroups(request.getParentId());
        } else {
            // 如果提供了区域分类，则直接从数据库查询特定区域
            return getListWithRegionType(request.getParentId(), request.getRegionType());
        }
    }

    /**
     * 按区域分组获取城市数据
     * @param parentId 父级ID
     * @return Map<String, List<SystemCity>> 按区域分组的城市数据
     */
    private Map<String, List<SystemCity>> getCitiesByRegionGroups(Integer parentId) {
        // 先尝试从Redis获取分组数据
        String redisKey = Constants.CITY_LIST + "_group_" + parentId;
        Map<String, List<SystemCity>> result = redisUtil.get(redisKey);
        
        if (result != null) {
            return result;
        }
        
        // Redis中没有数据，从数据库查询并分组
        result = new HashMap<>();
        
        // 查询所有城市
        LambdaQueryWrapper<SystemCity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemCity::getParentId, parentId);
        lambdaQueryWrapper.eq(SystemCity::getIsShow, true);
        
        List<SystemCity> allCities = dao.selectList(lambdaQueryWrapper);
        
        // 按区域类型分组
        List<SystemCity> oneAndTwoAreaCities = new ArrayList<>();
        List<SystemCity> threeAreaCities = new ArrayList<>();
        List<SystemCity> hkMacaoTaiwanCities = new ArrayList<>();
        
        for (SystemCity city : allCities) {
            Integer regionType = city.getRegionType();
            if (regionType == null) {
                regionType = 1; // 默认为一二区
            }
            
            switch (regionType) {
                case 1:
                    oneAndTwoAreaCities.add(city);
                    break;
                case 2:
                    threeAreaCities.add(city);
                    break;
                case 3:
                    hkMacaoTaiwanCities.add(city);
                    break;
                default:
                    oneAndTwoAreaCities.add(city);
            }
        }
        
        // 添加到结果集
        result.put("oneAndTwoArea", oneAndTwoAreaCities);
        result.put("threeArea", threeAreaCities);
        result.put("hkMacaoTaiwan", hkMacaoTaiwanCities);
        result.put("all", allCities);
        
        // 将分组数据存入Redis缓存，缓存10分钟
        redisUtil.set(redisKey, result, 10L, TimeUnit.MINUTES);
        
        return result;
    }

    /**
     * 根据父级id获取数据
     * @param parentId integer parentId
     * <AUTHOR>
     * @since 2020-04-17
     * @return Object
     */
    private Object getList(Integer parentId) {
        LambdaQueryWrapper<SystemCity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemCity::getParentId, parentId);
        lambdaQueryWrapper.in(SystemCity::getIsShow, true);
        return dao.selectList(lambdaQueryWrapper);
    }
    
    /**
     * 根据父级id和区域分类获取数据
     * @param parentId 父级ID
     * @param regionType 区域分类
     * @return Object
     */
    private Object getListWithRegionType(Integer parentId, Integer regionType) {
        LambdaQueryWrapper<SystemCity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemCity::getParentId, parentId);
        if (regionType > 0) {
            lambdaQueryWrapper.eq(SystemCity::getRegionType, regionType);
        }
        lambdaQueryWrapper.in(SystemCity::getIsShow, true);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 修改状态
     * @param id 城市id
     * @param status 状态
     */
    @Override
    public Boolean updateStatus(Integer id, Boolean status) {
        SystemCity systemCity = getById(id);
        systemCity.setId(id);
        systemCity.setIsShow(status);
        boolean result = updateById(systemCity);
        if (result) {
            asyncRedis(systemCity.getParentId());
        }
        return result;
    }

    /**
     * 修改城市
     * @param id 城市id
     * @param request 修改参数
     */
    @Override
    public Boolean update(Integer id, SystemCityRequest request) {
        SystemCity systemCity = getById(id);
        systemCity.setParentId(request.getParentId());
        systemCity.setName(request.getName());
        
        // 设置区域分类，如果前端未传则保持原值
        if (request.getRegionType() != null) {
            systemCity.setRegionType(request.getRegionType());
        }
        
        boolean result = updateById(systemCity);
        if (result) {
            asyncRedis(systemCity.getParentId());
        }
        return result;
    }
    /**
     * 获取tree结构的列表
     * @return Object
     */
    @Override
    public List<SystemCityTreeVo> getListTree() {
        List<SystemCityTreeVo> cityList = redisUtil.get(Constants.CITY_LIST_TREE);
        if (CollUtil.isEmpty(cityList)) {
            systemCityAsyncService.setListTree();
        }
        return redisUtil.get(Constants.CITY_LIST_TREE);
    }

    /**
     * 获取所有城市cityId
     * @return List<Integer>
     */
    @Override
    public List<Integer> getCityIdList() {
        Object data = redisUtil.get(Constants.CITY_LIST_LEVEL_0);
        List<Integer> collect;
        if (data == null || "".equals(data)) {
            LambdaQueryWrapper<SystemCity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.select(SystemCity::getCityId);
            lambdaQueryWrapper.eq(SystemCity::getLevel, 0);
            lambdaQueryWrapper.eq(SystemCity::getIsShow, true);
            List<SystemCity> systemCityList = dao.selectList(lambdaQueryWrapper);
            collect = systemCityList.stream().map(SystemCity::getCityId).distinct().collect(Collectors.toList());
            redisUtil.set(Constants.CITY_LIST_LEVEL_0, collect, 10L, TimeUnit.MINUTES);
        } else {
            collect = (List<Integer>) data;
        }

        return collect;
    }

    /**
     * 根据city_id获取城市信息
     */
    @Override
    public SystemCity getCityByCityId(Integer cityId) {
        LambdaQueryWrapper<SystemCity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemCity::getCityId, cityId).eq(SystemCity::getIsShow, 1);
        return getOne(lambdaQueryWrapper);
    }


    /**
     * 数据整体刷入redis
     * <AUTHOR>
     * @since 2020-05-18
     */
    public void asyncRedis(Integer pid) {
        // 清除按区域分组的缓存
        String groupRedisKey = Constants.CITY_LIST + "_group_" + pid;
        redisUtil.delete(groupRedisKey);
        
        // 调用原有的异步刷新方法
        systemCityAsyncService.async(pid);
    }

    /**
     * 根据城市名称获取城市详细数据
     * <AUTHOR>
     * @param cityName 城市名称
     * @return 城市数据
     */
    @Override
    public SystemCity getCityByCityName(String cityName) {
        LambdaQueryWrapper<SystemCity> systemCityLambdaQueryWrapper = Wrappers.lambdaQuery();
        systemCityLambdaQueryWrapper
                .eq(SystemCity::getName,cityName)
                .eq(SystemCity::getIsShow,1)
                .eq(SystemCity::getLevel, 1);
        return getOne(systemCityLambdaQueryWrapper);
    }
}

