package com.ylpz.core.service;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.UserSpreadRecordRequest;
import com.ylpz.core.common.request.UserSpreadUpdateRequest;
import com.ylpz.core.common.response.UserSpreadDetailResponse;
import com.ylpz.core.common.response.UserSpreadRecordResponse;
import com.ylpz.core.common.request.PageParamRequest;

/**
 * 用户推广关系服务接口
 */
public interface UserSpreadService {

    /**
     * 获取会员上下级关系列表
     * 
     * @param request     筛选条件
     * @param pageRequest 分页参数
     * @return 会员上下级关系列表
     */
    PageInfo<UserSpreadRecordResponse> getSpreadRecordList(UserSpreadRecordRequest request,
            PageParamRequest pageRequest);

    /**
     * 获取会员上下级变更详情
     * 
     * @param uid 用户ID
     * @return 变更详情
     */
    UserSpreadDetailResponse getSpreadDetail(Integer uid);

    /**
     * 手动更新会员上级关系
     *
     * @param request 更新参数
     * @return 是否成功
     */
    boolean updateUserSpread(UserSpreadUpdateRequest request);

    /**
     * 处理用户等级升级后的推广关系自动脱落
     * 当用户升级到与上级相同或更高等级时，自动解除推广关系
     *
     * @param userId 升级的用户ID
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @return 是否发生了关系脱落
     */
    boolean processLevelUpgradeSpreadDetachment(Integer userId, Integer oldLevel, Integer newLevel);
}