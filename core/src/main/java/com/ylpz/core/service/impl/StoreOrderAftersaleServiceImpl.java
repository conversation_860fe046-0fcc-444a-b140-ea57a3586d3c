package com.ylpz.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.*;
import com.ylpz.core.common.response.AftersaleFlowResponse;
import com.ylpz.core.common.response.ExternalRefundResponse;
import com.ylpz.core.common.response.StoreOrderAftersaleDetailResponse;
import com.ylpz.core.common.response.StoreOrderAftersaleResponse;
import com.ylpz.core.dao.StoreOrderAftersaleDao;
import com.ylpz.core.dao.StoreOrderAftersaleRecordDao;
import com.ylpz.core.dao.StoreOrderInfoDao;
import com.ylpz.core.service.ExternalRefundService;
import com.ylpz.core.service.StoreOrderAftersaleService;
import com.ylpz.core.service.StoreOrderService;
import com.ylpz.core.service.SystemAdminService;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.core.service.UserBillService;
import com.ylpz.model.order.AfterSaleStatusEnum;
import com.ylpz.model.order.AfterSaleTypeEnum;
import com.ylpz.model.order.ProcessingMethodEnum;
import com.ylpz.model.order.PayTypeEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderAftersale;
import com.ylpz.model.order.StoreOrderAftersaleRecord;
import com.ylpz.model.order.StoreOrderInfo;
import com.ylpz.model.system.SystemAdmin;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBill;
import com.ylpz.model.user.UserCommissionRecord;
import com.ylpz.model.user.enums.UserBillEnum;
import com.ylpz.core.common.constants.Constants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 订单售后服务实现类
 */
@Slf4j
@Service
public class StoreOrderAftersaleServiceImpl extends ServiceImpl<StoreOrderAftersaleDao, StoreOrderAftersale> implements StoreOrderAftersaleService {

    @Resource
    private StoreOrderAftersaleDao storeOrderAftersaleDao;

    @Resource
    private StoreOrderAftersaleRecordDao storeOrderAftersaleRecordDao;

    @Resource
    private StoreOrderInfoDao storeOrderInfoDao;

    @Autowired
    private ExternalRefundService externalRefundService;

    @Autowired
    private SystemAdminService systemAdminService;

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    /**
     * 获取售后流程
     *
     * @param aftersaleInfo   售后信息对象
     * @param aftersaleType   售后类型
     * @param aftersaleStatus 售后状态
     * @param createTime      创建时间
     * @return 售后流程
     */
    private AftersaleFlowResponse getAftersaleFlow(StoreOrderAftersale aftersaleInfo, Integer aftersaleType, Integer aftersaleStatus, Date createTime) {
        AftersaleFlowResponse flowResponse = new AftersaleFlowResponse();

        // 设置售后类型
        flowResponse.setAftersaleType(aftersaleType);

        // 设置申请状态
        flowResponse.setApplyStatus(1);
        flowResponse.setApplyTime(createTime);

        // 设置快递单号信息（如果存在的话）
        if (StrUtil.isNotBlank(aftersaleInfo.getReturnDeliveryId())) {
            flowResponse.setReturnExpressNo(aftersaleInfo.getReturnDeliveryId());
        }
        if (StrUtil.isNotBlank(aftersaleInfo.getDeliveryId())) {
            flowResponse.setReshippingExpressNo(aftersaleInfo.getDeliveryId());
        }

        // 根据售后状态设置流程
        if (aftersaleStatus >= AfterSaleStatusEnum.WAITING_RETURN.getCode()) {
            // 查询售后记录
            LambdaQueryWrapper<StoreOrderAftersaleRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(StoreOrderAftersaleRecord::getAftersaleId, aftersaleInfo.getId());
            queryWrapper.orderByAsc(StoreOrderAftersaleRecord::getId);
            List<StoreOrderAftersaleRecord> recordList = storeOrderAftersaleRecordDao.selectList(queryWrapper);

            // 处理记录
            for (StoreOrderAftersaleRecord record : recordList) {
                // 处理流程通用步骤
                if (record.getAftersaleStatus() >= AfterSaleStatusEnum.WAITING_RETURN.getCode()) {
                    flowResponse.setProcessStatus(1);
                    flowResponse.setProcessTime(record.getCreateTime());
                }

                // 买家退货信息
                if (record.getAftersaleStatus() >= AfterSaleStatusEnum.WAITING_RECEIVE.getCode()) {
                    flowResponse.setReturnStatus(1);
                    flowResponse.setReturnTime(record.getCreateTime());

                    // 尝试从处理描述中获取快递公司
                    if (StrUtil.isNotBlank(record.getProcessingExplain())) {
                        String explain = record.getProcessingExplain();
                        if (explain.contains("快递公司:")) {
                            try {
                                String company = explain.substring(explain.indexOf("快递公司:") + 5).trim();
                                if (company.contains("\n")) {
                                    company = company.substring(0, company.indexOf("\n")).trim();
                                }
                                flowResponse.setReturnExpressCompany(company);
                            } catch (Exception e) {
                                // 解析失败，不设置快递公司
                            }
                        }
                    }
                }

                // 换货相关流程 - 重新发货信息
                if (aftersaleType == AfterSaleTypeEnum.EXCHANGE.getCode() &&
                        record.getAftersaleStatus() >= AfterSaleStatusEnum.WAITING_EXCHANGE_SHIP.getCode()) {
                    flowResponse.setReshippingStatus(1);
                    flowResponse.setReshippingTime(record.getCreateTime());

                    // 尝试从处理描述中获取快递公司
                    if (StrUtil.isNotBlank(record.getProcessingExplain())) {
                        String explain = record.getProcessingExplain();
                        if (explain.contains("快递公司:")) {
                            try {
                                String company = explain.substring(explain.indexOf("快递公司:") + 5).trim();
                                if (company.contains("\n")) {
                                    company = company.substring(0, company.indexOf("\n")).trim();
                                }
                                flowResponse.setReshippingExpressCompany(company);
                            } catch (Exception e) {
                                // 解析失败，不设置快递公司
                            }
                        }
                    }
                }

                // 交易完成
                if (record.getAftersaleStatus() >= AfterSaleStatusEnum.COMPLETED.getCode()) {
                    flowResponse.setCompleteStatus(1);
                    flowResponse.setCompleteTime(record.getCreateTime());
                }

                // 设置最终售后类型和退款金额（从售后记录中获取）
                if (record.getProcessingMethod() != null && record.getProcessingMethod() > 0) {
                    flowResponse.setFinalAftersaleType(record.getProcessingMethod());
                }
                if (record.getRefundPrice() != null && record.getRefundPrice().compareTo(BigDecimal.ZERO) > 0) {
                    flowResponse.setRefundAmount(record.getRefundPrice().doubleValue());
                }
            }
        }

        return flowResponse;
    }

    /**
     * 获取售后订单列表（使用专门的售后请求和响应对象）
     *
     * @param request          售后查询请求
     * @param pageParamRequest 分页参数
     * @return 售后订单列表
     */
    @Override
    public CommonPage<StoreOrderAftersaleResponse> getAftersaleList(StoreOrderAftersaleSearchRequest request, PageParamRequest pageParamRequest) {
        // 设置分页
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());

        // 处理售后状态查询
        if (StrUtil.isNotBlank(request.getStatusType())) {
            List<Integer> statusList = getStatusCodesByType(request.getStatusType());
            request.setStatusList(statusList);
        }

        // 查询售后订单列表
        List<StoreOrderAftersaleResponse> responseList = storeOrderAftersaleDao.getAftersaleList(request, request.getStatusList());

        // 获取分页信息
        PageInfo<StoreOrderAftersaleResponse> pageInfo = new PageInfo<>(responseList);
        responseList = pageInfo.getList();
        // 查询商品信息
        if (!responseList.isEmpty()) {
            // 查询售后商品信息
            for (StoreOrderAftersaleResponse response : responseList) {
                // 查询订单商品信息
                List<StoreOrderInfo> orderInfoList = storeOrderInfoDao.selectList(
                        new LambdaQueryWrapper<StoreOrderInfo>()
                                .eq(StoreOrderInfo::getOrderId, response.getOrderId())
                );

                if (!orderInfoList.isEmpty()) {
                    List<StoreOrderAftersaleResponse.AftersaleProductInfo> productInfoList = new ArrayList<>();
                    for (StoreOrderInfo orderInfo : orderInfoList) {
                        StoreOrderAftersaleResponse.AftersaleProductInfo productInfo = new StoreOrderAftersaleResponse.AftersaleProductInfo();
                        productInfo.setProductId(orderInfo.getProductId());
                        productInfo.setProductImage(orderInfo.getImage());
                        productInfo.setProductName(orderInfo.getProductName());
                        productInfo.setSkuName(orderInfo.getSku());
                        productInfo.setSkuNum(orderInfo.getPayNum());
                        productInfo.setProductPrice(orderInfo.getPrice());
                        productInfo.setSubtotalAmount(orderInfo.getPrice().multiply(new java.math.BigDecimal(orderInfo.getPayNum())));
                        productInfoList.add(productInfo);
                    }
                    response.setProductList(productInfoList);
                }

                // 查询退款时间
                if (response.getAftersaleStatus() >= AfterSaleStatusEnum.COMPLETED.getCode()) {
                    // 查询最后一条退款记录
                    LambdaQueryWrapper<StoreOrderAftersaleRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
                    recordQueryWrapper.eq(StoreOrderAftersaleRecord::getAftersaleId, response.getAftersaleId());
                    recordQueryWrapper.eq(StoreOrderAftersaleRecord::getAftersaleStatus, AfterSaleStatusEnum.COMPLETED.getCode());
                    recordQueryWrapper.orderByDesc(StoreOrderAftersaleRecord::getCreateTime);
                    recordQueryWrapper.last("LIMIT 1");
                    StoreOrderAftersaleRecord record = storeOrderAftersaleRecordDao.selectOne(recordQueryWrapper);
                    if (record != null) {
                        response.setRefundTime(record.getCreateTime());
                    }
                }
            }
        }

        return CommonPage.restPage(CommonPage.copyPageInfo(pageInfo, responseList));
    }

    /**
     * 根据状态类型获取对应的状态码列表
     *
     * @param statusType 状态类型
     * @return 状态码列表
     */
    private List<Integer> getStatusCodesByType(String statusType) {
        List<Integer> statusList = new ArrayList<>();

        switch (statusType) {
            case "all":
                // 全部状态
                for (AfterSaleStatusEnum status : AfterSaleStatusEnum.values()) {
                    if (status != AfterSaleStatusEnum.NONE) {
                        statusList.add(status.getCode());
                    }
                }
                break;
            case "pending":
                // 待审核处理
                statusList.add(AfterSaleStatusEnum.PENDING_REVIEW.getCode());
                break;
            case "waiting_return":
                // 待买家发货
                statusList.add(AfterSaleStatusEnum.WAITING_RETURN.getCode());
                break;
            case "waiting_receive":
                // 待取货验货
                statusList.add(AfterSaleStatusEnum.WAITING_RECEIVE.getCode());
                break;
            case "closed":
                // 已关闭
                statusList.add(AfterSaleStatusEnum.CANCELLED.getCode());
                break;
            case "completed":
                // 已完成
                statusList.add(AfterSaleStatusEnum.COMPLETED.getCode());
                break;
            case "abnormal":
                // 异常 - 这个状态已经被移除，改为使用已拒绝状态
                statusList.add(AfterSaleStatusEnum.REJECTED.getCode());
                break;
            default:
                break;
        }

        return statusList;
    }

    /**
     * 获取待处理售后数量
     * 与售后维权页面的"待审核处理"状态保持一致
     *
     * @return 待处理售后数量
     */
    @Override
    public Integer getPendingAftersaleCount() {
        LambdaQueryWrapper<StoreOrderAftersale> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StoreOrderAftersale::getAftersaleStatus, AfterSaleStatusEnum.PENDING_REVIEW.getCode());
        return Math.toIntExact(count(queryWrapper));
    }

    /**
     * 获取售后订单详情（使用专门的售后响应对象）
     *
     * @param aftersaleId 售后ID
     * @return 售后订单详情
     */
    @Override
    public StoreOrderAftersaleDetailResponse getAftersaleDetail(Integer aftersaleId) {
        // 查询售后详情
        StoreOrderAftersaleDetailResponse response = storeOrderAftersaleDao.getAftersaleDetail(aftersaleId);

        if (response == null) {
            return null;
        }

        // 查询商品信息
        List<StoreOrderInfo> orderInfoList = storeOrderInfoDao.selectList(
                new LambdaQueryWrapper<StoreOrderInfo>()
                        .eq(StoreOrderInfo::getOrderId, response.getOrderId())
        );

        if (!orderInfoList.isEmpty()) {
            List<StoreOrderAftersaleDetailResponse.AftersaleProductInfo> productInfoList = new ArrayList<>();
            for (StoreOrderInfo orderInfo : orderInfoList) {
                StoreOrderAftersaleDetailResponse.AftersaleProductInfo productInfo = new StoreOrderAftersaleDetailResponse.AftersaleProductInfo();
                productInfo.setProductId(orderInfo.getProductId());
                productInfo.setProductImage(orderInfo.getImage());
                productInfo.setProductName(orderInfo.getProductName());
                productInfo.setSkuName(orderInfo.getSku());
                productInfo.setSkuNum(orderInfo.getPayNum());
                productInfo.setProductPrice(orderInfo.getPrice());
                productInfo.setSubtotalAmount(orderInfo.getPrice().multiply(new java.math.BigDecimal(orderInfo.getPayNum())));
                productInfoList.add(productInfo);
            }
            response.setProductList(productInfoList);
        }

        // 查询售后信息
        StoreOrderAftersale aftersaleInfo = storeOrderAftersaleDao.selectById(aftersaleId);

        // 查询售后流程
        AftersaleFlowResponse flowResponse = getAftersaleFlow(aftersaleInfo, response.getAftersaleType(), response.getAftersaleStatus(), response.getApplyTime());
        response.setAftersaleFlow(flowResponse);

        // 查询售后日志
        List<StoreOrderAftersaleRecord> recordList = storeOrderAftersaleRecordDao.selectList(
                new LambdaQueryWrapper<StoreOrderAftersaleRecord>()
                        .eq(StoreOrderAftersaleRecord::getAftersaleId, aftersaleId)
                        .orderByAsc(StoreOrderAftersaleRecord::getCreateTime)
        );

        if (!recordList.isEmpty()) {
            List<StoreOrderAftersaleDetailResponse.AftersaleLogInfo> logInfoList = new ArrayList<>();
            for (StoreOrderAftersaleRecord record : recordList) {
                StoreOrderAftersaleDetailResponse.AftersaleLogInfo logInfo = new StoreOrderAftersaleDetailResponse.AftersaleLogInfo();
                logInfo.setId(record.getId());
                logInfo.setChangeType(record.getChangeType());
                logInfo.setChangeMessage(record.getChangeMessage());
                logInfo.setProcessingMethod(record.getProcessingMethod());
                logInfo.setProcessingExplain(record.getProcessingExplain());
                logInfo.setExchangeDeliveryId(record.getExchangeDeliveryId());
                logInfo.setRefundPrice(record.getRefundPrice());
                logInfo.setRejectReason(record.getRejectReason());
                logInfo.setAftersaleStatus(record.getAftersaleStatus());

                // 设置售后状态描述
                AfterSaleStatusEnum statusEnum = AfterSaleStatusEnum.getByCode(record.getAftersaleStatus());
                if (statusEnum != null) {
                    logInfo.setAftersaleStatusDesc(statusEnum.getDesc());
                }

                logInfo.setCreateTime(record.getCreateTime());
                logInfoList.add(logInfo);
            }
            response.setAftersaleLogs(logInfoList);
        }

        // 查询退款时间
        if (response.getAftersaleStatus() >= AfterSaleStatusEnum.COMPLETED.getCode()) {
            // 查询最后一条退款记录
            LambdaQueryWrapper<StoreOrderAftersaleRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
            recordQueryWrapper.eq(StoreOrderAftersaleRecord::getAftersaleId, aftersaleId);
            recordQueryWrapper.eq(StoreOrderAftersaleRecord::getAftersaleStatus, AfterSaleStatusEnum.COMPLETED.getCode());
            recordQueryWrapper.orderByDesc(StoreOrderAftersaleRecord::getCreateTime);
            recordQueryWrapper.last("LIMIT 1");
            StoreOrderAftersaleRecord record = storeOrderAftersaleRecordDao.selectOne(recordQueryWrapper);
            if (record != null) {
                response.setRefundTime(record.getCreateTime());
            }
        }

        return response;
    }

    /**
     * 拒绝售后申请
     *
     * @param request 拒绝请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean rejectAftersale(AftersaleRejectRequest request) {
        // 查询售后申请详情
        StoreOrderAftersale aftersale = getById(request.getAftersaleId());
        if (aftersale == null) {
            throw new RuntimeException("售后申请不存在");
        }

        // 检查售后状态，只有处于待审核状态才能拒绝
        if (!AfterSaleStatusEnum.PENDING_REVIEW.getCode().equals(aftersale.getAftersaleStatus())) {
            throw new RuntimeException("只有待审核状态的售后申请才能被拒绝");
        }

        // 更新售后申请状态为已拒绝
        aftersale.setAftersaleStatus(AfterSaleStatusEnum.REJECTED.getCode());
        aftersale.setUpdateTime(new Date());
        boolean updateResult = updateById(aftersale);

        if (updateResult) {
            // 添加售后记录
            StoreOrderAftersaleRecord record = new StoreOrderAftersaleRecord();
            record.setAftersaleId(request.getAftersaleId());
            record.setChangeType("reject");
            record.setChangeMessage("商家拒绝了售后申请");
            record.setRejectReason(request.getRejectReason());
            record.setAftersaleStatus(AfterSaleStatusEnum.REJECTED.getCode());
            record.setCreateTime(new Date());
            storeOrderAftersaleRecordDao.insert(record);
        }

        return updateResult;
    }

    /**
     * 处理售后申请
     *
     * @param request 处理请求
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean processAftersale(AftersaleProcessRequest request) {
        // 查询售后申请详情
        StoreOrderAftersale aftersale = getById(request.getAftersaleId());
        if (aftersale == null) {
            throw new RuntimeException("售后申请不存在");
        }

        // 检查售后状态，只有处于待审核状态才能进行处理
        if (!AfterSaleStatusEnum.PENDING_REVIEW.getCode().equals(aftersale.getAftersaleStatus())) {
            throw new RuntimeException("只有待审核状态的售后申请才能被处理");
        }

        // 根据处理方式进行验证和状态更新
        Integer processingMethod = request.getProcessingMethod();
        if (processingMethod == null) {
            throw new RuntimeException("处理方式不能为空");
        }

        // 根据不同处理方式进行特定处理
        Integer newStatus = null;
        String changeType = "process";
        String changeMessage = "";

        // 根据请求的处理方式进行相应处理
        boolean needRefund = false; // 是否需要退款
        boolean refundSuccess = true; // 退款是否成功，默认为true（对于不需要退款的情况）

        if (request.isRefundOnly()) {
            // 仅退款
            if (request.getRefundPrice() == null) {
                throw new RuntimeException("退款金额不能为空");
            }
            aftersale.setRefundPrice(request.getRefundPrice());
            needRefund = true;

            // 调用退款接口
            refundSuccess = refund(aftersale, request.getRefundPrice());
            if (refundSuccess) {
                // 退款成功，进入售后完成状态
                newStatus = AfterSaleStatusEnum.COMPLETED.getCode();
                changeMessage = "商家同意退款申请，退款已完成";
            } else {
                // 退款失败，进入拒绝状态
                newStatus = AfterSaleStatusEnum.REJECTED.getCode();
                changeMessage = "退款处理失败，请联系客服";
            }
        } else if (request.isExchange()) {
            // 换货
            if (StrUtil.isBlank(request.getExchangeDeliveryId())) {
                throw new RuntimeException("换货单号不能为空");
            }
            // 对于换货，设置为换货发货状态
            newStatus = AfterSaleStatusEnum.WAITING_EXCHANGE_SHIP.getCode();
            changeMessage = "商家同意换货申请，等待换货发货";
        } else if (request.isSmallCompensation()) {
            // 小额补偿
            if (request.getRefundPrice() == null) {
                throw new RuntimeException("补偿金额不能为空");
            }
            aftersale.setRefundPrice(request.getRefundPrice());
            needRefund = true;

            // 调用退款接口
            refundSuccess = refund(aftersale, request.getRefundPrice());
            if (refundSuccess) {
                // 补偿成功，进入售后完成状态
                newStatus = AfterSaleStatusEnum.COMPLETED.getCode();
                changeMessage = "商家同意小额补偿，补偿已完成";
            } else {
                // 补偿失败，进入拒绝状态
                newStatus = AfterSaleStatusEnum.REJECTED.getCode();
                changeMessage = "补偿处理失败，请联系客服";
            }
        } else {
            throw new RuntimeException("无效的处理方式");
        }

        // 如果需要退款但退款失败，则抛出异常回滚事务
        if (needRefund && !refundSuccess) {
            throw new RuntimeException("退款处理失败，售后申请处理中止");
        }

        // 更新售后状态
        aftersale.setAftersaleStatus(newStatus);
        aftersale.setUpdateTime(new Date());
        boolean updateResult = updateById(aftersale);

        if (updateResult) {
            // 添加售后记录
            //售后日志在售后模块里面 他是对应协商记录
            //发起售后→审核记录→寄回商品→确认收货→商家发货（商家退款），还有用户取消售后
            StoreOrderAftersaleRecord record = new StoreOrderAftersaleRecord();
            record.setAftersaleId(request.getAftersaleId());
            record.setChangeType(changeType);
            record.setChangeMessage(changeMessage);
            record.setProcessingMethod(processingMethod);
            record.setProcessingExplain(request.getProcessingExplain());
            record.setExchangeDeliveryId(request.getExchangeDeliveryId());
            record.setRefundPrice(request.getRefundPrice());
            record.setAftersaleStatus(newStatus);
            record.setCreateTime(new Date());
            storeOrderAftersaleRecordDao.insert(record);

            // 处理佣金失效逻辑
            handleCommissionInvalidation(aftersale, request);
        }

        return updateResult;
    }

    //退款接口（参数：售后申请ID、金额）
    private Boolean refund(StoreOrderAftersale aftersale, BigDecimal amount) {
        log.info("开始调用退款接口，售后申请ID：{}，金额：{}", aftersale, amount);
        Integer id = aftersale.getId();
        try {
            // 获取订单信息
            Integer orderId = aftersale.getOrderId();
            StoreOrder order = storeOrderService.getById(orderId);
            if (order == null) {
                log.error("关联订单不存在，orderID: {}", orderId);
                return false;
            }

            // 根据支付方式判断退款逻辑
            String payType = order.getPayType();
            log.info("订单支付方式：{}，开始处理退款", payType);

            if (PayTypeEnum.YUE.getCode().equals(payType)) {
                // 余额支付，直接退回用户余额
                return processBalanceRefund(aftersale, order, amount);
            } else {
                // 其他支付方式（微信支付等），走外部退款接口
                return processExternalRefund(aftersale, order, amount);
            }
        } catch (Exception e) {
            log.error("退款处理异常，售后申请ID: {}", id, e);
            return false;
        }
    }

    /**
     * 处理余额支付退款
     * @return 退款是否成功
     */
    private Boolean processBalanceRefund(StoreOrderAftersale aftersale, StoreOrder order, BigDecimal amount) {
        Integer id = aftersale.getId();
        Integer uid = order.getUid();

        try {
            // 获取用户信息
            User user = userService.getById(uid);
            if (user == null) {
                log.error("用户不存在，用户ID: {}", uid);
                updateAftersaleStatus(aftersale, AfterSaleStatusEnum.REJECTED.getCode(),
                        "退款失败，用户不存在", amount);
                return false;
            }

            // 更新用户余额
            BigDecimal currentBalance = user.getNowMoney();
            boolean updateResult = userService.operationNowMoney(uid, amount, currentBalance, "add");

            if (updateResult) {
                // 保存退款账单记录
                UserBill userBill = new UserBill();
                userBill.setTitle(UserBillEnum.REFUND.getTitle());
                userBill.setUid(uid);
                userBill.setCategory(Constants.USER_BILL_CATEGORY_MONEY);
                userBill.setType(UserBillEnum.REFUND.getType());
                userBill.setNumber(amount);
                userBill.setLinkId(order.getId().toString());
                userBill.setBalance(currentBalance.add(amount));
                userBill.setMark("售后退款到余额" + amount + "元，售后单号：" + id);
                userBill.setPm(1); // 收入
                userBill.setStatus(1); // 有效
                userBill.setCreateTime(new Date());
                userBillService.save(userBill);

                log.info("余额退款成功，售后申请ID: {}, 用户ID: {}, 退款金额: {}", id, uid, amount);
                // 更新售后申请状态为退款完成
                updateAftersaleStatus(aftersale, AfterSaleStatusEnum.COMPLETED.getCode(),
                        "余额退款已完成，退款金额：" + amount, amount);
                return true;
            } else {
                log.error("余额退款失败，用户余额更新失败，售后申请ID: {}, 用户ID: {}", id, uid);
                updateAftersaleStatus(aftersale, AfterSaleStatusEnum.REJECTED.getCode(),
                        "退款失败，余额更新失败", amount);
                return false;
            }
        } catch (Exception e) {
            log.error("余额退款处理异常，售后申请ID: {}, 用户ID: {}", id, uid, e);
            updateAftersaleStatus(aftersale, AfterSaleStatusEnum.REJECTED.getCode(),
                    "退款失败，系统异常：" + e.getMessage(), amount);
            return false;
        }
    }

    /**
     * 处理外部支付退款（微信支付等）
     * @return 退款是否成功
     */
    private Boolean processExternalRefund(StoreOrderAftersale aftersale, StoreOrder order, BigDecimal amount) {
        Integer id = aftersale.getId();

        try {
            // 获取当前操作管理员信息
            Integer operatorId = 1; // 系统默认管理员ID
            String operatorName = "系统管理员";
            SystemAdmin info = systemAdminService.getInfo();
            if (info != null) {
                operatorId = info.getId();
                operatorName = info.getRealName();
            }

            // 构建退款请求
            ExternalRefundRequest request = new ExternalRefundRequest();
            request.setOperatorId(operatorId);
            request.setOperatorName(operatorName);
            request.setOperatorType(1); // 0表示后台管理员，小程序让传1
            request.setAmount(amount);
            request.setOrderNo(order.getOrderId());
            request.setRefundReason("售后申请退款，售后单号：" + id);

            // 调用外部退款接口
            ExternalRefundResponse response = externalRefundService.refundOrder(request);

            // 处理退款结果
            if (response.getSuccess()) {
                log.info("外部退款成功，售后申请ID: {}, 交易流水号: {}", id, response.getTradeNo());
                updateAftersaleStatus(aftersale, AfterSaleStatusEnum.COMPLETED.getCode(),
                        "退款已完成，退款金额：" + amount, amount);
                return true;
            } else {
                log.error("外部退款失败，售后申请ID: {}, 错误信息: {}", id, response.getMessage());
                updateAftersaleStatus(aftersale, AfterSaleStatusEnum.REJECTED.getCode(),
                        "退款失败，原因：" + response.getMessage(), amount);
                return false;
            }
        } catch (Exception e) {
            log.error("外部退款处理异常，售后申请ID: {}", id, e);
            updateAftersaleStatus(aftersale, AfterSaleStatusEnum.REJECTED.getCode(),
                    "退款失败，系统异常：" + e.getMessage(), amount);
            return false;
        }
    }

    /**
     * 更新售后申请状态并添加记录
     */
    private void updateAftersaleStatus(StoreOrderAftersale aftersale, Integer status, String message, BigDecimal amount) {
        try {
            // 更新售后申请状态
            aftersale.setAftersaleStatus(status);
            aftersale.setUpdateTime(new Date());
            updateById(aftersale);

            // 添加售后记录
            StoreOrderAftersaleRecord record = new StoreOrderAftersaleRecord();
            record.setAftersaleId(aftersale.getId());
            record.setChangeType(status.equals(AfterSaleStatusEnum.COMPLETED.getCode()) ? "refund_complete" : "refund_failed");
            record.setChangeMessage(message);
            record.setRefundPrice(amount);
            record.setAftersaleStatus(status);
            record.setCreateTime(new Date());
            storeOrderAftersaleRecordDao.insert(record);
        } catch (Exception e) {
            log.error("更新售后状态异常，售后申请ID: {}", aftersale.getId(), e);
        }
    }

    /**
     * 处理佣金失效逻辑
     * 当同意退货退款或仅退款时，将相关佣金记录状态修改为已失效
     *
     * @param aftersale 售后申请信息
     * @param request 处理请求
     */
    private void handleCommissionInvalidation(StoreOrderAftersale aftersale, AftersaleProcessRequest request) {
        try {
            // 判断是否需要将佣金设为失效
            boolean shouldInvalidateCommission = shouldInvalidateCommission(aftersale, request);

            if (!shouldInvalidateCommission) {
                log.info("售后申请ID: {}, 售后类型: {}, 处理方式: {}, 不需要将佣金设为失效",
                        aftersale.getId(), aftersale.getAftersaleType(), request.getProcessingMethod());
                return;
            }

            // 获取订单信息
            StoreOrder order = storeOrderService.getById(aftersale.getOrderId());
            if (order == null) {
                log.error("售后申请关联的订单不存在，售后申请ID: {}, 订单ID: {}", aftersale.getId(), aftersale.getOrderId());
                return;
            }

            // 查询与该订单相关的佣金记录
            LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCommissionRecord::getLinkId, order.getOrderId());
            queryWrapper.in(UserCommissionRecord::getStatus, 1, 2); // 待结算或已结算状态

            List<UserCommissionRecord> commissionRecords = userCommissionRecordService.list(queryWrapper);

            if (commissionRecords.isEmpty()) {
                log.info("订单{}没有找到相关的佣金记录", order.getOrderId());
                return;
            }

            // 将佣金记录状态修改为已失效
            int invalidatedCount = 0;
            for (UserCommissionRecord record : commissionRecords) {
                record.setStatus(3); // 3-已失效
                record.setUpdateTime(new Date());

                boolean updateSuccess = userCommissionRecordService.updateById(record);
                if (updateSuccess) {
                    invalidatedCount++;
                    log.info("佣金记录失效成功 - 记录ID: {}, 订单号: {}, 售后申请ID: {}, 原状态: {}, 金额: {}",
                            record.getId(), order.getOrderId(), aftersale.getId(),
                            record.getStatus() == 1 ? "待结算" : "已结算", record.getPrice());
                } else {
                    log.error("佣金记录失效失败 - 记录ID: {}, 订单号: {}", record.getId(), order.getOrderId());
                }
            }

            log.info("售后申请ID: {}, 订单号: {}, 共处理{}条佣金记录，成功失效{}条",
                    aftersale.getId(), order.getOrderId(), commissionRecords.size(), invalidatedCount);

        } catch (Exception e) {
            log.error("处理佣金失效逻辑时发生异常，售后申请ID: {}", aftersale.getId(), e);
        }
    }

    /**
     * 判断是否应该将佣金设为失效
     *
     * @param aftersale 售后申请信息
     * @param request 处理请求
     * @return 是否应该失效佣金
     */
    private boolean shouldInvalidateCommission(StoreOrderAftersale aftersale, AftersaleProcessRequest request) {
        // 获取售后类型和处理方式
        Integer aftersaleType = aftersale.getAftersaleType();
        Integer processingMethod = request.getProcessingMethod();

        // 售后类型为退货退款(1)或仅退款(4)时，需要将佣金设为失效
        boolean isRefundAftersaleType = AfterSaleTypeEnum.RETURN_REFUND.getCode().equals(aftersaleType)
                || AfterSaleTypeEnum.REFUND_ONLY.getCode().equals(aftersaleType) ;

        // 处理方式为仅退款(1)时，需要将佣金设为失效
        boolean isRefundProcessingMethod = ProcessingMethodEnum.REFUND_ONLY.getCode().equals(processingMethod);

        // 满足任一条件即需要失效佣金
        return isRefundAftersaleType || isRefundProcessingMethod;
    }
}