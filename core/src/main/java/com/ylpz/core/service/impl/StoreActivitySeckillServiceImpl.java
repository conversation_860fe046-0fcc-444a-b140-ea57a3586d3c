package com.ylpz.core.service.impl;

import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.StoreActivitySeckillDao;
import com.ylpz.core.service.StoreActivitySeckillService;
import com.ylpz.model.activity.StoreActivitySeckill;

import cn.hutool.core.util.ObjectUtil;

/**
 * 活动秒杀关联表 Service 实现类
 */
@Service
public class StoreActivitySeckillServiceImpl extends ServiceImpl<StoreActivitySeckillDao, StoreActivitySeckill>
    implements StoreActivitySeckillService {

    @Resource
    private StoreActivitySeckillDao dao;

    @Resource
    private TransactionTemplate transactionTemplate;

    /**
     * 根据活动ID获取秒杀ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    @Override
    public List<Integer> getSeckillIdsByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivitySeckill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivitySeckill::getActivityId, activityId)
            .eq(StoreActivitySeckill::getIsDel, false);
        List<StoreActivitySeckill> list = list(lambdaQueryWrapper);
        List<Integer> seckillIds = new ArrayList<>();
        for (StoreActivitySeckill activitySeckill : list) {
            seckillIds.add(activitySeckill.getSeckillId());
        }
        return seckillIds;
    }

    /**
     * 保存活动秒杀关联
     * 
     * @param activityId 活动ID
     * @param seckillIds 秒杀ID列表
     * @return Boolean
     */
    @Override
    public Boolean saveActivitySeckill(Integer activityId, List<Integer> seckillIds) {
        if (ObjectUtil.isEmpty(seckillIds)) {
            return Boolean.TRUE;
        }
        return transactionTemplate.execute(e -> {
            // 删除原有关联
            deleteByActivityId(activityId);
            // 保存新关联
            List<StoreActivitySeckill> activitySeckillList = new ArrayList<>();
            for (Integer seckillId : seckillIds) {
                StoreActivitySeckill activitySeckill = new StoreActivitySeckill();
                activitySeckill.setActivityId(activityId);
                activitySeckill.setSeckillId(seckillId);
                activitySeckillList.add(activitySeckill);
            }
            return saveBatch(activitySeckillList);
        });
    }

    /**
     * 删除活动秒杀关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    @Override
    public Boolean deleteByActivityId(Integer activityId) {
        LambdaQueryWrapper<StoreActivitySeckill> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(StoreActivitySeckill::getActivityId, activityId)
            .eq(StoreActivitySeckill::getIsDel, false);
        List<StoreActivitySeckill> list = list(lambdaQueryWrapper);
        for (StoreActivitySeckill activitySeckill : list) {
            activitySeckill.setIsDel(true);
        }
        return updateBatchById(list);
    }
} 