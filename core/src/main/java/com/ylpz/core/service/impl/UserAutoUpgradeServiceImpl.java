package com.ylpz.core.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.service.*;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserLevel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * 用户自动升级服务实现类
 * 
 * <AUTHOR>
 * @since 2025-01-10
 */
@Slf4j
@Service
public class UserAutoUpgradeServiceImpl implements UserAutoUpgradeService {

    @Autowired
    private UserService userService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private RankingTaskService rankingTaskService;

    @Autowired
    private UserSpreadService userSpreadService;

    /**
     * 检查并执行用户自动升级
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean checkAndUpgradeUser(User user) {
        if (user == null || user.getId() == null) {
            return false;
        }

        try {
            // 检查升级条件
            Integer targetLevelId = checkUpgradeCondition(user);
            if (targetLevelId == null) {
                return false;
            }

            // 执行升级
            return executeUpgrade(user, targetLevelId);
        } catch (Exception e) {
            log.error("用户自动升级失败，用户ID: {}, 错误信息: {}", user.getId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量检查所有用户的升级条件
     */
    @Override
    public String batchCheckAndUpgradeUsers() {
        log.info("开始执行用户自动升级批量检查任务");
        
        int totalChecked = 0;
        int totalUpgraded = 0;
        int errorCount = 0;

        try {
            // 获取所有启用的用户等级配置，按等级排序
            List<SystemUserLevel> userLevels = systemUserLevelService.getUsableList();
            if (userLevels.isEmpty()) {
                return "没有可用的用户等级配置";
            }

            // 遍历每个等级，检查符合条件的用户
            for (SystemUserLevel level : userLevels) {
                // 跳过不支持自动升级的等级
                if (level.getAutoUpgrade() == null || !level.getAutoUpgrade()) {
                    continue;
                }

                //跳过普通会员，默认就是
                if (MemberLevelConstants.Level.NORMAL.equals(level.getGrade())) {
                    continue;
                }
                // 查找经验值达到要求但等级未达到的用户
                LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.ge(User::getExperience, level.getExperience())
                           .lt(User::getLevel, level.getGrade())
                           .eq(User::getStatus, true);

                List<User> candidateUsers = userService.list(queryWrapper);
                totalChecked += candidateUsers.size();

                for (User user : candidateUsers) {
                    try {
                        if (checkAndUpgradeUser(user)) {
                            totalUpgraded++;
                            log.info("用户自动升级成功: 用户ID={}, 昵称={}, 新等级={}", 
                                   user.getId(), user.getNickname(), level.getName());
                        }
                    } catch (Exception e) {
                        errorCount++;
                        log.error("用户自动升级失败: 用户ID={}, 错误信息={}", user.getId(), e.getMessage());
                    }
                }
            }

        } catch (Exception e) {
            log.error("批量用户升级检查任务执行异常", e);
            return StrUtil.format("任务执行异常: {}", e.getMessage());
        }

        String result = StrUtil.format("用户自动升级任务完成 - 检查用户数: {}, 升级用户数: {}, 错误数: {}", 
                                     totalChecked, totalUpgraded, errorCount);
        log.info(result);
        return result;
    }

    /**
     * 检查用户是否满足升级条件
     */
    @Override
    public Integer checkUpgradeCondition(User user) {
        if (user.getExperience() == null || user.getLevel() == null) {
            return null;
        }

        // 获取所有启用的用户等级，按等级排序
        List<SystemUserLevel> userLevels = systemUserLevelService.getUsableList();
        
        // 找到用户当前可以升级到的最高等级
        Integer targetLevelId = null;
        for (SystemUserLevel level : userLevels) {
            // 检查是否支持自动升级
            if (level.getAutoUpgrade() == null || !level.getAutoUpgrade()) {
                continue;
            }
            
            // 检查用户经验值是否达到要求，且当前等级低于目标等级
            if (user.getExperience() >= level.getExperience() && 
                user.getLevel() < level.getGrade()) {
                targetLevelId = level.getId();
            }
        }

        return targetLevelId;
    }

    /**
     * 执行用户升级操作
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean executeUpgrade(User user, Integer targetLevelId) {
        SystemUserLevel targetLevel = systemUserLevelService.getById(targetLevelId);
        if (targetLevel == null) {
            log.error("目标等级不存在: levelId={}", targetLevelId);
            return false;
        }

        Integer oldLevel = user.getLevel();
        
        // 更新用户等级
        user.setLevel(targetLevel.getGrade());
        boolean updateResult = userService.updateById(user);
        
        if (!updateResult) {
            log.error("更新用户等级失败: userId={}, targetLevel={}", user.getId(), targetLevel.getGrade());
            return false;
        }

        // 记录升级日志
        recordUpgradeLog(user, oldLevel, targetLevel.getGrade(), "经验值自动升级");

        // 创建用户等级记录
        UserLevel userLevelRecord = new UserLevel();
        userLevelRecord.setUid(user.getId());
        userLevelRecord.setLevelId(targetLevelId);
        userLevelRecord.setGrade(targetLevel.getGrade());
        userLevelRecord.setStatus(true);
        userLevelRecord.setMark(StrUtil.format("用户 {} 经验值达到 {} 点，自动升级为 {}", 
                                             user.getNickname(), user.getExperience(), targetLevel.getName()));
        userLevelRecord.setDiscount(targetLevel.getDiscount());
        userLevelRecord.setCreateTime(DateUtil.date());
        
        userLevelService.save(userLevelRecord);

        // 触发升级后的奖励逻辑
        triggerUpgradeRewards(user, oldLevel, targetLevel.getGrade());

        return true;
    }

    /**
     * 记录升级日志
     */
    @Override
    public void recordUpgradeLog(User user, Integer oldLevel, Integer newLevel, String upgradeReason) {
        log.info("用户等级升级记录: 用户ID={}, 昵称={}, 原等级={}, 新等级={}, 升级原因={}", 
               user.getId(), user.getNickname(), oldLevel, newLevel, upgradeReason);
    }

    /**
     * 触发升级后的奖励逻辑
     */
    @Override
    public void triggerUpgradeRewards(User user, Integer oldLevel, Integer newLevel) {
        try {
            // 处理推广关系自动脱落逻辑
            userSpreadService.processLevelUpgradeSpreadDetachment(user.getId(), oldLevel, newLevel);

            // 如果用户从普通会员升级为VIP会员，触发推广奖励
            if (oldLevel != null && oldLevel.equals(MemberLevelConstants.Level.NORMAL) &&
                newLevel.equals(MemberLevelConstants.Level.VIP)) {
                rankingTaskService.processUpgradeToVipBonus(user);
            }

            // 如果用户从VIP会员升级为SVIP会员，触发推广奖励
            if (oldLevel != null && oldLevel.equals(MemberLevelConstants.Level.VIP) &&
                newLevel.equals(MemberLevelConstants.Level.SVIP)) {
                rankingTaskService.processUpgradeToSvipBonus(user);
            }

            log.info("升级奖励逻辑处理完成: 用户ID={}, 原等级={}, 新等级={}", user.getId(), oldLevel, newLevel);
        } catch (Exception e) {
            log.error("处理升级奖励逻辑失败: 用户ID={}, 错误信息={}", user.getId(), e.getMessage(), e);
        }
    }
}
