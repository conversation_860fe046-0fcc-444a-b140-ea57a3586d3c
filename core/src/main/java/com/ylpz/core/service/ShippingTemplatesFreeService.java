package com.ylpz.core.service;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.ShippingTemplatesFreeRequest;
import com.ylpz.model.express.ShippingTemplatesFree;

/**
 * 包邮模板服务接口
 */
public interface ShippingTemplatesFreeService extends IService<ShippingTemplatesFree> {

    /**
     * 保存包邮模板
     * 
     * @param request 包邮模板请求对象
     * @return 是否保存成功
     */
    boolean save(List<ShippingTemplatesFreeRequest> request);

    /**
     * 根据包邮金额分组获取包邮模板
     * 
     * @return 包邮模板列表
     */
    List<ShippingTemplatesFree> getShippingTemplatesFreeGroupByPrice();

    Map<BigDecimal, List<ShippingTemplatesFree>> getGroupShippingTemplatesFreeData();
}
