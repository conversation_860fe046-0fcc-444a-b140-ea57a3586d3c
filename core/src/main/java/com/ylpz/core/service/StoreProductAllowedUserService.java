package com.ylpz.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.product.StoreProductAllowedUser;

/**
 * 商品允许购买的特定用户服务接口
 */
@Deprecated
public interface StoreProductAllowedUserService extends IService<StoreProductAllowedUser> {

    /**
     * 根据商品ID获取允许购买的用户列表
     * 
     * @param productId 商品ID
     * @return 允许购买的用户ID列表
     */
    List<Integer> getAllowedUserIdsByProductId(Integer productId);

    /**
     * 批量保存商品允许购买的用户
     * 
     * @param productId 商品ID
     * @param userIds 用户ID列表
     * @return 是否成功
     */
    boolean saveAllowedUsers(Integer productId, List<Integer> userIds);

    /**
     * 删除商品所有允许购买的用户
     * 
     * @param productId 商品ID
     * @return 是否成功
     */
    boolean deleteByProductId(Integer productId);

    /**
     * 检查用户是否允许购买指定商品
     * 
     * @param productId 商品ID
     * @param uid 用户ID
     * @return 是否允许购买
     */
    boolean isUserAllowedToBuy(Integer productId, Integer uid);
}