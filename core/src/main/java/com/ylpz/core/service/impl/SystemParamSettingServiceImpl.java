package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.dao.SystemParamSettingDao;
import com.ylpz.core.service.SystemParamSettingService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.system.SystemUserLevel;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统参数设置Service实现类
 */
@Slf4j
@Service
public class SystemParamSettingServiceImpl extends ServiceImpl<SystemParamSettingDao, SystemParamSetting>
        implements SystemParamSettingService {

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 获取所有系统参数设置列表
     *
     * @return 系统参数设置列表
     */
    @Override
    public List<SystemParamSetting> getList() {
        LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByAsc(SystemParamSetting::getModuleName).orderByAsc(SystemParamSetting::getId).orderByAsc(SystemParamSetting::getSort);
        return list(queryWrapper);
    }

    /**
     * 根据模块名称获取系统参数设置列表
     *
     * @param moduleName 模块名称
     * @return 系统参数设置列表
     */
    @Override
    public List<SystemParamSetting> getListByModuleName(String moduleName) {
        LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SystemParamSetting::getModuleName, moduleName)
                .orderByAsc(SystemParamSetting::getSort);
        return list(queryWrapper);
    }

    /**
     * 获取按模块类型分组的系统参数设置
     *
     * @param moduleType 模块类型：member-会员相关模块，order-订单相关模块
     * @return 按模块分组的系统参数设置Map
     */
    @Override
    public Map<String, List<SystemParamSetting>> getGroupedConfigByModuleType(String moduleType) {
        List<SystemParamSetting> allSettings = getList();
        List<SystemParamSetting> filteredSettings;

        if ("member".equalsIgnoreCase(moduleType)) {
            // 会员相关模块
            filteredSettings = allSettings.stream()
                    .filter(setting -> "成长值设置".equals(setting.getModuleName())
                            || "提现设置".equals(setting.getModuleName())
                            || "佣金返现设置".equals(setting.getModuleName())
                            || "奖励金设置".equals(setting.getModuleName()))
                    .collect(Collectors.toList());
        } else if ("order".equalsIgnoreCase(moduleType)) {
            // 订单相关模块
            filteredSettings = allSettings.stream()
                    .filter(setting -> "交易设置".equals(setting.getModuleName())
                            || "打单设置".equals(setting.getModuleName())
                            || "地址管理".equals(setting.getModuleName()))
                    .collect(Collectors.toList());
        } else {
            // 未指定类型，返回所有设置
            filteredSettings = allSettings;
        }

        return filteredSettings.stream()
                .collect(Collectors.groupingBy(SystemParamSetting::getModuleName));
    }

    /**
     * 按模块批量保存或更新系统参数设置
     *
     * @param moduleName 模块名称
     * @param configList 配置列表
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveOrUpdateBatchByModule(String moduleName, List<SystemParamSetting> configList) {
        if (CollectionUtils.isEmpty(configList)) {
            return true; // 空列表直接返回成功
        }

        // 设置当前时间
        Date now = new Date();

        // 更新时间和模块名称
        for (SystemParamSetting config : configList) {
            config.setModuleName(moduleName);

            if (config.getId() == null) {
                // 新增的配置
                config.setCreateTime(now);
                config.setUpdateTime(now);

                // 设置默认值
                if (config.getStatus() == null) {
                    config.setStatus(true);
                }
                if (config.getSort() == null) {
                    config.setSort(0);
                }
            } else {
                // 更新的配置
                config.setUpdateTime(now);
            }
        }

        // 批量保存或更新
        boolean result = saveOrUpdateBatch(configList);

        // 如果是佣金返现设置模块，同步更新system_user_level表
        if (result && "佣金返现设置".equals(moduleName)) {
            syncCommissionSettingsToUserLevel(configList);
        }

        return result;
    }

    /**
     * 同步佣金返现设置到system_user_level表
     *
     * @param configList 佣金返现配置列表
     */
    private void syncCommissionSettingsToUserLevel(List<SystemParamSetting> configList) {
        try {
            log.info("开始同步佣金返现设置到system_user_level表");

            // 查找佣金返现比例配置
            SystemParamSetting commissionRatioConfig = configList.stream()
                    .filter(config -> "commission_ratio".equals(config.getConfigCode()))
                    .findFirst()
                    .orElse(null);

            if (commissionRatioConfig == null || commissionRatioConfig.getConfigValue() == null) {
                log.warn("未找到佣金返现比例配置，跳过同步");
                return;
            }

            // 查找SVIP自购返现配置
            SystemParamSetting svipAutoRefundConfig = configList.stream()
                    .filter(config -> "svip_auto_refund".equals(config.getConfigCode()))
                    .findFirst()
                    .orElse(null);

            // 解析佣金返现比例配置
            JSONObject ratioConfig;
            try {
                ratioConfig = JSONUtil.parseObj(commissionRatioConfig.getConfigValue());
            } catch (Exception e) {
                log.error("解析佣金返现比例配置失败: {}", commissionRatioConfig.getConfigValue(), e);
                return;
            }

            // 解析SVIP自购返现配置
            boolean svipAutoRefundEnabled = false;
            if (svipAutoRefundConfig != null && svipAutoRefundConfig.getConfigValue() != null) {
                try {
                    JSONObject svipConfig = JSONUtil.parseObj(svipAutoRefundConfig.getConfigValue());
                    svipAutoRefundEnabled = svipAutoRefundConfig.getStatus() != null &&
                                          svipAutoRefundConfig.getStatus() &&
                                          svipConfig.getBool("enabled", false);
                    log.info("SVIP自购返现配置状态: 配置启用={}, 功能启用={}, 最终状态={}",
                            svipAutoRefundConfig.getStatus(), svipConfig.getBool("enabled", false), svipAutoRefundEnabled);
                } catch (Exception e) {
                    log.error("解析SVIP自购返现配置失败: {}", svipAutoRefundConfig.getConfigValue(), e);
                }
            } else {
                log.warn("未找到SVIP自购返现配置，SVIP等级将禁用佣金返现");
            }

            // 获取所有用户等级
            List<SystemUserLevel> userLevels = systemUserLevelService.getUsableList();

            // 更新每个等级的佣金设置
            for (SystemUserLevel userLevel : userLevels) {
                updateUserLevelCommissionSettings(userLevel, ratioConfig, commissionRatioConfig.getStatus(), svipAutoRefundEnabled);
            }

            log.info("佣金返现设置同步完成，共更新{}个用户等级", userLevels.size());

        } catch (Exception e) {
            log.error("同步佣金返现设置到system_user_level表失败", e);
            // 不抛出异常，避免影响主要的保存流程
        }
    }

    /**
     * 更新单个用户等级的佣金设置
     *
     * @param userLevel 用户等级
     * @param ratioConfig 佣金比例配置
     * @param configEnabled 配置是否启用
     * @param svipAutoRefundEnabled SVIP自购返现是否启用
     */
    private void updateUserLevelCommissionSettings(SystemUserLevel userLevel, JSONObject ratioConfig, Boolean configEnabled, boolean svipAutoRefundEnabled) {
        try {
            String levelKey = String.valueOf(userLevel.getGrade());
            JSONObject levelConfig = ratioConfig.getJSONObject(levelKey);

            boolean commissionEnabled = false;
            int commissionRate = 0;

            if (configEnabled != null && configEnabled && levelConfig != null) {
                // 配置启用且该等级有配置
                Integer purchaseRate = null;

                // 处理混合格式：可能是字符串或数字
                Object purchaseValue = levelConfig.get("purchase");
                if (purchaseValue != null) {
                    try {
                        if (purchaseValue instanceof String) {
                            purchaseRate = Integer.parseInt((String) purchaseValue);
                        } else if (purchaseValue instanceof Number) {
                            purchaseRate = ((Number) purchaseValue).intValue();
                        }
                    } catch (NumberFormatException e) {
                        log.warn("解析等级{}的佣金比例失败，值: {}", levelKey, purchaseValue);
                    }
                }

                if (purchaseRate != null && purchaseRate > 0) {
                    // 检查是否为SVIP等级（等级3）
                    if (userLevel.getGrade() == 3) {
                        // SVIP等级需要额外检查自购返现开关
                        if (svipAutoRefundEnabled) {
                            commissionEnabled = true;
                            commissionRate = purchaseRate;
                            log.info("SVIP等级佣金设置：自购返现已启用，设置佣金比例为{}%", purchaseRate);
                        } else {
                            commissionEnabled = false;
                            commissionRate = 0;
                            log.info("SVIP等级佣金设置：自购返现未启用，禁用佣金返现");
                        }
                    } else {
                        // 非SVIP等级，直接根据比例配置设置
                        commissionEnabled = true;
                        commissionRate = purchaseRate;
                    }
                }
            }

            // 更新用户等级的佣金设置
            UpdateWrapper<SystemUserLevel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", userLevel.getId());
            updateWrapper.set("commission_enabled", commissionEnabled);
            updateWrapper.set("commission_rate", commissionRate);
            updateWrapper.set("update_time", new Date());

            boolean updateResult = systemUserLevelService.update(updateWrapper);

            if (updateResult) {
                log.info("更新用户等级佣金设置成功 - 等级ID: {}, 等级名称: {}, 佣金启用: {}, 佣金比例: {}%",
                        userLevel.getId(), userLevel.getName(), commissionEnabled, commissionRate);
            } else {
                log.warn("更新用户等级佣金设置失败 - 等级ID: {}, 等级名称: {}",
                        userLevel.getId(), userLevel.getName());
            }

        } catch (Exception e) {
            log.error("更新用户等级{}的佣金设置失败", userLevel.getName(), e);
        }
    }
}