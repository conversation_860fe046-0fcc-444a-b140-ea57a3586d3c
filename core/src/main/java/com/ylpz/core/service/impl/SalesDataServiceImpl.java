package com.ylpz.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SalesDataRequest;
import com.ylpz.core.common.response.SalesDataDetailResponse;
import com.ylpz.core.common.response.SalesDataResponse;
import com.ylpz.core.common.response.SalesDataStatisticsResponse;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.StoreOrderDao;
import com.ylpz.core.dao.StoreOrderInfoDao;
import com.ylpz.core.dao.UserCommissionRecordDao;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.service.SalesDataService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.model.order.OrderStatusEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderInfo;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserCommissionRecord;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;

/**
 * 销售数据服务实现类
 */
@Service
public class SalesDataServiceImpl implements SalesDataService {

    @Autowired
    private UserDao userDao;

    @Autowired
    private StoreOrderDao storeOrderDao;

    @Autowired
    private StoreOrderInfoDao storeOrderInfoDao;

    @Autowired
    private UserCommissionRecordDao userCommissionRecordDao;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 获取销售数据列表
     */
    @Override
    public PageInfo<SalesDataResponse> getSalesDataList(SalesDataRequest request, PageParamRequest pageParamRequest) {
        // 构建用户查询条件
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();

        // 关键字查询（同时查询手机号和昵称）
        if (StrUtil.isNotBlank(request.getKeyword())) {
            userWrapper.and(wrapper -> wrapper
                .like(User::getPhone, request.getKeyword())
                .or()
                .like(User::getNickname, request.getKeyword())
            );
        } else {
            // 手机号筛选
            if (StrUtil.isNotBlank(request.getMobile())) {
                userWrapper.like(User::getPhone, request.getMobile());
            }

            // 昵称筛选
            if (StrUtil.isNotBlank(request.getNickname())) {
                userWrapper.like(User::getNickname, request.getNickname());
            }
        }

        // 会员等级筛选
        if (ObjectUtil.isNotNull(request.getMemberLevel())) {
            userWrapper.eq(User::getLevel, request.getMemberLevel());
        }

        // 只查询正常状态的用户
        userWrapper.eq(User::getStatus, true);

        // 分页查询用户
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<User> userList = userDao.selectList(userWrapper);
        PageInfo<User> userPageInfo = new PageInfo<>(userList);

        // 转换为销售数据响应对象
        List<SalesDataResponse> responseList = new ArrayList<>();
        for (User user : userList) {
            SalesDataResponse response = convertToSalesDataResponse(user, request);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<SalesDataResponse> pageInfo = new PageInfo<>();
        pageInfo.setList(responseList);
        pageInfo.setTotal(userPageInfo.getTotal());
        pageInfo.setPageNum(userPageInfo.getPageNum());
        pageInfo.setPageSize(userPageInfo.getPageSize());
        pageInfo.setPages(userPageInfo.getPages());

        return pageInfo;
    }

    /**
     * 获取销售数据统计
     */
    @Override
    public SalesDataStatisticsResponse getSalesDataStatistics(SalesDataRequest request) {
        // 构建用户查询条件
        LambdaQueryWrapper<User> userWrapper = new LambdaQueryWrapper<>();

        // 关键字查询（同时查询手机号和昵称）
        if (StrUtil.isNotBlank(request.getKeyword())) {
            userWrapper.and(wrapper -> wrapper
                .like(User::getPhone, request.getKeyword())
                .or()
                .like(User::getNickname, request.getKeyword())
            );
        } else {
            // 手机号筛选
            if (StrUtil.isNotBlank(request.getMobile())) {
                userWrapper.like(User::getPhone, request.getMobile());
            }

            // 昵称筛选
            if (StrUtil.isNotBlank(request.getNickname())) {
                userWrapper.like(User::getNickname, request.getNickname());
            }
        }

        // 会员等级筛选
        if (ObjectUtil.isNotNull(request.getMemberLevel())) {
            userWrapper.eq(User::getLevel, request.getMemberLevel());
        }

        // 只查询正常状态的用户
        userWrapper.eq(User::getStatus, true);

        List<User> userList = userDao.selectList(userWrapper);

        SalesDataStatisticsResponse statistics = new SalesDataStatisticsResponse();
        statistics.setTotalMemberCount(userList.size());
        statistics.setTotalSalesAmount(BigDecimal.ZERO);
        statistics.setTotalOrderCount(0);
        statistics.setTotalPendingBrokerageAmount(BigDecimal.ZERO);
        statistics.setTotalSettledBrokerageAmount(BigDecimal.ZERO);
        statistics.setTotalSelfPurchaseAmount(BigDecimal.ZERO);

        // 统计每个用户的销售数据
        for (User user : userList) {
            SalesDataResponse userSalesData = convertToSalesDataResponse(user, request);

            statistics.setTotalSalesAmount(statistics.getTotalSalesAmount().add(userSalesData.getSalesAmount()));
            statistics.setTotalOrderCount(statistics.getTotalOrderCount() + userSalesData.getOrderCount());
            statistics.setTotalPendingBrokerageAmount(
                    statistics.getTotalPendingBrokerageAmount().add(userSalesData.getPendingBrokerageAmount()));
            statistics.setTotalSettledBrokerageAmount(
                    statistics.getTotalSettledBrokerageAmount().add(userSalesData.getSettledBrokerageAmount()));
            statistics.setTotalSelfPurchaseAmount(
                    statistics.getTotalSelfPurchaseAmount().add(userSalesData.getSelfPurchaseAmount()));
        }

        return statistics;
    }

    /**
     * 获取会员销售明细
     * 优化版本：使用user_commission_record表查询，避免重复查询订单表
     */
    @Override
    public PageInfo<SalesDataDetailResponse> getSalesDataDetail(Integer uid, SalesDataRequest request,
            PageParamRequest pageParamRequest) {
        // 构建佣金记录查询条件
        LambdaQueryWrapper<UserCommissionRecord> commissionWrapper = new LambdaQueryWrapper<>();
        commissionWrapper.eq(UserCommissionRecord::getUid, uid);
        commissionWrapper.eq(UserCommissionRecord::getType, 1); // 增加类型
        commissionWrapper.in(UserCommissionRecord::getStatus, 1, 2); // 待结算和已结算状态

        // 时间范围筛选（使用支付时间）
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            commissionWrapper.ge(UserCommissionRecord::getPayTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            commissionWrapper.le(UserCommissionRecord::getPayTime, endTime);
        }

        // 按支付时间倒序
        commissionWrapper.orderByDesc(UserCommissionRecord::getPayTime);

        // 分页查询佣金记录
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<UserCommissionRecord> commissionList = userCommissionRecordDao.selectList(commissionWrapper);
        PageInfo<UserCommissionRecord> commissionPageInfo = new PageInfo<>(commissionList);

        // 转换为销售明细响应对象
        List<SalesDataDetailResponse> responseList = new ArrayList<>();
        for (UserCommissionRecord record : commissionList) {
            SalesDataDetailResponse response = convertCommissionToSalesDataDetailResponse(record);
            responseList.add(response);
        }

        // 构建分页结果
        PageInfo<SalesDataDetailResponse> pageInfo = new PageInfo<>();
        pageInfo.setList(responseList);
        pageInfo.setTotal(commissionPageInfo.getTotal());
        pageInfo.setPageNum(commissionPageInfo.getPageNum());
        pageInfo.setPageSize(commissionPageInfo.getPageSize());
        pageInfo.setPages(commissionPageInfo.getPages());

        return pageInfo;
    }

    /**
     * 转换用户为销售数据响应对象
     */
    private SalesDataResponse convertToSalesDataResponse(User user, SalesDataRequest request) {
        SalesDataResponse response = new SalesDataResponse();
        response.setUid(user.getId());
        response.setNickname(user.getNickname());
        response.setPhone(user.getPhone());
        response.setAvatar(user.getAvatar());
        response.setMemberLevelId(user.getLevel());

        // 设置会员等级名称
        String memberLevelName = getUserLevelName(user.getLevel());
        response.setMemberLevel(memberLevelName);

        // 设置创建时间
        if (user.getCreateTime() != null) {
            response.setCreateTime(DateUtil.dateToStr(user.getCreateTime(), Constants.DATE_FORMAT));
        }

        // 统计销售数据
        calculateSalesData(response, user.getId(), request);

        return response;
    }

    /**
     * 计算用户的销售数据
     * 优化版本：使用user_commission_record表统计，避免重复查询订单表
     */
    private void calculateSalesData(SalesDataResponse response, Integer uid, SalesDataRequest request) {
        // 构建佣金记录查询条件
        LambdaQueryWrapper<UserCommissionRecord> commissionWrapper = new LambdaQueryWrapper<>();
        commissionWrapper.eq(UserCommissionRecord::getUid, uid);
        commissionWrapper.eq(UserCommissionRecord::getType, 1); // 增加类型
        commissionWrapper.in(UserCommissionRecord::getStatus, 1, 2); // 待结算和已结算状态

        // 时间范围筛选（使用支付时间）
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            commissionWrapper.ge(UserCommissionRecord::getPayTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            commissionWrapper.le(UserCommissionRecord::getPayTime, endTime);
        }

        List<UserCommissionRecord> commissionList = userCommissionRecordDao.selectList(commissionWrapper);

        BigDecimal salesAmount = BigDecimal.ZERO;
        BigDecimal selfPurchaseAmount = BigDecimal.ZERO;
        int orderCount = 0;

        // 使用Set来统计不重复的订单数量
        Set<String> uniqueOrderIds = new HashSet<>();

        for (UserCommissionRecord record : commissionList) {
            // 统计销售金额
            if (record.getOrderAmount() != null) {
                salesAmount = salesAmount.add(record.getOrderAmount());
                // 自购金额（用户自己的订单）
                selfPurchaseAmount = selfPurchaseAmount.add(record.getOrderAmount());
            }

            // 统计不重复的订单数量
            if (StrUtil.isNotBlank(record.getLinkId())) {
                uniqueOrderIds.add(record.getLinkId());
            }
        }

        orderCount = uniqueOrderIds.size();

        // 计算佣金数据
        BigDecimal[] brokerageAmounts = calculateBrokerageData(uid, request);

        response.setSalesAmount(salesAmount);
        response.setSelfPurchaseAmount(selfPurchaseAmount);
        response.setOrderCount(orderCount);
        response.setPendingBrokerageAmount(brokerageAmounts[0]); // 待结算返现
        response.setSettledBrokerageAmount(brokerageAmounts[1]); // 已结算返现
    }

    /**
     * 计算用户的佣金数据
     *
     * @param uid     用户ID
     * @param request 查询条件
     * @return [待结算返现, 已结算返现]
     */
    private BigDecimal[] calculateBrokerageData(Integer uid, SalesDataRequest request) {
        // 构建佣金记录查询条件
        LambdaQueryWrapper<UserCommissionRecord> commissionWrapper = new LambdaQueryWrapper<>();
        commissionWrapper.eq(UserCommissionRecord::getUid, uid);
        commissionWrapper.eq(UserCommissionRecord::getType, 1); // 只查询增加类型

        // 时间范围筛选
        if (StrUtil.isNotBlank(request.getStartTime())) {
            Date startTime = DateUtil.strToDate(request.getStartTime(), Constants.DATE_FORMAT);
            commissionWrapper.ge(UserCommissionRecord::getCreateTime, startTime);
        }
        if (StrUtil.isNotBlank(request.getEndTime())) {
            Date endTime = DateUtil.strToDate(request.getEndTime(), Constants.DATE_FORMAT);
            commissionWrapper.le(UserCommissionRecord::getCreateTime, endTime);
        }

        List<UserCommissionRecord> commissionList = userCommissionRecordDao.selectList(commissionWrapper);

        BigDecimal pendingAmount = BigDecimal.ZERO; // 待结算返现
        BigDecimal settledAmount = BigDecimal.ZERO; // 已结算返现

        for (UserCommissionRecord record : commissionList) {
            if (record.getPrice() != null) {
                // 根据状态分类统计
                if (record.getStatus() == 1) {
                    // 待结算：状态为1
                    pendingAmount = pendingAmount.add(record.getPrice());
                } else if (record.getStatus() == 2) {
                    // 已结算：状态为2
                    settledAmount = settledAmount.add(record.getPrice());
                }
            }
        }

        return new BigDecimal[] { pendingAmount, settledAmount };
    }

    /**
     * 转换订单为销售明细响应对象
     */
    private SalesDataDetailResponse convertToSalesDataDetailResponse(StoreOrder order) {
        SalesDataDetailResponse response = new SalesDataDetailResponse();
        response.setOrderId(order.getId());
        response.setOrderNo(order.getOrderId());
        response.setOrderAmount(order.getTotalPrice());
        response.setPayAmount(order.getPayPrice());
        response.setIsSelfPurchase(1); // 默认为自购

        // 设置下单时间
        if (order.getCreateTime() != null) {
            response.setOrderTime(DateUtil.dateToStr(order.getCreateTime(), Constants.DATE_FORMAT));
        }

        // 设置订单状态
        response.setOrderStatus(OrderStatusEnum.getByCode(order.getStatus()).getDesc());

        // 设置支付方式
        response.setPayType(order.getPayType());

        // 获取订单商品信息
        setOrderProductInfo(response, order.getId());

        // 获取返现金额
        setCommissionAmount(response, order.getOrderId());

        // 获取买家信息
        setBuyerInfo(response, order.getUid());

        return response;
    }

    /**
     * 转换佣金记录为销售明细响应对象
     * 优化版本：直接使用user_commission_record数据，避免查询订单表
     */
    private SalesDataDetailResponse convertCommissionToSalesDataDetailResponse(UserCommissionRecord record) {
        SalesDataDetailResponse response = new SalesDataDetailResponse();
        response.setOrderId(null); // 佣金记录中没有订单ID，使用linkId作为订单号
        response.setOrderNo(record.getLinkId());
        response.setOrderAmount(record.getOrderAmount());
        response.setPayAmount(record.getOrderAmount()); // 使用订单金额作为支付金额
        response.setIsSelfPurchase(1); // 默认为自购

        // 设置下单时间（使用支付时间）
        if (record.getPayTime() != null) {
            response.setOrderTime(DateUtil.dateToStr(record.getPayTime(), Constants.DATE_FORMAT));
        }

        // 设置订单状态（根据佣金状态推断）
        if (record.getStatus() == 1) {
            response.setOrderStatus("待结算");
        } else if (record.getStatus() == 2) {
            response.setOrderStatus("已结算");
        } else {
            response.setOrderStatus("已失效");
        }

        // 设置支付方式（佣金记录中没有，设为默认值）
        response.setPayType("unknown");

        // 设置返现金额（直接使用佣金记录中的金额）
        response.setCommissionAmount(record.getPrice());

        // 设置买家信息（使用佣金记录中的冗余字段）
        response.setBuyerName(record.getUserName());
        response.setBuyerLevel(record.getUserLevel());

        // 商品信息需要通过订单号查询
        if (StrUtil.isNotBlank(record.getLinkId())) {
            setOrderProductInfoByOrderNo(response, record.getLinkId());
        }

        return response;
    }

    /**
     * 设置订单商品信息
     */
    private void setOrderProductInfo(SalesDataDetailResponse response, Integer orderId) {
        LambdaQueryWrapper<StoreOrderInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StoreOrderInfo::getOrderId, orderId);
        List<StoreOrderInfo> orderInfoList = storeOrderInfoDao.selectList(wrapper);

        if (!orderInfoList.isEmpty()) {
            StoreOrderInfo orderInfo = orderInfoList.get(0);
            response.setProductName(orderInfo.getProductName());
            response.setQuantity(orderInfo.getPayNum());
            response.setProductImage(orderInfo.getImage());
        }
    }

    /**
     * 设置返现金额
     */
    private void setCommissionAmount(SalesDataDetailResponse response, String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            response.setCommissionAmount(BigDecimal.ZERO);
            return;
        }

        // 查询该订单对应的佣金记录
        LambdaQueryWrapper<UserCommissionRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserCommissionRecord::getLinkId, orderNo);
        wrapper.eq(UserCommissionRecord::getType, 1); // 增加类型
        List<UserCommissionRecord> commissionRecords = userCommissionRecordDao.selectList(wrapper);

        if (commissionRecords.isEmpty()) {
            response.setCommissionAmount(BigDecimal.ZERO);
        } else {
            // 计算该订单的总返现金额
            BigDecimal totalCommission = commissionRecords.stream()
                    .map(UserCommissionRecord::getPrice)
                    .filter(price -> price != null)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            response.setCommissionAmount(totalCommission);
        }
    }

    /**
     * 根据订单号设置订单商品信息
     * 优化版本：通过订单号查询商品信息
     */
    private void setOrderProductInfoByOrderNo(SalesDataDetailResponse response, String orderNo) {
        if (StrUtil.isBlank(orderNo)) {
            return;
        }

        // 先通过订单号查询订单ID
        LambdaQueryWrapper<StoreOrder> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.select(StoreOrder::getId);
        orderWrapper.eq(StoreOrder::getOrderId, orderNo);
        StoreOrder order = storeOrderDao.selectOne(orderWrapper);

        if (order != null) {
            // 再查询订单商品信息
            LambdaQueryWrapper<StoreOrderInfo> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StoreOrderInfo::getOrderId, order.getId());
            List<StoreOrderInfo> orderInfoList = storeOrderInfoDao.selectList(wrapper);

            if (!orderInfoList.isEmpty()) {
                StoreOrderInfo orderInfo = orderInfoList.get(0);
                response.setProductName(orderInfo.getProductName());
                response.setQuantity(orderInfo.getPayNum());
                response.setProductImage(orderInfo.getImage());
            }
        }
    }

    /**
     * 设置买家信息
     */
    private void setBuyerInfo(SalesDataDetailResponse response, Integer uid) {
        if (uid == null) {
            response.setBuyerName("未知用户");
            response.setBuyerLevel("0");
            return;
        }

        // 查询用户信息
        User user = userDao.selectById(uid);
        if (user == null) {
            response.setBuyerName("未知用户");
            response.setBuyerLevel("0");
            return;
        }

        // 设置买家名称
        response.setBuyerName(StrUtil.isNotBlank(user.getNickname()) ? user.getNickname() : "未设置昵称");

        // 设置买家等级
        response.setBuyerLevel(user.getLevel() + "");
    }

    /**
     * 获取用户等级名称
     */
    private String getUserLevelName(Integer levelId) {
        return MemberLevelConstants.getLevelName(levelId);
    }

    /**
     * 获取订单状态名称
     */
    private String getOrderStatusName(Integer status) {
        if (status == null) {
            return "未知";
        }

        OrderStatusEnum orderStatusEnum = OrderStatusEnum.getByCode(status);
        return orderStatusEnum != null ? orderStatusEnum.getDesc() : "未知";
    }

    /**
     * 导出销售数据
     */
    @Override
    public void exportSalesData(SalesDataRequest request, HttpServletResponse response) throws IOException {
        // 获取所有销售数据（不分页）
        PageParamRequest pageRequest = new PageParamRequest();
        pageRequest.setPage(1);
        pageRequest.setLimit(10000); // 设置一个较大的限制

        PageInfo<SalesDataResponse> pageInfo = getSalesDataList(request, pageRequest);
        List<SalesDataResponse> dataList = pageInfo.getList();

        // 创建Excel工作簿
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("销售数据");

        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = { "会员昵称", "手机号", "会员等级", "销售金额", "订单数量", "待结算返现", "已结算返现", "自购金额", "注册时间" };

        // 设置标题样式
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerStyle.setFont(headerFont);
        headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }

        // 填充数据行
        for (int i = 0; i < dataList.size(); i++) {
            Row row = sheet.createRow(i + 1);
            SalesDataResponse data = dataList.get(i);

            row.createCell(0).setCellValue(data.getNickname() != null ? data.getNickname() : "");
            row.createCell(1).setCellValue(data.getPhone() != null ? data.getPhone() : "");
            row.createCell(2).setCellValue(data.getMemberLevel() != null ? data.getMemberLevel() : "");
            row.createCell(3).setCellValue(data.getSalesAmount() != null ? data.getSalesAmount().doubleValue() : 0.0);
            row.createCell(4).setCellValue(data.getOrderCount() != null ? data.getOrderCount() : 0);
            row.createCell(5).setCellValue(
                    data.getPendingBrokerageAmount() != null ? data.getPendingBrokerageAmount().doubleValue() : 0.0);
            row.createCell(6).setCellValue(
                    data.getSettledBrokerageAmount() != null ? data.getSettledBrokerageAmount().doubleValue() : 0.0);
            row.createCell(7).setCellValue(
                    data.getSelfPurchaseAmount() != null ? data.getSelfPurchaseAmount().doubleValue() : 0.0);
            row.createCell(8).setCellValue(data.getCreateTime() != null ? data.getCreateTime() : "");
        }

        // 自动调整列宽
        for (int i = 0; i < headers.length; i++) {
            sheet.autoSizeColumn(i);
        }

        // 设置响应头
        String fileName = "销售数据_" + DateUtil.dateToStr(new Date(), "yyyyMMdd_HHmmss") + ".xlsx";
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }
}
