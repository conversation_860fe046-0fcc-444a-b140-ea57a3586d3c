package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreActivityRequest;
import com.ylpz.core.common.request.StoreActivitySearchRequest;
import com.ylpz.core.common.response.ProductMarketingActivityResponse;
import com.ylpz.core.common.response.StoreActivityResponse;
import com.ylpz.core.common.response.StoreActivityStatisticsResponse;
import com.ylpz.model.activity.StoreActivity;

import java.util.List;

/**
 * 活动管理Service接口
 */
public interface StoreActivityService extends IService<StoreActivity> {

    /**
     * 活动列表
     * 
     * @param request 搜索条件
     * @param pageParamRequest 分页参数
     * @return List<StoreActivityResponse>
     */
    List<StoreActivityResponse> getList(StoreActivitySearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 新增活动
     * 
     * @param request 活动请求对象
     * @return Boolean
     */
    Boolean create(StoreActivityRequest request);

    /**
     * 修改活动
     * 
     * @param id 活动ID
     * @param request 活动请求对象
     * @return Boolean
     */
    Boolean updateActivity(Integer id, StoreActivityRequest request);

    /**
     * 删除活动
     * 
     * @param id 活动ID
     * @return Boolean
     */
    Boolean deleteById(Integer id);

    /**
     * 活动详情
     * 
     * @param id 活动ID
     * @return StoreActivityResponse
     */
    StoreActivityResponse getDetail(Integer id);

    /**
     * 修改活动状态
     *
     * @param id 活动ID
     * @param status 状态
     * @return Boolean
     */
    Boolean updateStatus(Integer id, Boolean status);

    /**
     * 获取活动统计信息
     *
     * @return StoreActivityStatisticsResponse
     */
    StoreActivityStatisticsResponse getStatistics();

    /**
     * 根据商品ID获取商品优惠券信息
     *
     * @param productId 商品ID
     * @return ProductMarketingActivityResponse
     */
    ProductMarketingActivityResponse getProductCouponsByProductId(Integer productId);
}