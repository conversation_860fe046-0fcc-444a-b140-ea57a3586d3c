package com.ylpz.core.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.UserTagRequest;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.dao.UserTagDao;
import com.ylpz.core.service.UserService;
import com.ylpz.core.service.UserTagService;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserTag;

/**
 * UserTagServiceImpl 接口实现
 */
@Service
public class UserTagServiceImpl extends ServiceImpl<UserTagDao, UserTag> implements UserTagService {

    @Resource
    private UserTagDao dao;
    
    @Resource
    private UserDao userDao;

    @Autowired
    private UserService userService;

    /**
    * 列表
    * @param pageParamRequest 分页类参数
    * @return List<UserTag>
    */
    @Override
    public List<UserTag> getList(PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        List<UserTag> tagList = dao.selectList(null);
        
        // 为每个标签统计会员数量
        for (UserTag tag : tagList) {
            // 获取拥有该标签的用户数量
            LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(User::getTagId, tag.getTagType().toString())
                .or().like(User::getTagId, "," + tag.getTagType().toString())
                .or().like(User::getTagId, tag.getTagType().toString() + ",");
            long count = userDao.selectCount(lambdaQueryWrapper);
            tag.setMemberCount(count);
        }
        return tagList;
    }

    /**
     * 根据id in 返回name字符串
     * @param tagIdValue String 标签id
     * @return List<UserTag>
     */
    @Override
    public String getGroupNameInId(String tagIdValue) {
        LambdaQueryWrapper<UserTag> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(UserTag::getId, CrmebUtil.stringToArray(tagIdValue)).orderByDesc(UserTag::getId);
        List<UserTag> userTags = dao.selectList(lambdaQueryWrapper);
        if (null == userTags){
            return "无";
        }

        return userTags.stream().map(UserTag::getName).distinct().collect(Collectors.joining(","));
    }

    /**
     * 新增用户标签
     * @param userTagRequest 标签参数
     */
    @Override
    public Boolean create(UserTagRequest userTagRequest) {
        UserTag userTag = new UserTag();
        BeanUtils.copyProperties(userTagRequest, userTag);
        return save(userTag);
    }

    /**
     * 删除用户标签
     * @param id 标签id
     */
    @Override
    public Boolean delete(Integer id) {
        boolean remove = removeById(id);
        if (remove) {
            userService.clearGroupByGroupId(id+"");
        }
        return remove;
    }

    /**
     * 修改用户标签
     * @param id 标签id
     * @param userTagRequest 标签参数
     */
    @Override
    public Boolean updateTag(Integer id, UserTagRequest userTagRequest) {
        UserTag userTag = new UserTag();
        BeanUtils.copyProperties(userTagRequest, userTag);
        userTag.setId(id);
        return updateById(userTag);
    }

    /**
     * 获取所有用户标签列表，用于前端选择
     * @return 用户标签列表
     */
    @Override
    public List<UserTag> getAllList() {
        LambdaQueryWrapper<UserTag> lqw = new LambdaQueryWrapper<>();
        lqw.orderByDesc(UserTag::getId);
        List<UserTag> tagList = dao.selectList(lqw);
        
        // 为每个标签统计会员数量
        for (UserTag tag : tagList) {
            // 获取拥有该标签的用户数量
            LambdaQueryWrapper<User> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.like(User::getTagId, tag.getTagType().toString())
                .or().like(User::getTagId, "," + tag.getTagType().toString())
                .or().like(User::getTagId, tag.getTagType().toString() + ",");
            long count = userDao.selectCount(lambdaQueryWrapper);
            tag.setMemberCount(count);
        }
        return tagList;
    }
}

