package com.ylpz.core.service;

import javax.servlet.http.HttpServletResponse;

import org.springframework.web.multipart.MultipartFile;

import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SvipAddRequest;
import com.ylpz.core.common.request.SvipLevelAdjustRequest;
import com.ylpz.core.common.request.SvipMemberRequest;
import com.ylpz.core.common.request.SvipUpdateRequest;
import com.ylpz.core.common.response.SvipMemberResponse;

/**
 * SVIP会员管理服务接口
 */
public interface SvipManagementService {

    /**
     * 添加SVIP会员
     * 
     * @param request 添加请求
     * @return 是否成功
     */
    boolean addSvip(SvipAddRequest request);

    /**
     * 更新SVIP会员信息
     * 
     * @param request 更新请求
     * @return 是否成功
     */
    boolean updateSvip(SvipUpdateRequest request);

    /**
     * 下载SVIP会员导入模板
     * 
     * @param response HTTP响应对象
     */
    void downloadTemplate(HttpServletResponse response);

    /**
     * 批量导入SVIP会员
     * 
     * @param file 导入文件
     * @return 是否成功
     */
    boolean importSvip(MultipartFile file);

    /**
     * 调整会员等级
     * 
     * @param request 调整等级请求
     * @return 是否成功
     */
    boolean adjustLevel(SvipLevelAdjustRequest request);
    
    /**
     * 获取SVIP会员列表
     *
     * @param request 查询条件
     * @param pageParamRequest 分页参数
     * @return SVIP会员列表
     */
    PageInfo<SvipMemberResponse> getList(SvipMemberRequest request, PageParamRequest pageParamRequest);
}