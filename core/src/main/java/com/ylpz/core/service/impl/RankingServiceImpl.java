package com.ylpz.core.service.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ylpz.core.common.constants.BrokerageRecordConstants;
import com.ylpz.core.common.constants.SystemParamSettingConstants;
import com.ylpz.core.common.request.RankingRequest;
import com.ylpz.core.common.response.RankingDetailResponse;
import com.ylpz.core.common.response.RankingResponse;
import com.ylpz.core.common.response.RankingStatisticsResponse;
import com.ylpz.core.common.response.RankingYearResponse;
import com.ylpz.core.dao.RankingLeaderboardDetailMapper;
import com.ylpz.core.dao.RankingLeaderboardMapper;
import com.ylpz.core.service.SystemParamSettingService;
import com.ylpz.core.service.RankingService;
import com.ylpz.core.service.UserBonusRecordService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.ranking.RankItem;
import com.ylpz.model.ranking.RankingInfo;
import com.ylpz.model.ranking.RankingLeaderboard;
import com.ylpz.model.ranking.RankingLeaderboardDetail;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserBonusRecord;

import lombok.extern.slf4j.Slf4j;

/**
 * 排行榜服务实现类
 */
@Slf4j
@Service
public class RankingServiceImpl implements RankingService {

    @Autowired
    private RankingLeaderboardMapper rankingLeaderboardMapper;

    @Autowired
    private RankingLeaderboardDetailMapper rankingLeaderboardDetailMapper;

    @Autowired
    private UserBonusRecordService userBonusRecordService;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");
    private static final SimpleDateFormat DATETIME_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    // 排行榜来源类型常量
    private static final String SOURCE_TYPE_WEEKLY_RANK = "周排行榜奖励";
    private static final String SOURCE_TYPE_MONTHLY_RANK = "月排行榜奖励";
    private static final String SOURCE_TYPE_QUARTERLY_RANK = "季度排行榜奖励";

    @Override
    public IPage<RankingResponse> getRankingList(RankingRequest request, Integer page, Integer limit) {
        log.info("查询排行榜列表，参数：{}, 页码：{}, 每页数量：{}", request, page, limit);

        Page<RankingResponse> pageParam = new Page<>(page, limit);
        return rankingLeaderboardMapper.selectRankingList(pageParam, request);
    }

    @Override
    public RankingStatisticsResponse getRankingStatistics(RankingRequest request) {
        log.info("获取排行榜统计数据，参数：{}", request);

        return rankingLeaderboardMapper.selectRankingStatistics(request);
    }

    @Override
    public RankingDetailResponse getRankingDetail(Integer rankingId) {
        log.info("获取排行榜详细信息，排行榜ID：{}", rankingId);

        // 查询排行榜基本信息
        RankingLeaderboard ranking = rankingLeaderboardMapper.selectById(rankingId);
        if (ranking == null) {
            throw new RuntimeException("排行榜不存在");
        }

        // 构建响应对象
        RankingDetailResponse response = new RankingDetailResponse();

        // 设置排行榜信息
        RankingInfo rankingInfo = new RankingInfo();
        rankingInfo.setId(ranking.getId());
        rankingInfo.setRankType(ranking.getRankType());
        rankingInfo.setRankTypeName(getRankTypeName(ranking.getRankType()));
        rankingInfo.setRankPeriod(ranking.getRankPeriod());
        rankingInfo.setTotalSalesAmount(ranking.getTotalSalesAmount());
        rankingInfo.setParticipantCount(ranking.getParticipantCount());
        rankingInfo.setRewardStatus(ranking.getRewardStatus());
        rankingInfo.setRewardAmount(ranking.getRewardAmount());
        response.setRankingInfo(rankingInfo);

        // 查询排名详情
        List<RankItem> rankList = rankingLeaderboardDetailMapper.selectRankDetailByRankingId(rankingId);
        response.setRankList(rankList);

        return response;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer generateRanking(String rankType, String startDate, String endDate) {
        log.info("生成排行榜，类型：{}，开始日期：{}，结束日期：{}", rankType, startDate, endDate);

        try {
            // 检查是否已存在该期排行榜
            RankingLeaderboard existRanking = rankingLeaderboardMapper.selectByRankTypeAndDate(rankType, startDate,
                    endDate);
            if (existRanking != null) {
                log.warn("该期排行榜已存在，排行榜ID：{}", existRanking.getId());
                return existRanking.getId();
            }

            // 获取入榜门槛值配置
            BigDecimal threshold = getRankingThreshold(rankType);
            log.info("{}榜入榜门槛值：{}", rankType, threshold);

            // 获取显示数量配置
            Integer displayCount = getRankingDisplayCount();
            log.info("榜单显示数量：TOP{}", displayCount);

            // 查询销售排名数据
            List<RankItem> rankData = rankingLeaderboardDetailMapper
                    .selectSalesRankingByDateRange(startDate + " 00:00:00", endDate + " 23:59:59", displayCount);

            if (rankData.isEmpty()) {
                log.warn("该时间段内无销售数据，无法生成排行榜");
                return null;
            }

            // 过滤掉低于门槛值的数据
            rankData = rankData.stream().filter(item -> item.getSalesAmount().compareTo(threshold) >= 0)
                    .collect(Collectors.toList());

            // 计算总销售金额和参与人数
            BigDecimal totalSalesAmount = rankData.stream().map(RankItem::getSalesAmount).reduce(BigDecimal.ZERO,
                    BigDecimal::add);
            int participantCount = rankData.size();

            // 创建排行榜主记录
            RankingLeaderboard ranking = new RankingLeaderboard();
            ranking.setRankType(rankType);
            ranking.setRankPeriod(generateRankPeriod(rankType, startDate, endDate));
            ranking.setStartDate(DATE_FORMAT.parse(startDate));
            ranking.setEndDate(DATE_FORMAT.parse(endDate));
            ranking.setTotalSalesAmount(totalSalesAmount);
            ranking.setParticipantCount(participantCount);
            ranking.setRewardStatus("待发放");
            ranking.setRewardAmount(BigDecimal.ZERO);
            ranking.setIsAutoReward(isAutoRewardEnabled(rankType));
            ranking.setCreateTime(new Date());
            ranking.setUpdateTime(new Date());

            rankingLeaderboardMapper.insert(ranking);
            Integer rankingId = ranking.getId();

            // 创建排行榜明细记录
            List<RankingLeaderboardDetail> detailList = new ArrayList<>();
            for (RankItem item : rankData) {
                RankingLeaderboardDetail detail = new RankingLeaderboardDetail();
                detail.setRankingId(rankingId);
                detail.setUid(item.getUid());
                detail.setRank(item.getRank());
                detail.setSalesAmount(item.getSalesAmount());
                detail.setOrderCount(item.getOrderCount());
                detail.setRewardAmount(BigDecimal.ZERO);
                detail.setRewardStatus("待发放");
                detailList.add(detail);
            }

            if (!detailList.isEmpty()) {
                rankingLeaderboardDetailMapper.batchInsert(detailList);
            }

            log.info("排行榜生成成功，排行榜ID：{}，参与人数：{}", rankingId, participantCount);
            return rankingId;

        } catch (Exception e) {
            log.error("生成排行榜失败", e);
            throw new RuntimeException("生成排行榜失败：" + e.getMessage());
        }
    }

    /**
     * 获取排行类型名称
     */
    private String getRankTypeName(String rankType) {
        switch (rankType) {
            case "week":
                return "周榜";
            case "month":
                return "月榜";
            case "quarter":
                return "季度榜";
            case "year":
                return "年度榜";
            default:
                return rankType;
        }
    }

    /**
     * 生成排行周期描述
     */
    private String generateRankPeriod(String rankType, String startDate, String endDate) {
        try {
            LocalDate start = LocalDate.parse(startDate);
            LocalDate end = LocalDate.parse(endDate);

            switch (rankType) {
                case "week":
                    return String.format("%d年%d月%d日-%d月%d日", start.getYear(), start.getMonthValue(),
                            start.getDayOfMonth(), end.getMonthValue(), end.getDayOfMonth());
                case "month":
                    return String.format("%d年%d月", start.getYear(), start.getMonthValue());
                case "quarter":
                    int quarter = (start.getMonthValue() - 1) / 3 + 1;
                    return String.format("%d年第%d季度", start.getYear(), quarter);
                case "year":
                    return String.format("%d年度", start.getYear());
                default:
                    return String.format("%s-%s", startDate, endDate);
            }
        } catch (Exception e) {
            return String.format("%s-%s", startDate, endDate);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean distributeReward(Integer rankingId) {
        log.info("手动发放排行榜奖励，排行榜ID：{}", rankingId);

        try {
            // 查询排行榜信息
            RankingLeaderboard ranking = rankingLeaderboardMapper.selectById(rankingId);
            if (ranking == null) {
                throw new RuntimeException("排行榜不存在");
            }

            if ("已发放".equals(ranking.getRewardStatus())) {
                log.warn("排行榜奖励已发放，无需重复发放");
                return true;
            }

            // 查询排行榜明细
            List<RankItem> rankList = rankingLeaderboardDetailMapper.selectRankDetailByRankingId(rankingId);
            if (rankList.isEmpty()) {
                log.warn("排行榜明细为空，无法发放奖励");
                return false;
            }

            // 计算奖励金额（根据配置的奖励规则）
            BigDecimal totalRewardAmount = BigDecimal.ZERO;
            Integer rewardCount = getRankingRewardCount(ranking.getRankType());

            for (RankItem item : rankList) {
                // 年度榜单特殊处理：只统计人员排名，不设置奖励金额
                if ("year".equals(ranking.getRankType())) {
                    // 年度榜单只更新明细表状态，不创建奖励记录，不加余额
                    RankingLeaderboardDetail detail = new RankingLeaderboardDetail();
                    detail.setRankingId(rankingId);
                    detail.setUid(item.getUid());
                    detail.setRewardAmount(BigDecimal.ZERO); // 年度榜单不设置奖励金额
                    detail.setRewardStatus("线下发放");
                    detail.setRewardTime(new Date());
                    rankingLeaderboardDetailMapper.updateById(detail);

                    log.info("年度榜单人员统计完成: userId={}, rank={}", item.getUid(), item.getRank());
                } else {
                    // 其他榜单正常发放奖励
                    BigDecimal rewardAmount = BigDecimal.ZERO;

                    // 只有在奖励人数范围内才有奖励
                    if (item.getRank() <= rewardCount) {
                        rewardAmount = getRankingRewardAmount(ranking.getRankType(), item.getRank());
                    }

                    if (rewardAmount.compareTo(BigDecimal.ZERO) > 0) {
                        // 获取用户当前佣金余额
                        BigDecimal currentBalance = getUserBrokeragePrice(item.getUid());

                        // 创建奖励记录
                        UserBonusRecord record = new UserBonusRecord();
                        record.setUid(item.getUid());
                        record.setLinkId(rankingId.toString());
                        record.setBonusType("rank");
                        record.setSourceType(getRankTypeDisplayName(ranking.getRankType()));
                        record.setPrice(rewardAmount);
                        record.setBalance(currentBalance.add(rewardAmount));
                        record.setMark("排行榜第" + item.getRank() + "名奖励");
                        record.setStatus(1); // 已发放
                        record.setCreateTime(new Date());
                        record.setUpdateTime(new Date());

                        // 保存奖励记录
                        userBonusRecordService.save(record);

                        // 更新用户佣金余额
                        userService.operationBrokerage(item.getUid(), rewardAmount, currentBalance, "add");

                        // 更新用户可提现金额（排行榜奖励可以提现）
                        User rewardUser = userService.getById(item.getUid());
                        if (rewardUser != null) {
                            BigDecimal currentWithdrawablePrice = rewardUser.getWithdrawablePrice() == null ? BigDecimal.ZERO : rewardUser.getWithdrawablePrice();
                            userService.operationWithdrawablePrice(item.getUid(), rewardAmount, currentWithdrawablePrice, "add");
                        }

                        // 更新明细表的奖励信息
                        RankingLeaderboardDetail detail = new RankingLeaderboardDetail();
                        detail.setRankingId(rankingId);
                        detail.setUid(item.getUid());
                        detail.setRewardAmount(rewardAmount);
                        detail.setRewardStatus("已发放");
                        detail.setRewardTime(new Date());
                        rankingLeaderboardDetailMapper.updateById(detail);

                        totalRewardAmount = totalRewardAmount.add(rewardAmount);
                    }
                }
            }

            // 更新排行榜主表的奖励状态
            if ("year".equals(ranking.getRankType())) {
                ranking.setRewardStatus("仅统计排名");
                ranking.setRewardAmount(BigDecimal.ZERO); // 年度榜单不设置奖励金额
                log.info("年度排行榜人员统计完成（仅统计排名）");
            } else {
                ranking.setRewardStatus("已发放");
                ranking.setRewardAmount(totalRewardAmount);
                log.info("排行榜奖励发放成功，总奖励金额：{}", totalRewardAmount);
            }
            ranking.setRewardTime(new Date());
            ranking.setUpdateTime(new Date());
            rankingLeaderboardMapper.updateById(ranking);
            return true;

        } catch (Exception e) {
            log.error("发放排行榜奖励失败", e);
            throw new RuntimeException("发放排行榜奖励失败：" + e.getMessage());
        }
    }

    @Override
    public Integer batchDistributeReward(List<Integer> rankingIds) {
        log.info("批量发放排行榜奖励，排行榜ID列表：{}", rankingIds);

        int successCount = 0;
        for (Integer rankingId : rankingIds) {
            try {
                if (distributeReward(rankingId)) {
                    successCount++;
                }
            } catch (Exception e) {
                log.error("发放排行榜奖励失败，排行榜ID：{}", rankingId, e);
            }
        }

        log.info("批量发放排行榜奖励完成，成功数量：{}", successCount);
        return successCount;
    }

    @Override
    public void autoGenerateRanking() {
        log.info("开始自动生成排行榜");

        try {
            LocalDate now = LocalDate.now();

            // 生成上周周榜
            LocalDate lastWeekStart = now.minusWeeks(1)
                    .with(TemporalAdjusters.previousOrSame(java.time.DayOfWeek.MONDAY));
            LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
            Integer weekRankingId = generateRanking("week", lastWeekStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                    lastWeekEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

            // 如果配置了自动发放奖励，则自动发放
            if (weekRankingId != null) {
                RankingLeaderboard weekRanking = rankingLeaderboardMapper.selectById(weekRankingId);
                if (weekRanking != null && weekRanking.getIsAutoReward()) {
                    log.info("周排行榜配置了自动发放奖励，开始自动发放奖励，排行榜ID：{}", weekRankingId);
                    distributeReward(weekRankingId);
                }
            }

            // 如果是月初，生成上月月榜
            if (now.getDayOfMonth() == 1) {
                LocalDate lastMonthStart = now.minusMonths(1).withDayOfMonth(1);
                LocalDate lastMonthEnd = now.minusMonths(1).with(TemporalAdjusters.lastDayOfMonth());
                Integer monthRankingId = generateRanking("month",
                        lastMonthStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastMonthEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

                // 如果配置了自动发放奖励，则自动发放
                if (monthRankingId != null) {
                    RankingLeaderboard monthRanking = rankingLeaderboardMapper.selectById(monthRankingId);
                    if (monthRanking != null && monthRanking.getIsAutoReward()) {
                        log.info("月排行榜配置了自动发放奖励，开始自动发放奖励，排行榜ID：{}", monthRankingId);
                        distributeReward(monthRankingId);
                    }
                }
            }

            // 如果是季度初，生成上季度榜
            if (now.getDayOfMonth() == 1 && (now.getMonthValue() == 1 || now.getMonthValue() == 4
                    || now.getMonthValue() == 7 || now.getMonthValue() == 10)) {
                LocalDate lastQuarterStart = now.minusMonths(3).withDayOfMonth(1);
                LocalDate lastQuarterEnd = now.minusDays(1);
                Integer quarterRankingId = generateRanking("quarter",
                        lastQuarterStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastQuarterEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

                // 如果配置了自动发放奖励，则自动发放
                if (quarterRankingId != null) {
                    RankingLeaderboard quarterRanking = rankingLeaderboardMapper.selectById(quarterRankingId);
                    if (quarterRanking != null && quarterRanking.getIsAutoReward()) {
                        log.info("季度排行榜配置了自动发放奖励，开始自动发放奖励，排行榜ID：{}", quarterRankingId);
                        distributeReward(quarterRankingId);
                    }
                }
            }

            // 如果是年初，生成上年年榜
            if (now.getDayOfMonth() == 1 && now.getMonthValue() == 1) {
                LocalDate lastYearStart = now.minusYears(1).withDayOfYear(1);
                LocalDate lastYearEnd = now.minusYears(1).with(TemporalAdjusters.lastDayOfYear());
                Integer yearRankingId = generateRanking("year", lastYearStart.format(DateTimeFormatter.ISO_LOCAL_DATE),
                        lastYearEnd.format(DateTimeFormatter.ISO_LOCAL_DATE));

                // 如果配置了自动发放奖励，则自动发放
                if (yearRankingId != null) {
                    RankingLeaderboard yearRanking = rankingLeaderboardMapper.selectById(yearRankingId);
                    if (yearRanking != null && yearRanking.getIsAutoReward()) {
                        log.info("年度排行榜配置了自动发放奖励，开始自动发放奖励，排行榜ID：{}", yearRankingId);
                        distributeReward(yearRankingId);
                    }
                }
            }

            log.info("自动生成排行榜完成");

        } catch (Exception e) {
            log.error("自动生成排行榜失败", e);
        }
    }

    /**
     * 获取用户佣金余额
     */
    private BigDecimal getUserBrokeragePrice(Integer uid) {
        User user = userService.getById(uid);
        if (user != null && user.getBrokeragePrice() != null) {
            return user.getBrokeragePrice();
        }
        return BigDecimal.ZERO;
    }

    /**
     * 获取排行榜入榜门槛值
     */
    private BigDecimal getRankingThreshold(String rankType) {
        // 从配置表中查找对应的门槛值配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);

        if (!configs.isEmpty()) {
            // 查找对应排行榜类型的配置
            String configCode = null;
            switch (rankType) {
                case "week":
                    configCode = "weekly_ranking_bonus";
                    break;
                case "month":
                    configCode = "monthly_ranking_bonus";
                    break;
                case "quarter":
                    configCode = "quarterly_ranking_bonus";
                    break;
                default:
                    return new BigDecimal("10000"); // 默认值
            }

            // 查找对应配置
            for (SystemParamSetting config : configs) {
                if (configCode.equals(config.getConfigCode()) && config.getConfigValue() != null) {
                    JSONObject configValue = JSON.parseObject(config.getConfigValue());
                    if (configValue.getBigDecimal("threshold") != null) {
                        return configValue.getBigDecimal("threshold");
                    }
                }
            }
        }

        // 如果没有配置，使用默认门槛值
        switch (rankType) {
            case "week":
                return new BigDecimal("10000");
            case "month":
                return new BigDecimal("30000");
            case "quarter":
                return new BigDecimal("100000");
            case "year":
                return new BigDecimal("500000");
            default:
                return new BigDecimal("10000");
        }
    }

    /**
     * 获取排行榜显示数量
     */
    private Integer getRankingDisplayCount() {
        // 从配置表中查找榜单显示数量配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);

        for (SystemParamSetting config : configs) {
            if ("ranking_list_display".equals(config.getConfigCode()) && config.getConfigValue() != null) {
                JSONObject configValue = JSON.parseObject(config.getConfigValue());
                String displayCount = configValue.getString("display_count");
                if (displayCount != null && displayCount.startsWith("TOP")) {
                    try {
                        return Integer.parseInt(displayCount.substring(3));
                    } catch (NumberFormatException e) {
                        log.warn("解析排行榜显示数量失败: {}", displayCount, e);
                    }
                }
            }
        }

        return 20; // 默认显示TOP20
    }

    /**
     * 获取排行榜奖励人数
     * 
     * @param rankType 排行榜类型
     * @return 奖励人数
     */
    private Integer getRankingRewardCount(String rankType) {
        // 从配置表中查找榜单奖励人数配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);

        String configCode = null;
        switch (rankType) {
            case "week":
                configCode = "weekly_ranking_bonus";
                break;
            case "month":
                configCode = "monthly_ranking_bonus";
                break;
            case "quarter":
                configCode = "quarterly_ranking_bonus";
                break;
            case "year":
                return 0; // 年度榜单不设置奖励，返回0
            default:
                return 3; // 默认值
        }

        for (SystemParamSetting config : configs) {
            if (configCode.equals(config.getConfigCode()) && config.getConfigValue() != null) {
                JSONObject configValue = JSON.parseObject(config.getConfigValue());
                if (configValue.getInteger("reward_count") != null) {
                    return configValue.getInteger("reward_count");
                }
            }
        }

        return 3; // 默认奖励前3名
    }

    /**
     * 获取排行榜奖励金额
     *
     * @param rankType 排行榜类型
     * @param rank     排名
     * @return 奖励金额
     */
    private BigDecimal getRankingRewardAmount(String rankType, Integer rank) {
        if (rank == null || rank <= 0) {
            return BigDecimal.ZERO;
        }

        // 确定配置代码
        String configCode;
        if ("week".equals(rankType)) {
            configCode = "weekly_ranking_bonus";
        } else if ("month".equals(rankType)) {
            configCode = "monthly_ranking_bonus";
        } else if ("quarter".equals(rankType)) {
            configCode = "quarterly_ranking_bonus";
        } else if ("year".equals(rankType)) {
            return BigDecimal.ZERO; // 年度榜单不设置奖励金额
        } else {
            return BigDecimal.ZERO;
        }

        // 查询排行榜奖励配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);
        if (CollectionUtils.isEmpty(configs)) {
            return BigDecimal.ZERO;
        }

        // 查找对应排行榜类型的配置
        for (SystemParamSetting config : configs) {
            if (configCode.equals(config.getConfigCode()) && config.getConfigValue() != null) {
                JSONObject configValue = JSON.parseObject(config.getConfigValue());
                JSONArray awards = configValue.getJSONArray("awards");

                if (awards != null) {
                    // 查找对应排名的奖励配置
                    for (int i = 0; i < awards.size(); i++) {
                        JSONObject award = awards.getJSONObject(i);
                        if (award.getInteger("rank") != null && award.getInteger("rank").equals(rank)
                                && award.getBigDecimal("amount") != null) {
                            return award.getBigDecimal("amount");
                        }
                    }
                }
                break;
            }
        }

        return BigDecimal.ZERO;
    }

    /**
     * 获取排行榜类型的中文显示名称
     */
    private String getRankTypeDisplayName(String rankType) {
        switch (rankType) {
            case "week":
                return "周";
            case "month":
                return "月";
            case "quarter":
                return "季度";
            case "year":
                return "年";
            default:
                return rankType;
        }
    }

    /**
     * 检查是否启用自动发放奖励
     * 
     * @param rankType 排行榜类型
     * @return 是否自动发放奖励
     */
    private Boolean isAutoRewardEnabled(String rankType) {
        // 从配置表中查找是否自动发放奖励配置
        List<SystemParamSetting> configs = systemParamSettingService
                .getListByModuleName(SystemParamSettingConstants.ModuleName.BONUS);

        for (SystemParamSetting config : configs) {
            if ("ranking_auto_reward".equals(config.getConfigCode()) && config.getConfigValue() != null) {
                JSONObject configValue = JSON.parseObject(config.getConfigValue());
                // 根据排行榜类型返回对应的自动发放设置
                if ("week".equals(rankType) && configValue.getBoolean("weekly") != null) {
                    return configValue.getBoolean("weekly");
                } else if ("month".equals(rankType) && configValue.getBoolean("monthly") != null) {
                    return configValue.getBoolean("monthly");
                } else if ("quarter".equals(rankType) && configValue.getBoolean("quarterly") != null) {
                    return configValue.getBoolean("quarterly");
                } else if ("year".equals(rankType) && configValue.getBoolean("yearly") != null) {
                    return configValue.getBoolean("yearly");
                }
            }
        }

        return false; // 默认不自动发放
    }

    @Override
    public List<RankingYearResponse> getHistoryYears() {
        log.info("获取历史年度列表");

        // 查询所有历史年度
        List<RankingYearResponse> years = rankingLeaderboardMapper.selectHistoryYears();

        // 如果没有数据，默认返回当前年度
        if (years == null || years.isEmpty()) {
            RankingYearResponse currentYear = new RankingYearResponse();
            currentYear.setYear(LocalDate.now().getYear());
            currentYear.setIsCurrent(true);
            return Collections.singletonList(currentYear);
        }

        return years;
    }
}
