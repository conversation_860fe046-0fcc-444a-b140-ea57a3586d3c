package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.*;
import com.ylpz.core.common.response.*;
import com.ylpz.core.common.vo.ExpressSheetVo;
import com.ylpz.core.common.vo.LogisticsResultVo;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * StoreOrderService 接口
 */
public interface StoreOrderService extends IService<StoreOrder> {

    /**
     * 列表（PC）
     * @param request 请求参数
     * @param pageParamRequest 分页类参数
     * @return CommonPage<StoreOrderDetailResponse>
     */
    CommonPage<StoreOrderDetailResponse> getAdminList(StoreOrderSearchRequest request, PageParamRequest pageParamRequest);

    List<StoreOrder> getOrderGroupByDate(String dateLimit, int lefTime);

    /**
     * 订单详情（PC）
     * @param orderNo 订单编号
     * @return StoreOrderInfoResponse
     */
    StoreOrderInfoResponse info(String orderNo);

    /**
     * 订单备注
     * @param orderNo 订单编号
     * @param mark 备注
     * @return Boolean
     */
    Boolean mark(String orderNo, String mark);

    /**
     * 获取订单快递信息
     * @param orderNo 订单编号
     * @return LogisticsResultVo
     */
    LogisticsResultVo getLogisticsInfo(String orderNo);

    /**
     * 根据时间参数统计订单价格
     * @param dateLimit 时间区间
     * @param type 1=price 2=订单量
     * @return 统计订单信息
     */
    StoreOrderStatisticsResponse orderStatisticsByTime(String dateLimit, Integer type);

    StoreOrder getByOderId(String orderId);

    /**
     * 获取面单默认配置信息
     * @return ExpressSheetVo
     */
    ExpressSheetVo getDeliveryInfo();

    /**
     * 根据用户uid查询所有已支付订单
     * @param userId 用户uid
     * @param pageParamRequest 分页参数
     * @return List<StoreOrder>
     */
    List<StoreOrder> findPaidListByUid(Integer userId, PageParamRequest pageParamRequest);

    /**
     * 获取订单总数量
     * @param uid 用户uid
     * @return Integer
     */
    Integer getOrderCountByUid(Integer uid);

    /**
     * 获取用户总消费金额
     * @param userId 用户uid
     * @return BigDecimal
     */
    BigDecimal getSumPayPriceByUid(Integer userId);

    /**
     * 获取订单数量(时间)
     * @param uid 用户uid
     * @return Integer
     */
    Integer getOrderCountByUidAndDate(Integer uid, String date);

    /**
     * 获取用户消费金额(时间)
     * @param userId 用户uid
     * @return BigDecimal
     */
    BigDecimal getSumPayPriceByUidAndDate(Integer userId, String date);

    /**
     * 获取订单状态数量
     * @param dateLimit 时间端
     * @param type 订单类型：0普通订单，1-送礼, 2-全部订单
     * @return StoreOrderCountItemResponse
     */
    StoreOrderCountItemResponse getOrderStatusNum(String dateLimit, Integer type);

    /**
     * 获取订单统计数据
     * @param dateLimit 时间端
     * @return StoreOrderTopItemResponse
     */
    StoreOrderTopItemResponse getOrderData(String dateLimit);

    /**
     * 订单删除
     * @param orderNo 订单编号
     * @return Boolean
     */
    Boolean delete(String orderNo);

    /**
     * 订单取消
     * @param orderNo 订单编号
     * @param reason 取消原因
     * @return Boolean
     */
    Boolean cancel(String orderNo, String reason);

    /**
     * 通过日期获取订单数量
     * @param date 日期，yyyy-MM-dd格式
     * @return Integer
     */
    Integer getOrderNumByDate(String date);

    /**
     * 通过日期获取支付订单金额
     * @param date 日期，yyyy-MM-dd格式
     * @return BigDecimal
     */
    BigDecimal getPayOrderAmountByDate(String date);

    /**
     * 通过日期获取支付订单金额
     * @param startDate 日期
     * @param endDate 日期
     * @return BigDecimal
     */
    BigDecimal getPayOrderAmountByPeriod(String startDate, String endDate);

    /**
     * 获取待发货订单数量
     * @return Integer
     */
    Integer getNotShippingNum();

    /**
     * 获取退款中订单数量
     */
    Integer getRefundingNum();

    void updateOrderStatus(String orderIds, String shipped);

    /**
     * 计算用户销售金额（自购订单）
     * 仅统计成为SVIP之后的订单数据
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 销售金额
     */
    Double calculateUserSalesAmount(Integer userId, String startDate, String endDate);

    /**
     * 计算用户下线销售金额
     * 统计用户下级的购买订单金额，仅统计成为SVIP之后的数据
     *
     * @param userId 用户ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 下线销售金额
     */
    Double calculateDownlineSalesAmount(Integer userId, String startDate, String endDate);
}
