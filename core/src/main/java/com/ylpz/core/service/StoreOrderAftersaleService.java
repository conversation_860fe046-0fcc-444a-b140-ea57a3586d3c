package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.page.CommonPage;
import com.ylpz.core.common.request.AftersaleProcessRequest;
import com.ylpz.core.common.request.AftersaleRejectRequest;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.StoreOrderAftersaleSearchRequest;
import com.ylpz.core.common.response.StoreOrderAftersaleDetailResponse;
import com.ylpz.core.common.response.StoreOrderAftersaleResponse;
import com.ylpz.core.common.response.StoreOrderInfoResponse;
import com.ylpz.model.order.StoreOrderAftersale;

/**
 * 订单售后服务接口
 */
public interface StoreOrderAftersaleService extends IService<StoreOrderAftersale> {
    /**
     * 获取售后订单列表（使用专门的售后请求和响应对象）
     *
     * @param request          售后查询请求
     * @param pageParamRequest 分页参数
     * @return 售后订单列表
     */
    CommonPage<StoreOrderAftersaleResponse> getAftersaleList(StoreOrderAftersaleSearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取售后订单详情（使用专门的售后响应对象）
     *
     * @param aftersaleId 售后ID
     * @return 售后订单详情
     */
    StoreOrderAftersaleDetailResponse getAftersaleDetail(Integer aftersaleId);
    
    /**
     * 拒绝售后申请
     *
     * @param request 拒绝请求
     * @return 是否成功
     */
    Boolean rejectAftersale(AftersaleRejectRequest request);
    
    /**
     * 处理售后申请
     *
     * @param request 处理请求
     * @return 是否成功
     */
    Boolean processAftersale(AftersaleProcessRequest request);

    /**
     * 获取待处理售后数量
     * 与售后维权页面的"待审核处理"状态保持一致
     *
     * @return 待处理售后数量
     */
    Integer getPendingAftersaleCount();
}