package com.ylpz.core.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SystemThemeRequest;
import com.ylpz.model.system.SystemTheme;

public interface SystemThemeService extends IService<SystemTheme> {

    /**
     * 获取主题列表
     * 
     * @param pageParamRequest 分页参数
     * @return List<SystemTheme>
     */
    List<SystemTheme> getList(PageParamRequest pageParamRequest);

    /**
     * 新增主题
     * 
     * @param request 主题请求对象
     * @return Boolean
     */
    Boolean add(SystemThemeRequest request);

    /**
     * 编辑主题
     * 
     * @param request 主题请求对象
     * @return Boolean
     */
    Boolean edit(SystemThemeRequest request);

    /**
     * 删除主题
     * 
     * @param id 主题ID
     * @return Boolean
     */
    Boolean delete(Integer id);

    /**
     * 设置默认主题
     * 
     * @param id 主题ID
     * @return Boolean
     */
    Boolean setDefault(Integer id);

    /**
     * 获取当前主题
     * 
     * @return SystemTheme
     */
    SystemTheme getCurrentTheme();
}