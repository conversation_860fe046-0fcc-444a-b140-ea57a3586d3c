package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.dao.UserDao;
import com.ylpz.core.dao.UserSpreadRecordDao;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserSpreadService;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserSpreadRecord;
import com.ylpz.core.common.request.UserSpreadRecordRequest;
import com.ylpz.core.common.request.UserSpreadUpdateRequest;
import com.ylpz.core.common.response.UserSpreadDetailResponse;
import com.ylpz.core.common.response.UserSpreadRecordResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户推广关系服务实现类
 */
@Slf4j
@Service
public class UserSpreadServiceImpl extends ServiceImpl<UserSpreadRecordDao, UserSpreadRecord>
        implements UserSpreadService {

    @Resource
    private UserDao userDao;

    @Resource
    private UserSpreadRecordDao userSpreadRecordDao;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    /**
     * 获取会员上下级关系列表
     * 
     * @param request     筛选条件
     * @param pageRequest 分页参数
     * @return 会员上下级关系列表
     */
    @Override
    public PageInfo<UserSpreadRecordResponse> getSpreadRecordList(UserSpreadRecordRequest request,
            PageParamRequest pageRequest) {
        PageHelper.startPage(pageRequest.getPage(), pageRequest.getLimit());

        // 构建查询条件
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotBlank(request.getKeywords())) {
            queryWrapper.and(wrapper -> wrapper
                    .like(User::getNickname, request.getKeywords())
                    .or().like(User::getPhone, request.getKeywords())
                    .or().eq(User::getId, request.getKeywords()));
        }

        if (request.getSpreadUid() != null) {
            queryWrapper.eq(User::getSpreadUid, request.getSpreadUid());
        }

        if (request.getStatus() != null) {
            queryWrapper.eq(User::getSpreadStatus, request.getStatus());
        }

        if (StringUtils.isNotBlank(request.getStartTime()) && StringUtils.isNotBlank(request.getEndTime())) {
            queryWrapper.between(User::getSpreadChangeTime, request.getStartTime(), request.getEndTime());
        }

        // 查询上级手机号
        if (StringUtils.isNotBlank(request.getSpreadPhone())) {
            // 先查询该手机号的用户ID
            User spreadUser = userDao.selectOne(new LambdaQueryWrapper<User>()
                    .eq(User::getPhone, request.getSpreadPhone())
                    .last("LIMIT 1"));

            if (spreadUser != null) {
                queryWrapper.eq(User::getSpreadUid, spreadUser.getId());
            } else {
                // 找不到该手机号用户，返回空结果
                return new PageInfo<>(new ArrayList<>());
            }
        }

        queryWrapper.orderByDesc(User::getSpreadChangeTime);
        List<User> userList = userDao.selectList(queryWrapper);

        // 如果列表为空，直接返回空分页结果
        if (userList.size() == 0) {
            return new PageInfo<>(new ArrayList<>());
        }

        // 获取所有上级用户ID
        List<Integer> spreadUidList = userList.stream()
                .map(User::getSpreadUid)
                .filter(Objects::nonNull)
                .filter(uid -> uid > 0)
                .distinct()
                .collect(Collectors.toList());

        // 批量查询上级用户信息
        Map<Integer, User> spreadUserMap = new HashMap<>();
        if (spreadUidList.size() > 0) {
            List<User> spreadUsers = userDao.selectList(new LambdaQueryWrapper<User>()
                    .in(User::getId, spreadUidList));
            spreadUserMap = spreadUsers.stream()
                    .collect(Collectors.toMap(User::getId, user -> user, (u1, u2) -> u1));
        }

        // 查询会员等级信息
        Map<Integer, String> levelNameMap = new HashMap<>();
        try {
            levelNameMap = systemUserLevelService.getLevelNameMap();
        } catch (Exception e) {
            log.error("获取会员等级名称映射失败", e);
            // 使用常量类提供默认值
            levelNameMap.put(MemberLevelConstants.Level.NONE, MemberLevelConstants.Name.NONE);
            levelNameMap.put(MemberLevelConstants.Level.NORMAL, MemberLevelConstants.Name.NORMAL);
            levelNameMap.put(MemberLevelConstants.Level.VIP, MemberLevelConstants.Name.VIP);
            levelNameMap.put(MemberLevelConstants.Level.SVIP, MemberLevelConstants.Name.SVIP);
        }

        // 封装结果
        List<UserSpreadRecordResponse> result = new ArrayList<>();
        Map<Integer, User> finalSpreadUserMap = spreadUserMap;
        Map<Integer, String> finalLevelNameMap = levelNameMap;

        userList.forEach(user -> {
            UserSpreadRecordResponse response = new UserSpreadRecordResponse();
            response.setUid(user.getId());
            response.setNickname(user.getNickname());
            response.setAvatar(user.getAvatar());
            response.setPhone(user.getPhone());
            response.setMemberLevel(finalLevelNameMap.getOrDefault(user.getLevel(), "普通会员"));
            response.setMemberLevelCode(String.valueOf(user.getLevel()));
            response.setSpreadUid(user.getSpreadUid());
            response.setSpreadChangeTime(user.getSpreadChangeTime());
            response.setSpreadStatus(user.getSpreadStatus());
            response.setSpreadStatusText(user.getSpreadStatus() != null && user.getSpreadStatus() == 1 ? "有效" : "无效");
            // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
            if (MemberLevelConstants.isSvip(user.getLevel())) {
                response.setSpreadNickname("养乐铺子");
                response.setSpreadPhone(""); // SVIP用户不显示上级手机号
            } else {
                // 设置上级信息
                if (user.getSpreadUid() != null && user.getSpreadUid() > 0
                        && finalSpreadUserMap.containsKey(user.getSpreadUid())) {
                    User spreadUser = finalSpreadUserMap.get(user.getSpreadUid());
                    response.setSpreadNickname(spreadUser.getNickname());
                    response.setSpreadPhone(spreadUser.getPhone());
                } else {
                    response.setSpreadNickname("无");
                    response.setSpreadPhone("");
                }
            }
            result.add(response);
        });

        return new PageInfo<>(result);
    }

    /**
     * 获取会员上下级变更详情
     * 
     * @param uid 用户ID
     * @return 变更详情
     */
    @Override
    public UserSpreadDetailResponse getSpreadDetail(Integer uid) {
        UserSpreadDetailResponse response = new UserSpreadDetailResponse();

        // 查询用户基本信息
        User user = userDao.selectById(uid);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 用户信息
        Map<String, Object> userInfo = new HashMap<>();
        userInfo.put("uid", user.getId());
        userInfo.put("nickname", user.getNickname());
        userInfo.put("avatar", user.getAvatar());
        userInfo.put("phone", user.getPhone());

        // 获取会员等级信息
        Map<Integer, String> levelNameMap = new HashMap<>();
        try {
            levelNameMap = systemUserLevelService.getLevelNameMap();
        } catch (Exception e) {
            log.error("获取会员等级名称映射失败", e);
            // 使用常量类提供默认值
            levelNameMap.put(MemberLevelConstants.Level.NONE, MemberLevelConstants.Name.NONE);
            levelNameMap.put(MemberLevelConstants.Level.NORMAL, MemberLevelConstants.Name.NORMAL);
            levelNameMap.put(MemberLevelConstants.Level.VIP, MemberLevelConstants.Name.VIP);
            levelNameMap.put(MemberLevelConstants.Level.SVIP, MemberLevelConstants.Name.SVIP);
        }

        userInfo.put("memberLevel", levelNameMap.getOrDefault(user.getLevel(), MemberLevelConstants.Name.NORMAL));
        userInfo.put("memberLevelCode",String.valueOf(user.getLevel()));
        response.setUser(userInfo);

        // 当前上级信息
        Map<String, Object> currentSpreadInfo = new HashMap<>();
        if (user.getSpreadUid() != null && user.getSpreadUid() >= 0) {
            // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
            if (MemberLevelConstants.isSvip(user.getLevel())) {
                currentSpreadInfo.put("uid", 0); // 使用0表示系统默认上级
                currentSpreadInfo.put("nickname", "养乐铺子");
                currentSpreadInfo.put("phone", "");
                currentSpreadInfo.put("memberLevel", "系统");
                currentSpreadInfo.put("memberLevelCode", 0);
            } else {
                User spreadUser = userDao.selectById(user.getSpreadUid());
                if (spreadUser != null) {
                    currentSpreadInfo.put("uid", spreadUser.getId());
                    currentSpreadInfo.put("nickname", spreadUser.getNickname());
                    currentSpreadInfo.put("phone", spreadUser.getPhone());
                    currentSpreadInfo.put("memberLevel", levelNameMap.getOrDefault(spreadUser.getLevel(), MemberLevelConstants.Name.NORMAL));
                    currentSpreadInfo.put("memberLevelCode", spreadUser.getLevel());
                }
            }
        }
        response.setCurrentSpread(currentSpreadInfo);

        // 查询变更记录
        List<UserSpreadRecord> records = userSpreadRecordDao.selectList(
                new LambdaQueryWrapper<UserSpreadRecord>()
                        .eq(UserSpreadRecord::getUid, uid)
                        .orderByDesc(UserSpreadRecord::getChangeTime));

        if (records.size() > 0) {
            // 获取所有涉及的用户ID
            Set<Integer> userIds = new HashSet<>();
            for (UserSpreadRecord record : records) {
                if (record.getOldSpreadUid() != null && record.getOldSpreadUid() > 0) {
                    userIds.add(record.getOldSpreadUid());
                }
                if (record.getSpreadUid() != null && record.getSpreadUid() > 0) {
                    userIds.add(record.getSpreadUid());
                }
            }

            // 批量查询用户信息
            Map<Integer, User> userMap = new HashMap<>();
            if (userIds.size() > 0) {
                List<User> users = userDao.selectList(new LambdaQueryWrapper<User>()
                        .in(User::getId, userIds));
                userMap = users.stream().collect(Collectors.toMap(User::getId, u -> u, (u1, u2) -> u1));
                userMap.put(0, new User().setLevel(3).setNickname("养乐铺子"));
            }

            // 封装变更记录
            List<UserSpreadDetailResponse.ChangeRecord> changeRecords = new ArrayList<>();
            for (UserSpreadRecord record : records) {
                UserSpreadDetailResponse.ChangeRecord changeRecord = new UserSpreadDetailResponse.ChangeRecord();
                changeRecord.setId(record.getId());
                changeRecord.setChangeTime(record.getChangeTime());
                changeRecord.setOldSpreadUid(record.getOldSpreadUid());
                changeRecord.setNewSpreadUid(record.getSpreadUid());
                changeRecord.setChangeType(record.getChangeType());
                changeRecord.setChangeMessage(record.getChangeMessage());
                changeRecord.setStatus(record.getStatus());

                // 设置变更类型文本
                switch (record.getChangeType()) {
                    case 0:
                        changeRecord.setChangeTypeText("初始绑定");
                        break;
                    case 1:
                        changeRecord.setChangeTypeText("手动调整");
                        break;
                    case 2:
                        changeRecord.setChangeTypeText("自动转移");
                        break;
                    default:
                        changeRecord.setChangeTypeText("未知");
                }

                // 设置原上级信息
                if (record.getOldSpreadUid() != null && record.getOldSpreadUid() >= 0
                        && userMap.containsKey(record.getOldSpreadUid())) {
                    User oldSpread = userMap.get(record.getOldSpreadUid());
                    // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
                    if (MemberLevelConstants.isSvip(user.getLevel())) {
                        changeRecord.setOldSpreadNickname("养乐铺子");
                        changeRecord.setOldSpreadPhone("");
                        changeRecord.setOldSpreadLevel(0); // 系统级别
                    } else {
                        changeRecord.setOldSpreadNickname(oldSpread.getNickname());
                        changeRecord.setOldSpreadPhone(oldSpread.getPhone());
                        changeRecord.setOldSpreadLevel(oldSpread.getLevel());
                    }
                }

                // 设置新上级信息
                if (record.getSpreadUid() != null && record.getSpreadUid() >= 0
                        && userMap.containsKey(record.getSpreadUid())) {
                    User newSpread = userMap.get(record.getSpreadUid());
                    // 如果当前用户是SVIP，统一显示上级为"养乐铺子"
                    if (MemberLevelConstants.isSvip(user.getLevel())) {
                        changeRecord.setNewSpreadNickname("养乐铺子");
                        changeRecord.setNewSpreadPhone("");
                        changeRecord.setNewSpreadLevel(0); // 系统级别
                    } else {
                        changeRecord.setNewSpreadNickname(newSpread.getNickname());
                        changeRecord.setNewSpreadPhone(newSpread.getPhone());
                        changeRecord.setNewSpreadLevel(newSpread.getLevel());
                    }
                }

                changeRecords.add(changeRecord);
            }

            response.setChangeRecords(changeRecords);
        } else {
            response.setChangeRecords(new ArrayList<>());
        }

        return response;
    }

    /**
     * 手动更新会员上级关系
     * 
     * @param request 更新参数
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserSpread(UserSpreadUpdateRequest request) {
        // 查询用户
        User user = userDao.selectById(request.getUid());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 如果上级是自己，不允许设置
        if (request.getSpreadUid().equals(request.getUid())) {
            throw new RuntimeException("上级不能是自己");
        }

        // 校验上级用户
        if (request.getSpreadUid() > 0) {
            User spreadUser = userDao.selectById(request.getSpreadUid());
            if (spreadUser == null) {
                throw new RuntimeException("上级用户不存在");
            }

            // SVIP不能成为下级，检查目标上级是否是SVIP
            if (spreadUser.getLevel() != null && spreadUser.getLevel().equals(MemberLevelConstants.Level.SVIP)) {
                throw new RuntimeException("SVIP会员不能成为上级");
            }
        }

        // 记录原上级ID
        Integer oldSpreadUid = user.getSpreadUid();

        // 更新用户上级
        user.setSpreadUid(request.getSpreadUid());
        user.setSpreadStatus(request.getStatus());
        user.setSpreadChangeTime(new Date());
        user.setSpreadChangeType(1); // 1-手动调整
        user.setSpreadMessage(request.getChangeMessage());

        // 保存用户上级关系变更记录
        UserSpreadRecord record = new UserSpreadRecord();
        record.setUid(user.getId());
        record.setSpreadUid(request.getSpreadUid());
        record.setOldSpreadUid(oldSpreadUid);
        record.setChangeType(1); // 1-手动调整
        record.setChangeTime(new Date());
        record.setStatus(request.getStatus());
        record.setChangeMessage(request.getChangeMessage());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // 保存变更
        userDao.updateById(user);
        userSpreadRecordDao.insert(record);

        return true;
    }

    /**
     * 处理用户等级升级后的推广关系自动脱落
     * 当用户升级到与上级相同或更高等级时，自动解除推广关系
     *
     * @param userId 升级的用户ID
     * @param oldLevel 原等级
     * @param newLevel 新等级
     * @return 是否发生了关系脱落
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean processLevelUpgradeSpreadDetachment(Integer userId, Integer oldLevel, Integer newLevel) {
        if (userId == null || newLevel == null) {
            return false;
        }

        // 查询用户信息
        User user = userDao.selectById(userId);
        if (user == null || user.getSpreadUid() == null || user.getSpreadUid() <= 0) {
            // 用户不存在或没有上级，无需处理
            return false;
        }

        // 查询上级用户信息
        User spreadUser = userDao.selectById(user.getSpreadUid());
        if (spreadUser == null) {
            log.warn("用户{}的上级用户{}不存在，跳过脱落处理", userId, user.getSpreadUid());
            return false;
        }

        Integer spreadLevel = spreadUser.getLevel() != null ? spreadUser.getLevel() : MemberLevelConstants.Level.NONE;

        // 判断是否需要脱落绑定关系
        boolean shouldDetach = shouldDetachSpreadRelation(spreadLevel, newLevel);

        if (!shouldDetach) {
            return false;
        }

        // 记录原上级ID
        Integer oldSpreadUid = user.getSpreadUid();

        // 解除推广关系
        user.setSpreadUid(0);
        user.setSpreadStatus(1); // 设置为无效状态
        user.setSpreadChangeTime(new Date());
        user.setSpreadChangeType(2); // 2-自动转移
        user.setSpreadMessage(generateDetachMessage(spreadLevel, newLevel, spreadUser.getNickname()));

        // 保存用户推广关系变更记录
        UserSpreadRecord record = new UserSpreadRecord();
        record.setUid(user.getId());
        record.setSpreadUid(0); // 新上级为0，表示无上级
        record.setOldSpreadUid(oldSpreadUid);
        record.setChangeType(2); // 2-自动转移
        record.setChangeTime(new Date());
        record.setStatus(0); // 关系无效
        record.setChangeMessage(user.getSpreadMessage());
        record.setCreateTime(new Date());
        record.setUpdateTime(new Date());

        // 保存变更
        userDao.updateById(user);
        userSpreadRecordDao.insert(record);

        log.info("用户{}因等级升级自动脱落推广关系，原上级：{}，原上级等级：{}，用户新等级：{}",
                userId, oldSpreadUid, spreadLevel, newLevel);

        return true;
    }

    /**
     * 判断是否应该脱落推广关系
     *
     * @param spreadLevel 上级等级
     * @param userNewLevel 用户新等级
     * @return 是否应该脱落
     */
    private boolean shouldDetachSpreadRelation(Integer spreadLevel, Integer userNewLevel) {
        // 普通会员上级，下级升级为VIP或SVIP，脱落
        if (MemberLevelConstants.Level.NORMAL.equals(spreadLevel) &&
            MemberLevelConstants.isVipOrAbove(userNewLevel)) {
            return true;
        }

        // VIP会员上级，下级升级为SVIP，脱落
        if (MemberLevelConstants.Level.VIP.equals(spreadLevel) &&
            MemberLevelConstants.Level.SVIP.equals(userNewLevel)) {
            return true;
        }

        // SVIP会员上级，下级升级为SVIP，脱落
        if (MemberLevelConstants.Level.SVIP.equals(spreadLevel) &&
            MemberLevelConstants.Level.SVIP.equals(userNewLevel)) {
            return true;
        }

        return false;
    }

    /**
     * 生成脱落原因消息
     *
     * @param spreadLevel 上级等级
     * @param userNewLevel 用户新等级
     * @return 脱落原因
     */
    private String generateDetachMessage(Integer spreadLevel, Integer userNewLevel, String nickname) {
        String spreadLevelName = MemberLevelConstants.getLevelName(spreadLevel);
        String userLevelName = MemberLevelConstants.getLevelName(userNewLevel);

        return String.format("用户升级为%s，与%s上级%s等级相同或更高，自动脱落推广关系",
                userLevelName, spreadLevelName, nickname);
    }
}