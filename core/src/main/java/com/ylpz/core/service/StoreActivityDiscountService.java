package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.activity.StoreActivityDiscount;

import java.util.List;

/**
 * 活动折扣关联表 Service 接口
 */
public interface StoreActivityDiscountService extends IService<StoreActivityDiscount> {

    /**
     * 根据活动ID获取折扣ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    List<Integer> getDiscountIdsByActivityId(Integer activityId);

    /**
     * 保存活动折扣关联
     * 
     * @param activityId 活动ID
     * @param discountIds 折扣ID列表
     * @return Boolean
     */
    Boolean saveActivityDiscount(Integer activityId, List<Integer> discountIds);

    /**
     * 删除活动折扣关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    Boolean deleteByActivityId(Integer activityId);
} 