package com.ylpz.core.service.impl;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.util.List;

import javax.imageio.ImageIO;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.utils.CrmebUtil;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.common.utils.ImageSplitUtil;
import com.ylpz.core.common.utils.UploadUtil;
import com.ylpz.core.common.vo.CloudVo;
import com.ylpz.core.common.vo.FileResultVo;
import com.ylpz.core.common.vo.UploadCommonVo;
import com.ylpz.core.config.CrmebConfig;
import com.ylpz.core.config.LongImageConfig;
import com.ylpz.core.service.CosService;
import com.ylpz.core.service.OssService;
import com.ylpz.core.service.QiNiuService;
import com.ylpz.core.service.SystemAttachmentService;
import com.ylpz.core.service.SystemConfigService;
import com.ylpz.core.service.UploadService;
import com.ylpz.model.system.SystemAttachment;

import cn.hutool.core.util.StrUtil;
import net.coobird.thumbnailator.Thumbnails;

/**
 * UploadServiceImpl 接口实现
 */
@Service
public class UploadServiceImpl implements UploadService {

    private static final Logger logger = LoggerFactory.getLogger(UploadServiceImpl.class);

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private SystemAttachmentService systemAttachmentService;

    @Autowired
    private QiNiuService qiNiuService;

    @Autowired
    private OssService ossService;

    @Autowired
    private CosService cosService;

    @Autowired
    CrmebConfig crmebConfig;

    @Autowired
    LongImageConfig longImageConfig;

    /**
     * 图片上传
     * 
     * @param multipartFile 文件
     * @param model 模块 用户user,商品product,微信wechat,news文章
     * @param pid 分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列
     * @return FileResultVo
     */
    @Override
    public FileResultVo imageUpload(MultipartFile multipartFile, String model, Integer pid) throws IOException {
        // 默认不处理长图片
        return imageUploadWithLongImageSupport(multipartFile, model, pid, true);
    }

    /**
     * 图片上传（支持长图片分割）
     * 
     * @param multipartFile 文件
     * @param model 模块 用户user,商品product,微信wechat,news文章
     * @param pid 分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列
     * @param handleLongImage 是否处理长图片
     * @return FileResultVo
     */
    @Override
    public FileResultVo imageUploadWithLongImageSupport(MultipartFile multipartFile, String model, Integer pid,
        Boolean handleLongImage) throws IOException {
        if (null == multipartFile || multipartFile.isEmpty()) {
            throw new CrmebException("上传的文件对象不存在...");
        }

        String rootPath = crmebConfig.getImagePath().replace(" ", "").replace("//", "/");
        UploadUtil.setModelPath(model);
        String modelPath = "public/" + model + "/";
        String extStr = systemConfigService.getValueByKey(Constants.UPLOAD_IMAGE_EXT_STR_CONFIG_KEY);
        int size = Integer.parseInt(systemConfigService.getValueByKey(Constants.UPLOAD_IMAGE_MAX_SIZE_CONFIG_KEY));
        String type = Constants.UPLOAD_TYPE_IMAGE + "/";

        // 获取缩略图尺寸配置，默认为336*336
        int thumbnailWidth = 336;
        int thumbnailHeight = 336;
        String thumbnailSizeConfig = systemConfigService.getValueByKey("upload_image_thumbnail_size");
        if (StringUtils.isNotEmpty(thumbnailSizeConfig) && thumbnailSizeConfig.contains("x")) {
            String[] sizes = thumbnailSizeConfig.split("x");
            if (sizes.length == 2) {
                try {
                    thumbnailWidth = Integer.parseInt(sizes[0]);
                    thumbnailHeight = Integer.parseInt(sizes[1]);
                } catch (NumberFormatException e) {
                    logger.error("缩略图尺寸配置格式错误，使用默认值336x336");
                }
            }
        }

        UploadCommonVo uploadCommonVo = new UploadCommonVo();
        uploadCommonVo.setRootPath(rootPath);
        uploadCommonVo.setModelPath(modelPath);
        uploadCommonVo.setExtStr(extStr);
        uploadCommonVo.setSize(size);
        uploadCommonVo.setType(type);

        // 文件名
        String fileName = multipartFile.getOriginalFilename();
        System.out.println("fileName = " + fileName);
        // 文件后缀名
        String extName = FilenameUtils.getExtension(fileName);
        if (StringUtils.isEmpty(extName)) {
            throw new RuntimeException("文件类型未定义不能上传...");
        }

        if (fileName.length() > 99) {
            fileName = StrUtil.subPre(fileName, 90).concat(".").concat(extName);
        }

        // 文件大小验证
        // 文件分隔符转化为当前系统的格式
        float fileSize = (float)multipartFile.getSize() / 1024 / 1024;
        String fs = String.format("%.2f", fileSize);
        if (fileSize > uploadCommonVo.getSize()) {
            throw new CrmebException("最大允许上传" + uploadCommonVo.getSize() + " MB的文件, 当前文件大小为 " + fs + " MB");
        }

        // 判断文件的后缀名是否符合规则
        if (StringUtils.isNotEmpty(uploadCommonVo.getExtStr())) {
            // 切割文件扩展名
            List<String> extensionList = CrmebUtil.stringToArrayStr(uploadCommonVo.getExtStr());
            if (extensionList.size() > 0) {
                // 判断
                if (!extensionList.contains(extName)) {
                    throw new CrmebException("上传文件的类型只能是：" + uploadCommonVo.getExtStr());
                }
            } else {
                throw new CrmebException("上传文件的类型只能是：" + uploadCommonVo.getExtStr());
            }
        }

        // 变更文件名
        String newFileName = UploadUtil.fileName(extName);
        // 缩略图文件名
        String thumbnailFileName = FilenameUtils.getBaseName(newFileName) + "thumbnailImage." + extName;

        // 创建目标文件的名称，规则： 子目录/年/月/日.后缀名
        // 文件分隔符转化为当前系统的格式
        String webPath = uploadCommonVo.getType() + uploadCommonVo.getModelPath()
            + DateUtil.nowDate(Constants.DATE_FORMAT_DATE).replace("-", "/") + "/";
        String destPath = FilenameUtils.separatorsToSystem(uploadCommonVo.getRootPath() + webPath) + newFileName;
        // 缩略图路径
        String thumbnailPath =
            FilenameUtils.separatorsToSystem(uploadCommonVo.getRootPath() + webPath) + thumbnailFileName;

        // 创建文件
        File file = UploadUtil.createFile(destPath);
        File thumbnailFile = UploadUtil.createFile(thumbnailPath);

        // 拼装返回的数据
        FileResultVo resultFile = new FileResultVo();
        resultFile.setFileSize(multipartFile.getSize());
        resultFile.setFileName(fileName);
        resultFile.setExtName(extName);
        resultFile.setUrl(webPath + newFileName);
        resultFile.setThumbnailUrl(webPath + thumbnailFileName);
        resultFile.setType(multipartFile.getContentType());

        // 图片上传类型 1本地 2七牛云 3OSS 4COS, 默认本地
        String uploadType = systemConfigService.getValueByKeyException("uploadType");
        Integer uploadTypeInt = Integer.parseInt(uploadType);
        resultFile.setType(resultFile.getType().replace("image/", ""));
        SystemAttachment systemAttachment = new SystemAttachment();
        systemAttachment.setName(resultFile.getFileName());
        systemAttachment.setAttDir(resultFile.getUrl());
        systemAttachment.setSattDir(resultFile.getThumbnailUrl());
        systemAttachment.setAttSize(resultFile.getFileSize().toString());
        systemAttachment.setAttType(resultFile.getType());
        systemAttachment.setImageType(1); // 图片上传类型 1本地 2七牛云 3OSS 4COS, 默认本地
        systemAttachment.setPid(pid);

        if (uploadTypeInt.equals(1)) {
            // 保存文件
            multipartFile.transferTo(file);

            // 判断是否为可压缩的常规图片格式（排除svg、视频等）
            boolean isCompressibleImage = isCompressibleImageFormat(extName, multipartFile.getContentType());

            // 处理长图片分割
            if (isCompressibleImage && handleLongImage && longImageConfig.getEnabled()) {
                try {
                    // 读取图片
                    BufferedImage sourceImage = ImageIO.read(file);

                    // 使用基于宽度比例的分割方法，传入图片URL作为基准文件名，并生成缩略图
                    ImageSplitUtil.SplitImageResult splitResult = ImageSplitUtil.splitLongImageByWidthRatio(
                        sourceImage, rootPath, webPath + newFileName, extName, thumbnailWidth, thumbnailHeight);

                    if (!splitResult.getSplitImageUrls().isEmpty()) {
                        resultFile.setIsLongImage(true);
                        resultFile.setSplitImageUrls(splitResult.getSplitImageUrls());
                        resultFile.setSplitImageThumbnailUrls(splitResult.getSplitImageThumbnailUrls());

                        // 设置原记录的字段
                        systemAttachment.setIsLongImage(1);
                        systemAttachment.setSplitImageUrls(String.join(",", splitResult.getSplitImageUrls()));
                        systemAttachment.setSplitImageThumbnailUrls(String.join(",", splitResult.getSplitImageThumbnailUrls()));

                        logger.info("长图片分割成功，共分割为{}份：{}", splitResult.getSplitImageUrls().size(), splitResult.getSplitImageUrls());
                        logger.info("长图片缩略图生成成功，共生成{}份：{}", splitResult.getSplitImageThumbnailUrls().size(), splitResult.getSplitImageThumbnailUrls());
                    }
                } catch (Exception e) {
                    logger.error("长图片分割失败：" + e.getMessage(), e);
                }
            }

            // 只对可压缩的图片格式生成缩略图
            if (isCompressibleImage) {
                try {
                    Thumbnails.of(file).size(thumbnailWidth, thumbnailHeight)
                        // .outputQuality(0.9f)
                        .toFile(thumbnailFile);
                } catch (IOException e) {
                    logger.error("生成缩略图失败: " + e.getMessage());
                    // 缩略图生成失败不影响原图上传
                }
            } else {
                // 对于不可压缩的格式，复制原文件作为缩略图文件
                try {
                    FileUtils.copyFile(file, thumbnailFile);
                    logger.info("文件格式 [" + extName + "] 不支持压缩，使用原图副本作为缩略图");
                } catch (IOException e) {
                    logger.error("创建缩略图副本失败: " + e.getMessage());
                    // 如果复制失败，URL仍然使用原图
                    resultFile.setThumbnailUrl(resultFile.getUrl());
                    systemAttachment.setSattDir(resultFile.getUrl());
                }
            }

            systemAttachmentService.save(systemAttachment);
            return resultFile;
        }
        return resultFile;
    }

    /**
     * 判断是否为可压缩的图片格式
     * 
     * @param extName 文件扩展名
     * @param contentType 文件内容类型
     * @return 是否可压缩
     */
    private boolean isCompressibleImageFormat(String extName, String contentType) {
        if (StringUtils.isBlank(extName)) {
            return false;
        }

        // 可压缩的常规图片格式列表
        String[] compressibleFormats = {"jpg", "jpeg", "png", "gif", "bmp", "webp", "tiff", "tif"};

        // 转换为小写进行比较
        String ext = extName.toLowerCase();

        // 检查扩展名是否在可压缩格式列表中
        for (String format : compressibleFormats) {
            if (format.equals(ext)) {
                return true;
            }
        }

        // 检查内容类型
        if (contentType != null && contentType.startsWith("image/") && !contentType.contains("svg")
            && !contentType.contains("webp+xml")) {
            // 排除svg相关格式
            if (!ext.equals("svg")) {
                return true;
            }
        }

        return false;
    }

    /**
     * 文件长传
     * 
     * @param multipartFile 文件
     * @param model 模块 用户user,商品product,微信wechat,news文章
     * @param pid 分类ID 0编辑器,1商品图片,2拼团图片,3砍价图片,4秒杀图片,5文章图片,6组合数据图,7前台用户,8微信系列
     * @return FileResultVo
     * @throws IOException
     */
    @Override
    public FileResultVo fileUpload(MultipartFile multipartFile, String model, Integer pid) throws IOException {
        String rootPath = (crmebConfig.getImagePath() + "/").replace(" ", "").replace("//", "/");
        UploadUtil.setModelPath(model);
        String modelPath = "public/" + model + "/";
        String extStr = systemConfigService.getValueByKey(Constants.UPLOAD_FILE_EXT_STR_CONFIG_KEY);
        int size = Integer.parseInt(systemConfigService.getValueByKey(Constants.UPLOAD_FILE_MAX_SIZE_CONFIG_KEY));
        String type = Constants.UPLOAD_TYPE_FILE + "/";

        UploadCommonVo uploadCommonVo = new UploadCommonVo();
        uploadCommonVo.setRootPath(rootPath);
        uploadCommonVo.setModelPath(modelPath);
        uploadCommonVo.setExtStr(extStr);
        uploadCommonVo.setSize(size);
        uploadCommonVo.setType(type);

        if (null == multipartFile || multipartFile.isEmpty()) {
            throw new CrmebException("上传的文件对象不存在...");
        }
        // 文件名
        String fileName = multipartFile.getOriginalFilename();
        System.out.println("fileName = " + fileName);
        // 文件后缀名
        String extName = FilenameUtils.getExtension(fileName);
        if (StringUtils.isEmpty(extName)) {
            throw new RuntimeException("文件类型未定义不能上传...");
        }

        if (fileName.length() > 99) {
            fileName = StrUtil.subPre(fileName, 90).concat(".").concat(extName);
        }

        // 文件大小验证
        // 文件分隔符转化为当前系统的格式
        float fileSize = (float)multipartFile.getSize() / 1024 / 1024;
        String fs = String.format("%.2f", fileSize);
        if (fileSize > uploadCommonVo.getSize()) {
            throw new CrmebException("最大允许上传" + uploadCommonVo.getSize() + " MB的文件, 当前文件大小为 " + fs + " MB");
        }

        // 判断文件的后缀名是否符合规则
        // isContains(extName);
        if (StringUtils.isNotEmpty(uploadCommonVo.getExtStr())) {
            // 切割文件扩展名
            List<String> extensionList = CrmebUtil.stringToArrayStr(uploadCommonVo.getExtStr());

            if (extensionList.size() > 0) {
                // 判断
                if (!extensionList.contains(extName)) {
                    throw new CrmebException("上传文件的类型只能是：" + uploadCommonVo.getExtStr());
                }
            } else {
                throw new CrmebException("上传文件的类型只能是：" + uploadCommonVo.getExtStr());
            }
        }

        // 文件名
        String newFileName = UploadUtil.fileName(extName);
        // 创建目标文件的名称，规则请看destPath方法
        // 规则： 子目录/年/月/日.后缀名
        // 文件分隔符转化为当前系统的格式
        // 文件分隔符转化为当前系统的格式
        String webPath = uploadCommonVo.getType() + uploadCommonVo.getModelPath()
            + DateUtil.nowDate(Constants.DATE_FORMAT_DATE).replace("-", "/") + "/";
        String destPath = FilenameUtils.separatorsToSystem(uploadCommonVo.getRootPath() + webPath) + newFileName;
        // 创建文件
        File file = UploadUtil.createFile(destPath);

        // 拼装返回的数据
        FileResultVo resultFile = new FileResultVo();
        resultFile.setFileSize(multipartFile.getSize());
        resultFile.setFileName(fileName);
        resultFile.setExtName(extName);
        // resultFile.setServerPath(destPath);
        resultFile.setUrl(webPath + newFileName);
        resultFile.setType(multipartFile.getContentType());

        // 图片上传类型 1本地 2七牛云 3OSS 4COS, 默认本地
        String uploadType = systemConfigService.getValueByKeyException("uploadType");
        Integer uploadTypeInt = Integer.parseInt(uploadType);
        String pre;
        CloudVo cloudVo = new CloudVo();

        resultFile.setType(resultFile.getType().replace("file/", ""));
        SystemAttachment systemAttachment = new SystemAttachment();
        systemAttachment.setName(resultFile.getFileName());
        systemAttachment.setSattDir(resultFile.getUrl());
        systemAttachment.setAttSize(resultFile.getFileSize().toString());
        systemAttachment.setAttType(resultFile.getType());
        systemAttachment.setImageType(1); // 图片上传类型 1本地 2七牛云 3OSS 4COS, 默认本地，任务轮询数据库放入云服务
        systemAttachment.setPid(pid);

        if (uploadTypeInt.equals(1)) {
            // 保存文件
            multipartFile.transferTo(file);
            systemAttachmentService.save(systemAttachment);
            return resultFile;
        }

        return resultFile;
    }
}
