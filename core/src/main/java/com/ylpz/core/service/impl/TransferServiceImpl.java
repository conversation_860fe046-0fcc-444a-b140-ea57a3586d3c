package com.ylpz.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ylpz.core.common.request.TransferRequest;
import com.ylpz.core.common.response.TransferResponse;
import com.ylpz.core.common.utils.RSAProvider;
import com.ylpz.core.config.RSAConfig;
import com.ylpz.core.service.TransferService;
import com.ylpz.model.finance.UserExtract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Map;

/**
 * 提现服务实现类
 */
@Slf4j
@Service
public class TransferServiceImpl implements TransferService {

    @Autowired
    private RSAProvider rsaProvider;

    @Autowired
    private RSAConfig rsaConfig;

    /**
     * 调用提现接口
     *
     * @param userExtract 提现记录
     * @return 提现结果
     */
    @Override
    public TransferResponse requestTransfer(UserExtract userExtract) {
        try {
            // 创建提现请求对象
            TransferRequest request = new TransferRequest();
            request.setExtractId(userExtract.getId());

            // 设置随机字符串
            request.setNonce(RandomUtil.randomString(16));

            // 设置时间戳
            request.setTimestamp(Instant.now().getEpochSecond());

            // 生成签名
            String signContent = request.getExtractId() + request.getNonce() + request.getTimestamp();
            request.setSignature(rsaProvider.encrypt(signContent));

            // 将请求对象转换为Map
            Map<String, Object> formParams = BeanUtil.beanToMap(request);

            // 发送请求
            String url = rsaConfig.getApiUrl() + "/transfer/requestLockTransfer";
            log.info("发送提现请求: {}, 参数: {}", url, formParams);

            // 使用Hutool的HttpRequest以表单形式发送POST请求
            HttpResponse response = HttpRequest.post(url)
                    .contentType("application/x-www-form-urlencoded")
                    .form(formParams)  // 使用form方法设置表单参数
                    .timeout(10000) // 设置超时时间为10秒
                    .execute();

            String responseBody = response.body();
            log.info("提现请求响应: {}", responseBody);

            // 解析结果
            JSONObject jsonResponse = JSON.parseObject(responseBody);

            if (jsonResponse.getInteger("code") == 0) {
                // 成功
                return TransferResponse.success(
                        jsonResponse.getString("tradeNo"),
                        jsonResponse.getLong("transferTime")
                );
            } else {
                // 失败
                return TransferResponse.fail(jsonResponse.getString("message"));
            }

        } catch (Exception e) {
            log.error("调用提现接口异常", e);
            return TransferResponse.fail("提现接口调用失败: " + e.getMessage());
        }
    }
} 