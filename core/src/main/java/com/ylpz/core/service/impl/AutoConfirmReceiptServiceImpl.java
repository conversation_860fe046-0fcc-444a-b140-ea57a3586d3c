package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylpz.core.service.AutoConfirmReceiptService;
import com.ylpz.core.service.StoreOrderService;
import com.ylpz.core.service.StoreOrderStatusService;
import com.ylpz.core.service.SystemParamSettingService;
import com.ylpz.core.service.UserCommissionRecordService;
import com.ylpz.model.order.OrderStatusEnum;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.order.StoreOrderStatus;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.user.UserCommissionRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 自动确认收货服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-19
 */
@Slf4j
@Service
public class AutoConfirmReceiptServiceImpl implements AutoConfirmReceiptService {

    @Autowired
    private StoreOrderService storeOrderService;

    @Autowired
    private StoreOrderStatusService storeOrderStatusService;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    @Autowired
    private UserCommissionRecordService userCommissionRecordService;

    /**
     * 执行自动确认收货任务
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> executeAutoConfirmReceiptTask() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            log.info("开始执行自动确认收货任务");
            
            // 1. 获取自动确认收货配置
            SystemParamSetting receiveTimeSetting = getSystemParamSetting("order_receive_time");
            if (receiveTimeSetting == null) {
                result.put("success", false);
                result.put("message", "未找到发货后自动确认收货时间配置");
                return result;
            }

            // 获取买家申请售后期限配置
            SystemParamSetting afterSaleTimeSetting = getSystemParamSetting("order_after_sale_time");
            if (afterSaleTimeSetting == null) {
                result.put("success", false);
                result.put("message", "未找到买家申请售后期限配置");
                return result;
            }

            // 2. 解析配置天数
            int autoReceiveDays = parseReceiveDays(receiveTimeSetting.getConfigValue());
            if (autoReceiveDays <= 0) {
                result.put("success", false);
                result.put("message", "自动确认收货时间配置无效");
                return result;
            }

            int afterSaleDays = parseReceiveDays(afterSaleTimeSetting.getConfigValue());
            if (afterSaleDays <= 0) {
                result.put("success", false);
                result.put("message", "买家申请售后期限配置无效");
                return result;
            }

            log.info("自动确认收货配置：发货后{}天自动确认收货，确认收货后{}天为售后期限", autoReceiveDays, afterSaleDays);

            // 3. 计算截止时间（发货后超过X天）
            Date cutoffDate = DateUtil.offsetDay(new Date(), -autoReceiveDays);
            
            // 4. 查询符合条件的订单
            List<StoreOrder> eligibleOrders = getEligibleOrdersForAutoConfirm(cutoffDate);
            
            if (CollUtil.isEmpty(eligibleOrders)) {
                log.info("没有找到需要自动确认收货的订单");
                result.put("success", true);
                result.put("message", "没有需要自动确认收货的订单");
                result.put("processedCount", 0);
                return result;
            }

            // 5. 处理每个订单的自动确认收货
            int successCount = 0;
            int failedCount = 0;
            
            for (StoreOrder order : eligibleOrders) {
                try {
                    // 确认收货：将订单状态从"待收货"更新为"已完成"，并处理佣金冻结期
                    boolean success = confirmOrderReceipt(order, afterSaleDays);
                    if (success) {
                        successCount++;
                        log.info("订单{}自动确认收货成功", order.getOrderId());
                    } else {
                        failedCount++;
                        log.error("订单{}自动确认收货失败", order.getOrderId());
                    }
                } catch (Exception e) {
                    failedCount++;
                    log.error("处理订单{}自动确认收货时发生异常", order.getOrderId(), e);
                }
            }

            result.put("success", true);
            result.put("message", String.format("自动确认收货任务执行完成，共处理%d个订单，成功%d个，失败%d个", 
                    eligibleOrders.size(), successCount, failedCount));
            result.put("totalOrders", eligibleOrders.size());
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            
            log.info("自动确认收货任务执行完成：总订单数={}, 成功数={}, 失败数={}", 
                    eligibleOrders.size(), successCount, failedCount);
            
        } catch (Exception e) {
            log.error("执行自动确认收货任务时发生异常", e);
            result.put("success", false);
            result.put("message", "执行自动确认收货任务时发生异常：" + e.getMessage());
        }
        
        return result;
    }

    /**
     * 手动执行自动确认收货任务
     */
    @Override
    public Map<String, Object> manualExecuteTask() {
        log.info("手动触发自动确认收货任务");
        return executeAutoConfirmReceiptTask();
    }

    /**
     * 获取系统参数配置
     */
    private SystemParamSetting getSystemParamSetting(String configCode) {
        try {
            LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SystemParamSetting::getConfigCode, configCode);
            queryWrapper.eq(SystemParamSetting::getStatus, 1); // 只查询启用的配置
            return systemParamSettingService.getOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取系统参数配置失败，configCode: {}", configCode, e);
            return null;
        }
    }

    /**
     * 解析自动确认收货天数配置
     */
    private int parseReceiveDays(String configValue) {
        try {
            if (StrUtil.isBlank(configValue)) {
                return 0;
            }
            
            JSONObject jsonObject = JSONUtil.parseObj(configValue);
            Object value = jsonObject.get("value");
            
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof String) {
                return Integer.parseInt((String) value);
            }
            
            return 0;
        } catch (Exception e) {
            log.error("解析自动确认收货天数配置失败，configValue: {}", configValue, e);
            return 0;
        }
    }

    /**
     * 查询符合自动确认收货条件的订单
     */
    private List<StoreOrder> getEligibleOrdersForAutoConfirm(Date cutoffDate) {
        LambdaQueryWrapper<StoreOrder> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件：已支付、未删除
        queryWrapper.eq(StoreOrder::getPaid, true);
        queryWrapper.eq(StoreOrder::getIsDel, false);
        
        // 订单状态条件：待收货
        queryWrapper.eq(StoreOrder::getStatus, OrderStatusEnum.SHIPPED.getCode());
        
        // 发货时间条件：发货时间早于截止时间
        queryWrapper.le(StoreOrder::getUpdateTime, cutoffDate);
        
        List<StoreOrder> orders = storeOrderService.list(queryWrapper);
        log.info("查询到{}个符合自动确认收货条件的订单", orders.size());
        
        return orders;
    }

    /**
     * 确认订单收货
     *
     * @param order 订单信息
     * @param afterSaleDays 售后期限天数
     * @return 是否成功
     */
    private boolean confirmOrderReceipt(StoreOrder order, int afterSaleDays) {
        try {
            // 1. 更新订单状态为已完成
            order.setStatus(OrderStatusEnum.COMPLETED.getCode());
            order.setUpdateTime(new Date());
            boolean updateSuccess = storeOrderService.updateById(order);

            if (!updateSuccess) {
                log.error("更新订单状态失败，订单号：{}", order.getOrderId());
                return false;
            }

            // 2. 记录订单状态变更
            StoreOrderStatus orderStatus = new StoreOrderStatus();
            orderStatus.setOid(order.getId());
            orderStatus.setChangeType(OrderStatusEnum.COMPLETED.getCode().toString());
            orderStatus.setChangeMessage("系统自动确认收货");
            orderStatus.setCreateTime(new Date());

            boolean statusSuccess = storeOrderStatusService.save(orderStatus);

            if (!statusSuccess) {
                log.error("保存订单状态变更记录失败，订单号：{}", order.getOrderId());
                // 这里不返回false，因为主要的状态更新已经成功
            }

            // 3. 处理相关佣金记录的冻结期
            updateCommissionRecordFrozenTime(order, afterSaleDays);

            return true;

        } catch (Exception e) {
            log.error("确认订单收货失败，订单号：{}", order.getOrderId(), e);
            return false;
        }
    }

    /**
     * 更新佣金记录的冻结期时间
     * 确认收货后，相关佣金记录需要设置售后期限作为冻结期
     *
     * @param order 订单信息
     * @param afterSaleDays 售后期限天数
     */
    private void updateCommissionRecordFrozenTime(StoreOrder order, int afterSaleDays) {
        try {
            // 查询与该订单相关的佣金记录
            LambdaQueryWrapper<UserCommissionRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserCommissionRecord::getLinkId, order.getId());
            queryWrapper.eq(UserCommissionRecord::getStatus, 1); // 待结算状态

            List<UserCommissionRecord> commissionRecords = userCommissionRecordService.list(queryWrapper);

            if (CollUtil.isEmpty(commissionRecords)) {
                log.info("订单{}没有找到相关的佣金记录", order.getOrderId());
                return;
            }

            // 计算解冻时间：当前时间 + 售后期限天数
            long thawTime = DateUtil.offsetDay(new Date(), afterSaleDays).getTime();

            for (UserCommissionRecord record : commissionRecords) {
                // 更新冻结期和解冻时间
                record.setFrozenTime(afterSaleDays);
                record.setThawTime(thawTime);
                record.setUpdateTime(new Date());

                boolean updateSuccess = userCommissionRecordService.updateById(record);
                if (updateSuccess) {
                    log.info("更新佣金记录冻结期成功 - 记录ID: {}, 订单号: {}, 冻结期: {}天, 解冻时间: {}",
                            record.getId(), order.getOrderId(), afterSaleDays, new Date(thawTime));
                } else {
                    log.error("更新佣金记录冻结期失败 - 记录ID: {}, 订单号: {}", record.getId(), order.getOrderId());
                }
            }

        } catch (Exception e) {
            log.error("更新订单{}相关佣金记录冻结期时发生异常", order.getOrderId(), e);
        }
    }
}
