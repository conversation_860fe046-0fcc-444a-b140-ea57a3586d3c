package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.activity.StoreActivitySeckill;

import java.util.List;

/**
 * 活动秒杀关联表 Service 接口
 */
public interface StoreActivitySeckillService extends IService<StoreActivitySeckill> {

    /**
     * 根据活动ID获取秒杀ID列表
     * 
     * @param activityId 活动ID
     * @return List<Integer>
     */
    List<Integer> getSeckillIdsByActivityId(Integer activityId);

    /**
     * 保存活动秒杀关联
     * 
     * @param activityId 活动ID
     * @param seckillIds 秒杀ID列表
     * @return Boolean
     */
    Boolean saveActivitySeckill(Integer activityId, List<Integer> seckillIds);

    /**
     * 删除活动秒杀关联
     * 
     * @param activityId 活动ID
     * @return Boolean
     */
    Boolean deleteByActivityId(Integer activityId);
} 