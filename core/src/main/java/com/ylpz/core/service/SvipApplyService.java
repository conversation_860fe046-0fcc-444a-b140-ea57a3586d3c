package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.github.pagehelper.PageInfo;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.request.SvipApplyAuditRequest;
import com.ylpz.core.common.request.SvipApplyRequest;
import com.ylpz.core.common.request.SvipApplySearchRequest;
import com.ylpz.core.common.response.SvipApplyResponse;
import com.ylpz.model.user.UserSvipApply;

/**
 * SVIP申请服务接口
 */
public interface SvipApplyService extends IService<UserSvipApply> {

    /**
     * 提交SVIP申请
     * 
     * @param request 申请请求
     * @return 是否成功
     */
    boolean submitApply(SvipApplyRequest request);

    /**
     * 获取SVIP申请列表
     * 
     * @param request 查询条件
     * @param pageParamRequest 分页参数
     * @return 申请列表
     */
    PageInfo<SvipApplyResponse> getApplyList(SvipApplySearchRequest request, PageParamRequest pageParamRequest);

    /**
     * 获取SVIP申请详情
     * 
     * @param id 申请ID
     * @return 申请详情
     */
    SvipApplyResponse getApplyDetail(Integer id);

    /**
     * 审核SVIP申请
     * 
     * @param request 审核请求
     * @return 是否成功
     */
    boolean auditApply(SvipApplyAuditRequest request);
} 