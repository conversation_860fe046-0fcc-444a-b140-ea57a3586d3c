package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.core.common.request.SystemUserLevelRequest;
import com.ylpz.core.common.request.SystemUserLevelUpdateShowRequest;

import java.util.List;
import java.util.Map;

/**
 * SystemUserLevelService 接口
 */
public interface SystemUserLevelService extends IService<SystemUserLevel> {

    /**
     * 获取等级列表
     */
    List<SystemUserLevel> getList();

    /**
     * 系统等级新增
     * 
     * @param request request
     * @return Boolean
     */
    Boolean create(SystemUserLevelRequest request);

    /**
     * 系统等级更新
     * 
     * @param id      等级id
     * @param request 等级数据
     * @return Boolean
     */
    Boolean update(Integer id, SystemUserLevelRequest request);

    SystemUserLevel getByLevelId(Integer levelId);

    /**
     * 获取系统等级列表（移动端）
     */
    List<SystemUserLevel> getH5LevelList();

    /**
     * 删除系统等级
     * 
     * @param id 等级id
     * @return Boolean
     */
    Boolean delete(Integer id);

    /**
     * 使用/禁用
     * 
     * @param request request
     */
    Boolean updateShow(SystemUserLevelUpdateShowRequest request);

    /**
     * 获取可用等级列表
     * 
     * @return List
     */
    List<SystemUserLevel> getUsableList();

    /**
     * 获取所有可用会员等级列表，用于前端选择
     * 
     * @return 会员等级列表
     */
    List<SystemUserLevel> findAllList();

    /**
     * 获取会员等级ID与名称的映射关系
     * 
     * @return 等级ID与名称的映射Map
     */
    Map<Integer, String> getLevelNameMap();
}
