package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.model.system.SystemThemeIcon;

import java.util.List;

public interface SystemThemeIconService extends IService<SystemThemeIcon> {

    /**
     * 根据主题ID获取图标列表
     * @param themeId 主题ID
     * @return List<SystemThemeIcon>
     */
    List<SystemThemeIcon> getListByThemeId(Integer themeId);

    /**
     * 批量保存主题图标
     * @param themeId 主题ID
     * @param icons 图标列表
     * @return Boolean
     */
    Boolean saveIcons(Integer themeId, List<SystemThemeIcon> icons);

    /**
     * 删除主题图标
     * @param themeId 主题ID
     * @return Boolean
     */
    Boolean deleteByThemeId(Integer themeId);
} 