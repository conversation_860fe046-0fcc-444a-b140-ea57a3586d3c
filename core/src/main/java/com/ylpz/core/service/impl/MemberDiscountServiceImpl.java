package com.ylpz.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.dao.MemberDiscountDao;
import com.ylpz.core.service.MemberDiscountService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.discount.MemberDiscount;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员折扣Service实现类
 */
@Slf4j
@Service
public class MemberDiscountServiceImpl extends ServiceImpl<MemberDiscountDao, MemberDiscount> implements MemberDiscountService {

    @Resource
    private MemberDiscountDao dao;
    
    @Autowired
    private SystemUserLevelService systemUserLevelService;
    
    @Autowired
    private UserService userService;

    /**
     * 获取会员折扣列表
     * @param keywords 搜索关键词
     * @param status 状态
     * @param pageParamRequest 分页参数
     * @return 会员折扣列表
     */
    @Override
    public List<MemberDiscount> getList(String keywords, Boolean status, PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        
        LambdaQueryWrapper<MemberDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberDiscount::getIsDel, false);
        
        if (ObjectUtil.isNotNull(status)) {
            lambdaQueryWrapper.eq(MemberDiscount::getStatus, status);
        }
        
        if (StrUtil.isNotBlank(keywords)) {
            lambdaQueryWrapper.like(MemberDiscount::getName, keywords);
        }
        
        lambdaQueryWrapper.orderByDesc(MemberDiscount::getSort, MemberDiscount::getId);
        return dao.selectList(lambdaQueryWrapper);
    }

    /**
     * 新增会员折扣
     * @param memberDiscount 会员折扣信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean create(MemberDiscount memberDiscount) {
        if (checkNameExist(memberDiscount.getName(), null)) {
            throw new CrmebException("折扣名称已存在");
        }
        
        // 校验会员等级是否存在
        checkMemberLevels(memberDiscount.getMemberLevels());
        
        memberDiscount.setStatus(true);
        memberDiscount.setIsDel(false);
        memberDiscount.setCreateTime(new Date());
        memberDiscount.setUpdateTime(new Date());
        
        return save(memberDiscount);
    }

    /**
     * 修改会员折扣
     * @param memberDiscount 会员折扣信息
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(MemberDiscount memberDiscount) {
        MemberDiscount discount = getById(memberDiscount.getId());
        if (ObjectUtil.isNull(discount)) {
            throw new CrmebException("会员折扣不存在");
        }
        
        if (checkNameExist(memberDiscount.getName(), memberDiscount.getId())) {
            throw new CrmebException("折扣名称已存在");
        }
        
        // 校验会员等级是否存在
        checkMemberLevels(memberDiscount.getMemberLevels());
        
        memberDiscount.setUpdateTime(new Date());
        
        return updateById(memberDiscount);
    }

    /**
     * 修改会员折扣状态
     * @param id 折扣id
     * @param status 状态
     * @return 是否成功
     */
    @Override
    public boolean updateStatus(Integer id, Boolean status) {
        MemberDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount)) {
            throw new CrmebException("会员折扣不存在");
        }
        
        if (discount.getStatus().equals(status)) {
            throw new CrmebException("折扣状态无需变更");
        }
        
        MemberDiscount updateDiscount = new MemberDiscount();
        updateDiscount.setId(id);
        updateDiscount.setStatus(status);
        updateDiscount.setUpdateTime(new Date());
        
        return updateById(updateDiscount);
    }

    /**
     * 删除会员折扣
     * @param id 折扣id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(Integer id) {
        MemberDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount) || discount.getIsDel()) {
            throw new CrmebException("会员折扣不存在");
        }
        
        MemberDiscount updateDiscount = new MemberDiscount();
        updateDiscount.setId(id);
        updateDiscount.setIsDel(true);
        updateDiscount.setUpdateTime(new Date());
        
        return updateById(updateDiscount);
    }

    /**
     * 复制会员折扣
     * @param id 折扣id
     * @return 是否成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copy(Integer id) {
        MemberDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount) || discount.getIsDel()) {
            throw new CrmebException("会员折扣不存在");
        }
        
        MemberDiscount copyDiscount = new MemberDiscount();
        BeanUtils.copyProperties(discount, copyDiscount);
        copyDiscount.setId(null);
        copyDiscount.setName(discount.getName() + "-复制");
        copyDiscount.setStatus(false);
        copyDiscount.setCreateTime(new Date());
        copyDiscount.setUpdateTime(new Date());
        
        return save(copyDiscount);
    }

    /**
     * 获取会员折扣详情
     * @param id 折扣id
     * @return 会员折扣信息
     */
    @Override
    public MemberDiscount getInfo(Integer id) {
        MemberDiscount discount = getById(id);
        if (ObjectUtil.isNull(discount) || discount.getIsDel()) {
            throw new CrmebException("会员折扣不存在");
        }
        
        return discount;
    }

    /**
     * 根据商品ID获取适用的会员折扣
     * @param productId 商品ID
     * @param userId 用户ID
     * @return 适用的会员折扣
     */
    @Override
    public MemberDiscount getByProductAndUser(Integer productId, Integer userId) {
        if (productId <= 0 || userId <= 0) {
            return null;
        }
        
        // 获取用户信息
        User user = userService.getById(userId);
        if (ObjectUtil.isNull(user) || ObjectUtil.isNull(user.getLevel()) || user.getLevel() <= 0) {
            return null;
        }
        
        // 查询用户等级适用的折扣
        LambdaQueryWrapper<MemberDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberDiscount::getIsDel, false);
        lambdaQueryWrapper.eq(MemberDiscount::getStatus, true);
        lambdaQueryWrapper.like(MemberDiscount::getMemberLevels, user.getLevel());
        
        List<MemberDiscount> discountList = list(lambdaQueryWrapper);
        if (discountList.isEmpty()) {
            return null;
        }
        
        // 筛选适用于当前商品的折扣
        for (MemberDiscount discount : discountList) {
            // 全场通用
            if (discount.getUseType() == 1) {
                return discount;
            }
            
            // 指定商品可用
            if (discount.getUseType() == 2 && StrUtil.isNotBlank(discount.getProductIds())) {
                List<String> productIds = Arrays.asList(discount.getProductIds().split(","));
                if (productIds.contains(productId.toString())) {
                    return discount;
                }
            }
            
            // 指定商品不可用
            if (discount.getUseType() == 3 && StrUtil.isNotBlank(discount.getProductIds())) {
                List<String> productIds = Arrays.asList(discount.getProductIds().split(","));
                if (!productIds.contains(productId.toString())) {
                    return discount;
                }
            }
        }
        
        return null;
    }

    /**
     * 获取数据统计
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getStatistics() {
        Map<String, Object> map = new HashMap<>();
        
        // 获取会员折扣总数
        LambdaQueryWrapper<MemberDiscount> totalQuery = new LambdaQueryWrapper<>();
        totalQuery.eq(MemberDiscount::getIsDel, false);
        map.put("total", count(totalQuery));
        
        // 获取启用的会员折扣数量
        LambdaQueryWrapper<MemberDiscount> enableQuery = new LambdaQueryWrapper<>();
        enableQuery.eq(MemberDiscount::getIsDel, false);
        enableQuery.eq(MemberDiscount::getStatus, true);
        map.put("enable", count(enableQuery));
        
        return map;
    }
    
    /**
     * 检查折扣名称是否已存在
     * @param name 折扣名称
     * @param id 折扣ID (更新时使用，新增时传null)
     * @return 是否存在
     */
    private boolean checkNameExist(String name, Integer id) {
        LambdaQueryWrapper<MemberDiscount> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(MemberDiscount::getName, name);
        lambdaQueryWrapper.eq(MemberDiscount::getIsDel, false);
        
        if (ObjectUtil.isNotNull(id)) {
            lambdaQueryWrapper.ne(MemberDiscount::getId, id);
        }
        
        return dao.selectCount(lambdaQueryWrapper) > 0;
    }
    
    /**
     * 校验会员等级是否存在
     * @param memberLevels 会员等级，多个用逗号分隔
     */
    private void checkMemberLevels(String memberLevels) {
        if (StrUtil.isBlank(memberLevels)) {
            throw new CrmebException("请选择会员等级");
        }
        
        // 获取所有可用会员等级
        List<SystemUserLevel> levelList = systemUserLevelService.getUsableList();
        List<Integer> validLevelIds = levelList.stream().map(SystemUserLevel::getId).collect(Collectors.toList());
        
        // 校验选择的会员等级是否有效
        String[] levelIds = memberLevels.split(",");
        for (String levelId : levelIds) {
            if (!validLevelIds.contains(Integer.parseInt(levelId))) {
                throw new CrmebException("选择的会员等级不存在或已禁用");
            }
        }
    }
} 