package com.ylpz.core.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ylpz.core.common.constants.ExperienceRecordConstants;
import com.ylpz.core.common.constants.SystemParamSettingConstants;
import com.ylpz.core.service.OrderExperienceService;
import com.ylpz.core.service.SystemParamSettingService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserExperienceRecordService;
import com.ylpz.core.service.UserLevelService;
import com.ylpz.core.service.UserService;
import com.ylpz.model.order.StoreOrder;
import com.ylpz.model.system.SystemParamSetting;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserExperienceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 2025-07-21
 * 订单经验值服务实现类
 * 处理订单支付成功后的经验值增加逻辑
 */
@Service
public class OrderExperienceServiceImpl implements OrderExperienceService {

    private static final Logger logger = LoggerFactory.getLogger(OrderExperienceServiceImpl.class);

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserExperienceRecordService userExperienceRecordService;

    @Autowired
    private UserService userService;

    @Autowired
    private UserLevelService userLevelService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private SystemParamSettingService systemParamSettingService;

    /**
     * 订单支付成功后增加经验值
     */
    @Override
    public Boolean addExperienceForOrder(StoreOrder order, User user) {
        try {
            // 检查参数
            if (order == null || user == null || order.getPayPrice() == null) {
                logger.warn("订单或用户信息为空，无法增加经验值");
                return false;
            }

            // 检查用户等级是否支持自购消费经验值
            if (!isOrderExperienceEnabledForUser(user.getLevel())) {
                logger.info("用户等级{}不支持自购消费经验值", user.getLevel());
                return true; // 不是错误，只是该等级不支持
            }

            // 计算应增加的经验值
            Integer addExperience = calculateOrderExperience(order.getPayPrice());
            if (addExperience <= 0) {
                logger.info("订单金额{}计算出的经验值为0，无需增加", order.getPayPrice());
                return true;
            }

            // 创建经验值记录
            UserExperienceRecord experienceRecord = new UserExperienceRecord();
            experienceRecord.setUid(user.getId());
            experienceRecord.setLinkId(order.getOrderId());
            experienceRecord.setLinkType(ExperienceRecordConstants.EXPERIENCE_RECORD_LINK_TYPE_ORDER);
            experienceRecord.setType(ExperienceRecordConstants.EXPERIENCE_RECORD_TYPE_ADD);
            experienceRecord.setTitle(ExperienceRecordConstants.EXPERIENCE_RECORD_TITLE_ORDER);
            experienceRecord.setExperience(addExperience);
            experienceRecord.setBalance(user.getExperience() + addExperience);
            experienceRecord.setMark(StrUtil.format("订单{}消费{}元，获得{}经验值",
                order.getOrderId(), order.getPayPrice(), addExperience));
            experienceRecord.setStatus(ExperienceRecordConstants.EXPERIENCE_RECORD_STATUS_CREATE);
            experienceRecord.setCreateTime(new Date());

            // 更新用户经验值
            User updateUser = new User();
            updateUser.setId(user.getId());
            updateUser.setExperience(user.getExperience() + addExperience);

            // 事务处理
            Boolean result = transactionTemplate.execute(e -> {
                // 保存经验值记录
                userExperienceRecordService.save(experienceRecord);
                // 更新用户经验值
                userService.updateById(updateUser);
                // 检查用户升级
                User updatedUser = userService.getById(user.getId());
                userLevelService.upLevel(updatedUser);
                return Boolean.TRUE;
            });

            if (result) {
                logger.info("订单{}支付成功，用户{}增加{}经验值",
                    order.getOrderId(), user.getId(), addExperience);
            }

            return result;

        } catch (Exception e) {
            logger.error("订单支付成功增加经验值失败，订单号：{}，用户ID：{}",
                order.getOrderId(), user.getId(), e);
            return false;
        }
    }

    /**
     * 根据订单金额计算经验值
     * 根据系统参数设置表中的self_purchase配置计算
     */
    private Integer calculateOrderExperience(BigDecimal orderAmount) {
        if (orderAmount == null || orderAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return 0;
        }

        try {
            // 从系统参数设置表获取自购商品经验值配置
            SystemParamSetting setting = getSystemParamSetting(SystemParamSettingConstants.ExperienceConfig.SELF_PURCHASE);
            if (setting == null || !setting.getStatus()) {
                logger.info("自购商品经验值配置未启用或不存在");
                return 0;
            }

            // 解析配置值
            JSONObject configJson = JSONUtil.parseObj(setting.getConfigValue());
            if (configJson == null) {
                logger.warn("自购商品经验值配置格式错误：{}", setting.getConfigValue());
                return 0;
            }

            // 获取配置参数：number（每消费金额）和ratio（获得经验值比例）
            Integer number = configJson.getInt("number");
            Integer ratio = configJson.getInt("ratio");

            if (number == null || number <= 0 || ratio == null || ratio <= 0) {
                logger.warn("自购商品经验值配置参数无效，number: {}, ratio: {}", number, ratio);
                return 0;
            }

            // 计算经验值：(订单金额 / number) * ratio
            BigDecimal experienceDecimal = orderAmount.divide(new BigDecimal(number), 2, RoundingMode.DOWN)
                                                     .multiply(new BigDecimal(ratio));

            return experienceDecimal.intValue();

        } catch (Exception e) {
            logger.error("计算订单经验值失败，订单金额：{}", orderAmount, e);
            return 0;
        }
    }
    
    /**
     * 检查用户等级是否支持自购消费经验值
     */
    private Boolean isOrderExperienceEnabledForUser(Integer userLevel) {
        if (userLevel == null) {
            return false;
        }

        try {
            if (userLevel <= 1) {
                userLevel = userLevel++;
            }
            SystemUserLevel systemUserLevel = systemUserLevelService.getByLevelId(userLevel);
            if (systemUserLevel == null) {
                logger.warn("未找到用户等级配置，等级：{}", userLevel);
                return false;
            }

            // 检查经验值来源配置是否包含"自购消费"
            String experienceSource = systemUserLevel.getExperienceSource();
            if (StrUtil.isBlank(experienceSource)) {
                return false;
            }

            // 检查是否包含"自购消费"
            return experienceSource.contains("自购消费");

        } catch (Exception e) {
            logger.error("检查用户等级自购消费经验值配置失败，等级：{}", userLevel, e);
            return false;
        }
    }

    /**
     * 获取系统参数配置
     */
    private SystemParamSetting getSystemParamSetting(String configCode) {
        try {
            LambdaQueryWrapper<SystemParamSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SystemParamSetting::getConfigCode, configCode);
            queryWrapper.eq(SystemParamSetting::getStatus, true); // 只查询启用的配置
            return systemParamSettingService.getOne(queryWrapper);
        } catch (Exception e) {
            logger.error("获取系统参数配置失败，configCode: {}", configCode, e);
            return null;
        }
    }

}
