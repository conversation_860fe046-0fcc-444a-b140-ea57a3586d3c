package com.ylpz.core.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.core.common.exception.CrmebException;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.core.common.request.SystemUserLevelRequest;
import com.ylpz.core.common.request.SystemUserLevelUpdateShowRequest;
import com.ylpz.core.dao.SystemUserLevelDao;
import com.ylpz.core.service.SystemAttachmentService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserLevelService;
import com.ylpz.core.service.UserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * SystemUserLevelServiceImpl 接口实现
 */
@Service
public class SystemUserLevelServiceImpl extends ServiceImpl<SystemUserLevelDao, SystemUserLevel>
        implements SystemUserLevelService {

    @Resource
    private SystemUserLevelDao dao;

    @Autowired
    private SystemAttachmentService systemAttachmentService;
    @Autowired
    private UserLevelService userLevelService;
    @Autowired
    private UserService userService;
    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 分页显示设置用户等级表
     * 
     * @return List<SystemUserLevel>
     */
    @Override
    public List<SystemUserLevel> getList() {
        LambdaQueryWrapper<SystemUserLevel> levelLambdaQueryWrapper = new LambdaQueryWrapper<>();
        levelLambdaQueryWrapper.eq(SystemUserLevel::getIsDel, false);
        levelLambdaQueryWrapper.orderByAsc(SystemUserLevel::getGrade);
        return dao.selectList(levelLambdaQueryWrapper);
    }

    /**
     * 新增设置用户等级表
     * 
     * @param request SystemUserLevelRequest 新增参数
     * @return boolean
     *         等级名称不能重复
     *         等级级别不能重复
     */
    @Override
    public Boolean create(SystemUserLevelRequest request) {
        checkLevel(request);
        SystemUserLevel systemUserLevel = new SystemUserLevel();
        BeanUtils.copyProperties(request, systemUserLevel);
        systemUserLevel.setIcon(systemAttachmentService.clearPrefix(request.getIcon()));
        return save(systemUserLevel);
    }

    /**
     * 添加、修改校验
     * 
     * @param request 用户等级参数
     *                等级名称不能重复
     *                等级级别不能重复
     */
    private void checkLevel(SystemUserLevelRequest request) {
        SystemUserLevel temp;
        // 校验名称
        LambdaQueryWrapper<SystemUserLevel> lqw = Wrappers.lambdaQuery();
        lqw.eq(SystemUserLevel::getName, request.getName());
        if (ObjectUtil.isNotNull(request.getId())) {
            lqw.ne(SystemUserLevel::getId, request.getId());
        }
        lqw.eq(SystemUserLevel::getIsDel, false);
        temp = dao.selectOne(lqw);
        if (ObjectUtil.isNotNull(temp)) {
            throw new CrmebException("用户等级名称重复");
        }
        // 校验等级级别
        lqw.clear();
        lqw.eq(SystemUserLevel::getGrade, request.getGrade());
        if (ObjectUtil.isNotNull(request.getId())) {
            lqw.ne(SystemUserLevel::getId, request.getId());
        }
        lqw.eq(SystemUserLevel::getIsDel, false);
        temp = dao.selectOne(lqw);
        if (ObjectUtil.isNotNull(temp)) {
            throw new CrmebException("用户等级级别重复");
        }
        // 校验等级经验不能比上一级别的低,不能比下一级别高
        if (request.getGrade() > MemberLevelConstants.Level.NORMAL) {
            lqw.clear();
            lqw.lt(SystemUserLevel::getGrade, request.getGrade());
            if (ObjectUtil.isNotNull(request.getId())) {
                lqw.ne(SystemUserLevel::getId, request.getId());
            }
            lqw.eq(SystemUserLevel::getIsDel, false);
            lqw.orderByDesc(SystemUserLevel::getGrade);
            lqw.last(" limit 1");
            temp = dao.selectOne(lqw);
            if (ObjectUtil.isNotNull(temp) && temp.getExperience() >= request.getExperience()) {
                throw new CrmebException("当前等级的经验不能比上一级别的经验低");
            }
        }
        lqw.clear();
        lqw.gt(SystemUserLevel::getGrade, request.getGrade());
        if (ObjectUtil.isNotNull(request.getId())) {
            lqw.ne(SystemUserLevel::getId, request.getId());
        }
        lqw.eq(SystemUserLevel::getIsDel, false);
        lqw.orderByAsc(SystemUserLevel::getGrade);
        lqw.last(" limit 1");
        temp = dao.selectOne(lqw);
        if (ObjectUtil.isNotNull(temp) && temp.getExperience() <= request.getExperience()) {
            throw new CrmebException("当前等级的经验不能比下一级别的经验高");
        }
    }

    /**
     * 系统等级更新
     * 
     * @param id      等级id
     * @param request 等级数据
     * @return Boolean
     */
    @Override
    public Boolean update(Integer id, SystemUserLevelRequest request) {
        SystemUserLevel level = getById(id);
        if (ObjectUtil.isNull(level) || level.getIsDel()) {
            throw new CrmebException("等级不存在");
        }
        request.setId(id);
        checkLevel(request);

        return transactionTemplate.execute(e -> {
            // 使用UpdateWrapper来精确控制更新，允许null值
            UpdateWrapper<SystemUserLevel> updateWrapper = new UpdateWrapper<>();
            updateWrapper.eq("id", id);

            // 设置所有字段，包括null值
            updateWrapper.set("name", request.getName());
            updateWrapper.set("experience", request.getExperience());
            updateWrapper.set("grade", request.getGrade());
            updateWrapper.set("discount_enabled", request.getDiscountEnabled());
            updateWrapper.set("discount", request.getDiscount());
            updateWrapper.set("experience_source", request.getExperienceSource());
            updateWrapper.set("auto_upgrade", request.getAutoUpgrade());
            updateWrapper.set("phone_required", request.getPhoneRequired());
            updateWrapper.set("avatar_nickname_required", request.getAvatarNicknameRequired());
            updateWrapper.set("birthday_required", request.getBirthdayRequired());
            updateWrapper.set("coupon_enabled", request.getCouponEnabled());
            updateWrapper.set("coupon_id", request.getCouponId());
            updateWrapper.set("birth_coupon_enabled", request.getBirthCouponEnabled());
            updateWrapper.set("birth_coupon_id", request.getBirthCouponId());
            updateWrapper.set("commission_enabled", request.getCommissionEnabled() != null && request.getCommissionEnabled());
            updateWrapper.set("commission_rate", request.getCommissionRate()==null ? 0 : request.getCommissionRate());
            updateWrapper.set("customer_service_enabled", request.getCustomerServiceEnabled());
            updateWrapper.set("customer_service_wechat", request.getCustomerServiceWechat());
            updateWrapper.set("is_show", request.getIsShow());

            // 处理特殊字段 - icon字段需要清理前缀
            if (ObjectUtil.isNotNull(request.getIcon())) {
                updateWrapper.set("icon", systemAttachmentService.clearPrefix(request.getIcon()));
            } else {
                updateWrapper.set("icon", null);
            }

            dao.update(null, updateWrapper);
            // 删除对应的用户等级数据
            //userLevelService.deleteByLevelId(id);
            // 清除对应的用户等级
            //userService.removeLevelByLevelId(id);
            return Boolean.TRUE;
        });
    }

    @Override
    public SystemUserLevel getByLevelId(Integer levelId) {
        LambdaQueryWrapper<SystemUserLevel> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SystemUserLevel::getIsShow, 1);
        lambdaQueryWrapper.eq(SystemUserLevel::getIsDel, 0);
        lambdaQueryWrapper.eq(SystemUserLevel::getId, levelId);
        return dao.selectOne(lambdaQueryWrapper);
    }

    /**
     * 获取系统等级列表（移动端）
     */
    @Override
    public List<SystemUserLevel> getH5LevelList() {
        LambdaQueryWrapper<SystemUserLevel> lqw = new LambdaQueryWrapper<>();
        lqw.select(SystemUserLevel::getId, SystemUserLevel::getName, SystemUserLevel::getIcon,
                SystemUserLevel::getExperience);
        lqw.eq(SystemUserLevel::getIsShow, true);
        lqw.eq(SystemUserLevel::getIsDel, false);
        lqw.orderByAsc(SystemUserLevel::getGrade);
        return dao.selectList(lqw);
    }

    /**
     * 删除系统等级
     * 
     * @param id 等级id
     * @return Boolean
     */
    @Override
    public Boolean delete(Integer id) {
        SystemUserLevel level = getById(id);
        if (ObjectUtil.isNull(level) || level.getIsDel()) {
            throw new CrmebException("系统等级不存在");
        }
        level.setIsDel(true);
        return transactionTemplate.execute(e -> {
            dao.updateById(level);
            // 删除对应的用户等级数据
            //userLevelService.deleteByLevelId(id);
            // 清除对应的用户等级
            //userService.removeLevelByLevelId(id);
            return Boolean.TRUE;
        });
    }

    /**
     * 使用/禁用
     * 
     * @param request request
     */
    @Override
    public Boolean updateShow(SystemUserLevelUpdateShowRequest request) {
        SystemUserLevel systemUserLevel = getById(request.getId());
        if (ObjectUtil.isNull(systemUserLevel) || systemUserLevel.getIsDel()) {
            throw new CrmebException("系统等级不存在");
        }
        systemUserLevel.setIsShow(request.getIsShow());
        return updateById(systemUserLevel);
    }

    /**
     * 获取可用等级列表
     * 
     * @return List
     */
    @Override
    public List<SystemUserLevel> getUsableList() {
        LambdaQueryWrapper<SystemUserLevel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SystemUserLevel::getIsShow, true);
        lqw.eq(SystemUserLevel::getIsDel, false);
        lqw.orderByAsc(SystemUserLevel::getGrade);
        return dao.selectList(lqw);
    }

    /**
     * 获取所有可用会员等级列表，用于前端选择
     * 
     * @return 会员等级列表
     */
    @Override
    public List<SystemUserLevel> findAllList() {
        LambdaQueryWrapper<SystemUserLevel> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SystemUserLevel::getIsDel, false);
        lqw.orderByAsc(SystemUserLevel::getGrade);
        return dao.selectList(lqw);
    }

    /**
     * 获取会员等级Grade与名称的映射关系
     *
     * @return 等级Grade与名称的映射Map (0=无会员等级, 1=普通会员, 2=VIP会员, 3=SVIP会员)
     */
    @Override
    public Map<Integer, String> getLevelNameMap() {
        Map<Integer, String> levelMap = new HashMap<>();

        // 基础等级
        levelMap.put(MemberLevelConstants.Level.NONE, MemberLevelConstants.Name.NONE);

        // 获取系统配置的等级，使用grade作为key而不是id
        LambdaQueryWrapper<SystemUserLevel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SystemUserLevel::getId, SystemUserLevel::getName, SystemUserLevel::getGrade);
        queryWrapper.eq(SystemUserLevel::getIsDel, false);
        queryWrapper.eq(SystemUserLevel::getIsShow, true);
        queryWrapper.orderByAsc(SystemUserLevel::getGrade);

        List<SystemUserLevel> levelList = dao.selectList(queryWrapper);
        if (levelList != null && !levelList.isEmpty()) {
            // 使用grade作为key而不是id，确保等级映射的一致性
            Map<Integer, String> systemLevelMap = levelList.stream()
                    .collect(Collectors.toMap(SystemUserLevel::getGrade, SystemUserLevel::getName));

            levelMap.putAll(systemLevelMap);
        }

        return levelMap;
    }
}
