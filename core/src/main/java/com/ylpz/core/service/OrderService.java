package com.ylpz.core.service;

import com.ylpz.core.common.request.StoreProductReplyAddRequest;

import java.math.BigDecimal;

/**
 * H5端订单操作
 */
public interface OrderService {
    /**
     * 创建订单商品评价
     *
     * @param request 请求参数
     * @return Boolean
     */
    Boolean reply(StoreProductReplyAddRequest request);

    /**
     * 订单取消
     *
     * @param id 订单id
     * @return Boolean
     */
    Boolean cancel(Integer id);


    /**
     * 获取用户消费总金额
     *
     * @param userId 用户ID
     * @return 消费总金额
     */
    BigDecimal getUserOrderAmount(Integer userId);
}
