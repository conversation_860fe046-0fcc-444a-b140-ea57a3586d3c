package com.ylpz.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.ylpz.core.common.constants.Constants;
import com.ylpz.core.common.constants.MemberLevelConstants;
import com.ylpz.model.system.SystemUserLevel;
import com.ylpz.model.user.User;
import com.ylpz.model.user.UserLevel;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.core.common.utils.DateUtil;
import com.ylpz.core.dao.UserLevelDao;
import com.ylpz.core.service.StoreCouponUserService;
import com.ylpz.core.service.SystemUserLevelService;
import com.ylpz.core.service.UserLevelService;
import com.ylpz.core.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * UserLevelServiceImpl 接口实现
 */
@Service
public class UserLevelServiceImpl extends ServiceImpl<UserLevelDao, UserLevel> implements UserLevelService {

    @Resource
    private UserLevelDao dao;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private UserService userService;

    @Autowired
    private StoreCouponUserService storeCouponUserService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 列表
     * 
     * @param pageParamRequest 分页类参数
     * @return List<UserLevel>
     */
    @Override
    public List<UserLevel> getList(PageParamRequest pageParamRequest) {
        PageHelper.startPage(pageParamRequest.getPage(), pageParamRequest.getLimit());
        return dao.selectList(null);
    }

    /**
     * 用户升级
     * 
     * @param user 用户数据
     * @return Boolean
     */
    @Override
    public Boolean upLevel(User user) {
        // 确定当前经验所达到的等级
        List<SystemUserLevel> list = systemUserLevelService.getUsableList();
        if (CollUtil.isEmpty(list)) {
            log.error("系统会员等级未配置，请配置对应数据");
            return Boolean.TRUE;
        }

        SystemUserLevel userLevelConfig = null;
        for (SystemUserLevel systemUserLevel : list) {
            if (1 == systemUserLevel.getGrade()) {
                continue;
            }
            if (user.getExperience() > systemUserLevel.getExperience()) {
                userLevelConfig = systemUserLevel;
                break;
            }
        }

        if (ObjectUtil.isNull(userLevelConfig)) {
            log.warn("未找到用户对应的会员等级,uid = " + user.getId());
            return Boolean.TRUE;
        }

        // 判断用户是否还在原等级（现在user.level存储的是grade值）
        Integer currentLevel = user.getLevel() != null ? user.getLevel() : MemberLevelConstants.Level.NONE;
        if (currentLevel.equals(userLevelConfig.getGrade())) {
            return Boolean.TRUE;
        }

        // 判断用户等级是否已经高于目标等级（防止降级）
        if (currentLevel > userLevelConfig.getGrade()) {
            return Boolean.TRUE;
        }

        // 判断是否开启自动升级
        if (userLevelConfig.getAutoUpgrade() == null || !userLevelConfig.getAutoUpgrade()) {
            log.warn("用户ID：" + user.getId() + "达到了升级经验，但会员等级ID：" + userLevelConfig.getId() + "未开启自动升级");
            return Boolean.TRUE;
        }

        UserLevel newLevel = new UserLevel();
        newLevel.setStatus(true);
        newLevel.setIsDel(false);
        newLevel.setGrade(userLevelConfig.getGrade());
        newLevel.setUid(user.getId());
        newLevel.setLevelId(userLevelConfig.getId());

        // 处理折扣值，如果折扣功能未启用则设置为10（不打折）
        if (userLevelConfig.getDiscountEnabled() != null && userLevelConfig.getDiscountEnabled()) {
            newLevel.setDiscount(userLevelConfig.getDiscount());
        } else {
            newLevel.setDiscount(10.0);
        }

        Date date = DateUtil.nowDateTimeReturnDate(Constants.DATE_FORMAT);
        String mark = Constants.USER_LEVEL_UP_LOG_MARK.replace("【{$userName}】", user.getNickname())
                .replace("{$date}", DateUtil.dateToStr(date, Constants.DATE_FORMAT))
                .replace("{$levelName}", userLevelConfig.getName());
        newLevel.setMark(mark);

        // 更新会员等级（存储grade值而不是id）
        user.setLevel(userLevelConfig.getGrade());
        return transactionTemplate.execute(e -> {
            save(newLevel);
            userService.updateById(user);
            return Boolean.TRUE;
        });
    }

    /**
     * 经验降级
     * 
     * @param user 用户
     * @return Boolean
     */
    @Override
    public Boolean downLevel(User user) {
        // 确定当前经验所达到的等级
        List<SystemUserLevel> list = systemUserLevelService.getUsableList();
        if (CollUtil.isEmpty(list)) {
            log.error("系统会员等级未配置，请配置对应数据");
            return Boolean.TRUE;
        }

        SystemUserLevel userLevelConfig = null;
        for (SystemUserLevel systemUserLevel : list) {
            if (user.getExperience() > systemUserLevel.getExperience()) {
                userLevelConfig = systemUserLevel;
                continue;
            }
            break;
        }

        if (ObjectUtil.isNull(userLevelConfig)) {
            log.warn("未找到用户对应的会员等级,uid = " + user.getId());
            return Boolean.TRUE;
        }

        // 判断用户是否还在原等级（现在user.level存储的是grade值）
        Integer currentLevel = user.getLevel() != null ? user.getLevel() : MemberLevelConstants.Level.NONE;
        if (currentLevel.equals(userLevelConfig.getGrade())) {
            return Boolean.TRUE;
        }

        UserLevel newLevel = new UserLevel();
        newLevel.setStatus(true);
        newLevel.setIsDel(false);
        newLevel.setGrade(userLevelConfig.getGrade());
        newLevel.setUid(user.getId());
        newLevel.setLevelId(userLevelConfig.getId());

        // 处理折扣值，如果折扣功能未启用则设置为10（不打折）
        if (userLevelConfig.getDiscountEnabled() != null && userLevelConfig.getDiscountEnabled()) {
            newLevel.setDiscount(userLevelConfig.getDiscount());
        } else {
            newLevel.setDiscount(10.0);
        }

        Date date = DateUtil.nowDateTimeReturnDate(Constants.DATE_FORMAT);
        String mark = Constants.USER_LEVEL_OPERATE_LOG_MARK.replace("【{$userName}】", user.getNickname())
                .replace("{$date}", DateUtil.dateToStr(date, Constants.DATE_FORMAT))
                .replace("{$levelName}", userLevelConfig.getName());
        newLevel.setMark(mark);

        // 更新会员等级（存储grade值而不是id）
        user.setLevel(userLevelConfig.getGrade());
        return transactionTemplate.execute(e -> {
            save(newLevel);
            userService.updateById(user);
            return Boolean.TRUE;
        });
    }

    /**
     * 删除（通过系统等级id）
     * 
     * @param levelId 系统等级id
     * @return Boolean
     */
    @Override
    public Boolean deleteByLevelId(Integer levelId) {
        LambdaUpdateWrapper<UserLevel> luw = Wrappers.lambdaUpdate();
        luw.set(UserLevel::getIsDel, true);
        luw.eq(UserLevel::getLevelId, levelId);
        luw.eq(UserLevel::getIsDel, false);
        return update(luw);
    }

    /**
     * 检查用户等级是否需要注册信息（获取手机号）
     * 
     * @param user 用户对象
     * @return Boolean 是否需要获取手机号
     */
    @Override
    public Boolean checkUserLevelNeedInfo(User user) {
        if (user.getLevel() == null || MemberLevelConstants.Level.NONE.equals(user.getLevel())) {
            return false;
        }

        // 获取用户当前等级配置
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (userLevel == null) {
            return false;
        }

        // 检查是否需要完善资料
        // 1. 检查是否需要手机号
        if (userLevel.getPhoneRequired() != null && userLevel.getPhoneRequired() && StrUtil.isBlank(user.getPhone())) {
            return true;
        }

        // 2. 检查是否需要头像和昵称
        if (userLevel.getAvatarNicknameRequired() != null && userLevel.getAvatarNicknameRequired()
            && (StrUtil.isBlank(user.getAvatar()) || StrUtil.isBlank(user.getNickname()))) {
            return true;
        }

        // 3. 检查是否需要生日信息
        if (userLevel.getBirthdayRequired() != null && userLevel.getBirthdayRequired() && user.getBirthday() == null) {
            return true;
        }

        return false;
    }

    /**
     * 处理用户完善资料后的优惠券发放
     * 
     * @param user 用户对象
     * @return Boolean 是否成功发放优惠券
     */
    @Override
    public Boolean processUserLevelCoupon(User user) {
        if (user.getLevel() == null || MemberLevelConstants.Level.NONE.equals(user.getLevel())) {
            return false;
        }

        // 获取用户当前等级配置
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (userLevel == null || userLevel.getCouponId() == null) {
            return false;
        }

        // 检查是否开启优惠券赠送
        if (userLevel.getCouponEnabled() == null || !userLevel.getCouponEnabled()) {
            return false;
        }

        // 发放优惠券
        return storeCouponUserService.send(user.getId(), userLevel.getCouponId(), "会员等级资料完善赠送");
    }

    /**
     * 处理用户生日赠送的优惠券
     * 
     * @param user 用户对象
     * @return Boolean 是否成功发放生日券
     */
    @Override
    public Boolean processBirthCoupon(User user) {
        if (user.getLevel() == null || MemberLevelConstants.Level.NONE.equals(user.getLevel())) {
            return false;
        }

        // 获取用户当前等级配置
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        if (userLevel == null || userLevel.getBirthCouponId() == null) {
            return false;
        }

        // 检查是否开启生日券赠送
        if (userLevel.getBirthCouponEnabled() == null || !userLevel.getBirthCouponEnabled()) {
            return false;
        }

        // 发放生日券
        return storeCouponUserService.send(user.getId(), userLevel.getBirthCouponId(), "会员等级生日赠送优惠券");
    }
}
