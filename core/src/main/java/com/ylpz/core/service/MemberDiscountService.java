package com.ylpz.core.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ylpz.core.common.request.PageParamRequest;
import com.ylpz.model.discount.MemberDiscount;

import java.util.List;
import java.util.Map;

/**
 * 会员折扣Service接口
 */
public interface MemberDiscountService extends IService<MemberDiscount> {
    
    /**
     * 获取会员折扣列表
     * @param keywords 搜索关键词
     * @param status 状态
     * @param pageParamRequest 分页参数
     * @return 会员折扣列表
     */
    List<MemberDiscount> getList(String keywords, Boolean status, PageParamRequest pageParamRequest);
    
    /**
     * 新增会员折扣
     * @param memberDiscount 会员折扣信息
     * @return 是否成功
     */
    boolean create(MemberDiscount memberDiscount);
    
    /**
     * 修改会员折扣
     * @param memberDiscount 会员折扣信息
     * @return 是否成功
     */
    boolean update(MemberDiscount memberDiscount);
    
    /**
     * 修改会员折扣状态
     * @param id 折扣id
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatus(Integer id, Boolean status);
    
    /**
     * 删除会员折扣
     * @param id 折扣id
     * @return 是否成功
     */
    boolean delete(Integer id);

    /**
     * 复制会员折扣
     * @param id 折扣id
     * @return 是否成功
     */
    boolean copy(Integer id);
    
    /**
     * 获取会员折扣详情
     * @param id 折扣id
     * @return 会员折扣信息
     */
    MemberDiscount getInfo(Integer id);
    
    /**
     * 根据商品ID获取适用的会员折扣
     * @param productId 商品ID
     * @param userId 用户ID
     * @return 适用的会员折扣
     */
    MemberDiscount getByProductAndUser(Integer productId, Integer userId);
    
    /**
     * 获取数据统计
     * @return 统计数据
     */
    Map<String, Object> getStatistics();
} 