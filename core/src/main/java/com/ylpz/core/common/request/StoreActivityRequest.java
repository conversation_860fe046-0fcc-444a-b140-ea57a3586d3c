package com.ylpz.core.common.request;

import java.io.Serializable;
import java.util.List;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 活动管理请求对象
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "StoreActivityRequest对象", description = "活动管理请求对象")
public class StoreActivityRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动名称", required = true)
    @NotBlank(message = "请填写活动名称")
    private String name;

    @ApiModelProperty(value = "活动主图", required = true)
    @NotBlank(message = "请上传活动主图")
    private String image;
    
    @ApiModelProperty(value = "氛围图标")
    private String atmosphereIcon;

    @ApiModelProperty(value = "未领取弹窗图")
    private String unclaimedPopupImage;

    @ApiModelProperty(value = "已领取弹窗图")
    private String claimedPopupImage;

    @ApiModelProperty(value = "活动内容类型（1：图片模式 images，2：富文本模式description）")
    private Integer contentType;

    @ApiModelProperty(value = "活动轮播图（管理员上传的原图）")
    private String imagesAdmin;

    @ApiModelProperty(value = "开始时间", required = true)
    @NotBlank(message = "请选择开始时间")
    private String startTime;

    @ApiModelProperty(value = "结束时间", required = true)
    @NotBlank(message = "请选择结束时间")
    private String stopTime;

    @ApiModelProperty(value = "活动简介")
    private String info;

    @ApiModelProperty(value = "活动详情")
    private String description;

    @ApiModelProperty(value = "关联文章ID")
    private Integer articleId;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "关联商品ID列表")
    private List<Integer> productIds;

    @ApiModelProperty(value = "关联优惠券ID列表")
    private List<Integer> couponIds;

    @ApiModelProperty(value = "关联秒杀ID列表")
    private List<Integer> seckillIds;

    @ApiModelProperty(value = "关联折扣ID列表")
    private List<Integer> discountIds;
}