package com.ylpz.core.common.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ylpz.model.article.Article;
import com.ylpz.model.coupon.StoreCoupon;
import com.ylpz.model.discount.StoreDiscount;
import com.ylpz.model.product.StoreProduct;
import com.ylpz.model.seckill.StoreSeckill;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 活动管理响应对象
 */
@Data
@ApiModel(value = "StoreActivityResponse对象", description = "活动管理响应对象")
public class StoreActivityResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "活动ID")
    private Integer id;

    @ApiModelProperty(value = "活动名称")
    private String name;

    @ApiModelProperty(value = "活动主图")
    private String image;
    
    @ApiModelProperty(value = "氛围图标")
    private String atmosphereIcon;

    @ApiModelProperty(value = "未领取弹窗图")
    private String unclaimedPopupImage;

    @ApiModelProperty(value = "已领取弹窗图")
    private String claimedPopupImage;

    @ApiModelProperty(value = "活动内容类型（1：图片模式，2：富文本模式）")
    private Integer contentType;

    @ApiModelProperty(value = "活动详情图（分割）")
    private List<String> images;

    @ApiModelProperty(value = "活动详情图（管理员上传的原图）")
    private List<String> imagesAdmin;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopTime;

    @ApiModelProperty(value = "状态（0：关闭，1：开启）")
    private Boolean status;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "活动简介")
    private String info;

    @ApiModelProperty(value = "活动详情")
    private String description;

    @ApiModelProperty(value = "关联文章")
    private Article article;

    @ApiModelProperty(value = "关联商品列表")
    private List<StoreProduct> productList;

    @ApiModelProperty(value = "关联优惠券列表")
    private List<StoreCoupon> couponList;

    @ApiModelProperty(value = "关联秒杀列表")
    private List<StoreSeckill> seckillList;

    @ApiModelProperty(value = "关联折扣列表")
    private List<StoreDiscount> discountList;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}