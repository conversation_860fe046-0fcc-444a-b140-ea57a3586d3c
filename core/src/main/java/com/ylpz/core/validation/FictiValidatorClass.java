package com.ylpz.core.validation;

import com.ylpz.core.common.request.StoreProductBatchSettingRequest;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 虚拟销量验证器实现类
 */
public class FictiValidatorClass implements ConstraintValidator<FictiValidator, StoreProductBatchSettingRequest> {
    
    @Override
    public void initialize(FictiValidator constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(StoreProductBatchSettingRequest request, ConstraintValidatorContext context) {
        if (request.getSetFicti() == null || !request.getSetFicti()) {
            return true;
        }
        
        // 当设置了虚拟销量时，虚拟销量值不能为空
        return request.getFicti() != null && request.getFicti() >= 0;
    }
} 