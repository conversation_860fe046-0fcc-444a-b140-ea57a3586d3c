package com.ylpz.core.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Documented
@Constraint(validatedBy = FreightTypeValidatorClass.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface FreightTypeValidator {
    String message() default "当设置快递方式时，必须正确设置运费参数";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
} 