package com.ylpz.core.validation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import javax.validation.Constraint;
import javax.validation.Payload;

@Documented
@Constraint(validatedBy = FictiValidatorClass.class)
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface FictiValidator {
    String message() default "当设置虚拟销量时，虚拟销量值不能为空";

    Class<?>[] groups() default {};

    Class<? extends Payload>[] payload() default {};
} 