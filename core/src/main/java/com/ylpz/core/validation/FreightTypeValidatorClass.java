package com.ylpz.core.validation;

import com.ylpz.core.common.request.StoreProductBatchSettingRequest;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * 快递方式验证器实现类
 */
public class FreightTypeValidatorClass implements ConstraintValidator<FreightTypeValidator, StoreProductBatchSettingRequest> {
    
    @Override
    public void initialize(FreightTypeValidator constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(StoreProductBatchSettingRequest request, ConstraintValidatorContext context) {
        if (request.getSetFreightType() == null || !request.getSetFreightType()) {
            return true;
        }
        
        // 当设置了快递方式时，运费类型不能为空
        if (request.getFreightType() == null) {
            return false;
        }
        
        // 统一邮费
        if (request.getFreightType() == 0) {
            return request.getPostage() != null && request.getPostage().compareTo(java.math.BigDecimal.ZERO) >= 0;
        }
        
        // 运费模板
        if (request.getFreightType() == 1) {
            return request.getTempId() != null && request.getTempId() > 0;
        }
        
        return false;
    }
} 