-- 为StoreCoupon表修改is_fixed_time字段为use_time_type
ALTER TABLE `store_coupon` CHANGE COLUMN `is_fixed_time` `use_time_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '用券时间类型，1-固定时间，2-领取后天数，3-领取后增加天数';

-- 为StoreCoupon表添加after_days字段
ALTER TABLE `store_coupon` ADD COLUMN `after_days` int(11) DEFAULT NULL COMMENT '领取后需等待天数后才可使用' AFTER `day`;

-- 更新已有数据，将使用是否固定时间=1的数据设为use_time_type=1，否则设为use_time_type=2
UPDATE `store_coupon` SET `use_time_type` = 1 WHERE `use_time_type` = 1;
UPDATE `store_coupon` SET `use_time_type` = 2 WHERE `use_time_type` = 0; 