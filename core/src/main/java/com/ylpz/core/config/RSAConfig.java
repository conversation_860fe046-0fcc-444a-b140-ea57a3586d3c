package com.ylpz.core.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * RSA加密配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "rsa")
public class RSAConfig {
    // 私钥路径
    private String privateKeyPath = "key/privateKey.pem";
    
    // 公钥路径 
    private String publicKeyPath = "key/publicKey.pem";
    
    // 外部接口地址
    private String apiUrl = "https://yanglebo.cn/api";
} 