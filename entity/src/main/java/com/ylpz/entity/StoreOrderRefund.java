package com.ylpz.entity;

import com.tangzc.autotable.annotation.Index;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@Table
@Data
@AutoDefine
@AutoMapper
@AutoRepository
public class StoreOrderRefund extends BaseEntity {
    /**
     * 操作人员Id
     */
    private String operatorId;
    /**
     * 操作人员姓名
     */
    private String operatorName;
    /**
     * 操作人员类别
     */
    private Integer operatorType;

    /**
     * 订单编号
     */
    @Index
    private String orderNo;
    /**
     * 退款金额
     */
    private double amount;
    /**
     * 退款单号 商户端
     */
    @Index
    private String refundNo;
    /**
     * 退款id 微信端
     */
    private String refundId;
    /**
     * 退款状态
     */
    private String status;
    /**
     * 退款原因
     */
    private String refundReason;
    /**
     * 退款流水号
     */
    private String transactionId;
    /**
     *
     */
    private String userReceivedAccount;
    private String channel;
    private String successTime;
    private String errorMsg;

}
