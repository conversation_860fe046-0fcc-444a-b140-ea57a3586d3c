package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class SystemMiniHotProduct extends com.ylpz.model.system.SystemMiniHotProduct {

    @BindEntity(conditions = @JoinCondition(selfField = SystemMiniHotProductDefine.productId, joinField = StoreProductDefine.id))
    StoreProduct product;
}
