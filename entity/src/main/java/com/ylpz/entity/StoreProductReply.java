package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
@NoArgsConstructor
public class StoreProductReply extends com.ylpz.model.product.StoreProductReply {
    @BindEntity(conditions = @JoinCondition(selfField = StoreProductReplyDefine.uid, joinField = UserDefine.id))
    private User user;
    @BindEntity(conditions = @JoinCondition(selfField = StoreProductReplyDefine.oid, joinField = StoreOrderDefine.id))
    private StoreOrder order;
}
