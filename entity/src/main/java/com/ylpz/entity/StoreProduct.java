package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.BindField;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.bind.metadata.annotation.JoinOrderBy;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

import java.util.List;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class StoreProduct extends com.ylpz.model.product.StoreProduct {

    @BindEntity(conditions = @JoinCondition(selfField = StoreProductDefine.id, joinField = StoreProductAttrDefine.productId))
    private List<StoreProductAttr> productAttrs;
    @BindEntity(conditions = @JoinCondition(selfField = StoreProductDefine.id, joinField = StoreProductAttrValueDefine.productId)
    ,customCondition = "is_del = 0",orderBy=@JoinOrderBy(field = StoreProductAttrValueDefine.id)
    )
    private List<StoreProductAttrValue> productAttrValues;
//    @BindEntity(conditions = @JoinCondition(selfField = StoreProductDefine.id, joinField = StoreProductDescriptionDefine.productId))
//    private StoreProductDescription description;
//    @BindField(entity = StoreProductDescription.class,
//            field = StoreProductDescriptionDefine.description,
//            conditions = @JoinCondition(selfField = StoreProductDefine.id, joinField = StoreProductDescriptionDefine.productId))
//    private String description;
@BindEntity(conditions = @JoinCondition(selfField = StoreProductDefine.id, joinField =StoreProductReplyDefine.productId)
,customCondition = "is_del = 0 and product_score >=4",last = "limit 2")
private List<StoreProductReply> replys;
}
