package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

import java.util.List;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class StoreProductCombination extends com.ylpz.model.product.ProductCombination {

    @BindEntity(conditions = @JoinCondition(selfField = StoreProductCombinationDefine.id, joinField = StoreProductCombinationDetailDefine.combinationId))
    private List<StoreProductCombinationDetail> details;
}
