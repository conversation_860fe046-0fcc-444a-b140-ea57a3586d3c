package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class StoreCart extends com.ylpz.model.cat.StoreCart {

    @BindEntity(conditions = @JoinCondition(selfField = StoreCartDefine.productId, joinField = StoreProductDefine.id))
    private StoreProduct storeProduct;

    @BindEntity(conditions = @JoinCondition(selfField = StoreCartDefine.productAttrUnique, joinField = StoreProductAttrValueDefine.id))
    private StoreProductAttrValue productAttrValue;

}

