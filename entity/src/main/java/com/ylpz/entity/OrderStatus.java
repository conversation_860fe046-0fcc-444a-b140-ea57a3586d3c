package com.ylpz.entity;

public enum OrderStatus {
    ALL("All", "全部"),
    UNPAID("Unpaid", "待付款"),
    UNSHIPPED("Unshipped", "待发货"),
    SHIPPED("Shipped", "待收货"),
    UNREVIEWED("Unreviewed", "待评价"),
    COMPLETED("Completed", "已完成"),
    REFUNDING("Refunding", "退款中"),
    REFUNDED("Refunded", "已退款"),
    DELETED("Deleted", "已删除"),
    CLOSED("Closed", "已关闭"),
    APPLY_REFUND("ApplyRefund", "申请退款"),
    TO_BE_AUDIT_ADDRESS("ToBeAuditAddress", "待审核收货地址"),
    AUDIT_ADDRESS_PASS("AuditAddressPass", "收货地址审核通过"),
    AUDIT_ADDRESS_REFUSE("AuditAddressRefuse", "收货地址审核失败"),
    AUDIT_ADDRESS_ALL("AuditAddressAll", "全部审核地址");

    /**
     * 状态码
     */
    public String code;

    /**
     * 状态描述
     */
    public String desc;

    OrderStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
