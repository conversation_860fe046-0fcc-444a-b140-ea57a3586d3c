package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

import java.util.List;

@Table
@Data
@AutoDefine
@AutoMapper
@AutoRepository
public class WxCallback extends BaseEntity {
    private String appid;
    private String mchid;
    private String outTradeNo;
    private String transactionId;
    private String tradeType;
    private String tradeState;
    private String tradeStateDesc;
    private String bankType;
    private String attach;
    private String successTime;
    private String openid;
    private int total;
    private int payerTotal;
    private String currency;
    private String payerCurrency;

}
