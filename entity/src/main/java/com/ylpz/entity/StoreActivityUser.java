package com.ylpz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@AutoMapper
@AutoRepository
@AutoDefine
@Table("store_activity_user")
@Data
public class StoreActivityUser {
    private Integer userId;

    private Integer activityId;

}
