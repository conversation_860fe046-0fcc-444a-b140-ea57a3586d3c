package com.ylpz.entity;

import com.tangzc.autotable.annotation.Index;
import com.tangzc.autotable.annotation.enums.IndexTypeEnum;
import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@Table
@Data
@AutoDefine
@AutoMapper
@AutoRepository
public class WxOrder extends BaseEntity {
    /**
     * 订单名称
     */
    private String title;
    /**
     * 描述
     */
    private String description;
    /**
     * 订单总价格
     */
    private double price;
    /**
     * 订单编号
     */
    @Index(name = "order_index",type = IndexTypeEnum.UNIQUE,comment = "订单号索引")
    private String orderNo;

    /**
     * 订单用户
     */
    @Index(name = "user_index")
    private Integer userId;
    @BindEntity(entity = User.class, conditions = @JoinCondition(selfField = "userId"))
    private User wxUser;

    /**
     * 邮寄地址
     */
    private Integer addressId;
    @BindEntity(entity = UserAddress.class, conditions = @JoinCondition(selfField = "userId"))
    private UserAddress wxAddress;

    private Integer status;
    private String statusName;
    private String remark;

}
