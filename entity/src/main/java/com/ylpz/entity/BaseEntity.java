package com.ylpz.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.tangzc.autotable.annotation.ColumnComment;
import com.tangzc.mpe.annotation.InsertFillTime;
import com.tangzc.mpe.annotation.InsertUpdateFillTime;
import com.tangzc.mpe.autotable.annotation.Column;
import com.tangzc.mpe.autotable.annotation.Table;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
@Data
@Getter
@Setter
public class BaseEntity implements Serializable {
    @TableId(type = IdType.AUTO)
    @ColumnComment("主键")
    protected Integer id;
    @InsertFillTime
    @ColumnComment("创建时间")
    protected Date createTime;
    @InsertUpdateFillTime
    @ColumnComment("更新时间")
    protected Date updateTime;
}
