package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.BindField;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class User extends com.ylpz.model.user.User {
    private String openid;
    @BindEntity(   conditions = @JoinCondition(selfField = UserDefine.level, joinField = SystemUserLevelDefine.id))
    private SystemUserLevel systemUserLevel;
//    //享受折扣
//    @BindField(entity = SystemUserLevel.class,
//            field = SystemUserLevelDefine.discount,
//            conditions = @JoinCondition(selfField = UserDefine.level, joinField = SystemUserLevelDefine.id))
//    private double discount;

    private String payPassword;
    //是否是新人用于发放优惠券
    private boolean isNew;

}
