package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.BindField;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class UserExtract extends com.ylpz.model.finance.UserExtract {

    //提现用户
//    @BindEntity(
//            conditions = @JoinCondition(selfField = UserExtractDefine.uid, joinField = UserDefine.id))
//    private User extractUser;

    private String openid;
    private String packageInfo;
    private String appid;
    private String merchid;
}
