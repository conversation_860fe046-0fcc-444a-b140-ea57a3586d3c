package com.ylpz.entity;

import com.tangzc.mpe.autotable.annotation.Table;
import com.tangzc.mpe.bind.metadata.annotation.BindEntity;
import com.tangzc.mpe.bind.metadata.annotation.BindField;
import com.tangzc.mpe.bind.metadata.annotation.JoinCondition;
import com.tangzc.mpe.bind.metadata.annotation.JoinOrderBy;
import com.tangzc.mpe.processer.annotation.AutoDefine;
import com.tangzc.mpe.processer.annotation.AutoMapper;
import com.tangzc.mpe.processer.annotation.AutoRepository;
import lombok.Data;

import java.util.List;

@AutoMapper
@AutoRepository
@AutoDefine
@Table
@Data
public class StoreOrder extends com.ylpz.model.order.StoreOrder {

    //订单商品详细
    @BindEntity(conditions = @JoinCondition(selfField = StoreOrderDefine.id, joinField = StoreOrderInfoDefine.orderId))
    private List<StoreOrderInfo> orderInfos;
    //订单状态
    @BindEntity(conditions = @JoinCondition(selfField = StoreOrderDefine.id, joinField = StoreOrderStatusDefine.oid))
    private List<StoreOrderStatus> orderStatuses;

    //订单优惠券
    @BindEntity(conditions = @JoinCondition(selfField = StoreOrderDefine.id, joinField = StoreCouponUserDefine.oid))
    private List<StoreCouponUser> orderCoupons;

    private Integer addressId;
    private Integer payUserId;
    private String cartsId;
    private String bagAddr;
    private String bagMark;

    //订单邮寄地址
    @BindEntity(conditions = @JoinCondition(selfField = StoreOrderDefine.addressId, joinField = UserAddressDefine.id))
    private UserAddress addressInfo;
    //订单支付用户
    @BindEntity(conditions = @JoinCondition(selfField = StoreOrderDefine.uid, joinField = UserDefine.id))
    private User payUser;
    private String waybillToken;
    private String transactionId;

}
